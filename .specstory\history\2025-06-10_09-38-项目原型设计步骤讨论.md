<!-- Generated by SpecStory -->

# 项目原型设计步骤讨论 (2025-06-10 17:38:31)

_**User**_

请你根据方案 @月度工资异动处理系统详细设计方案.md  ，一步一步思考，给出项目的原型设计，保存到目录 design

---

_**Assistant**_



---

我来根据详细设计方案，一步一步思考并创建项目的原型设计。

---



---



---



---



---



---

<details>
            <summary>Listed current directory • **8** results</summary>
        
| Name |
|-------|
| 📁 `temp` |
| 📁 `docs` |
| 📁 `.cursor` |
| 📁 `template` |
| 📁 `data` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📄 `.cursor.json` |

</details>

---



---

```diff
- 
+ # 月度工资异动处理系统原型设计
+ 
+ ## 设计目录说明
+ 
+ 本目录包含月度工资异动处理系统的完整原型设计，基于详细设计方案文档创建。
+ 
+ ## 设计文档列表
+ 
+ 1. **系统架构设计** (`system_architecture.md`)
+    - 整体系统架构图
+    - 技术栈选型说明
+    - 模块依赖关系
+ 
+ 2. **数据库设计** (`database_design.md`)
+    - 数据库表结构设计
+    - 数据关系图
+    - 索引和优化策略
+ 
+ 3. **用户界面原型** (`ui_prototype.md`)
+    - 主界面布局设计
+    - 交互流程设计
+    - 用户体验优化
+ 
+ 4. **业务流程设计** (`business_process.md`)
+    - 核心业务流程图
+    - 异动处理流程
+    - 异常处理流程
+ 
+ 5. **模块详细设计** (`module_design.md`)
+    - 各模块详细设计
+    - 接口定义
+    - 数据结构设计
+ 
+ 6. **配置和规则设计** (`config_rules_design.md`)
+    - 异动规则配置
+    - 系统配置项
+    - 模板管理策略
+ 
+ ## 设计原则
+ 
+ - **模块化设计**：高内聚、低耦合
+ - **可扩展性**：支持功能和规则扩展
+ - **用户友好**：简洁直观的操作界面
+ - **数据安全**：完善的数据保护和备份机制
+ - **稳定可靠**：异常处理和错误恢复机制
+ 
+ ## 技术特点
+ 
+ - **单机版应用**：基于Tkinter的桌面应用
+ - **SQLite数据库**：轻量级、易部署
+ - **Excel处理**：支持.xls和.xlsx格式
+ - **模板引擎**：灵活的文档生成机制
+ - **一键打包**：PyInstaller生成exe文件 
```

---

现在我开始创建详细的原型设计文档：

## 1. 系统架构设计

---

```diff
- 
+ # 系统架构设计
+ 
+ ## 1. 整体系统架构
+ 
+ ### 1.1 架构概览
+ 
+ ```mermaid
+ graph TB
+     A[用户界面层 - GUI Layer] --> B[业务逻辑层 - Business Layer]
+     B --> C[数据访问层 - Data Access Layer]
+     C --> D[数据存储层 - Storage Layer]
+     
+     A --> E[Tkinter主界面]
+     A --> F[文件选择对话框]
+     A --> G[进度显示界面]
+     A --> H[结果预览界面]
+     
+     B --> I[主控制器]
+     B --> J[人员匹配器]
+     B --> K[工资计算引擎]
+     B --> L[报表生成器]
+     B --> M[模板管理器]
+     
+     C --> N[Excel处理器]
+     C --> O[数据库管理器]
+     C --> P[数据同步器]
+     C --> Q[配置管理器]
+     
+     D --> R[SQLite数据库]
+     D --> S[Excel文件]
+     D --> T[Word模板]
+     D --> U[配置文件]
+ ```
+ 
+ ### 1.2 架构分层说明
+ 
+ #### 1.2.1 用户界面层 (GUI Layer)
+ - **职责**: 用户交互、界面展示、事件处理
+ - **技术**: Tkinter + ttk
+ - **组件**:
+   - 主窗口管理器
+   - 文件选择对话框
+   - 进度条和状态显示
+   - 结果预览和错误提示
+ 
+ #### 1.2.2 业务逻辑层 (Business Layer)
+ - **职责**: 核心业务逻辑、流程控制、规则引擎
+ - **组件**:
+   - 主控制器 (`MainController`)
+   - 人员匹配器 (`PersonMatcher`)
+   - 工资计算引擎 (`SalaryCalculator`)
+   - 报表生成器 (`ReportGenerator`)
+   - 模板管理器 (`TemplateManager`)
+ 
+ #### 1.2.3 数据访问层 (Data Access Layer)
+ - **职责**: 数据读写、格式转换、数据验证
+ - **组件**:
+   - Excel处理器 (`ExcelProcessor`)
+   - 数据库管理器 (`DatabaseManager`)
+   - 数据同步器 (`DataSynchronizer`)
+   - 配置管理器 (`ConfigManager`)
+ 
+ #### 1.2.4 数据存储层 (Storage Layer)
+ - **职责**: 数据持久化存储
+ - **组件**:
+   - SQLite数据库文件
+   - Excel工资表文件
+   - Word模板文件
+   - JSON配置文件
+ 
+ ## 2. 技术栈详细选型
+ 
+ ### 2.1 核心技术栈
+ 
+ | 技术组件 | 选择方案 | 版本要求 | 选择理由 |
+ |---------|---------|---------|---------|
+ | **开发语言** | Python | 3.8+ | 生态丰富、易于维护 |
+ | **GUI框架** | Tkinter + ttk | 内置 | 无需额外安装、跨平台 |
+ | **数据处理** | pandas | 2.0+ | 强大的数据处理能力 |
+ | **Excel处理** | openpyxl + xlrd | 最新版 | 完整Excel格式支持 |
+ | **数据库** | SQLite | 内置 | 轻量级、单文件部署 |
+ | **文档处理** | python-docx | 最新版 | Word文档生成和编辑 |
+ | **打包工具** | PyInstaller | 最新版 | 生成独立可执行文件 |
+ | **日志系统** | logging | 内置 | 分级日志管理 |
+ 
+ ### 2.2 依赖包清单
+ 
+ ```txt
+ # 核心数据处理
+ pandas>=2.0.0
+ openpyxl>=3.1.0
+ xlrd>=2.0.0
+ xlwt>=1.3.0
+ xlsxwriter>=3.0.0
+ 
+ # 文档处理
+ python-docx>=0.8.11
+ 
+ # 数据库ORM (可选)
+ sqlalchemy>=2.0.0
+ 
+ # 界面增强 (可选)
+ ttkbootstrap>=1.10.0
+ 
+ # 开发和测试
+ pytest>=7.0.0
+ pytest-cov>=4.0.0
+ 
+ # 打包部署
+ pyinstaller>=5.0.0
+ auto-py-to-exe>=2.3.0
+ ```
+ 
+ ## 3. 模块架构设计
+ 
+ ### 3.1 核心模块关系图
+ 
+ ```mermaid
+ graph TD
+     A[main.py 主程序入口] --> B[MainController 主控制器]
+     
+     B --> C[GUI模块组]
+     B --> D[业务逻辑模块组]
+     B --> E[数据访问模块组]
+     
+     C --> F[MainWindow 主窗口]
+     C --> G[FileDialog 文件对话框]
+     C --> H[ProgressDialog 进度对话框]
+     C --> I[ResultViewer 结果查看器]
+     
+     D --> J[PersonMatcher 人员匹配器]
+     D --> K[SalaryCalculator 工资计算器]
+     D --> L[ReportGenerator 报表生成器]
+     D --> M[TemplateManager 模板管理器]
+     D --> N[RuleEngine 规则引擎]
+     
+     E --> O[ExcelProcessor Excel处理器]
+     E --> P[DatabaseManager 数据库管理器]
+     E --> Q[DataSynchronizer 数据同步器]
+     E --> R[ConfigManager 配置管理器]
+     
+     P --> S[EmployeeDAO 员工数据访问]
+     P --> T[SalaryDAO 工资数据访问]
+     P --> U[ChangeDAO 异动数据访问]
+     P --> V[ConfigDAO 配置数据访问]
+ ```
+ 
+ ### 3.2 模块依赖层次
+ 
+ ```mermaid
+ graph LR
+     subgraph "Level 1 - 基础工具层"
+         A1[日志管理]
+         A2[配置管理] 
+         A3[数据验证]
+         A4[异常处理]
+     end
+     
+     subgraph "Level 2 - 数据访问层"
+         B1[Excel处理器]
+         B2[数据库管理器]
+         B3[文件系统操作]
+         B4[数据同步器]
+     end
+     
+     subgraph "Level 3 - 业务逻辑层"
+         C1[人员匹配器]
+         C2[工资计算器]
+         C3[规则引擎]
+         C4[报表生成器]
+         C5[模板管理器]
+     end
+     
+     subgraph "Level 4 - 应用控制层"
+         D1[主控制器]
+         D2[工作流管理器]
+     end
+     
+     subgraph "Level 5 - 用户界面层"
+         E1[主界面]
+         E2[对话框]
+         E3[组件]
+     end
+     
+     A1 --> B1
+     A2 --> B2
+     A3 --> B3
+     A4 --> B4
+     
+     B1 --> C1
+     B2 --> C2
+     B3 --> C3
+     B4 --> C4
+     
+     C1 --> D1
+     C2 --> D1
+     C3 --> D1
+     C4 --> D1
+     C5 --> D1
+     
+     D1 --> E1
+     D2 --> E2
+ ```
+ 
+ ## 4. 设计模式应用
+ 
+ ### 4.1 主要设计模式
+ 
+ #### 4.1.1 MVC模式 (Model-View-Controller)
+ - **Model**: 数据模型和业务逻辑
+ - **View**: Tkinter用户界面
+ - **Controller**: 主控制器协调模型和视图
+ 
+ #### 4.1.2 策略模式 (Strategy Pattern)
+ - **应用场景**: 异动规则处理
+ - **实现**: 不同异动类型采用不同处理策略
+ 
+ ```python
+ class SalaryChangeStrategy:
+     def apply_change(self, employee_data, change_data):
+         pass
+ 
+ class ResignationStrategy(SalaryChangeStrategy):
+     def apply_change(self, employee_data, change_data):
+         # 离职处理逻辑
+         pass
+ 
+ class PromotionStrategy(SalaryChangeStrategy):
+     def apply_change(self, employee_data, change_data):
+         # 晋升处理逻辑
+         pass
+ ```
+ 
+ #### 4.1.3 工厂模式 (Factory Pattern)
+ - **应用场景**: 报表生成器创建
+ - **实现**: 根据报表类型创建对应生成器
+ 
+ ```python
+ class ReportGeneratorFactory:
+     @staticmethod
+     def create_generator(report_type):
+         if report_type == "summary":
+             return SummaryReportGenerator()
+         elif report_type == "approval":
+             return ApprovalReportGenerator()
+         # ... 其他类型
+ ```
+ 
+ #### 4.1.4 观察者模式 (Observer Pattern)
+ - **应用场景**: 进度通知和状态更新
+ - **实现**: 业务处理进度通知GUI更新
+ 
+ ```python
+ class ProgressNotifier:
+     def __init__(self):
+         self.observers = []
+     
+     def add_observer(self, observer):
+         self.observers.append(observer)
+     
+     def notify_progress(self, progress, message):
+         for observer in self.observers:
+             observer.update_progress(progress, message)
+ ```
+ 
+ #### 4.1.5 单例模式 (Singleton Pattern)
+ - **应用场景**: 配置管理器、日志管理器
+ - **实现**: 确保全局唯一实例
+ 
+ ```python
+ class ConfigManager:
+     _instance = None
+     
+     def __new__(cls):
+         if cls._instance is None:
+             cls._instance = super().__new__(cls)
+         return cls._instance
+ ```
+ 
+ ## 5. 性能与扩展性设计
+ 
+ ### 5.1 性能优化策略
+ 
+ #### 5.1.1 内存管理
+ - **分批处理**: 大数据集分块处理，避免内存溢出
+ - **延迟加载**: 按需加载数据，减少内存占用
+ - **缓存机制**: 常用数据缓存，提高访问速度
+ 
+ #### 5.1.2 数据库优化
+ - **索引策略**: 关键字段建立索引
+ - **批量操作**: 批量插入和更新，减少数据库访问次数
+ - **连接池**: 数据库连接复用
+ 
+ #### 5.1.3 文件处理优化
+ - **流式读取**: 大文件流式处理
+ - **并行处理**: 多线程处理独立任务
+ - **压缩存储**: 历史数据压缩存储
+ 
+ ### 5.2 扩展性设计
+ 
+ #### 5.2.1 模块化扩展
+ - **插件架构**: 支持功能模块插件化扩展
+ - **配置驱动**: 通过配置文件扩展功能
+ - **接口标准化**: 统一的接口规范
+ 
+ #### 5.2.2 规则引擎扩展
+ - **规则配置化**: 异动规则外部配置
+ - **动态加载**: 运行时规则更新
+ - **版本管理**: 规则版本控制
+ 
+ #### 5.2.3 模板系统扩展
+ - **模板热更新**: 无需重启即可更新模板
+ - **多格式支持**: 支持多种文档格式
+ - **自定义模板**: 用户自定义模板功能
+ 
+ ## 6. 安全性设计
+ 
+ ### 6.1 数据安全
+ - **数据加密**: 敏感数据加密存储
+ - **访问控制**: 文件和数据访问权限控制
+ - **数据备份**: 自动数据备份和恢复
+ 
+ ### 6.2 操作安全
+ - **操作审计**: 详细操作日志记录
+ - **回滚机制**: 错误操作回滚功能
+ - **权限验证**: 关键操作权限验证
+ 
+ ### 6.3 系统安全
+ - **异常处理**: 完善的异常捕获和处理
+ - **资源限制**: 系统资源使用限制
+ - **版本控制**: 代码和配置版本管理
+ 
+ ## 7. 部署架构
+ 
+ ### 7.1 单机部署架构
+ 
+ ```mermaid
+ graph TB
+     A[Windows桌面环境] --> B[Python运行时环境]
+     B --> C[应用程序主体]
+     C --> D[SQLite数据库]
+     C --> E[Excel文件处理]
+     C --> F[Word文档生成]
+     C --> G[配置文件]
+     C --> H[日志文件]
+     C --> I[模板文件]
+     
+     subgraph "文件系统结构"
+         J[应用根目录]
+         J --> K[src/ 源码目录]
+         J --> L[data/ 数据目录]
+         J --> M[template/ 模板目录]
+         J --> N[output/ 输出目录]
+         J --> O[logs/ 日志目录]
+         J --> P[config/ 配置目录]
+     end
+ ```
+ 
+ ### 7.2 打包部署策略
+ 
+ #### 7.2.1 PyInstaller配置
+ ```python
+ # build.spec 配置文件
+ a = Analysis(['src/main.py'],
+              pathex=['.'],
+              binaries=[],
+              datas=[('template', 'template'),
+                     ('config', 'config')],
+              hiddenimports=['openpyxl', 'xlrd'],
+              hookspath=[],
+              runtime_hooks=[],
+              excludes=[],
+              win_no_prefer_redirects=False,
+              win_private_assemblies=False,
+              cipher=None,
+              noarchive=False)
+ ```
+ 
+ #### 7.2.2 安装包结构
+ ```
+ SalaryChangeSystem/
+ ├── SalaryChangeSystem.exe    # 主程序
+ ├── template/                 # 模板文件
+ ├── config/                   # 配置文件
+ ├── docs/                     # 用户文档
+ ├── examples/                 # 示例数据
+ └── README.txt               # 使用说明
+ ```
+ 
+ 这个系统架构设计提供了完整的技术架构蓝图，确保系统具备良好的可维护性、扩展性和稳定性。 
```

---

## 2. 数据库设计

---

```diff
- 
+ # 数据库设计
+ 
+ ## 1. 数据库概览
+ 
+ ### 1.1 设计原则
+ - **轻量级部署**: 使用SQLite，单文件存储，易于部署和备份
+ - **数据完整性**: 支持外键约束，确保数据一致性
+ - **性能优化**: 合理设计索引，提升查询效率
+ - **扩展友好**: 预留扩展字段，支持业务发展
+ 
+ ### 1.2 数据库选型对比
+ 
+ | 特性 | SQLite | MySQL | PostgreSQL | SQL Server |
+ |------|--------|-------|------------|------------|
+ | 部署复杂度 | ★★★★★ | ★★ | ★★ | ★ |
+ | 单机性能 | ★★★★ | ★★★ | ★★★ | ★★★★ |
+ | 维护成本 | ★★★★★ | ★★ | ★★ | ★ |
+ | 功能完整性 | ★★★ | ★★★★ | ★★★★★ | ★★★★★ |
+ | 适用场景 | 单机桌面应用 | 中小型Web应用 | 大型应用 | 企业级应用 |
+ 
+ ## 2. 数据模型设计
+ 
+ ### 2.1 核心实体关系图 (ERD)
+ 
+ ```mermaid
+ erDiagram
+     EMPLOYEES ||--o{ SALARY_DATA : has
+     EMPLOYEES ||--o{ SALARY_CHANGES : has
+     SALARY_CHANGES }o--|| CHANGE_RULES : follows
+     PROCESSING_HISTORY ||--o{ SALARY_CHANGES : includes
+     
+     EMPLOYEES {
+         int id PK
+         varchar employee_id UK "工号"
+         varchar id_card UK "身份证号"
+         varchar name "姓名"
+         varchar department "部门"
+         varchar position "职位"
+         varchar employee_type "人员类别"
+         varchar status "状态"
+         datetime created_time
+         datetime updated_time
+     }
+     
+     SALARY_DATA {
+         int id PK
+         varchar employee_id FK "工号"
+         varchar salary_month "工资月份"
+         decimal basic_salary "岗位工资"
+         decimal grade_salary "薪级工资"
+         decimal allowance "津贴"
+         decimal performance_basic "基础性绩效"
+         decimal performance_reward "奖励性绩效"
+         decimal gross_salary "应发工资"
+         decimal net_salary "实发工资"
+         datetime created_time
+     }
+     
+     SALARY_CHANGES {
+         int id PK
+         varchar employee_id FK "工号"
+         varchar change_month "异动月份"
+         varchar change_type "异动类型"
+         text change_reason "异动原因"
+         date effective_date "生效日期"
+         text affected_fields "影响字段JSON"
+         text original_values "原始值JSON"
+         text new_values "新值JSON"
+         decimal change_amount "变动金额"
+         varchar operator "操作人"
+         varchar approval_status "审批状态"
+         datetime created_time
+     }
+     
+     CHANGE_RULES {
+         int id PK
+         varchar rule_name UK "规则名称"
+         varchar change_type "异动类型"
+         varchar operation_type "操作类型"
+         text affected_fields "影响字段JSON"
+         text calculation_formula "计算公式"
+         text rule_config "规则配置JSON"
+         boolean is_active "是否启用"
+         int priority "优先级"
+         datetime created_time
+     }
+     
+     PROCESSING_HISTORY {
+         int id PK
+         varchar batch_id UK "批次ID"
+         varchar process_month "处理月份"
+         varchar process_type "处理类型"
+         text input_files "输入文件JSON"
+         text output_files "输出文件JSON"
+         int total_employees "处理员工数"
+         int success_count "成功数量"
+         int error_count "错误数量"
+         decimal total_amount "总金额变动"
+         varchar process_status "处理状态"
+         datetime start_time
+         datetime end_time
+     }
+     
+     SYSTEM_CONFIG {
+         int id PK
+         varchar config_key UK "配置键"
+         text config_value "配置值"
+         varchar config_type "配置类型"
+         text description "描述"
+         datetime created_time
+         datetime updated_time
+     }
+ ```
+ 
+ ## 3. 详细表结构设计
+ 
+ ### 3.1 员工基础信息表 (employees)
+ 
+ ```sql
+ CREATE TABLE employees (
+     id INTEGER PRIMARY KEY AUTOINCREMENT,
+     employee_id VARCHAR(20) UNIQUE NOT NULL COMMENT '工号',
+     id_card VARCHAR(18) UNIQUE COMMENT '身份证号',
+     name VARCHAR(50) NOT NULL COMMENT '姓名',
+     department VARCHAR(100) COMMENT '部门',
+     position VARCHAR(100) COMMENT '职位',
+     employee_type VARCHAR(50) COMMENT '人员类别',
+     hire_date DATE COMMENT '入职日期',
+     status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active/inactive/resigned',
+     phone VARCHAR(20) COMMENT '联系电话',
+     email VARCHAR(100) COMMENT '邮箱',
+     education VARCHAR(50) COMMENT '学历',
+     graduation_school VARCHAR(200) COMMENT '毕业学校',
+     created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     
+     -- 约束定义
+     CONSTRAINT chk_status CHECK (status IN ('active', 'inactive', 'resigned')),
+     CONSTRAINT chk_employee_id CHECK (LENGTH(employee_id) = 8 AND employee_id GLOB '[0-9]*')
+ );
+ 
+ -- 索引创建
+ CREATE INDEX idx_employees_id ON employees(employee_id);
+ CREATE INDEX idx_employees_idcard ON employees(id_card);
+ CREATE INDEX idx_employees_name ON employees(name);
+ CREATE INDEX idx_employees_dept ON employees(department);
+ CREATE INDEX idx_employees_status ON employees(status);
+ ```
+ 
+ **字段说明：**
+ - `employee_id`: 8位数字工号，系统主要匹配字段
+ - `id_card`: 18位身份证号，重要匹配字段
+ - `status`: 员工状态，用于过滤有效员工
+ - `employee_type`: 人员类别（在编在岗、编外A岗、编外B岗等）
+ 
+ ### 3.2 工资数据表 (salary_data)
+ 
+ ```sql
+ CREATE TABLE salary_data (
+     id INTEGER PRIMARY KEY AUTOINCREMENT,
+     employee_id VARCHAR(20) NOT NULL COMMENT '工号',
+     salary_month VARCHAR(7) NOT NULL COMMENT '工资月份（YYYY-MM）',
+     
+     -- 基本工资项目
+     basic_salary DECIMAL(10,2) DEFAULT 0 COMMENT '岗位工资',
+     grade_salary DECIMAL(10,2) DEFAULT 0 COMMENT '薪级工资',
+     allowance DECIMAL(10,2) DEFAULT 0 COMMENT '津贴',
+     remaining_allowance DECIMAL(10,2) DEFAULT 0 COMMENT '结余津贴',
+     
+     -- 绩效工资项目
+     performance_basic DECIMAL(10,2) DEFAULT 0 COMMENT '基础性绩效',
+     performance_reward DECIMAL(10,2) DEFAULT 0 COMMENT '奖励性绩效预发',
+     
+     -- 补贴项目
+     subsidy_transport DECIMAL(10,2) DEFAULT 0 COMMENT '交通补贴',
+     subsidy_housing DECIMAL(10,2) DEFAULT 0 COMMENT '住房补贴',
+     subsidy_property DECIMAL(10,2) DEFAULT 0 COMMENT '物业补贴',
+     subsidy_communication DECIMAL(10,2) DEFAULT 0 COMMENT '通讯补贴',
+     subsidy_car DECIMAL(10,2) DEFAULT 0 COMMENT '车补',
+     subsidy_health DECIMAL(10,2) DEFAULT 0 COMMENT '卫生费',
+     
+     -- 其他项目
+     bonus DECIMAL(10,2) DEFAULT 0 COMMENT '补发',
+     advance DECIMAL(10,2) DEFAULT 0 COMMENT '借支',
+     
+     -- 汇总项目
+     gross_salary DECIMAL(10,2) DEFAULT 0 COMMENT '应发工资',
+     
+     -- 扣除项目
+     fund_housing DECIMAL(10,2) DEFAULT 0 COMMENT '公积金',
+     insurance_pension DECIMAL(10,2) DEFAULT 0 COMMENT '养老保险',
+     insurance_medical DECIMAL(10,2) DEFAULT 0 COMMENT '医疗保险',
+     insurance_unemployment DECIMAL(10,2) DEFAULT 0 COMMENT '失业保险',
+     
+     -- 实发工资
+     net_salary DECIMAL(10,2) DEFAULT 0 COMMENT '实发工资',
+     
+     -- 元数据
+     data_source VARCHAR(100) COMMENT '数据来源',
+     import_batch_id VARCHAR(50) COMMENT '导入批次ID',
+     is_calculated BOOLEAN DEFAULT FALSE COMMENT '是否已计算',
+     created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     
+     -- 约束定义
+     FOREIGN KEY (employee_id) REFERENCES employees(employee_id),
+     UNIQUE(employee_id, salary_month),
+     CONSTRAINT chk_salary_month CHECK (salary_month GLOB '[0-9][0-9][0-9][0-9]-[0-1][0-9]'),
+     CONSTRAINT chk_gross_positive CHECK (gross_salary >= 0)
+ );
+ 
+ -- 索引创建
+ CREATE INDEX idx_salary_employee_month ON salary_data(employee_id, salary_month);
+ CREATE INDEX idx_salary_month ON salary_data(salary_month);
+ CREATE INDEX idx_salary_batch ON salary_data(import_batch_id);
+ CREATE INDEX idx_salary_calculated ON salary_data(is_calculated);
+ ```
+ 
+ ### 3.3 异动记录表 (salary_changes)
+ 
+ ```sql
+ CREATE TABLE salary_changes (
+     id INTEGER PRIMARY KEY AUTOINCREMENT,
+     employee_id VARCHAR(20) NOT NULL COMMENT '工号',
+     change_month VARCHAR(7) NOT NULL COMMENT '异动月份',
+     change_type VARCHAR(50) NOT NULL COMMENT '异动类型',
+     change_subtype VARCHAR(50) COMMENT '异动子类型',
+     change_reason TEXT COMMENT '异动原因',
+     effective_date DATE COMMENT '生效日期',
+     end_date DATE COMMENT '结束日期（如适用）',
+     
+     -- 变动详情（JSON格式存储）
+     affected_fields TEXT COMMENT '影响字段（JSON格式）',
+     original_values TEXT COMMENT '原始值（JSON格式）',
+     new_values TEXT COMMENT '新值（JSON格式）',
+     calculation_details TEXT COMMENT '计算详情（JSON格式）',
+     
+     -- 变动金额
+     change_amount DECIMAL(10,2) COMMENT '变动金额',
+     percentage_change DECIMAL(5,2) COMMENT '变动百分比',
+     
+     -- 处理信息
+     operator VARCHAR(50) COMMENT '操作人',
+     approval_status VARCHAR(20) DEFAULT 'pending' COMMENT '审批状态',
+     approval_time DATETIME COMMENT '审批时间',
+     approver VARCHAR(50) COMMENT '审批人',
+     
+     -- 状态管理
+     processing_status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态',
+     error_message TEXT COMMENT '错误信息',
+     
+     -- 元数据
+     created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     processed_time DATETIME COMMENT '处理完成时间',
+     remarks TEXT COMMENT '备注',
+     
+     -- 约束定义
+     FOREIGN KEY (employee_id) REFERENCES employees(employee_id),
+     CONSTRAINT chk_approval_status CHECK (approval_status IN ('pending', 'approved', 'rejected')),
+     CONSTRAINT chk_processing_status CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed'))
+ );
+ 
+ -- 索引创建
+ CREATE INDEX idx_changes_employee_month ON salary_changes(employee_id, change_month);
+ CREATE INDEX idx_changes_type ON salary_changes(change_type);
+ CREATE INDEX idx_changes_status ON salary_changes(processing_status);
+ CREATE INDEX idx_changes_approval ON salary_changes(approval_status);
+ CREATE INDEX idx_changes_effective_date ON salary_changes(effective_date);
+ ```
+ 
+ ### 3.4 异动规则配置表 (change_rules)
+ 
+ ```sql
+ CREATE TABLE change_rules (
+     id INTEGER PRIMARY KEY AUTOINCREMENT,
+     rule_name VARCHAR(100) UNIQUE NOT NULL COMMENT '规则名称',
+     rule_code VARCHAR(50) UNIQUE NOT NULL COMMENT '规则代码',
+     change_type VARCHAR(50) NOT NULL COMMENT '异动类型',
+     change_subtype VARCHAR(50) COMMENT '异动子类型',
+     
+     -- 操作定义
+     operation_type VARCHAR(20) NOT NULL COMMENT '操作类型：add/subtract/replace/clear/calculate',
+     affected_fields TEXT NOT NULL COMMENT '影响字段（JSON格式）',
+     calculation_formula TEXT COMMENT '计算公式',
+     
+     -- 规则配置
+     rule_config TEXT COMMENT '规则配置（JSON格式）',
+     condition_expression TEXT COMMENT '适用条件表达式',
+     
+     -- 规则元数据
+     description TEXT COMMENT '规则描述',
+     examples TEXT COMMENT '使用示例',
+     is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
+     priority INTEGER DEFAULT 0 COMMENT '优先级',
+     
+     -- 版本控制
+     version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
+     created_by VARCHAR(50) COMMENT '创建人',
+     updated_by VARCHAR(50) COMMENT '更新人',
+     created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     
+     -- 约束定义
+     CONSTRAINT chk_operation_type CHECK (operation_type IN ('add', 'subtract', 'replace', 'clear', 'calculate')),
+     CONSTRAINT chk_priority CHECK (priority >= 0 AND priority <= 100)
+ );
+ 
+ -- 索引创建
+ CREATE INDEX idx_rules_type ON change_rules(change_type);
+ CREATE INDEX idx_rules_active ON change_rules(is_active);
+ CREATE INDEX idx_rules_priority ON change_rules(priority);
+ ```
+ 
+ ### 3.5 处理历史表 (processing_history)
+ 
+ ```sql
+ CREATE TABLE processing_history (
+     id INTEGER PRIMARY KEY AUTOINCREMENT,
+     batch_id VARCHAR(50) UNIQUE NOT NULL COMMENT '批次ID',
+     process_month VARCHAR(7) NOT NULL COMMENT '处理月份',
+     process_type VARCHAR(50) NOT NULL COMMENT '处理类型',
+     
+     -- 输入输出文件
+     input_files TEXT COMMENT '输入文件（JSON格式）',
+     output_files TEXT COMMENT '输出文件（JSON格式）',
+     
+     -- 处理统计
+     total_employees INTEGER DEFAULT 0 COMMENT '处理员工数',
+     success_count INTEGER DEFAULT 0 COMMENT '成功数量',
+     error_count INTEGER DEFAULT 0 COMMENT '错误数量',
+     warning_count INTEGER DEFAULT 0 COMMENT '警告数量',
+     
+     -- 金额统计
+     total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额变动',
+     increase_amount DECIMAL(15,2) DEFAULT 0 COMMENT '增加金额',
+     decrease_amount DECIMAL(15,2) DEFAULT 0 COMMENT '减少金额',
+     
+     -- 处理状态
+     process_status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态',
+     progress_percentage INTEGER DEFAULT 0 COMMENT '进度百分比',
+     current_step VARCHAR(100) COMMENT '当前步骤',
+     
+     -- 时间记录
+     start_time DATETIME COMMENT '开始时间',
+     end_time DATETIME COMMENT '结束时间',
+     duration_seconds INTEGER COMMENT '处理耗时（秒）',
+     
+     -- 操作信息
+     operator VARCHAR(50) COMMENT '操作人',
+     operator_ip VARCHAR(45) COMMENT '操作IP',
+     
+     -- 详细信息
+     error_details TEXT COMMENT '错误详情（JSON格式）',
+     summary_report TEXT COMMENT '处理摘要报告',
+     remarks TEXT COMMENT '备注',
+     
+     -- 约束定义
+     CONSTRAINT chk_process_status CHECK (process_status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
+     CONSTRAINT chk_progress CHECK (progress_percentage >= 0 AND progress_percentage <= 100)
+ );
+ 
+ -- 索引创建
+ CREATE INDEX idx_processing_month ON processing_history(process_month);
+ CREATE INDEX idx_processing_status ON processing_history(process_status);
+ CREATE INDEX idx_processing_time ON processing_history(start_time);
+ CREATE INDEX idx_processing_operator ON processing_history(operator);
+ ```
+ 
+ ### 3.6 系统配置表 (system_config)
+ 
+ ```sql
+ CREATE TABLE system_config (
+     id INTEGER PRIMARY KEY AUTOINCREMENT,
+     config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
+     config_value TEXT COMMENT '配置值',
+     config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型',
+     category VARCHAR(50) DEFAULT 'general' COMMENT '配置分类',
+     
+     -- 配置元数据
+     description TEXT COMMENT '配置描述',
+     default_value TEXT COMMENT '默认值',
+     valid_values TEXT COMMENT '有效值列表（JSON格式）',
+     
+     -- 访问控制
+     is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
+     is_readonly BOOLEAN DEFAULT FALSE COMMENT '是否只读',
+     requires_restart BOOLEAN DEFAULT FALSE COMMENT '是否需要重启',
+     
+     -- 版本控制
+     version INTEGER DEFAULT 1 COMMENT '版本号',
+     updated_by VARCHAR(50) COMMENT '更新人',
+     created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
+     
+     -- 约束定义
+     CONSTRAINT chk_config_type CHECK (config_type IN ('string', 'number', 'boolean', 'json', 'date', 'file_path'))
+ );
+ 
+ -- 索引创建
+ CREATE INDEX idx_config_key ON system_config(config_key);
+ CREATE INDEX idx_config_category ON system_config(category);
+ CREATE INDEX idx_config_system ON system_config(is_system);
+ ```
+ 
+ ## 4. 数据访问层设计
+ 
+ ### 4.1 DAO接口定义
+ 
+ ```python
+ from abc import ABC, abstractmethod
+ from typing import List, Dict, Optional, Any
+ import sqlite3
+ from datetime import datetime
+ 
+ class BaseDAO(ABC):
+     """数据访问对象基类"""
+     
+     def __init__(self, db_connection: sqlite3.Connection):
+         self.conn = db_connection
+         self.conn.row_factory = sqlite3.Row  # 返回字典形式的结果
+     
+     def execute_query(self, sql: str, params: tuple = ()) -> List[sqlite3.Row]:
+         """执行查询操作"""
+         cursor = self.conn.cursor()
+         cursor.execute(sql, params)
+         return cursor.fetchall()
+     
+     def execute_update(self, sql: str, params: tuple = ()) -> int:
+         """执行更新操作"""
+         cursor = self.conn.cursor()
+         cursor.execute(sql, params)
+         self.conn.commit()
+         return cursor.rowcount
+     
+     def execute_insert(self, sql: str, params: tuple = ()) -> int:
+         """执行插入操作，返回新记录ID"""
+         cursor = self.conn.cursor()
+         cursor.execute(sql, params)
+         self.conn.commit()
+         return cursor.lastrowid
+ 
+ class EmployeeDAO(BaseDAO):
+     """员工数据访问对象"""
+     
+     def find_by_employee_id(self, employee_id: str) -> Optional[Dict]:
+         """根据工号查找员工"""
+         sql = "SELECT * FROM employees WHERE employee_id = ?"
+         result = self.execute_query(sql, (employee_id,))
+         return dict(result[0]) if result else None
+     
+     def find_by_id_card(self, id_card: str) -> Optional[Dict]:
+         """根据身份证号查找员工"""
+         sql = "SELECT * FROM employees WHERE id_card = ?"
+         result = self.execute_query(sql, (id_card,))
+         return dict(result[0]) if result else None
+     
+     def find_by_name(self, name: str, fuzzy: bool = False) -> List[Dict]:
+         """根据姓名查找员工"""
+         if fuzzy:
+             sql = "SELECT * FROM employees WHERE name LIKE ?"
+             params = (f"%{name}%",)
+         else:
+             sql = "SELECT * FROM employees WHERE name = ?"
+             params = (name,)
+         
+         results = self.execute_query(sql, params)
+         return [dict(row) for row in results]
+     
+     def batch_insert(self, employees: List[Dict]) -> int:
+         """批量插入员工数据"""
+         sql = """INSERT OR REPLACE INTO employees 
+                  (employee_id, id_card, name, department, position, employee_type, status)
+                  VALUES (?, ?, ?, ?, ?, ?, ?)"""
+         
+         cursor = self.conn.cursor()
+         count = 0
+         for emp in employees:
+             params = (
+                 emp['employee_id'], emp.get('id_card'), emp['name'],
+                 emp.get('department'), emp.get('position'), 
+                 emp.get('employee_type'), emp.get('status', 'active')
+             )
+             cursor.execute(sql, params)
+             count += 1
+         
+         self.conn.commit()
+         return count
+     
+     def get_active_employees(self) -> List[Dict]:
+         """获取所有在职员工"""
+         sql = "SELECT * FROM employees WHERE status = 'active' ORDER BY employee_id"
+         results = self.execute_query(sql)
+         return [dict(row) for row in results]
+ 
+ class SalaryDAO(BaseDAO):
+     """工资数据访问对象"""
+     
+     def get_monthly_salary(self, month: str) -> List[Dict]:
+         """获取指定月份所有员工工资"""
+         sql = """SELECT sd.*, e.name, e.department, e.employee_type 
+                  FROM salary_data sd
+                  JOIN employees e ON sd.employee_id = e.employee_id
+                  WHERE sd.salary_month = ?
+                  ORDER BY sd.employee_id"""
+         results = self.execute_query(sql, (month,))
+         return [dict(row) for row in results]
+     
+     def get_employee_salary(self, employee_id: str, month: str) -> Optional[Dict]:
+         """获取指定员工指定月份工资"""
+         sql = "SELECT * FROM salary_data WHERE employee_id = ? AND salary_month = ?"
+         result = self.execute_query(sql, (employee_id, month))
+         return dict(result[0]) if result else None
+     
+     def save_salary_data(self, salary_data: List[Dict], month: str) -> int:
+         """保存工资数据"""
+         sql = """INSERT OR REPLACE INTO salary_data 
+                  (employee_id, salary_month, basic_salary, grade_salary, allowance,
+                   performance_basic, performance_reward, subsidy_transport, 
+                   subsidy_housing, gross_salary, net_salary, data_source)
+                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
+         
+         cursor = self.conn.cursor()
+         count = 0
+         for salary in salary_data:
+             params = (
+                 salary['employee_id'], month,
+                 salary.get('basic_salary', 0), salary.get('grade_salary', 0),
+                 salary.get('allowance', 0), salary.get('performance_basic', 0),
+                 salary.get('performance_reward', 0), salary.get('subsidy_transport', 0),
+                 salary.get('subsidy_housing', 0), salary.get('gross_salary', 0),
+                 salary.get('net_salary', 0), salary.get('data_source', 'import')
+             )
+             cursor.execute(sql, params)
+             count += 1
+         
+         self.conn.commit()
+         return count
+     
+     def update_salary(self, employee_id: str, month: str, updates: Dict) -> bool:
+         """更新员工工资数据"""
+         if not updates:
+             return False
+         
+         fields = ', '.join([f"{key} = ?" for key in updates.keys()])
+         sql = f"UPDATE salary_data SET {fields}, updated_time = CURRENT_TIMESTAMP WHERE employee_id = ? AND salary_month = ?"
+         
+         params = list(updates.values()) + [employee_id, month]
+         rows_affected = self.execute_update(sql, params)
+         return rows_affected > 0
+ 
+ class ChangeDAO(BaseDAO):
+     """异动记录数据访问对象"""
+     
+     def record_change(self, change_data: Dict) -> int:
+         """记录工资异动"""
+         sql = """INSERT INTO salary_changes 
+                  (employee_id, change_month, change_type, change_reason,
+                   effective_date, affected_fields, original_values, 
+                   new_values, change_amount, operator, processing_status)
+                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
+         
+         params = (
+             change_data['employee_id'], change_data['change_month'],
+             change_data['change_type'], change_data.get('change_reason'),
+             change_data.get('effective_date'), 
+             change_data.get('affected_fields', '{}'),
+             change_data.get('original_values', '{}'),
+             change_data.get('new_values', '{}'),
+             change_data.get('change_amount', 0),
+             change_data.get('operator', 'system'),
+             change_data.get('processing_status', 'pending')
+         )
+         
+         return self.execute_insert(sql, params)
+     
+     def get_monthly_changes(self, month: str) -> List[Dict]:
+         """获取月度异动汇总"""
+         sql = """SELECT sc.*, e.name, e.department 
+                  FROM salary_changes sc
+                  JOIN employees e ON sc.employee_id = e.employee_id
+                  WHERE sc.change_month = ?
+                  ORDER BY sc.created_time"""
+         results = self.execute_query(sql, (month,))
+         return [dict(row) for row in results]
+     
+     def get_change_statistics(self, start_month: str, end_month: str) -> List[Dict]:
+         """获取异动统计数据"""
+         sql = """SELECT 
+                     change_type,
+                     COUNT(*) as change_count,
+                     SUM(change_amount) as total_amount,
+                     AVG(change_amount) as avg_amount
+                  FROM salary_changes
+                  WHERE change_month BETWEEN ? AND ?
+                  GROUP BY change_type
+                  ORDER BY total_amount DESC"""
+         results = self.execute_query(sql, (start_month, end_month))
+         return [dict(row) for row in results]
+     
+     def update_change_status(self, change_id: int, status: str, error_message: str = None) -> bool:
+         """更新异动处理状态"""
+         if error_message:
+             sql = """UPDATE salary_changes 
+                      SET processing_status = ?, error_message = ?, processed_time = CURRENT_TIMESTAMP 
+                      WHERE id = ?"""
+             params = (status, error_message, change_id)
+         else:
+             sql = """UPDATE salary_changes 
+                      SET processing_status = ?, processed_time = CURRENT_TIMESTAMP 
+                      WHERE id = ?"""
+             params = (status, change_id)
+         
+         rows_affected = self.execute_update(sql, params)
+         return rows_affected > 0
+ ```
+ 
+ ## 5. 数据初始化和配置
+ 
+ ### 5.1 初始系统配置
+ 
+ ```sql
+ -- 插入系统默认配置
+ INSERT INTO system_config (config_key, config_value, config_type, category, description) VALUES
+ ('app.version', '1.0.0', 'string', 'system', '应用程序版本号'),
+ ('app.name', '月度工资异动处理系统', 'string', 'system', '应用程序名称'),
+ 
+ -- 数据处理配置
+ ('data.batch_size', '1000', 'number', 'data', '批处理大小'),
+ ('data.backup_enabled', 'true', 'boolean', 'data', '是否启用数据备份'),
+ ('data.backup_retention_days', '30', 'number', 'data', '备份保留天数'),
+ 
+ -- 文件路径配置
+ ('path.data_dir', 'data', 'string', 'path', '数据目录'),
+ ('path.template_dir', 'template', 'string', 'path', '模板目录'),
+ ('path.output_dir', 'output', 'string', 'path', '输出目录'),
+ ('path.log_dir', 'logs', 'string', 'path', '日志目录'),
+ 
+ -- 匹配算法配置
+ ('match.fuzzy_threshold', '0.85', 'number', 'algorithm', '模糊匹配阈值'),
+ ('match.enable_fuzzy_name', 'true', 'boolean', 'algorithm', '是否启用姓名模糊匹配'),
+ 
+ -- 计算规则配置
+ ('calc.precision', '2', 'number', 'calculation', '金额计算精度'),
+ ('calc.enable_validation', 'true', 'boolean', 'calculation', '是否启用计算验证'),
+ 
+ -- 界面配置
+ ('ui.theme', 'default', 'string', 'ui', '界面主题'),
+ ('ui.font_size', '12', 'number', 'ui', '字体大小'),
+ ('ui.auto_save_interval', '300', 'number', 'ui', '自动保存间隔（秒）');
+ ```
+ 
+ ### 5.2 默认异动规则
+ 
+ ```sql
+ -- 插入默认异动规则
+ INSERT INTO change_rules (rule_name, rule_code, change_type, operation_type, affected_fields, rule_config, description, priority) VALUES
+ ('离职人员工资清零', 'RESIGN_CLEAR', '停薪-退休', 'clear', 
+  '["basic_salary", "grade_salary", "allowance", "performance_basic", "performance_reward"]',
+  '{"clear_all": true, "effective_immediately": true}',
+  '离职人员停发所有工资项目', 90),
+ 
+ ('调离人员工资清零', 'TRANSFER_CLEAR', '停薪-调离', 'clear',
+  '["basic_salary", "grade_salary", "allowance", "performance_basic", "performance_reward"]',
+  '{"clear_all": true, "effective_immediately": true}',
+  '调离人员停发所有工资项目', 90),
+ 
+ ('恢复薪资待遇', 'RESTORE_SALARY', '起薪', 'replace',
+  '["basic_salary", "grade_salary", "performance_basic", "performance_reward"]',
+  '{"restore_from_standard": true, "calculate_proportional": true}',
+  '恢复正常薪资待遇，按实际工作天数计算', 80),
+ 
+ ('考核扣款处理', 'ASSESSMENT_DEDUCTION', '其他-考核扣款', 'subtract',
+  '["gross_salary"]',
+  '{"fixed_amount": true, "monthly_deduction": true, "min_remaining": 0}',
+  '高层次人才考核扣款，按月扣减固定金额', 70),
+ 
+ ('请假扣款计算', 'LEAVE_DEDUCTION', '请假扣款', 'calculate',
+  '["basic_salary", "performance_basic"]',
+  '{"calculation_method": "proportional", "base_days": 22}',
+  '请假按天扣减基本工资和绩效工资', 60),
+ 
+ ('脱产读博调整', 'STUDY_ADJUSTMENT', '脱产读博', 'clear',
+  '["performance_reward"]',
+  '{"keep_basic": true, "clear_reward": true}',
+  '脱产读博停发奖励性绩效，保留基本工资', 50);
+ ```
+ 
+ ## 6. 性能优化策略
+ 
+ ### 6.1 索引优化
+ 
+ ```sql
+ -- 复合索引优化查询性能
+ CREATE INDEX idx_salary_employee_month_status ON salary_data(employee_id, salary_month, is_calculated);
+ CREATE INDEX idx_changes_type_month_status ON salary_changes(change_type, change_month, processing_status);
+ CREATE INDEX idx_employees_status_type ON employees(status, employee_type);
+ 
+ -- 覆盖索引减少回表查询
+ CREATE INDEX idx_salary_summary ON salary_data(salary_month, employee_id, gross_salary, net_salary);
+ ```
+ 
+ ### 6.2 查询优化
+ 
+ ```sql
+ -- 使用视图简化复杂查询
+ CREATE VIEW v_employee_current_salary AS
+ SELECT 
+     e.employee_id,
+     e.name,
+     e.department,
+     e.employee_type,
+     s.salary_month,
+     s.gross_salary,
+     s.net_salary,
+     s.basic_salary + s.grade_salary as base_total,
+     s.performance_basic + s.performance_reward as performance_total
+ FROM employees e
+ JOIN salary_data s ON e.employee_id = s.employee_id
+ WHERE e.status = 'active'
+   AND s.salary_month = (
+       SELECT MAX(salary_month) 
+       FROM salary_data s2 
+       WHERE s2.employee_id = e.employee_id
+   );
+ 
+ -- 异动汇总视图
+ CREATE VIEW v_monthly_change_summary AS
+ SELECT 
+     change_month,
+     change_type,
+     COUNT(*) as change_count,
+     SUM(change_amount) as total_amount,
+     AVG(change_amount) as avg_amount,
+     MIN(change_amount) as min_amount,
+     MAX(change_amount) as max_amount
+ FROM salary_changes
+ WHERE processing_status = 'completed'
+ GROUP BY change_month, change_type;
+ ```
+ 
+ ### 6.3 数据库维护
+ 
+ ```sql
+ -- 定期维护SQL
+ -- 1. 分析表统计信息
+ ANALYZE employees;
+ ANALYZE salary_data;
+ ANALYZE salary_changes;
+ 
+ -- 2. 重建索引
+ REINDEX idx_salary_employee_month;
+ REINDEX idx_changes_type_month_status;
+ 
+ -- 3. 清理过期数据（可选）
+ -- DELETE FROM processing_history WHERE start_time < date('now', '-1 year');
+ -- DELETE FROM salary_changes WHERE created_time < date('now', '-2 years') AND processing_status = 'completed';
+ ```
+ 
+ 这个数据库设计提供了完整的数据存储方案，支持工资异动处理的各种业务需求，同时具备良好的性能和扩展性。 
```

---

## 3. 用户界面原型设计

---

```diff
- 
+ # 用户界面原型设计
+ 
+ ## 1. 界面设计原则
+ 
+ ### 1.1 设计理念
+ - **简洁明了**: 界面简洁，信息层次清晰
+ - **操作便捷**: 减少用户操作步骤，提供快捷方式
+ - **信息透明**: 处理过程可视化，状态反馈及时
+ - **容错友好**: 提供清晰的错误提示和恢复机制
+ 
+ ### 1.2 界面设计规范
+ - **配色方案**: 采用简洁的蓝白配色，突出重要操作
+ - **字体规范**: 统一使用微软雅黑，确保可读性
+ - **控件规范**: 统一控件样式，保持界面一致性
+ - **响应式设计**: 适配不同分辨率屏幕
+ 
+ ## 2. 主界面设计
+ 
+ ### 2.1 主窗口布局
+ 
+ ```
+ ┌─────────────────────────────────────────────────────────────────────────────┐
+ │ 月度工资异动处理系统 v1.0                                    [_] [□] [×]      │
+ ├─────────────────────────────────────────────────────────────────────────────┤
+ │ 文件(F)  工具(T)  设置(S)  帮助(H)                                            │
+ ├─────────────────────────────────────────────────────────────────────────────┤
+ │ ┌─── 文件选择区 ────────────────────────────┐ ┌─── 人员查询区 ──────────────┐ │
+ │ │ 📁 工资表文件:                              │ │ 🔍 人员查询:                 │ │
+ │ │ [data/工资表/2025年5月工资表.xls     ] [浏览] │ │ [请输入工号/身份证/姓名      ] │ │
+ │ │                                           │ │ [🔍查询] [清空]               │ │
+ │ │ 📁 异动人员文件:                           │ │                             │ │
+ │ │ [data/异动人员/5月异动人员.xlsx     ] [浏览] │ │ 查询结果:                    │ │
+ │ │                                           │ │ ┌─────────────────────────┐ │ │
+ │ │ 📁 输出目录:                              │ │ │ 工号: 12345678           │ │ │
+ │ │ [output/2025年5月/               ] [浏览] │ │ │ 姓名: 张三               │ │ │
+ │ │                                           │ │ │ 部门: 计算机学院          │ │ │
+ │ │ ☑ 自动备份原文件                           │ │ │ 当前工资: 8500.00       │ │ │
+ │ │ ☑ 生成处理报告                             │ │ └─────────────────────────┘ │ │
+ │ └───────────────────────────────────────────┘ └─────────────────────────────┘ │
+ │                                                                               │
+ │ ┌─── 异动处理配置 ──────────────────────────┐ ┌─── 处理进度 ────────────────┐ │
+ │ │ 处理月份: [2025-05 ▼]                     │ │ 当前状态: 就绪              │ │
+ │ │                                           │ │                             │ │
+ │ │ 异动规则:                                 │ │ 进度: [████████████] 100%   │ │
+ │ │ ☑ 起薪（恢复薪资待遇）                     │ │                             │ │
+ │ │ ☑ 停薪-退休                               │ │ 处理详情:                   │ │
+ │ │ ☑ 停薪-调离                               │ │ ┌─────────────────────────┐ │ │
+ │ │ ☑ 其他-考核扣款                           │ │ │ [时间] 操作日志...        │ │ │
+ │ │ ☑ 请假扣款                                │ │ │ 14:30:25 开始读取工资表   │ │ │
+ │ │ ☑ 脱产读博                                │ │ │ 14:30:26 读取完成,共1250条 │ │ │
+ │ │                                           │ │ │ 14:30:27 开始人员匹配...  │ │ │
+ │ │ [高级设置...]                              │ │ │ 14:30:35 匹配完成,成功98% │ │ │
+ │ └───────────────────────────────────────────┘ │ │ ...                     │ │ │
+ │                                                │ └─────────────────────────┘ │ │
+ │ ┌─── 操作按钮区 ────────────────────────────┐ └─────────────────────────────┘ │
+ │ │ [🚀 开始处理]  [⏸ 暂停]  [⏹ 停止]  [📋 预览结果]                         │ │
+ │ │                                                                         │ │
+ │ │ [📊 生成汇总表]  [📋 生成审批表]  [📄 生成请示文档]                        │ │
+ │ │                                                                         │ │
+ │ │ [📁 打开输出目录]  [📝 查看日志]  [❓ 帮助]                               │ │
+ │ └─────────────────────────────────────────────────────────────────────────┘ │
+ ├─────────────────────────────────────────────────────────────────────────────┤
+ │ 状态栏: 就绪 | 工资记录: 1250条 | 异动记录: 45条 | 最后更新: 2025-01-20 14:30:35 │
+ └─────────────────────────────────────────────────────────────────────────────┘
+ ```
+ 
+ ### 2.2 界面区域说明
+ 
+ #### 2.2.1 文件选择区
+ - **工资表文件选择**: 支持.xls和.xlsx格式
+ - **异动人员文件选择**: 支持多sheet Excel文件
+ - **输出目录选择**: 可自定义输出位置
+ - **处理选项**: 备份和报告生成选项
+ 
+ #### 2.2.2 人员查询区
+ - **查询输入框**: 支持工号、身份证、姓名查询
+ - **查询结果显示**: 展示人员基本信息和当前工资
+ - **操作按钮**: 查询和清空功能
+ 
+ #### 2.2.3 异动处理配置区
+ - **处理月份选择**: 下拉框选择处理月份
+ - **异动规则配置**: 复选框选择启用的异动类型
+ - **高级设置**: 详细参数配置入口
+ 
+ #### 2.2.4 处理进度区
+ - **状态显示**: 当前处理状态
+ - **进度条**: 可视化处理进度
+ - **操作日志**: 实时显示处理过程
+ 
+ #### 2.2.5 操作按钮区
+ - **处理控制**: 开始、暂停、停止按钮
+ - **报表生成**: 各类报表和文档生成
+ - **辅助功能**: 文件夹打开、日志查看等
+ 
+ ## 3. 子界面设计
+ 
+ ### 3.1 文件选择对话框
+ 
+ ```
+ ┌─────────────────────────────────────────────────────┐
+ │ 选择工资表文件                                [×]   │
+ ├─────────────────────────────────────────────────────┤
+ │ 查找范围: [data/工资表/            ] [浏览...]       │
+ │                                                     │
+ │ 文件列表:                                           │
+ │ ┌─────────────────────────────────────────────────┐ │
+ │ │ 📄 2025年4月份正式工工资表.xls    (1.2MB)      │ │
+ │ │ 📄 2025年5月份正式工工资表.xls    (1.3MB)      │ │
+ │ │ 📄 2025年5月份正式工工资表(修正).xls (1.3MB)   │ │
+ │ │ 📁 历史文件/                                  │ │
+ │ │ 📁 备份文件/                                  │ │
+ │ └─────────────────────────────────────────────────┘ │
+ │                                                     │
+ │ 文件名: [2025年5月份正式工工资表.xls               ] │
+ │                                                     │
+ │ 文件类型: [Excel文件(*.xls;*.xlsx) ▼]              │
+ │                                                     │
+ │ ☑ 显示文件预览                                      │
+ │                                                     │
+ │ ┌─── 文件预览 ─────────────────────────────────────┐ │
+ │ │ Sheet: 全部在职人员工资表 (1250行 × 23列)        │ │
+ │ │ ┌─────────────────────────────────────────────┐ │ │
+ │ │ │ 序号 | 工号     | 姓名 | 部门     | ...     │ │ │
+ │ │ │ 1    | 12345678 | 张三 | 计算机学院 | ...   │ │ │
+ │ │ │ 2    | 12345679 | 李四 | 数学学院  | ...    │ │ │
+ │ │ │ ...  | ...      | ... | ...       | ...   │ │ │
+ │ │ └─────────────────────────────────────────────┘ │ │
+ │ └─────────────────────────────────────────────────┘ │
+ │                                                     │
+ │              [确定]  [取消]  [帮助]                 │
+ └─────────────────────────────────────────────────────┘
+ ```
+ 
+ ### 3.2 人员匹配确认对话框
+ 
+ ```
+ ┌─────────────────────────────────────────────────────────────────┐
+ │ 人员匹配确认                                              [×]   │
+ ├─────────────────────────────────────────────────────────────────┤
+ │ 发现以下人员需要手动确认匹配:                                   │
+ │                                                                 │
+ │ ┌─────────────────────────────────────────────────────────────┐ │
+ │ │ 异动人员: 张三 (身份证: 320***************8)                │ │
+ │ │                                                             │ │
+ │ │ 可能匹配的工资表人员:                                       │ │
+ │ │ ○ 张三   (工号: 12345678, 部门: 计算机学院)                │ │
+ │ │ ○ 张三丰 (工号: 12345679, 部门: 数学学院)                  │ │
+ │ │ ○ 张小三 (工号: 12345680, 部门: 外语学院)                  │ │
+ │ │ ○ 不匹配 (标记为未找到)                                     │ │
+ │ │                                                             │ │
+ │ │ 匹配置信度: ★★★☆☆ (75%)                                   │ │
+ │ └─────────────────────────────────────────────────────────────┘ │
+ │                                                                 │
+ │ ┌─────────────────────────────────────────────────────────────┐ │
+ │ │ 异动人员: 李四 (工号: 12345999)                             │ │
+ │ │                                                             │ │
+ │ │ ❌ 在工资表中未找到该工号                                   │ │
+ │ │                                                             │ │
+ │ │ 可能的原因:                                                 │ │
+ │ │ • 工号输入错误                                              │ │
+ │ │ • 该员工不在当月工资表中                                    │ │
+ │ │ • 该员工已离职                                              │ │
+ │ │                                                             │ │
+ │ │ 处理方式:                                                   │ │
+ │ │ ○ 手动输入正确工号: [____________]                          │ │
+ │ │ ○ 跳过此人员                                                │ │
+ │ │ ○ 标记为异常记录                                            │ │
+ │ └─────────────────────────────────────────────────────────────┘ │
+ │                                                                 │
+ │ 进度: 2/5 待确认                                               │ │
+ │                                                                 │
+ │        [< 上一个]  [下一个 >]  [全部确认]  [取消]              │
+ └─────────────────────────────────────────────────────────────────┘
+ ```
+ 
+ ### 3.3 处理结果预览窗口
+ 
+ ```
+ ┌─────────────────────────────────────────────────────────────────────────────┐
+ │ 异动处理结果预览                                                      [×]   │
+ ├─────────────────────────────────────────────────────────────────────────────┤
+ │ 标签: [汇总统计] [详细列表] [异常记录] [金额变动]                            │
+ │                                                                             │
+ │ ═══ 汇总统计 ═══════════════════════════════════════════════════════════════ │
+ │                                                                             │
+ │ 📊 处理统计:                                                                │
+ │ ├─ 总计处理人员: 45人                                                      │
+ │ ├─ 成功处理: 42人 (93.3%)                                                  │
+ │ ├─ 异常记录: 3人 (6.7%)                                                    │
+ │ └─ 跳过处理: 0人                                                           │
+ │                                                                             │
+ │ 💰 金额统计:                                                                │
+ │ ├─ 工资总变动: -85,600.00元                                                │
+ │ ├─ 增加金额: +12,800.00元 (6人)                                            │
+ │ ├─ 减少金额: -98,400.00元 (36人)                                           │
+ │ └─ 平均变动: -2,035.71元/人                                                │
+ │                                                                             │
+ │ 📋 异动类型分布:                                                            │
+ │ ┌─────────────────────────────────────────────────────────────────────┐     │
+ │ │ 异动类型        │ 人数 │ 变动金额      │ 占比     │ 平均金额        │     │
+ │ ├─────────────────────────────────────────────────────────────────────┤     │
+ │ │ 停薪-退休       │ 15   │ -75,000.00   │ 35.7%    │ -5,000.00      │     │
+ │ │ 其他-考核扣款    │ 12   │ -30,000.00   │ 28.6%    │ -2,500.00      │     │
+ │ │ 请假扣款        │ 8    │ -8,400.00    │ 19.0%    │ -1,050.00      │     │
+ │ │ 起薪           │ 6    │ +12,800.00   │ 14.3%    │ +2,133.33      │     │
+ │ │ 脱产读博        │ 4    │ -4,000.00    │ 9.5%     │ -1,000.00      │     │
+ │ │ 停薪-调离       │ 2    │ -10,000.00   │ 4.8%     │ -5,000.00      │     │
+ │ └─────────────────────────────────────────────────────────────────────┘     │
+ │                                                                             │
+ │                    [导出详细报告]  [保存结果]  [关闭]                      │
+ └─────────────────────────────────────────────────────────────────────────────┘
+ ```
+ 
+ ### 3.4 高级设置对话框
+ 
+ ```
+ ┌─────────────────────────────────────────────────────────────────┐
+ │ 高级设置                                                  [×]   │
+ ├─────────────────────────────────────────────────────────────────┤
+ │ 标签: [匹配设置] [计算规则] [文件格式] [系统设置]                │
+ │                                                                 │
+ │ ═══ 匹配设置 ═══════════════════════════════════════════════════ │
+ │                                                                 │
+ │ 匹配优先级:                                                     │
+ │ ☑ 工号匹配 (最高优先级)                                         │
+ │ ☑ 身份证号匹配                                                  │
+ │ ☑ 姓名匹配                                                      │
+ │                                                                 │
+ │ 模糊匹配设置:                                                   │
+ │ ☑ 启用姓名模糊匹配                                              │
+ │ 相似度阈值: [85    ]% (推荐值: 80-90%)                         │
+ │                                                                 │
+ │ ☑ 启用身份证号容错匹配                                          │
+ │ □ 忽略身份证号中的空格和连字符                                  │
+ │                                                                 │
+ │ 工号格式验证:                                                   │
+ │ ☑ 验证工号为8位数字                                             │
+ │ ☑ 工号范围检查: [10000000] 到 [99999999]                      │
+ │                                                                 │
+ │ 匹配结果处理:                                                   │
+ │ ○ 自动接受高置信度匹配 (≥95%)                                  │
+ │ ● 所有匹配结果都需要确认                                        │
+ │ ○ 仅对低置信度匹配进行确认 (<80%)                              │
+ │                                                                 │
+ │ ☑ 保存匹配结果到数据库                                          │
+ │ ☑ 生成匹配报告                                                  │
+ │                                                                 │
+ │              [恢复默认]  [确定]  [取消]  [应用]                 │
+ └─────────────────────────────────────────────────────────────────┘
+ ```
+ 
+ ## 4. 交互流程设计
+ 
+ ### 4.1 主流程交互图
+ 
+ ```mermaid
+ graph TD
+     A[启动应用] --> B[加载界面]
+     B --> C[选择文件]
+     C --> D[验证文件]
+     D --> E{文件有效?}
+     E -->|否| F[显示错误信息]
+     F --> C
+     E -->|是| G[预览数据]
+     G --> H[配置处理参数]
+     H --> I[开始处理]
+     I --> J[人员匹配]
+     J --> K{需要手动确认?}
+     K -->|是| L[显示匹配确认对话框]
+     L --> M[用户确认匹配]
+     M --> N[执行异动计算]
+     K -->|否| N
+     N --> O[显示处理进度]
+     O --> P[生成结果预览]
+     P --> Q[用户确认结果]
+     Q --> R{结果满意?}
+     R -->|否| S[调整参数]
+     S --> H
+     R -->|是| T[生成报表文档]
+     T --> U[保存结果]
+     U --> V[显示完成信息]
+ ```
+ 
+ ### 4.2 人员匹配交互流程
+ 
+ ```mermaid
+ sequenceDiagram
+     participant U as 用户
+     participant GUI as 界面
+     participant M as 匹配器
+     participant DB as 数据库
+     
+     U->>GUI: 点击"开始处理"
+     GUI->>M: 开始人员匹配
+     M->>DB: 查询员工数据
+     DB-->>M: 返回员工列表
+     
+     loop 对每个异动人员
+         M->>M: 执行匹配算法
+         alt 精确匹配成功
+             M->>GUI: 更新进度 (自动匹配)
+         else 模糊匹配
+             M->>GUI: 显示匹配确认对话框
+             GUI->>U: 请求用户确认
+             U->>GUI: 选择匹配结果
+             GUI->>M: 返回用户选择
+         else 匹配失败
+             M->>GUI: 标记为异常记录
+             GUI->>U: 显示异常提示
+         end
+     end
+     
+     M->>GUI: 匹配完成
+     GUI->>U: 显示匹配结果摘要
+ ```
+ 
+ ### 4.3 数据验证交互流程
+ 
+ ```mermaid
+ flowchart TD
+     A[用户选择文件] --> B[文件格式检查]
+     B --> C{格式正确?}
+     C -->|否| D[显示格式错误提示]
+     D --> A
+     C -->|是| E[读取文件内容]
+     E --> F[数据结构验证]
+     F --> G{结构正确?}
+     G -->|否| H[显示结构错误详情]
+     H --> A
+     G -->|是| I[数据内容验证]
+     I --> J[检查必填字段]
+     J --> K[检查数据类型]
+     K --> L[检查数据范围]
+     L --> M{验证通过?}
+     M -->|否| N[显示验证错误列表]
+     N --> O[用户选择处理方式]
+     O --> P{继续处理?}
+     P -->|否| A
+     P -->|是| Q[标记问题数据]
+     M -->|是| Q
+     Q --> R[显示数据预览]
+     R --> S[用户确认数据]
+ ```
+ 
+ ## 5. 界面响应式设计
+ 
+ ### 5.1 分辨率适配
+ 
+ | 分辨率类型 | 最小尺寸 | 推荐尺寸 | 布局调整 |
+ |------------|----------|----------|----------|
+ | **标准屏幕** | 1024×768 | 1280×1024 | 标准布局 |
+ | **宽屏** | 1366×768 | 1920×1080 | 横向扩展 |
+ | **高分辨率** | 1920×1080 | 2560×1440 | 字体放大 |
+ | **超宽屏** | 2560×1080 | 3440×1440 | 分栏显示 |
+ 
+ ### 5.2 字体和控件自适应
+ 
+ ```python
+ # 自适应字体配置
+ FONT_CONFIG = {
+     "small_screen": {
+         "base_font": ("微软雅黑", 9),
+         "title_font": ("微软雅黑", 11, "bold"),
+         "button_font": ("微软雅黑", 9)
+     },
+     "standard_screen": {
+         "base_font": ("微软雅黑", 10),
+         "title_font": ("微软雅黑", 12, "bold"),
+         "button_font": ("微软雅黑", 10)
+     },
+     "large_screen": {
+         "base_font": ("微软雅黑", 12),
+         "title_font": ("微软雅黑", 14, "bold"),
+         "button_font": ("微软雅黑", 11)
+     }
+ }
+ 
+ # 控件尺寸自适应
+ WIDGET_CONFIG = {
+     "button_height": {"min": 25, "standard": 30, "large": 35},
+     "entry_height": {"min": 22, "standard": 25, "large": 30},
+     "listbox_height": {"min": 120, "standard": 150, "large": 180}
+ }
+ ```
+ 
+ ## 6. 主题和样式设计
+ 
+ ### 6.1 颜色方案
+ 
+ ```python
+ # 主题颜色配置
+ THEME_COLORS = {
+     "primary": "#0078D4",      # 主色调（微软蓝）
+     "secondary": "#106EBE",    # 辅助色
+     "success": "#107C10",      # 成功色（绿色）
+     "warning": "#FF8C00",      # 警告色（橙色）
+     "danger": "#D13438",       # 危险色（红色）
+     "info": "#00BCF2",         # 信息色（浅蓝）
+     
+     "background": "#FFFFFF",   # 背景色
+     "surface": "#F5F5F5",      # 表面色
+     "border": "#D1D1D1",       # 边框色
+     "text_primary": "#323232", # 主文本色
+     "text_secondary": "#717171" # 次要文本色
+ }
+ 
+ # 控件样式配置
+ WIDGET_STYLES = {
+     "button_primary": {
+         "bg": THEME_COLORS["primary"],
+         "fg": "white",
+         "relief": "flat",
+         "borderwidth": 0,
+         "padx": 20,
+         "pady": 8
+     },
+     "button_secondary": {
+         "bg": THEME_COLORS["surface"],
+         "fg": THEME_COLORS["text_primary"],
+         "relief": "solid",
+         "borderwidth": 1,
+         "padx": 15,
+         "pady": 6
+     },
+     "entry_standard": {
+         "bg": "white",
+         "fg": THEME_COLORS["text_primary"],
+         "relief": "solid",
+         "borderwidth": 1,
+         "highlightthickness": 2,
+         "highlightcolor": THEME_COLORS["primary"]
+     }
+ }
+ ```
+ 
+ ### 6.2 图标和资源
+ 
+ ```python
+ # 图标资源配置
+ ICONS = {
+     "file_open": "📁",
+     "search": "🔍", 
+     "start": "🚀",
+     "pause": "⏸",
+     "stop": "⏹",
+     "preview": "📋",
+     "report": "📊",
+     "document": "📄",
+     "folder": "📁",
+     "log": "📝",
+     "help": "❓",
+     "success": "✅",
+     "warning": "⚠️",
+     "error": "❌",
+     "info": "ℹ️"
+ }
+ 
+ # 状态图标
+ STATUS_ICONS = {
+     "ready": "🟢",
+     "processing": "🟡", 
+     "completed": "✅",
+     "error": "🔴",
+     "warning": "🟠"
+ }
+ ```
+ 
+ ## 7. 用户体验优化
+ 
+ ### 7.1 操作便捷性
+ 
+ #### 7.1.1 快捷键支持
+ - **Ctrl+O**: 打开工资表文件
+ - **Ctrl+I**: 打开异动人员文件
+ - **F5**: 开始处理
+ - **Ctrl+R**: 生成报表
+ - **F1**: 帮助
+ - **Ctrl+Q**: 退出程序
+ 
+ #### 7.1.2 拖拽支持
+ - 支持将Excel文件直接拖拽到相应的文件选择框
+ - 拖拽文件夹到输出目录选择框
+ 
+ #### 7.1.3 自动保存
+ - 自动保存用户设置和首选项
+ - 记住最后使用的文件路径
+ - 保存处理历史记录
+ 
+ ### 7.2 错误处理和提示
+ 
+ #### 7.2.1 友好的错误信息
+ ```python
+ ERROR_MESSAGES = {
+     "file_not_found": {
+         "title": "文件未找到",
+         "message": "指定的文件不存在，请检查文件路径是否正确。",
+         "suggestion": "点击'浏览'按钮重新选择文件。"
+     },
+     "file_format_error": {
+         "title": "文件格式错误", 
+         "message": "文件格式不正确或文件已损坏。",
+         "suggestion": "请确保文件是有效的Excel格式(.xls或.xlsx)。"
+     },
+     "data_validation_error": {
+         "title": "数据验证失败",
+         "message": "文件中存在无效的数据格式。",
+         "suggestion": "请检查以下字段的数据格式：{field_list}"
+     }
+ }
+ ```
+ 
+ #### 7.2.2 进度反馈机制
+ - 实时进度条显示
+ - 详细的操作日志
+ - 预计完成时间显示
+ - 可取消的长时间操作
+ 
+ ### 7.3 帮助和指导
+ 
+ #### 7.3.1 内置帮助系统
+ - 操作步骤向导
+ - 常见问题解答
+ - 字段说明文档
+ - 视频教程链接
+ 
+ #### 7.3.2 工具提示
+ - 重要按钮和字段的工具提示
+ - 鼠标悬停显示详细说明
+ - 状态指示器解释
+ 
+ ## 8. 可访问性设计
+ 
+ ### 8.1 键盘导航
+ - 支持Tab键在控件间切换
+ - 支持Enter键确认操作
+ - 支持Esc键取消操作
+ - 支持方向键在列表中导航
+ 
+ ### 8.2 视觉辅助
+ - 高对比度模式支持
+ - 字体大小可调节
+ - 颜色盲友好的配色
+ - 重要信息的多重提示（颜色+图标+文字）
+ 
+ ### 8.3 屏幕阅读器支持
+ - 控件标签和描述
+ - 状态信息的文字描述
+ - 进度信息的语音反馈
+ 
+ 这个用户界面原型设计提供了完整的界面规范和交互流程，确保系统具备良好的用户体验和操作便捷性。 
```

---

## 4. 业务流程设计

---

```diff
- 
+ # 业务流程设计
+ 
+ ## 1. 核心业务流程概览
+ 
+ ### 1.1 主流程图
+ 
+ ```mermaid
+ flowchart TD
+     A[系统启动] --> B[用户界面初始化]
+     B --> C[加载系统配置]
+     C --> D[文件选择和验证]
+     D --> E[数据预处理]
+     E --> F[人员信息匹配]
+     F --> G[异动规则应用]
+     G --> H[工资计算处理]
+     H --> I[结果验证和确认]
+     I --> J[报表文档生成]
+     J --> K[数据保存和备份]
+     K --> L[处理完成]
+     
+     %% 异常处理分支
+     D --> D1[文件格式异常]
+     E --> E1[数据验证异常]
+     F --> F1[匹配失败异常]
+     G --> G1[规则配置异常]
+     H --> H1[计算错误异常]
+     I --> I1[结果异常]
+     
+     D1 --> M[错误处理和提示]
+     E1 --> M
+     F1 --> M
+     G1 --> M
+     H1 --> M
+     I1 --> M
+     
+     M --> N{用户选择}
+     N -->|重试| D
+     N -->|修正| O[手动修正]
+     N -->|跳过| P[记录异常继续]
+     N -->|退出| Q[终止处理]
+     
+     O --> F
+     P --> G
+     
+     style A fill:#e1f5fe
+     style L fill:#c8e6c9
+     style M fill:#ffcdd2
+     style Q fill:#ffecb3
+ ```
+ 
+ ### 1.2 业务流程分阶段说明
+ 
+ #### 1.2.1 数据准备阶段
+ - **输入数据收集**: 工资表、异动人员表
+ - **数据格式验证**: 文件格式、数据结构检查
+ - **数据完整性检查**: 必填字段、数据类型验证
+ - **数据备份**: 原始数据安全备份
+ 
+ #### 1.2.2 人员匹配阶段
+ - **人员索引建立**: 工号、身份证、姓名索引
+ - **自动匹配执行**: 多字段匹配算法
+ - **匹配结果验证**: 置信度评估
+ - **手动确认处理**: 低置信度匹配人工确认
+ 
+ #### 1.2.3 异动计算阶段
+ - **异动规则解析**: 规则配置加载
+ - **计算逻辑执行**: 异动类型对应计算
+ - **结果验证检查**: 计算结果合理性验证
+ - **异常处理机制**: 计算错误处理和回滚
+ 
+ #### 1.2.4 结果生成阶段
+ - **报表数据整理**: 异动汇总统计
+ - **模板文档填充**: 审批表、请示文档生成
+ - **输出文件生成**: Excel、Word文件导出
+ - **处理记录保存**: 操作历史和审计日志
+ 
+ ## 2. 详细业务流程设计
+ 
+ ### 2.1 文件处理流程
+ 
+ ```mermaid
+ flowchart TD
+     A[用户选择文件] --> B[文件存在性检查]
+     B --> C{文件存在?}
+     C -->|否| D[提示文件不存在]
+     D --> A
+     
+     C -->|是| E[文件格式检查]
+     E --> F{格式正确?}
+     F -->|否| G[提示格式错误]
+     G --> A
+     
+     F -->|是| H[文件读取权限检查]
+     H --> I{有读取权限?}
+     I -->|否| J[提示权限不足]
+     J --> A
+     
+     I -->|是| K[读取文件内容]
+     K --> L[解析Excel结构]
+     L --> M{结构完整?}
+     M -->|否| N[提示结构错误]
+     N --> A
+     
+     M -->|是| O[提取工作表数据]
+     O --> P[数据类型验证]
+     P --> Q{数据有效?}
+     Q -->|否| R[显示数据错误详情]
+     R --> S[用户选择处理方式]
+     S --> T{继续处理?}
+     T -->|否| A
+     T -->|是| U[标记问题数据]
+     
+     Q -->|是| V[数据预览显示]
+     U --> V
+     V --> W[用户确认数据]
+     W --> X[数据处理就绪]
+     
+     style A fill:#e3f2fd
+     style X fill:#e8f5e8
+     style D,G,J,N,R fill:#ffecb3
+ ```
+ 
+ #### 2.1.1 文件验证规则
+ 
+ **工资表文件验证:**
+ ```python
+ SALARY_FILE_VALIDATION = {
+     "file_extensions": [".xls", ".xlsx"],
+     "required_sheets": ["全部在职人员工资表"],
+     "required_columns": [
+         "工号", "姓名", "部门名称", "2025年岗位工资", 
+         "2025年薪级工资", "应发工资"
+     ],
+     "data_types": {
+         "工号": "string",
+         "姓名": "string", 
+         "2025年岗位工资": "float",
+         "应发工资": "float"
+     },
+     "validation_rules": {
+         "工号": "^[0-9]{8}$",  # 8位数字
+         "姓名": "^[\u4e00-\u9fa5]{2,10}$",  # 2-10个中文字符
+         "应发工资": ">=0"  # 非负数
+     }
+ }
+ ```
+ 
+ **异动人员文件验证:**
+ ```python
+ CHANGE_FILE_VALIDATION = {
+     "file_extensions": [".xlsx"],
+     "expected_sheets": [
+         "起薪（恢复薪资待遇）", "转正定级", "停薪-退休", 
+         "停薪-调离", "其他-考核扣款", "请假扣款", "脱产读博"
+     ],
+     "common_columns": ["工作证号", "姓名", "证件号码"],
+     "validation_rules": {
+         "工作证号": "^[0-9]{8}$",
+         "证件号码": "^[0-9]{17}[0-9Xx]$|^[0-9]{15}$"
+     }
+ }
+ ```
+ 
+ ### 2.2 人员匹配流程
+ 
+ ```mermaid
+ flowchart TD
+     A[开始人员匹配] --> B[加载工资表人员数据]
+     B --> C[建立人员索引]
+     C --> D[加载异动人员数据]
+     D --> E[初始化匹配结果列表]
+     
+     E --> F[遍历异动人员]
+     F --> G[获取当前异动人员信息]
+     G --> H[工号精确匹配]
+     H --> I{匹配成功?}
+     
+     I -->|是| J[记录精确匹配结果]
+     J --> K[置信度=100%]
+     K --> L[下一个人员]
+     
+     I -->|否| M[身份证号匹配]
+     M --> N{匹配成功?}
+     
+     N -->|是| O[记录身份证匹配结果]
+     O --> P[置信度=95%]
+     P --> Q{置信度>=阈值?}
+     Q -->|是| L
+     Q -->|否| R[加入待确认列表]
+     
+     N -->|否| S[姓名模糊匹配]
+     S --> T[计算姓名相似度]
+     T --> U{相似度>=阈值?}
+     
+     U -->|是| V[记录模糊匹配结果]
+     V --> W[置信度=相似度分数]
+     W --> X{置信度>=阈值?}
+     X -->|是| L
+     X -->|否| R
+     
+     U -->|否| Y[标记为未匹配]
+     Y --> R
+     
+     R --> Z[下一个人员]
+     Z --> AA{还有人员?}
+     AA -->|是| G
+     AA -->|否| BB[处理待确认列表]
+     
+     L --> AA
+     
+     BB --> CC{有待确认项?}
+     CC -->|是| DD[显示匹配确认界面]
+     CC -->|否| EE[匹配完成]
+     
+     DD --> FF[用户手动确认]
+     FF --> GG[更新匹配结果]
+     GG --> EE
+     
+     style A fill:#e3f2fd
+     style EE fill:#e8f5e8
+     style DD,FF fill:#fff3e0
+     style Y fill:#ffcdd2
+ ```
+ 
+ #### 2.2.1 匹配算法详细设计
+ 
+ **多字段匹配策略:**
+ ```python
+ class PersonMatchingAlgorithm:
+     def __init__(self):
+         self.confidence_threshold = 0.85
+         self.fuzzy_threshold = 0.80
+         
+     def match_person(self, change_person, salary_persons):
+         """执行人员匹配的核心算法"""
+         
+         # 1. 工号精确匹配 (优先级最高)
+         if change_person.employee_id:
+             exact_match = self.exact_match_by_id(
+                 change_person.employee_id, salary_persons)
+             if exact_match:
+                 return MatchResult(
+                     matched_person=exact_match,
+                     confidence=1.0,
+                     method="employee_id_exact"
+                 )
+         
+         # 2. 身份证号匹配
+         if change_person.id_card:
+             id_match = self.exact_match_by_id_card(
+                 change_person.id_card, salary_persons)
+             if id_match:
+                 return MatchResult(
+                     matched_person=id_match,
+                     confidence=0.95,
+                     method="id_card_exact"
+                 )
+         
+         # 3. 姓名模糊匹配
+         if change_person.name:
+             fuzzy_matches = self.fuzzy_match_by_name(
+                 change_person.name, salary_persons)
+             if fuzzy_matches:
+                 best_match = max(fuzzy_matches, key=lambda x: x.confidence)
+                 if best_match.confidence >= self.fuzzy_threshold:
+                     return best_match
+         
+         # 4. 未匹配
+         return MatchResult(
+             matched_person=None,
+             confidence=0.0,
+             method="no_match"
+         )
+     
+     def fuzzy_match_by_name(self, name, salary_persons):
+         """姓名模糊匹配实现"""
+         from difflib import SequenceMatcher
+         
+         matches = []
+         for person in salary_persons:
+             similarity = SequenceMatcher(None, name, person.name).ratio()
+             if similarity >= self.fuzzy_threshold:
+                 matches.append(MatchResult(
+                     matched_person=person,
+                     confidence=similarity,
+                     method="name_fuzzy"
+                 ))
+         
+         return sorted(matches, key=lambda x: x.confidence, reverse=True)
+ ```
+ 
+ ### 2.3 异动计算流程
+ 
+ ```mermaid
+ flowchart TD
+     A[开始异动计算] --> B[加载异动规则配置]
+     B --> C[初始化计算引擎]
+     C --> D[遍历匹配成功的人员]
+     
+     D --> E[获取人员工资数据]
+     E --> F[获取人员异动信息]
+     F --> G[确定异动类型]
+     G --> H[查找对应规则]
+     
+     H --> I{规则存在?}
+     I -->|否| J[使用默认规则]
+     I -->|是| K[加载规则配置]
+     
+     J --> K
+     K --> L[解析规则参数]
+     L --> M[执行计算前验证]
+     M --> N{验证通过?}
+     
+     N -->|否| O[记录验证错误]
+     O --> P[跳过当前人员]
+     
+     N -->|是| Q[创建工资副本]
+     Q --> R[应用异动规则]
+     R --> S[执行具体计算]
+     S --> T[计算后验证]
+     
+     T --> U{结果合理?}
+     U -->|否| V[回滚计算结果]
+     V --> W[记录计算错误]
+     W --> P
+     
+     U -->|是| X[记录变动详情]
+     X --> Y[保存计算结果]
+     Y --> Z[更新处理状态]
+     
+     Z --> AA[下一个人员]
+     P --> AA
+     AA --> BB{还有人员?}
+     BB -->|是| E
+     BB -->|否| CC[生成计算摘要]
+     
+     CC --> DD[计算完成]
+     
+     style A fill:#e3f2fd
+     style DD fill:#e8f5e8
+     style O,W fill:#ffcdd2
+     style V fill:#fff3e0
+ ```
+ 
+ #### 2.3.1 异动计算规则引擎
+ 
+ **规则执行器设计:**
+ ```python
+ class SalaryChangeRuleEngine:
+     def __init__(self, rule_config):
+         self.rules = self.load_rules(rule_config)
+         self.calculators = {
+             "clear": ClearCalculator(),
+             "replace": ReplaceCalculator(), 
+             "subtract": SubtractCalculator(),
+             "add": AddCalculator(),
+             "calculate": FormulaCalculator()
+         }
+     
+     def apply_change(self, employee_salary, change_data):
+         """应用异动规则"""
+         change_type = change_data['change_type']
+         rule = self.get_rule(change_type)
+         
+         if not rule:
+             raise RuleNotFoundError(f"未找到异动类型 {change_type} 的规则")
+         
+         # 创建计算上下文
+         context = CalculationContext(
+             original_salary=employee_salary.copy(),
+             change_data=change_data,
+             rule=rule
+         )
+         
+         # 执行计算前验证
+         self.validate_before_calculation(context)
+         
+         # 选择计算器并执行
+         calculator = self.calculators[rule.operation_type]
+         result = calculator.calculate(context)
+         
+         # 执行计算后验证
+         self.validate_after_calculation(context, result)
+         
+         return result
+ 
+ # 具体计算器实现
+ class ClearCalculator:
+     """清零计算器 - 用于离职、调离等"""
+     def calculate(self, context):
+         result = context.original_salary.copy()
+         affected_fields = context.rule.affected_fields
+         
+         for field in affected_fields:
+             if field in result:
+                 result[field] = 0.0
+         
+         return result
+ 
+ class SubtractCalculator:
+     """扣减计算器 - 用于考核扣款、请假扣款等"""
+     def calculate(self, context):
+         result = context.original_salary.copy()
+         change_amount = context.change_data.get('change_amount', 0)
+         
+         if context.rule.operation_type == 'fixed_amount':
+             # 固定金额扣减
+             result['gross_salary'] -= change_amount
+         elif context.rule.operation_type == 'proportional':
+             # 按比例扣减
+             ratio = context.change_data.get('deduction_ratio', 0)
+             for field in context.rule.affected_fields:
+                 if field in result:
+                     result[field] *= (1 - ratio)
+         
+         return result
+ ```
+ 
+ ### 2.4 报表生成流程
+ 
+ ```mermaid
+ flowchart TD
+     A[开始报表生成] --> B[收集处理结果数据]
+     B --> C[生成异动汇总统计]
+     C --> D[创建异动明细列表]
+     D --> E[准备报表数据结构]
+     
+     E --> F[生成异动汇总表]
+     F --> G[加载审批表模板]
+     G --> H[填充审批表数据]
+     H --> I[生成工资审批表]
+     
+     I --> J[加载请示文档模板]
+     J --> K[填充请示文档数据]
+     K --> L[生成请示文档]
+     
+     L --> M[生成处理日志报告]
+     M --> N[创建输出目录]
+     N --> O[保存所有文件]
+     
+     O --> P{保存成功?}
+     P -->|否| Q[显示保存错误]
+     Q --> R[用户选择重试或修改路径]
+     R --> N
+     
+     P -->|是| S[更新文件清单]
+     S --> T[生成完成通知]
+     T --> U[报表生成完成]
+     
+     style A fill:#e3f2fd
+     style U fill:#e8f5e8
+     style Q fill:#ffcdd2
+ ```
+ 
+ #### 2.4.1 报表数据结构设计
+ 
+ **异动汇总表数据结构:**
+ ```python
+ @dataclass
+ class ChangeSummaryData:
+     """异动汇总数据结构"""
+     change_month: str           # 异动月份
+     total_employees: int        # 总处理人数
+     success_count: int          # 成功处理数
+     error_count: int            # 异常记录数
+     total_amount_change: float  # 总金额变动
+     increase_amount: float      # 增加总额
+     decrease_amount: float      # 减少总额
+     
+     change_type_summary: List[ChangeTypeSummary]  # 按类型汇总
+     department_summary: List[DepartmentSummary]   # 按部门汇总
+     detail_records: List[ChangeDetailRecord]      # 详细记录
+ 
+ @dataclass
+ class ChangeTypeSummary:
+     """异动类型汇总"""
+     change_type: str           # 异动类型
+     employee_count: int        # 人员数量
+     total_amount: float        # 变动总额
+     average_amount: float      # 平均变动
+     percentage: float          # 占比
+ 
+ @dataclass 
+ class ChangeDetailRecord:
+     """异动详细记录"""
+     employee_id: str           # 工号
+     employee_name: str         # 姓名
+     department: str            # 部门
+     change_type: str           # 异动类型
+     original_salary: float     # 原工资
+     new_salary: float          # 新工资
+     change_amount: float       # 变动金额
+     effective_date: str        # 生效日期
+     remarks: str              # 备注
+ ```
+ 
+ ### 2.5 异常处理流程
+ 
+ ```mermaid
+ flowchart TD
+     A[检测到异常] --> B[异常类型识别]
+     B --> C{异常级别}
+     
+     C -->|严重| D[立即停止处理]
+     C -->|警告| E[记录警告继续]
+     C -->|一般| F[用户选择处理方式]
+     
+     D --> G[保存当前状态]
+     G --> H[显示错误详情]
+     H --> I[用户选择恢复方式]
+     
+     I --> J{选择恢复?}
+     J -->|是| K[尝试错误恢复]
+     J -->|否| L[终止处理]
+     
+     K --> M{恢复成功?}
+     M -->|是| N[从断点继续]
+     M -->|否| L
+     
+     E --> O[记录到日志]
+     O --> P[继续后续处理]
+     
+     F --> Q{用户选择}
+     Q -->|跳过| R[跳过当前项目]
+     Q -->|重试| S[重新执行]
+     Q -->|修正| T[手动修正数据]
+     Q -->|停止| L
+     
+     R --> P
+     S --> U[重新执行操作]
+     T --> V[显示修正界面]
+     V --> W[用户修正数据]
+     W --> U
+     
+     U --> X{执行成功?}
+     X -->|是| P
+     X -->|否| Y[增加重试计数]
+     Y --> Z{重试次数超限?}
+     Z -->|是| L
+     Z -->|否| F
+     
+     P --> AA[处理完成]
+     L --> BB[生成异常报告]
+     N --> AA
+     
+     style A fill:#ffcdd2
+     style AA fill:#e8f5e8
+     style L,BB fill:#f8d7da
+     style V,W fill:#fff3e0
+ ```
+ 
+ #### 2.5.1 异常处理策略
+ 
+ **异常分类和处理策略:**
+ ```python
+ class ExceptionHandler:
+     """异常处理器"""
+     
+     EXCEPTION_LEVELS = {
+         "CRITICAL": {
+             "description": "严重错误，必须停止处理",
+             "action": "stop_immediately",
+             "examples": ["数据库连接失败", "磁盘空间不足", "内存溢出"]
+         },
+         "ERROR": {
+             "description": "处理错误，需要用户干预",
+             "action": "user_decision",
+             "examples": ["计算结果异常", "文件读写失败", "数据格式错误"]
+         },
+         "WARNING": {
+             "description": "警告信息，可以继续处理",
+             "action": "log_and_continue", 
+             "examples": ["匹配置信度低", "数据缺失", "格式不规范"]
+         },
+         "INFO": {
+             "description": "信息提示，正常处理",
+             "action": "log_only",
+             "examples": ["处理进度更新", "状态变更", "操作完成"]
+         }
+     }
+     
+     def handle_exception(self, exception, context=None):
+         """处理异常的统一入口"""
+         
+         # 1. 异常分类
+         level = self.classify_exception(exception)
+         
+         # 2. 记录异常日志
+         self.log_exception(exception, level, context)
+         
+         # 3. 根据级别执行对应策略
+         if level == "CRITICAL":
+             return self.handle_critical_exception(exception, context)
+         elif level == "ERROR":
+             return self.handle_error_exception(exception, context)
+         elif level == "WARNING":
+             return self.handle_warning_exception(exception, context)
+         else:
+             return self.handle_info_exception(exception, context)
+     
+     def handle_critical_exception(self, exception, context):
+         """处理严重异常"""
+         # 1. 立即保存当前状态
+         self.save_current_state(context)
+         
+         # 2. 停止所有处理
+         self.stop_all_processing()
+         
+         # 3. 显示错误信息给用户
+         error_dialog = CriticalErrorDialog(exception)
+         user_choice = error_dialog.show()
+         
+         if user_choice == "retry_with_fix":
+             return self.attempt_error_recovery(exception, context)
+         else:
+             return ProcessingResult(status="terminated", reason=str(exception))
+ ```
+ 
+ ## 3. 数据流设计
+ 
+ ### 3.1 系统数据流图
+ 
+ ```mermaid
+ flowchart LR
+     A[工资表Excel] --> B[数据读取器]
+     C[异动人员Excel] --> B
+     
+     B --> D[数据验证器]
+     D --> E[数据清洗器]
+     E --> F[人员匹配器]
+     
+     F --> G[异动计算引擎]
+     G --> H[结果验证器]
+     H --> I[数据存储器]
+     
+     I --> J[报表生成器]
+     I --> K[模板填充器]
+     
+     J --> L[异动汇总表]
+     K --> M[工资审批表]
+     K --> N[请示文档]
+     
+     I --> O[SQLite数据库]
+     I --> P[处理日志]
+     
+     subgraph "数据处理流水线"
+         B --> D --> E --> F --> G --> H --> I
+     end
+     
+     subgraph "输出生成器"
+         J
+         K
+     end
+     
+     subgraph "数据存储"
+         O
+         P
+     end
+ ```
+ 
+ ### 3.2 数据转换流程
+ 
+ #### 3.2.1 Excel到内部数据结构
+ 
+ ```python
+ class DataTransformer:
+     """数据转换器"""
+     
+     def transform_salary_data(self, excel_data):
+         """转换工资表数据"""
+         salary_records = []
+         
+         for row in excel_data.iterrows():
+             record = SalaryRecord(
+                 employee_id=str(row['工号']).zfill(8),
+                 name=str(row['姓名']).strip(),
+                 department=str(row['部门名称']).strip(),
+                 basic_salary=float(row['2025年岗位工资'] or 0),
+                 grade_salary=float(row['2025年薪级工资'] or 0),
+                 allowance=float(row['津贴'] or 0),
+                 performance_basic=float(row['2025年基础性绩效'] or 0),
+                 performance_reward=float(row['2025年奖励性绩效预发'] or 0),
+                 gross_salary=float(row['应发工资'] or 0),
+                 # ... 其他字段
+             )
+             salary_records.append(record)
+         
+         return salary_records
+     
+     def transform_change_data(self, excel_sheets):
+         """转换异动人员数据"""
+         change_records = []
+         
+         for sheet_name, sheet_data in excel_sheets.items():
+             change_type = self.parse_change_type(sheet_name)
+             
+             for row in sheet_data.iterrows():
+                 record = ChangeRecord(
+                     employee_id=str(row.get('工作证号', '')).zfill(8),
+                     name=str(row['姓名']).strip(),
+                     id_card=str(row.get('证件号码', '')).strip(),
+                     change_type=change_type,
+                     effective_date=self.parse_date(row.get('生效日期')),
+                     change_amount=float(row.get('变动金额', 0)),
+                     # ... 其他字段
+                 )
+                 change_records.append(record)
+         
+         return change_records
+ ```
+ 
+ ## 4. 性能和可靠性设计
+ 
+ ### 4.1 性能优化策略
+ 
+ #### 4.1.1 分批处理机制
+ ```python
+ class BatchProcessor:
+     """分批处理器"""
+     
+     def __init__(self, batch_size=100):
+         self.batch_size = batch_size
+     
+     def process_in_batches(self, data_list, processor_func):
+         """分批处理数据"""
+         total_count = len(data_list)
+         results = []
+         
+         for i in range(0, total_count, self.batch_size):
+             batch = data_list[i:i + self.batch_size]
+             batch_results = processor_func(batch)
+             results.extend(batch_results)
+             
+             # 更新进度
+             progress = min((i + self.batch_size) / total_count * 100, 100)
+             self.notify_progress(progress, f"已处理 {min(i + self.batch_size, total_count)}/{total_count} 条记录")
+         
+         return results
+ ```
+ 
+ #### 4.1.2 内存管理优化
+ ```python
+ class MemoryManager:
+     """内存管理器"""
+     
+     def __init__(self, max_memory_mb=500):
+         self.max_memory_mb = max_memory_mb
+         
+     def monitor_memory_usage(self):
+         """监控内存使用"""
+         import psutil
+         process = psutil.Process()
+         memory_usage = process.memory_info().rss / 1024 / 1024  # MB
+         
+         if memory_usage > self.max_memory_mb:
+             self.trigger_memory_cleanup()
+             
+     def trigger_memory_cleanup(self):
+         """触发内存清理"""
+         import gc
+         gc.collect()
+         
+         # 清理大数据对象缓存
+         self.clear_data_cache()
+ ```
+ 
+ ### 4.2 可靠性保障
+ 
+ #### 4.2.1 数据一致性检查
+ ```python
+ class DataConsistencyChecker:
+     """数据一致性检查器"""
+     
+     def check_salary_data_consistency(self, salary_data):
+         """检查工资数据一致性"""
+         errors = []
+         
+         for record in salary_data:
+             # 检查工资计算是否正确
+             calculated_gross = (record.basic_salary + record.grade_salary + 
+                               record.allowance + record.performance_basic + 
+                               record.performance_reward)
+             
+             if abs(calculated_gross - record.gross_salary) > 0.01:
+                 errors.append(f"工号 {record.employee_id} 工资计算不一致")
+         
+         return errors
+     
+     def check_change_calculation_consistency(self, original, modified, change_amount):
+         """检查异动计算一致性"""
+         expected_change = modified.gross_salary - original.gross_salary
+         
+         if abs(expected_change - change_amount) > 0.01:
+             return False, f"预期变动 {expected_change} 与记录变动 {change_amount} 不一致"
+         
+         return True, "计算一致"
+ ```
+ 
+ #### 4.2.2 断点续传机制
+ ```python
+ class CheckpointManager:
+     """断点管理器"""
+     
+     def save_checkpoint(self, stage, processed_data, current_index):
+         """保存处理检查点"""
+         checkpoint = {
+             "timestamp": datetime.now().isoformat(),
+             "stage": stage,
+             "current_index": current_index,
+             "processed_count": len(processed_data),
+             "data_hash": self.calculate_data_hash(processed_data)
+         }
+         
+         with open("checkpoint.json", "w", encoding="utf-8") as f:
+             json.dump(checkpoint, f, ensure_ascii=False, indent=2)
+     
+     def load_checkpoint(self):
+         """加载处理检查点"""
+         try:
+             with open("checkpoint.json", "r", encoding="utf-8") as f:
+                 return json.load(f)
+         except FileNotFoundError:
+             return None
+     
+     def resume_from_checkpoint(self, checkpoint):
+         """从检查点恢复处理"""
+         stage = checkpoint["stage"]
+         current_index = checkpoint["current_index"]
+         
+         # 根据阶段恢复相应的处理状态
+         if stage == "matching":
+             return self.resume_matching_process(current_index)
+         elif stage == "calculation":
+             return self.resume_calculation_process(current_index)
+         # ... 其他阶段
+ ```
+ 
+ 这个业务流程设计提供了完整的工作流程规范，确保系统能够稳定、高效地完成工资异动处理任务。 
```

---

现在让我继续创建剩余的设计文档：

## 5. 模块详细设计

---

```diff
- 
+ # 模块详细设计
+ 
+ ## 1. 模块架构总览
+ 
+ ### 1.1 模块分层架构
+ 
+ ```mermaid
+ graph TB
+     subgraph "用户界面层"
+         A[MainWindow 主窗口]
+         B[FileDialog 文件对话框]
+         C[ProgressDialog 进度对话框]
+         D[SettingsDialog 设置对话框]
+     end
+     
+     subgraph "业务逻辑层"
+         E[MainController 主控制器]
+         F[PersonMatcher 人员匹配器]
+         G[SalaryCalculator 工资计算器]
+         H[ReportGenerator 报表生成器]
+         I[TemplateManager 模板管理器]
+     end
+     
+     subgraph "数据访问层"
+         J[ExcelProcessor Excel处理器]
+         K[DatabaseManager 数据库管理器]
+         L[DataSynchronizer 数据同步器]
+         M[ConfigManager 配置管理器]
+     end
+     
+     subgraph "工具支持层"
+         N[LogManager 日志管理器]
+         O[Validator 数据验证器]
+         P[ExceptionHandler 异常处理器]
+         Q[FileManager 文件管理器]
+     end
+     
+     A --> E
+     B --> E
+     C --> E
+     D --> E
+     
+     E --> F
+     E --> G
+     E --> H
+     E --> I
+     
+     F --> J
+     F --> K
+     G --> J
+     G --> K
+     H --> J
+     H --> L
+     I --> M
+     
+     J --> N
+     K --> N
+     L --> O
+     M --> P
+     
+     style A,B,C,D fill:#e3f2fd
+     style E,F,G,H,I fill:#f3e5f5
+     style J,K,L,M fill:#e8f5e8
+     style N,O,P,Q fill:#fff3e0
+ ```
+ 
+ ## 2. 核心模块详细设计
+ 
+ ### 2.1 主控制器模块 (MainController)
+ 
+ #### 2.1.1 模块职责
+ - 协调各业务模块的工作流程
+ - 管理应用程序的生命周期
+ - 处理用户交互事件
+ - 控制数据流转和状态管理
+ 
+ #### 2.1.2 类设计
+ 
+ ```python
+ from abc import ABC, abstractmethod
+ from typing import List, Dict, Optional, Callable
+ from enum import Enum
+ import asyncio
+ from dataclasses import dataclass
+ 
+ class ProcessingState(Enum):
+     """处理状态枚举"""
+     IDLE = "idle"
+     LOADING = "loading"
+     MATCHING = "matching"
+     CALCULATING = "calculating"
+     GENERATING = "generating"
+     COMPLETED = "completed"
+     ERROR = "error"
+     CANCELLED = "cancelled"
+ 
+ @dataclass
+ class ProcessingContext:
+     """处理上下文数据"""
+     salary_file: str = ""
+     change_file: str = ""
+     output_dir: str = ""
+     processing_month: str = ""
+     enabled_rules: List[str] = None
+     batch_id: str = ""
+     start_time: datetime = None
+     
+     def __post_init__(self):
+         if self.enabled_rules is None:
+             self.enabled_rules = []
+ 
+ class MainController:
+     """主控制器"""
+     
+     def __init__(self):
+         self.state = ProcessingState.IDLE
+         self.context = ProcessingContext()
+         self.progress_callback: Optional[Callable] = None
+         self.error_callback: Optional[Callable] = None
+         
+         # 初始化各模块
+         self.excel_processor = ExcelProcessor()
+         self.person_matcher = PersonMatcher()
+         self.salary_calculator = SalaryCalculator()
+         self.report_generator = ReportGenerator()
+         self.template_manager = TemplateManager()
+         self.database_manager = DatabaseManager()
+         
+         # 日志和异常处理
+         self.logger = LogManager().get_logger(__name__)
+         self.exception_handler = ExceptionHandler()
+         
+     def set_progress_callback(self, callback: Callable[[int, str], None]):
+         """设置进度回调函数"""
+         self.progress_callback = callback
+         
+     def set_error_callback(self, callback: Callable[[Exception], None]):
+         """设置错误回调函数"""
+         self.error_callback = callback
+     
+     async def execute_workflow(self, context: ProcessingContext) -> ProcessingResult:
+         """执行主工作流程"""
+         try:
+             self.context = context
+             self.context.batch_id = self._generate_batch_id()
+             self.context.start_time = datetime.now()
+             
+             self.logger.info(f"开始执行工作流程，批次ID: {self.context.batch_id}")
+             
+             # 阶段1: 数据加载和验证
+             self._update_state(ProcessingState.LOADING)
+             await self._load_and_validate_data()
+             
+             # 阶段2: 人员匹配
+             self._update_state(ProcessingState.MATCHING)
+             matching_results = await self._execute_person_matching()
+             
+             # 阶段3: 异动计算
+             self._update_state(ProcessingState.CALCULATING)
+             calculation_results = await self._execute_salary_calculation(matching_results)
+             
+             # 阶段4: 报表生成
+             self._update_state(ProcessingState.GENERATING)
+             report_results = await self._generate_reports(calculation_results)
+             
+             # 阶段5: 完成处理
+             self._update_state(ProcessingState.COMPLETED)
+             return self._create_success_result(report_results)
+             
+         except Exception as e:
+             self._update_state(ProcessingState.ERROR)
+             self.logger.error(f"工作流程执行失败: {str(e)}")
+             
+             if self.error_callback:
+                 self.error_callback(e)
+             
+             return self._create_error_result(e)
+     
+     async def _load_and_validate_data(self):
+         """加载和验证数据"""
+         self._update_progress(10, "正在读取工资表文件...")
+         
+         # 读取工资表
+         salary_data = await self.excel_processor.read_salary_file(self.context.salary_file)
+         if not salary_data:
+             raise DataLoadException("工资表文件读取失败")
+         
+         self._update_progress(30, "正在读取异动人员文件...")
+         
+         # 读取异动人员表
+         change_data = await self.excel_processor.read_change_file(self.context.change_file)
+         if not change_data:
+             raise DataLoadException("异动人员文件读取失败")
+         
+         self._update_progress(50, "正在验证数据完整性...")
+         
+         # 数据验证
+         validation_result = self._validate_data(salary_data, change_data)
+         if not validation_result.is_valid:
+             raise DataValidationException(f"数据验证失败: {validation_result.errors}")
+         
+         # 保存到数据库
+         await self.database_manager.save_salary_data(salary_data, self.context.processing_month)
+         await self.database_manager.save_change_data(change_data, self.context.processing_month)
+         
+         self._update_progress(70, "数据加载完成")
+     
+     async def _execute_person_matching(self) -> MatchingResults:
+         """执行人员匹配"""
+         self._update_progress(10, "开始人员匹配...")
+         
+         # 获取数据
+         salary_persons = await self.database_manager.get_salary_persons(self.context.processing_month)
+         change_persons = await self.database_manager.get_change_persons(self.context.processing_month)
+         
+         # 执行匹配
+         matching_results = await self.person_matcher.match_persons(
+             change_persons, salary_persons, 
+             progress_callback=lambda p, m: self._update_progress(10 + p * 0.8, m)
+         )
+         
+         # 处理需要手动确认的匹配
+         if matching_results.pending_confirmations:
+             confirmed_results = await self._handle_manual_confirmations(matching_results.pending_confirmations)
+             matching_results.apply_confirmations(confirmed_results)
+         
+         # 保存匹配结果
+         await self.database_manager.save_matching_results(matching_results)
+         
+         self._update_progress(100, f"人员匹配完成，成功匹配 {matching_results.success_count} 人")
+         return matching_results
+     
+     async def _execute_salary_calculation(self, matching_results: MatchingResults) -> CalculationResults:
+         """执行工资计算"""
+         self._update_progress(10, "开始工资异动计算...")
+         
+         # 获取异动规则
+         rules = await self.database_manager.get_change_rules(self.context.enabled_rules)
+         
+         # 执行计算
+         calculation_results = await self.salary_calculator.calculate_changes(
+             matching_results, rules,
+             progress_callback=lambda p, m: self._update_progress(10 + p * 0.8, m)
+         )
+         
+         # 验证计算结果
+         validation_result = self._validate_calculation_results(calculation_results)
+         if not validation_result.is_valid:
+             raise CalculationException(f"计算结果验证失败: {validation_result.errors}")
+         
+         # 保存计算结果
+         await self.database_manager.save_calculation_results(calculation_results)
+         
+         self._update_progress(100, f"工资计算完成，处理 {calculation_results.processed_count} 人")
+         return calculation_results
+     
+     async def _generate_reports(self, calculation_results: CalculationResults) -> ReportResults:
+         """生成报表"""
+         self._update_progress(10, "正在生成异动汇总表...")
+         
+         # 生成异动汇总表
+         summary_report = await self.report_generator.generate_summary_report(
+             calculation_results, self.context.output_dir
+         )
+         
+         self._update_progress(40, "正在生成工资审批表...")
+         
+         # 生成工资审批表
+         approval_report = await self.report_generator.generate_approval_report(
+             calculation_results, self.context.output_dir
+         )
+         
+         self._update_progress(70, "正在生成请示文档...")
+         
+         # 生成请示文档
+         request_documents = await self.template_manager.generate_request_documents(
+             calculation_results, self.context.output_dir
+         )
+         
+         self._update_progress(100, "报表生成完成")
+         
+         return ReportResults(
+             summary_report=summary_report,
+             approval_report=approval_report,
+             request_documents=request_documents
+         )
+     
+     def _update_state(self, new_state: ProcessingState):
+         """更新处理状态"""
+         old_state = self.state
+         self.state = new_state
+         self.logger.info(f"状态变更: {old_state.value} -> {new_state.value}")
+     
+     def _update_progress(self, percentage: int, message: str):
+         """更新进度"""
+         self.logger.debug(f"进度更新: {percentage}% - {message}")
+         if self.progress_callback:
+             self.progress_callback(percentage, message)
+     
+     def _generate_batch_id(self) -> str:
+         """生成批次ID"""
+         import uuid
+         return f"BATCH_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
+     
+     def cancel_processing(self):
+         """取消处理"""
+         if self.state in [ProcessingState.LOADING, ProcessingState.MATCHING, 
+                          ProcessingState.CALCULATING, ProcessingState.GENERATING]:
+             self._update_state(ProcessingState.CANCELLED)
+             self.logger.info("用户取消了处理流程")
+ ```
+ 
+ #### 2.1.3 接口定义
+ 
+ ```python
+ class IMainController(ABC):
+     """主控制器接口"""
+     
+     @abstractmethod
+     async def execute_workflow(self, context: ProcessingContext) -> ProcessingResult:
+         """执行主工作流程"""
+         pass
+     
+     @abstractmethod
+     def set_progress_callback(self, callback: Callable[[int, str], None]):
+         """设置进度回调"""
+         pass
+     
+     @abstractmethod
+     def cancel_processing(self):
+         """取消处理"""
+         pass
+     
+     @abstractmethod
+     def get_current_state(self) -> ProcessingState:
+         """获取当前状态"""
+         pass
+ ```
+ 
+ ### 2.2 人员匹配器模块 (PersonMatcher)
+ 
+ #### 2.2.1 模块职责
+ - 实现多字段人员匹配算法
+ - 提供模糊匹配和精确匹配功能
+ - 管理匹配结果和置信度评估
+ - 处理手动确认流程
+ 
+ #### 2.2.2 类设计
+ 
+ ```python
+ from difflib import SequenceMatcher
+ from typing import List, Dict, Tuple, Optional
+ import re
+ 
+ @dataclass
+ class MatchResult:
+     """匹配结果"""
+     source_person: ChangePersonRecord
+     matched_person: Optional[SalaryPersonRecord]
+     confidence: float
+     match_method: str
+     match_details: Dict[str, Any]
+     
+     @property
+     def is_matched(self) -> bool:
+         return self.matched_person is not None
+ 
+ @dataclass
+ class MatchingResults:
+     """匹配结果集合"""
+     total_count: int
+     success_count: int
+     failed_count: int
+     pending_count: int
+     results: List[MatchResult]
+     pending_confirmations: List[MatchResult]
+     
+     def get_success_rate(self) -> float:
+         return self.success_count / self.total_count if self.total_count > 0 else 0.0
+ 
+ class PersonMatcher:
+     """人员匹配器"""
+     
+     def __init__(self, config: MatchingConfig = None):
+         self.config = config or MatchingConfig()
+         self.logger = LogManager().get_logger(__name__)
+         
+         # 匹配策略
+         self.strategies = [
+             EmployeeIdExactMatcher(),
+             IdCardExactMatcher(),
+             NameFuzzyMatcher(self.config.fuzzy_threshold),
+             ComprehensiveMatcher()
+         ]
+     
+     async def match_persons(self, change_persons: List[ChangePersonRecord], 
+                            salary_persons: List[SalaryPersonRecord],
+                            progress_callback: Optional[Callable] = None) -> MatchingResults:
+         """执行人员匹配"""
+         
+         # 建立索引以提高匹配效率
+         salary_index = self._build_salary_index(salary_persons)
+         
+         results = []
+         pending_confirmations = []
+         
+         total_count = len(change_persons)
+         
+         for i, change_person in enumerate(change_persons):
+             if progress_callback:
+                 progress = int((i + 1) / total_count * 100)
+                 progress_callback(progress, f"正在匹配第 {i+1}/{total_count} 个人员: {change_person.name}")
+             
+             # 执行匹配
+             match_result = await self._match_single_person(change_person, salary_index)
+             
+             # 根据置信度决定是否需要手动确认
+             if match_result.is_matched and match_result.confidence >= self.config.auto_confirm_threshold:
+                 results.append(match_result)
+             elif match_result.confidence >= self.config.manual_confirm_threshold:
+                 pending_confirmations.append(match_result)
+             else:
+                 # 匹配失败
+                 match_result.matched_person = None
+                 match_result.confidence = 0.0
+                 results.append(match_result)
+         
+         # 统计结果
+         success_count = sum(1 for r in results if r.is_matched)
+         failed_count = len(results) - success_count
+         pending_count = len(pending_confirmations)
+         
+         return MatchingResults(
+             total_count=total_count,
+             success_count=success_count,
+             failed_count=failed_count,
+             pending_count=pending_count,
+             results=results,
+             pending_confirmations=pending_confirmations
+         )
+     
+     async def _match_single_person(self, change_person: ChangePersonRecord, 
+                                   salary_index: SalaryPersonIndex) -> MatchResult:
+         """匹配单个人员"""
+         
+         # 依次尝试各种匹配策略
+         for strategy in self.strategies:
+             match_result = strategy.match(change_person, salary_index)
+             
+             if match_result and match_result.confidence > 0:
+                 self.logger.debug(f"使用策略 {strategy.__class__.__name__} 匹配到人员: "
+                                 f"{change_person.name} -> {match_result.matched_person.name}, "
+                                 f"置信度: {match_result.confidence}")
+                 return match_result
+         
+         # 未找到匹配
+         return MatchResult(
+             source_person=change_person,
+             matched_person=None,
+             confidence=0.0,
+             match_method="no_match",
+             match_details={"reason": "未找到匹配的人员"}
+         )
+     
+     def _build_salary_index(self, salary_persons: List[SalaryPersonRecord]) -> SalaryPersonIndex:
+         """建立工资表人员索引"""
+         index = SalaryPersonIndex()
+         
+         for person in salary_persons:
+             # 工号索引
+             if person.employee_id:
+                 index.employee_id_index[person.employee_id] = person
+             
+             # 身份证索引
+             if person.id_card:
+                 normalized_id = self._normalize_id_card(person.id_card)
+                 index.id_card_index[normalized_id] = person
+             
+             # 姓名索引
+             if person.name:
+                 normalized_name = self._normalize_name(person.name)
+                 if normalized_name not in index.name_index:
+                     index.name_index[normalized_name] = []
+                 index.name_index[normalized_name].append(person)
+         
+         return index
+     
+     def _normalize_id_card(self, id_card: str) -> str:
+         """规范化身份证号"""
+         # 移除空格和连字符
+         normalized = re.sub(r'[\s\-]', '', id_card.upper())
+         return normalized
+     
+     def _normalize_name(self, name: str) -> str:
+         """规范化姓名"""
+         # 移除空格和特殊字符
+         normalized = re.sub(r'\s+', '', name.strip())
+         return normalized
+ 
+ class SalaryPersonIndex:
+     """工资表人员索引"""
+     
+     def __init__(self):
+         self.employee_id_index: Dict[str, SalaryPersonRecord] = {}
+         self.id_card_index: Dict[str, SalaryPersonRecord] = {}
+         self.name_index: Dict[str, List[SalaryPersonRecord]] = {}
+ 
+ # 匹配策略实现
+ class MatchingStrategy(ABC):
+     """匹配策略基类"""
+     
+     @abstractmethod
+     def match(self, change_person: ChangePersonRecord, 
+               salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
+         pass
+ 
+ class EmployeeIdExactMatcher(MatchingStrategy):
+     """工号精确匹配"""
+     
+     def match(self, change_person: ChangePersonRecord, 
+               salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
+         
+         if not change_person.employee_id:
+             return None
+         
+         matched_person = salary_index.employee_id_index.get(change_person.employee_id)
+         
+         if matched_person:
+             return MatchResult(
+                 source_person=change_person,
+                 matched_person=matched_person,
+                 confidence=1.0,
+                 match_method="employee_id_exact",
+                 match_details={
+                     "matched_field": "employee_id",
+                     "matched_value": change_person.employee_id
+                 }
+             )
+         
+         return None
+ 
+ class IdCardExactMatcher(MatchingStrategy):
+     """身份证精确匹配"""
+     
+     def match(self, change_person: ChangePersonRecord, 
+               salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
+         
+         if not change_person.id_card:
+             return None
+         
+         normalized_id = self._normalize_id_card(change_person.id_card)
+         matched_person = salary_index.id_card_index.get(normalized_id)
+         
+         if matched_person:
+             return MatchResult(
+                 source_person=change_person,
+                 matched_person=matched_person,
+                 confidence=0.95,
+                 match_method="id_card_exact",
+                 match_details={
+                     "matched_field": "id_card",
+                     "matched_value": normalized_id
+                 }
+             )
+         
+         return None
+     
+     def _normalize_id_card(self, id_card: str) -> str:
+         return re.sub(r'[\s\-]', '', id_card.upper())
+ 
+ class NameFuzzyMatcher(MatchingStrategy):
+     """姓名模糊匹配"""
+     
+     def __init__(self, threshold: float = 0.8):
+         self.threshold = threshold
+     
+     def match(self, change_person: ChangePersonRecord, 
+               salary_index: SalaryPersonIndex) -> Optional[MatchResult]:
+         
+         if not change_person.name:
+             return None
+         
+         best_match = None
+         best_similarity = 0.0
+         
+         # 搜索所有姓名
+         for name, persons in salary_index.name_index.items():
+             similarity = SequenceMatcher(None, change_person.name, name).ratio()
+             
+             if similarity > best_similarity and similarity >= self.threshold:
+                 best_similarity = similarity
+                 best_match = persons[0]  # 取第一个匹配的人员
+         
+         if best_match:
+             return MatchResult(
+                 source_person=change_person,
+                 matched_person=best_match,
+                 confidence=best_similarity,
+                 match_method="name_fuzzy",
+                 match_details={
+                     "matched_field": "name",
+                     "similarity": best_similarity,
+                     "matched_name": best_match.name
+                 }
+             )
+         
+         return None
+ ```
+ 
+ ### 2.3 工资计算器模块 (SalaryCalculator)
+ 
+ #### 2.3.1 模块职责
+ - 实现各种异动类型的工资计算逻辑
+ - 管理计算规则和配置
+ - 提供计算结果验证机制
+ - 支持批量计算和进度反馈
+ 
+ #### 2.3.2 类设计
+ 
+ ```python
+ from decimal import Decimal, ROUND_HALF_UP
+ from typing import Dict, List, Optional, Any
+ import json
+ 
+ @dataclass
+ class CalculationContext:
+     """计算上下文"""
+     employee: SalaryPersonRecord
+     change_data: ChangePersonRecord
+     rule: ChangeRule
+     processing_month: str
+     additional_params: Dict[str, Any] = None
+     
+     def __post_init__(self):
+         if self.additional_params is None:
+             self.additional_params = {}
+ 
+ @dataclass
+ class CalculationResult:
+     """计算结果"""
+     employee_id: str
+     original_salary: Dict[str, Decimal]
+     modified_salary: Dict[str, Decimal]
+     change_amount: Decimal
+     change_details: Dict[str, Any]
+     calculation_method: str
+     is_successful: bool
+     error_message: Optional[str] = None
+ 
+ class SalaryCalculator:
+     """工资计算器"""
+     
+     def __init__(self):
+         self.logger = LogManager().get_logger(__name__)
+         self.precision = Decimal('0.01')  # 精度到分
+         
+         # 注册计算器
+         self.calculators = {
+             "clear": ClearSalaryCalculator(),
+             "replace": ReplaceSalaryCalculator(),
+             "subtract": SubtractSalaryCalculator(),
+             "add": AddSalaryCalculator(),
+             "calculate": FormulaSalaryCalculator()
+         }
+         
+         # 验证器
+         self.validators = [
+             NegativeSalaryValidator(),
+             ExcessiveChangeValidator(),
+             DataConsistencyValidator()
+         ]
+     
+     async def calculate_changes(self, matching_results: MatchingResults, 
+                                rules: List[ChangeRule],
+                                progress_callback: Optional[Callable] = None) -> CalculationResults:
+         """批量计算工资变动"""
+         
+         # 构建规则映射
+         rule_map = {rule.change_type: rule for rule in rules}
+         
+         results = []
+         successful_count = 0
+         error_count = 0
+         
+         # 获取成功匹配的人员
+         matched_persons = [r for r in matching_results.results if r.is_matched]
+         total_count = len(matched_persons)
+         
+         for i, match_result in enumerate(matched_persons):
+             if progress_callback:
+                 progress = int((i + 1) / total_count * 100)
+                 progress_callback(progress, f"正在计算第 {i+1}/{total_count} 个人员: {match_result.source_person.name}")
+             
+             try:
+                 # 查找对应的计算规则
+                 rule = rule_map.get(match_result.source_person.change_type)
+                 if not rule:
+                     raise CalculationException(f"未找到异动类型 {match_result.source_person.change_type} 的计算规则")
+                 
+                 # 执行计算
+                 calc_result = await self._calculate_single_change(
+                     match_result.matched_person,
+                     match_result.source_person,
+                     rule
+                 )
+                 
+                 results.append(calc_result)
+                 
+                 if calc_result.is_successful:
+                     successful_count += 1
+                 else:
+                     error_count += 1
+                     
+             except Exception as e:
+                 self.logger.error(f"计算工资变动失败 - 员工: {match_result.source_person.name}, 错误: {str(e)}")
+                 
+                 error_result = CalculationResult(
+                     employee_id=match_result.matched_person.employee_id,
+                     original_salary={},
+                     modified_salary={},
+                     change_amount=Decimal('0'),
+                     change_details={},
+                     calculation_method="error",
+                     is_successful=False,
+                     error_message=str(e)
+                 )
+                 
+                 results.append(error_result)
+                 error_count += 1
+         
+         return CalculationResults(
+             total_count=total_count,
+             successful_count=successful_count,
+             error_count=error_count,
+             results=results,
+             processing_month=matching_results.processing_month
+         )
+     
+     async def _calculate_single_change(self, employee: SalaryPersonRecord,
+                                       change_data: ChangePersonRecord,
+                                       rule: ChangeRule) -> CalculationResult:
+         """计算单个人员的工资变动"""
+         
+         # 创建计算上下文
+         context = CalculationContext(
+             employee=employee,
+             change_data=change_data,
+             rule=rule,
+             processing_month=change_data.processing_month
+         )
+         
+         # 获取对应的计算器
+         calculator = self.calculators.get(rule.operation_type)
+         if not calculator:
+             raise CalculationException(f"不支持的计算类型: {rule.operation_type}")
+         
+         # 执行计算前验证
+         self._validate_before_calculation(context)
+         
+         # 执行计算
+         original_salary = self._get_salary_dict(employee)
+         modified_salary = calculator.calculate(context, original_salary.copy())
+         
+         # 计算变动金额
+         change_amount = self._calculate_total_change(original_salary, modified_salary)
+         
+         # 创建计算结果
+         result = CalculationResult(
+             employee_id=employee.employee_id,
+             original_salary=original_salary,
+             modified_salary=modified_salary,
+             change_amount=change_amount,
+             change_details=calculator.get_calculation_details(context),
+             calculation_method=rule.operation_type,
+             is_successful=True
+         )
+         
+         # 执行计算后验证
+         self._validate_after_calculation(result)
+         
+         return result
+     
+     def _get_salary_dict(self, employee: SalaryPersonRecord) -> Dict[str, Decimal]:
+         """获取员工工资字典"""
+         return {
+             'basic_salary': Decimal(str(employee.basic_salary)),
+             'grade_salary': Decimal(str(employee.grade_salary)),
+             'allowance': Decimal(str(employee.allowance)),
+             'performance_basic': Decimal(str(employee.performance_basic)),
+             'performance_reward': Decimal(str(employee.performance_reward)),
+             'subsidy_transport': Decimal(str(employee.subsidy_transport)),
+             'subsidy_housing': Decimal(str(employee.subsidy_housing)),
+             'gross_salary': Decimal(str(employee.gross_salary)),
+             'net_salary': Decimal(str(employee.net_salary))
+         }
+     
+     def _calculate_total_change(self, original: Dict[str, Decimal], 
+                                modified: Dict[str, Decimal]) -> Decimal:
+         """计算总变动金额"""
+         original_total = original.get('gross_salary', Decimal('0'))
+         modified_total = modified.get('gross_salary', Decimal('0'))
+         return modified_total - original_total
+     
+     def _validate_before_calculation(self, context: CalculationContext):
+         """计算前验证"""
+         # 检查必要字段
+         if not context.employee.employee_id:
+             raise ValidationException("员工工号不能为空")
+         
+         if not context.rule:
+             raise ValidationException("计算规则不能为空")
+     
+     def _validate_after_calculation(self, result: CalculationResult):
+         """计算后验证"""
+         for validator in self.validators:
+             validation_result = validator.validate(result)
+             if not validation_result.is_valid:
+                 self.logger.warning(f"计算结果验证警告: {validation_result.message}")
+ 
+ # 具体计算器实现
+ class SalaryCalculatorBase(ABC):
+     """工资计算器基类"""
+     
+     @abstractmethod
+     def calculate(self, context: CalculationContext, 
+                   salary_data: Dict[str, Decimal]) -> Dict[str, Decimal]:
+         pass
+     
+     def get_calculation_details(self, context: CalculationContext) -> Dict[str, Any]:
+         """获取计算详情"""
+         return {
+             "rule_name": context.rule.rule_name,
+             "change_type": context.change_data.change_type,
+             "effective_date": str(context.change_data.effective_date),
+             "calculation_time": datetime.now().isoformat()
+         }
+ 
+ class ClearSalaryCalculator(SalaryCalculatorBase):
+     """清零计算器 - 用于离职、调离等"""
+     
+     def calculate(self, context: CalculationContext, 
+                   salary_data: Dict[str, Decimal]) -> Dict[str, Decimal]:
+         
+         # 解析影响字段
+         affected_fields = json.loads(context.rule.affected_fields)
+         
+         # 清零指定字段
+         for field in affected_fields:
+             if field in salary_data:
+                 salary_data[field] = Decimal('0')
+         
+         # 重新计算应发工资
+         salary_data['gross_salary'] = self._recalculate_gross_salary(salary_data)
+         
+         return salary_data
+     
+     def _recalculate_gross_salary(self, salary_data: Dict[str, Decimal]) -> Decimal:
+         """重新计算应发工资"""
+         return (salary_data.get('basic_salary', Decimal('0')) +
+                 salary_data.get('grade_salary', Decimal('0')) +
+                 salary_data.get('allowance', Decimal('0')) +
+                 salary_data.get('performance_basic', Decimal('0')) +
+                 salary_data.get('performance_reward', Decimal('0')) +
+                 salary_data.get('subsidy_transport', Decimal('0')) +
+                 salary_data.get('subsidy_housing', Decimal('0')))
+ 
+ class SubtractSalaryCalculator(SalaryCalculatorBase):
+     """扣减计算器 - 用于考核扣款、请假扣款等"""
+     
+     def calculate(self, context: CalculationContext, 
+                   salary_data: Dict[str, Decimal]) -> Dict[str, Decimal]:
+         
+         rule_config = json.loads(context.rule.rule_config)
+         
+         if rule_config.get('fixed_amount'):
+             # 固定金额扣减
+             deduction_amount = Decimal(str(context.change_data.change_amount or 0))
+             salary_data['gross_salary'] -= deduction_amount
+             
+         elif rule_config.get('proportional'):
+             # 按比例扣减
+             ratio = Decimal(str(context.change_data.deduction_ratio or 0))
+             affected_fields = json.loads(context.rule.affected_fields)
+             
+             for field in affected_fields:
+                 if field in salary_data:
+                     salary_data[field] *= (Decimal('1') - ratio)
+             
+             # 重新计算应发工资
+             salary_data['gross_salary'] = self._recalculate_gross_salary(salary_data)
+         
+         # 确保不为负数
+         for field, value in salary_data.items():
+             if value < Decimal('0'):
+                 salary_data[field] = Decimal('0')
+         
+         return salary_data
+ ```
+ 
+ ### 2.4 报表生成器模块 (ReportGenerator)
+ 
+ #### 2.4.1 模块职责
+ - 生成各种格式的报表文档
+ - 处理数据汇总和统计分析
+ - 管理报表模板和样式
+ - 支持多种输出格式
+ 
+ #### 2.4.2 类设计
+ 
+ ```python
+ import pandas as pd
+ from openpyxl import Workbook
+ from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
+ from openpyxl.utils.dataframe import dataframe_to_rows
+ 
+ class ReportGenerator:
+     """报表生成器"""
+     
+     def __init__(self):
+         self.logger = LogManager().get_logger(__name__)
+         
+         # 报表样式配置
+         self.styles = {
+             'header': {
+                 'font': Font(name='微软雅黑', size=12, bold=True, color='FFFFFF'),
+                 'fill': PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid'),
+                 'alignment': Alignment(horizontal='center', vertical='center'),
+                 'border': Border(
+                     left=Side(style='thin'),
+                     right=Side(style='thin'),
+                     top=Side(style='thin'),
+                     bottom=Side(style='thin')
+                 )
+             },
+             'data': {
+                 'font': Font(name='微软雅黑', size=10),
+                 'alignment': Alignment(horizontal='center', vertical='center'),
+                 'border': Border(
+                     left=Side(style='thin'),
+                     right=Side(style='thin'),
+                     top=Side(style='thin'),
+                     bottom=Side(style='thin')
+                 )
+             }
+         }
+     
+     async def generate_summary_report(self, calculation_results: CalculationResults,
+                                      output_dir: str) -> str:
+         """生成异动汇总报表"""
+         
+         # 准备数据
+         summary_data = self._prepare_summary_data(calculation_results)
+         
+         # 创建Excel文件
+         output_file = os.path.join(output_dir, f"{calculation_results.processing_month}_异动汇总表.xlsx")
+         
+         with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
+             # 写入汇总统计
+             summary_df = pd.DataFrame([{
+                 '处理月份': calculation_results.processing_month,
+                 '总处理人数': calculation_results.total_count,
+                 '成功处理': calculation_results.successful_count,
+                 '失败处理': calculation_results.error_count,
+                 '总变动金额': summary_data['total_change_amount'],
+                 '增加金额': summary_data['increase_amount'],
+                 '减少金额': summary_data['decrease_amount']
+             }])
+             summary_df.to_excel(writer, sheet_name='处理汇总', index=False)
+             
+             # 写入异动类型统计
+             type_summary_df = pd.DataFrame(summary_data['type_summary'])
+             type_summary_df.to_excel(writer, sheet_name='异动类型统计', index=False)
+             
+             # 写入部门统计
+             dept_summary_df = pd.DataFrame(summary_data['department_summary'])
+             dept_summary_df.to_excel(writer, sheet_name='部门统计', index=False)
+             
+             # 写入详细记录
+             detail_df = pd.DataFrame(summary_data['detail_records'])
+             detail_df.to_excel(writer, sheet_name='异动明细', index=False)
+         
+         # 应用样式
+         self._apply_excel_styles(output_file)
+         
+         self.logger.info(f"异动汇总报表已生成: {output_file}")
+         return output_file
+     
+     def _prepare_summary_data(self, calculation_results: CalculationResults) -> Dict[str, Any]:
+         """准备汇总数据"""
+         
+         # 统计各类数据
+         total_change_amount = Decimal('0')
+         increase_amount = Decimal('0')
+         decrease_amount = Decimal('0')
+         
+         type_stats = {}
+         dept_stats = {}
+         detail_records = []
+         
+         for result in calculation_results.results:
+             if not result.is_successful:
+                 continue
+             
+             # 累计变动金额
+             change_amount = result.change_amount
+             total_change_amount += change_amount
+             
+             if change_amount > 0:
+                 increase_amount += change_amount
+             else:
+                 decrease_amount += abs(change_amount)
+             
+             # 按异动类型统计
+             change_type = result.change_details.get('change_type', '未知')
+             if change_type not in type_stats:
+                 type_stats[change_type] = {
+                     'count': 0,
+                     'total_amount': Decimal('0')
+                 }
+             type_stats[change_type]['count'] += 1
+             type_stats[change_type]['total_amount'] += change_amount
+             
+             # 按部门统计
+             department = result.change_details.get('department', '未知')
+             if department not in dept_stats:
+                 dept_stats[department] = {
+                     'count': 0,
+                     'total_amount': Decimal('0')
+                 }
+             dept_stats[department]['count'] += 1
+             dept_stats[department]['total_amount'] += change_amount
+             
+             # 详细记录
+             detail_records.append({
+                 '工号': result.employee_id,
+                 '姓名': result.change_details.get('employee_name', ''),
+                 '部门': department,
+                 '异动类型': change_type,
+                 '原工资': float(result.original_salary.get('gross_salary', 0)),
+                 '新工资': float(result.modified_salary.get('gross_salary', 0)),
+                 '变动金额': float(change_amount),
+                 '生效日期': result.change_details.get('effective_date', ''),
+                 '处理时间': result.change_details.get('calculation_time', '')
+             })
+         
+         # 转换统计数据
+         type_summary = []
+         for change_type, stats in type_stats.items():
+             type_summary.append({
+                 '异动类型': change_type,
+                 '人数': stats['count'],
+                 '变动总金额': float(stats['total_amount']),
+                 '平均变动': float(stats['total_amount'] / stats['count']) if stats['count'] > 0 else 0,
+                 '占比': f"{stats['count'] / len(detail_records) * 100:.1f}%" if detail_records else "0%"
+             })
+         
+         department_summary = []
+         for department, stats in dept_stats.items():
+             department_summary.append({
+                 '部门': department,
+                 '人数': stats['count'],
+                 '变动总金额': float(stats['total_amount']),
+                 '平均变动': float(stats['total_amount'] / stats['count']) if stats['count'] > 0 else 0
+             })
+         
+         return {
+             'total_change_amount': float(total_change_amount),
+             'increase_amount': float(increase_amount),
+             'decrease_amount': float(decrease_amount),
+             'type_summary': type_summary,
+             'department_summary': department_summary,
+             'detail_records': detail_records
+         }
+     
+     def _apply_excel_styles(self, file_path: str):
+         """应用Excel样式"""
+         wb = openpyxl.load_workbook(file_path)
+         
+         for sheet_name in wb.sheetnames:
+             ws = wb[sheet_name]
+             
+             # 应用标题行样式
+             for cell in ws[1]:
+                 cell.font = self.styles['header']['font']
+                 cell.fill = self.styles['header']['fill']
+                 cell.alignment = self.styles['header']['alignment']
+                 cell.border = self.styles['header']['border']
+             
+             # 应用数据行样式
+             for row in ws.iter_rows(min_row=2):
+                 for cell in row:
+                     cell.font = self.styles['data']['font']
+                     cell.alignment = self.styles['data']['alignment']
+                     cell.border = self.styles['data']['border']
+             
+             # 自动调整列宽
+             for column in ws.columns:
+                 max_length = 0
+                 column_letter = column[0].column_letter
+                 
+                 for cell in column:
+                     try:
+                         if len(str(cell.value)) > max_length:
+                             max_length = len(str(cell.value))
+                     except:
+                         pass
+                 
+                 adjusted_width = min(max_length + 2, 50)
+                 ws.column_dimensions[column_letter].width = adjusted_width
+         
+         wb.save(file_path)
+ ```
+ 
+ 这个模块详细设计文档提供了系统核心模块的完整设计规范，包括接口定义、类结构、数据流和实现细节，为后续的代码实现提供了详细的技术指导。 
```

---



---

您好！我来帮您分析当前的薪资变更项目。首先让我查看项目的基本结构和文档，了解项目的现状。

---



---

让我用正确的路径格式来访问：

---



---

让我检查当前工作目录和可访问的目录：

---



---

现在让我正确访问项目根目录：

---



---

让我尝试使用斜杠路径：

---

<details>
            <summary>Listed current directory • **9** results</summary>
        
| Name |
|-------|
| 📁 `design` |
| 📁 `temp` |
| 📁 `docs` |
| 📁 `.cursor` |
| 📁 `template` |
| 📁 `data` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📄 `.cursor.json` |

</details>

---

现在让我查看是否有README.md或其他项目文档：

---

<details>
            <summary>Searched codebase "README" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "CHANGELOG" • **0** results</summary>
        
No results found
</details>

---

让我先查看README.md文档：

---

Read file: design/README.md

---

现在让我查看更多的设计文档和项目结构：

---

<details>
            <summary>Listed directory docs • **4** results</summary>
        
| Name |
|-------|
| 📁 `prd` |
| 📁 `draft` |
| 📁 `task` |
| 📁 `problems` |

</details>

---

<details>
            <summary>Listed directory data • **2** results</summary>
        
| Name |
|-------|
| 📁 `异动人员` |
| 📁 `工资表` |

</details>

---

Could not resolve URI: Error: Unable to resolve nonexistent file 'c:\test\salary_changes\src'

---

看起来还没有创建src目录。让我查看一下模板目录和设计文档，更好地了解项目：

---

<details>
            <summary>Listed directory template • **3** results</summary>
        
| Name |
|-------|
| 📁 `关于发放教职工薪酬的请示-部务会` |
| 📁 `关于发放教职工薪酬的请示-OA流程` |
| 📁 `工资审批表` |

</details>

---

Could not find file 'docs/prd/PRD.md' in the workspace.

---





---

