<!-- Generated by SpecStory -->

# Modify prototype interface for payroll system (2025-06-11 17:38:14)

_**User**_

@ui_design_specification.md @interaction_flow_design.md @README.md @main_interface_prototype.html @月度工资异动处理系统详细设计方案.md 
需要在原型图中修改以下内容：
我希望对主界面中的列表进行一系列优化，具体如下：
1、鼠标悬浮在列表项上时，希望弹框显示该列表项对应的原始工资表对应人员的信息。
2、当前选中列表项是可以允许用户双击进行修改的，来自相应的可修改工资表。

请你先复述一下你的想法，待我确认后再进行具体实现。

---

_**Assistant**_



---

我来复述一下我的理解和实现思路：

## 需求理解

您希望对主界面中的列表进行两项优化：

### 1. 悬浮弹框显示人员信息
- **触发条件**：鼠标悬浮在列表项上时
- **显示内容**：该列表项对应的原始工资表中人员的详细信息
- **交互方式**：弹框形式展示，鼠标移开时隐藏

### 2. 双击编辑功能
- **触发条件**：用户双击列表项
- **功能说明**：允许修改该列表项的数据
- **数据来源**：可修改工资表中的对应数据
- **编辑方式**：通过弹出编辑对话框或行内编辑实现

## 我的实现思路

### 1. 悬浮弹框设计
```html
<!-- 悬浮信息弹框 -->
<div id="personInfoTooltip" class="person-info-tooltip">
  <div class="tooltip-header">人员详细信息</div>
  <div class="tooltip-content">
    <div class="info-row"><span class="label">姓名：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">工号：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">部门：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">职位：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">身份证号：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">入职日期：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">基本工资：</span><span class="value"></span></div>
    <div class="info-row"><span class="label">岗位津贴：</span><span class="value"></span></div>
  </div>
</div>
```

### 2. 双击编辑对话框设计
```html
<!-- 编辑对话框 -->
<div id="editPersonDialog" class="edit-dialog">
  <div class="dialog-header">
    <h3>编辑人员信息</h3>
    <button class="close-btn">&times;</button>
  </div>
  <div class="dialog-content">
    <form id="editPersonForm">
      <!-- 各种可编辑字段 -->
    </form>
  </div>
  <div class="dialog-footer">
    <button type="button" class="btn-cancel">取消</button>
    <button type="button" class="btn-save">保存</button>
  </div>
</div>
```

### 3. JavaScript交互逻辑
```javascript
// 悬浮显示人员信息
function showPersonTooltip(event, personData) {
  // 填充弹框数据并显示
}

// 双击编辑功能
function openEditDialog(personData) {
  // 打开编辑对话框并填充当前数据
}

// 保存编辑结果
function savePersonEdit(formData) {
  // 保存到可修改工资表并更新显示
}
```

### 4. 样式特点
- **悬浮弹框**：深色半透明背景，白色内容区，圆角阴影，跟随鼠标位置
- **编辑对话框**：模态对话框，居中显示，背景遮罩，表单布局
- **列表交互**：悬浮时高亮显示，双击时视觉反馈

## 技术实现要点

1. **数据关联**：需要建立列表项与原始工资表数据的映射关系
2. **位置计算**：弹框位置需要考虑边界溢出和鼠标跟随
3. **编辑验证**：双击编辑时需要数据验证和错误提示
4. **状态管理**：编辑后需要更新相关状态和显示

请确认我的理解是否正确，以及实现思路是否符合您的期望？如果没问题，我将开始具体实现这些功能。

---

_**User**_

请你开始修改原型图及相关文档

---

_**Assistant**_



---

好的，我现在开始修改原型图及相关文档。首先让我查看需要修改的文件内容，然后逐步实现新功能。

---



---

让我检查一下当前的文件结构，并使用正确的路径：

---



---

让我先查看当前工作目录的正确结构：

---



---



---

现在我明白了，我需要在正确的目录中工作。让我查看正确的目录结构：

---



---

<details>
            <summary>Listed current directory • **8** results</summary>
        
| Name |
|-------|
| 📁 `temp` |
| 📁 `docs` |
| 📁 `.cursor` |
| 📁 `template` |
| 📁 `data` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📄 `.cursor.json` |

</details>

---

<details>
            <summary>Listed directory docs/draft/design • **4** results</summary>
        
| Name |
|-------|
| 📄 `README.md` |
| 📄 `interaction_flow_design.md` |
| 📄 `ui_design_specification.md` |
| 📄 `main_interface_prototype.html` |

</details>

---

很好！现在我可以看到文件了。让我先读取这些文件的内容：

---

Read file: docs/draft/design/main_interface_prototype.html

---

Read file: docs/draft/design/ui_design_specification.md

---

Read file: docs/draft/design/interaction_flow_design.md

---

现在我需要查看HTML文件中列表部分的代码，以便理解当前的列表结构：

---

<details>
            <summary>Grep search for "table|tr|td|list" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L57 | `transition: background 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L69 | `transition: background 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L103 | `transition: background 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L169 | `transition: all 0.3s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L210 | `.control-panel {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L229 | `transition: all 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L235 | `transform: translateY(-1px);` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L247 | `transition: all 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L255 | `.control-actions {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L269 | `transition: all 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L274 | `transform: translateY(-1px);` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L313 | `transition: all 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L346 | `transform: translateY(-50%);` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L354 | `transition: color 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L370 | `transition: background 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L457 | `.data-table {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L466 | `.data-table th {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L476 | `.data-table td {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L482 | `.data-table tr:hover {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L596 | `transition: background 0.2s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L636 | `transition: background 0.3s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L652 | `transition: left 0.3s;` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L802 | `<div class="control-panel">` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L807 | `<div class="control-actions">` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L808 | `<button class="export-button" onclick="exportResults()">导出审批表</button>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L839 | `<div id="query-results-list">` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1077 | `dropdown.classList.toggle('show');` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1081 | `document.addEventListener('click', function(e) {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1086 | `dropdown.classList.remove('show');` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1093 | `document.getElementById('settings-dropdown').classList.remove('show');` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1160 | `<table class="data-table">` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1162 | `<tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1169 | `</tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1172 | `<tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1173 | `<td>1</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1174 | `<td>张三</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1175 | `<td>20240001</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1176 | `<td>起薪</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1177 | `<td><span class="status-badge status-success">匹配成功</span></td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1178 | `<td><button class="secondary-button" style="padding: 6px 12px; font-size: 12px;">查看详情</button></td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1179 | `</tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1180 | `<tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1181 | `<td>2</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1182 | `<td>李四</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1183 | `<td>20240002</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1184 | `<td>停薪-退休</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1185 | `<td><span class="status-badge status-warning">待确认</span></td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1186 | `<td><button class="secondary-button" style="padding: 6px 12px; font-size: 12px;">手动匹配</button></td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1187 | `</tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1188 | `<tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1189 | `<td>3</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1190 | `<td>王五</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1191 | `<td>--</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1192 | `<td>考核扣款</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1193 | `<td><span class="status-badge status-error">匹配失败</span></td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1194 | `<td><button class="secondary-button" style="padding: 6px 12px; font-size: 12px;">重新匹配</button></td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1195 | `</tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1197 | `</table>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1247 | `<table class="data-table">` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1249 | `<tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1259 | `</tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1263 | `<tr>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1264 | `<td>${index + 1}</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1265 | `<td>${person.name}</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1266 | `<td>${person.employeeId}</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1267 | `<td>${person.department}</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1268 | `<td>${person.position}</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1269 | `<td>${person.idCard}</td>` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/draft/design/main_interface_prototype.html` | L1270 | `<td>${person.hireDate}</td>` |

</details>

---

让我查看表格相关的具体代码：

---

Read file: docs/draft/design/main_interface_prototype.html

---

Read file: docs/draft/design/main_interface_prototype.html

---

现在我已经了解了当前的代码结构。接下来我要为表格行添加悬浮弹框和双击编辑功能。让我开始修改这些文件：

---

```diff
          .data-table tr:hover {
              background: #f8f9ff;
+             cursor: pointer;
          }
  
```

```diff
              font-weight: 500;
          }
+ 
+         /* 人员信息悬浮弹框样式 */
+         .person-info-tooltip {
+             position: absolute;
+             background: rgba(51, 51, 51, 0.95);
+             color: white;
+             border-radius: 8px;
+             padding: 16px;
+             box-shadow: 0 4px 20px rgba(0,0,0,0.3);
+             z-index: 1000;
+             min-width: 280px;
+             max-width: 350px;
+             display: none;
+             backdrop-filter: blur(10px);
+         }
+ 
+         .tooltip-header {
+             font-size: 14px;
+             font-weight: 500;
+             color: #4285F4;
+             margin-bottom: 12px;
+             padding-bottom: 8px;
+             border-bottom: 1px solid rgba(255,255,255,0.2);
+         }
+ 
+         .tooltip-content {
+             display: flex;
+             flex-direction: column;
+             gap: 8px;
+         }
+ 
+         .info-row {
+             display: flex;
+             align-items: center;
+             font-size: 13px;
+         }
+ 
+         .info-label {
+             color: #ccc;
+             min-width: 80px;
+             margin-right: 8px;
+         }
+ 
+         .info-value {
+             color: white;
+             font-weight: 500;
+         }
+ 
+         /* 编辑对话框样式 */
+         .edit-overlay {
+             position: fixed;
+             top: 0;
+             left: 0;
+             width: 100%;
+             height: 100%;
+             background: rgba(0,0,0,0.5);
+             display: none;
+             justify-content: center;
+             align-items: center;
+             z-index: 1500;
+         }
+ 
+         .edit-dialog {
+             background: white;
+             border-radius: 12px;
+             width: 600px;
+             max-height: 80vh;
+             box-shadow: 0 20px 40px rgba(0,0,0,0.3);
+             display: flex;
+             flex-direction: column;
+             overflow: hidden;
+         }
+ 
+         .dialog-header {
+             height: 60px;
+             background: #f8f9fa;
+             border-bottom: 1px solid #e0e0e0;
+             display: flex;
+             align-items: center;
+             justify-content: space-between;
+             padding: 0 24px;
+         }
+ 
+         .dialog-header h3 {
+             font-size: 18px;
+             font-weight: 500;
+             color: #333;
+         }
+ 
+         .dialog-close-btn {
+             background: none;
+             border: none;
+             font-size: 24px;
+             color: #666;
+             cursor: pointer;
+             padding: 8px;
+             border-radius: 6px;
+             transition: all 0.2s;
+             width: 40px;
+             height: 40px;
+             display: flex;
+             align-items: center;
+             justify-content: center;
+         }
+ 
+         .dialog-close-btn:hover {
+             background: #e0e0e0;
+             color: #333;
+         }
+ 
+         .dialog-content {
+             flex: 1;
+             padding: 24px;
+             overflow-y: auto;
+         }
+ 
+         .form-grid {
+             display: grid;
+             grid-template-columns: 1fr 1fr;
+             gap: 20px;
+         }
+ 
+         .form-group {
+             display: flex;
+             flex-direction: column;
+         }
+ 
+         .form-group.full-width {
+             grid-column: 1 / -1;
+         }
+ 
+         .form-label {
+             font-size: 14px;
+             font-weight: 500;
+             color: #333;
+             margin-bottom: 8px;
+         }
+ 
+         .form-input {
+             padding: 12px 16px;
+             border: 1px solid #ddd;
+             border-radius: 8px;
+             font-size: 14px;
+             transition: all 0.2s;
+         }
+ 
+         .form-input:focus {
+             outline: none;
+             border-color: #4285F4;
+             box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
+         }
+ 
+         .form-input:disabled {
+             background: #f5f5f5;
+             color: #999;
+             cursor: not-allowed;
+         }
+ 
+         .dialog-footer {
+             height: 70px;
+             background: #f8f9fa;
+             border-top: 1px solid #e0e0e0;
+             display: flex;
+             align-items: center;
+             justify-content: flex-end;
+             gap: 12px;
+             padding: 0 24px;
+         }
+ 
+         .btn-cancel {
+             background: white;
+             color: #666;
+             border: 1px solid #ddd;
+             padding: 10px 20px;
+             border-radius: 6px;
+             font-size: 14px;
+             cursor: pointer;
+             transition: all 0.2s;
+         }
+ 
+         .btn-cancel:hover {
+             background: #f5f5f5;
+             border-color: #ccc;
+         }
+ 
+         .btn-save {
+             background: #4285F4;
+             color: white;
+             border: none;
+             padding: 10px 20px;
+             border-radius: 6px;
+             font-size: 14px;
+             cursor: pointer;
+             transition: all 0.2s;
+         }
+ 
+         .btn-save:hover {
+             background: #3367D6;
+         }
+ 
+         .btn-save:disabled {
+             background: #ccc;
+             cursor: not-allowed;
+         }
      </style>
  </head>
```

---

现在我需要添加HTML结构，包括悬浮弹框和编辑对话框：

---

Read file: docs/draft/design/main_interface_prototype.html

---

Read file: docs/draft/design/main_interface_prototype.html

---

现在我需要在body的末尾添加悬浮弹框和编辑对话框的HTML结构：

---

Read file: docs/draft/design/main_interface_prototype.html

---

现在我需要在HTML的body末尾添加悬浮弹框和编辑对话框的结构，并修改表格的事件处理：

---

```diff
          </div>
      </div>
+ 
+     <!-- 人员信息悬浮弹框 -->
+     <div id="personInfoTooltip" class="person-info-tooltip">
+         <div class="tooltip-header">人员详细信息</div>
+         <div class="tooltip-content">
+             <div class="info-row">
+                 <span class="info-label">姓名：</span>
+                 <span class="info-value" id="tooltip-name"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">工号：</span>
+                 <span class="info-value" id="tooltip-employeeId"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">部门：</span>
+                 <span class="info-value" id="tooltip-department"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">职位：</span>
+                 <span class="info-value" id="tooltip-position"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">身份证号：</span>
+                 <span class="info-value" id="tooltip-idCard"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">入职日期：</span>
+                 <span class="info-value" id="tooltip-hireDate"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">基本工资：</span>
+                 <span class="info-value" id="tooltip-basicSalary"></span>
+             </div>
+             <div class="info-row">
+                 <span class="info-label">岗位津贴：</span>
+                 <span class="info-value" id="tooltip-allowance"></span>
+             </div>
+         </div>
+     </div>
+ 
+     <!-- 编辑对话框 -->
+     <div id="editPersonOverlay" class="edit-overlay">
+         <div class="edit-dialog">
+             <div class="dialog-header">
+                 <h3>编辑人员信息</h3>
+                 <button class="dialog-close-btn" onclick="closeEditDialog()">&times;</button>
+             </div>
+             <div class="dialog-content">
+                 <form id="editPersonForm">
+                     <div class="form-grid">
+                         <div class="form-group">
+                             <label class="form-label">姓名</label>
+                             <input type="text" class="form-input" id="edit-name" disabled>
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">工号</label>
+                             <input type="text" class="form-input" id="edit-employeeId" disabled>
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">部门</label>
+                             <input type="text" class="form-input" id="edit-department">
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">职位</label>
+                             <input type="text" class="form-input" id="edit-position">
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">基本工资</label>
+                             <input type="number" class="form-input" id="edit-basicSalary" step="0.01">
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">岗位津贴</label>
+                             <input type="number" class="form-input" id="edit-allowance" step="0.01">
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">绩效工资</label>
+                             <input type="number" class="form-input" id="edit-performance" step="0.01">
+                         </div>
+                         <div class="form-group">
+                             <label class="form-label">其他补贴</label>
+                             <input type="number" class="form-input" id="edit-subsidy" step="0.01">
+                         </div>
+                         <div class="form-group full-width">
+                             <label class="form-label">身份证号</label>
+                             <input type="text" class="form-input" id="edit-idCard" disabled>
+                         </div>
+                         <div class="form-group full-width">
+                             <label class="form-label">备注</label>
+                             <textarea class="form-input" id="edit-remarks" rows="3" placeholder="请输入备注信息"></textarea>
+                         </div>
+                     </div>
+                 </form>
+             </div>
+             <div class="dialog-footer">
+                 <button type="button" class="btn-cancel" onclick="closeEditDialog()">取消</button>
+                 <button type="button" class="btn-save" onclick="savePersonEdit()">保存</button>
+             </div>
+         </div>
+     </div>
  
      <script>
```

---

现在我需要修改JavaScript部分，添加悬浮弹框和双击编辑的功能。首先修改表格生成函数，为表格行添加事件监听：

---

```diff
                      </thead>
                      <tbody>
-                         <tr>
+                         <tr data-person-id="1" onmouseover="showPersonTooltip(event, this)" onmouseout="hidePersonTooltip()" ondblclick="openEditDialog(this)">
                              <td>1</td>
                              <td>张三</td>
```

```diff
                              <td><button class="secondary-button" style="padding: 6px 12px; font-size: 12px;">查看详情</button></td>
                          </tr>
-                         <tr>
+                         <tr data-person-id="2" onmouseover="showPersonTooltip(event, this)" onmouseout="hidePersonTooltip()" ondblclick="openEditDialog(this)">
                              <td>2</td>
                              <td>李四</td>
```

```diff
                              <td><button class="secondary-button" style="padding: 6px 12px; font-size: 12px;">手动匹配</button></td>
                          </tr>
-                         <tr>
+                         <tr data-person-id="3" onmouseover="showPersonTooltip(event, this)" onmouseout="hidePersonTooltip()" ondblclick="openEditDialog(this)">
                              <td>3</td>
                              <td>王五</td>
```

---

现在我需要同样修改showQueryView函数，为查询结果表格也添加事件监听：

---

```diff
                      <tbody>
                          ${results.map((person, index) => `
-                             <tr>
+                             <tr data-person-id="${index + 1}" onmouseover="showPersonTooltip(event, this)" onmouseout="hidePersonTooltip()" ondblclick="openEditDialog(this)">
                                  <td>${index + 1}</td>
                                  <td>${person.name}</td>
```

---

现在我需要添加JavaScript函数来处理悬浮弹框和双击编辑功能：

---

```diff
              }
          }
+ 
+         // 悬浮弹框相关功能
+         function showPersonTooltip(event, row) {
+             const tooltip = document.getElementById('personInfoTooltip');
+             const personId = row.getAttribute('data-person-id');
+             
+             // 根据personId获取人员详细信息（这里用模拟数据）
+             const personData = getPersonDetailById(personId);
+             
+             // 填充弹框数据
+             document.getElementById('tooltip-name').textContent = personData.name;
+             document.getElementById('tooltip-employeeId').textContent = personData.employeeId;
+             document.getElementById('tooltip-department').textContent = personData.department;
+             document.getElementById('tooltip-position').textContent = personData.position;
+             document.getElementById('tooltip-idCard').textContent = personData.idCard;
+             document.getElementById('tooltip-hireDate').textContent = personData.hireDate;
+             document.getElementById('tooltip-basicSalary').textContent = personData.basicSalary;
+             document.getElementById('tooltip-allowance').textContent = personData.allowance;
+             
+             // 计算并设置弹框位置
+             const rect = row.getBoundingClientRect();
+             const tooltipRect = tooltip.getBoundingClientRect();
+             
+             let left = event.pageX + 15;
+             let top = event.pageY - 10;
+             
+             // 防止弹框超出屏幕边界
+             if (left + 350 > window.innerWidth) {
+                 left = event.pageX - 365;
+             }
+             if (top + 200 > window.innerHeight) {
+                 top = event.pageY - 210;
+             }
+             
+             tooltip.style.left = left + 'px';
+             tooltip.style.top = top + 'px';
+             tooltip.style.display = 'block';
+         }
+ 
+         function hidePersonTooltip() {
+             const tooltip = document.getElementById('personInfoTooltip');
+             tooltip.style.display = 'none';
+         }
+ 
+         // 双击编辑功能
+         function openEditDialog(row) {
+             const personId = row.getAttribute('data-person-id');
+             const personData = getPersonDetailById(personId);
+             
+             // 填充编辑表单
+             document.getElementById('edit-name').value = personData.name;
+             document.getElementById('edit-employeeId').value = personData.employeeId;
+             document.getElementById('edit-department').value = personData.department;
+             document.getElementById('edit-position').value = personData.position;
+             document.getElementById('edit-basicSalary').value = personData.basicSalary.replace('¥', '').replace(',', '');
+             document.getElementById('edit-allowance').value = personData.allowance.replace('¥', '').replace(',', '');
+             document.getElementById('edit-performance').value = personData.performance || '0';
+             document.getElementById('edit-subsidy').value = personData.subsidy || '0';
+             document.getElementById('edit-idCard').value = personData.idCard;
+             document.getElementById('edit-remarks').value = personData.remarks || '';
+             
+             // 显示编辑对话框
+             document.getElementById('editPersonOverlay').style.display = 'flex';
+             
+             // 存储当前编辑的人员ID
+             window.currentEditPersonId = personId;
+         }
+ 
+         function closeEditDialog() {
+             document.getElementById('editPersonOverlay').style.display = 'none';
+             window.currentEditPersonId = null;
+         }
+ 
+         function savePersonEdit() {
+             // 获取表单数据
+             const formData = {
+                 name: document.getElementById('edit-name').value,
+                 employeeId: document.getElementById('edit-employeeId').value,
+                 department: document.getElementById('edit-department').value,
+                 position: document.getElementById('edit-position').value,
+                 basicSalary: document.getElementById('edit-basicSalary').value,
+                 allowance: document.getElementById('edit-allowance').value,
+                 performance: document.getElementById('edit-performance').value,
+                 subsidy: document.getElementById('edit-subsidy').value,
+                 idCard: document.getElementById('edit-idCard').value,
+                 remarks: document.getElementById('edit-remarks').value
+             };
+             
+             // 验证必填字段
+             if (!formData.department || !formData.position) {
+                 alert('请填写部门和职位信息！');
+                 return;
+             }
+             
+             // 验证数字字段
+             if (isNaN(formData.basicSalary) || isNaN(formData.allowance) || 
+                 isNaN(formData.performance) || isNaN(formData.subsidy)) {
+                 alert('工资字段请输入有效数字！');
+                 return;
+             }
+             
+             // 这里应该调用API保存数据，现在用模拟方式
+             console.log('保存人员信息：', formData);
+             alert(`已保存 ${formData.name} 的信息修改！`);
+             
+             // 关闭对话框
+             closeEditDialog();
+             
+             // 可以在这里更新表格显示的数据
+             updatePersonDataInTable(window.currentEditPersonId, formData);
+         }
+ 
+         // 模拟获取人员详细信息
+         function getPersonDetailById(personId) {
+             // 这里应该从实际数据源获取，现在用模拟数据
+             const mockPersonData = {
+                 '1': {
+                     name: '张三',
+                     employeeId: '20240001',
+                     department: '财务处',
+                     position: '会计师',
+                     idCard: '320101199001010001',
+                     hireDate: '2020-03-15',
+                     basicSalary: '¥8,500',
+                     allowance: '¥2,000',
+                     performance: '3000',
+                     subsidy: '500',
+                     remarks: '优秀员工'
+                 },
+                 '2': {
+                     name: '李四',
+                     employeeId: '20240002',
+                     department: '人事处',
+                     position: '人事专员',
+                     idCard: '320101199002020002',
+                     hireDate: '2021-06-20',
+                     basicSalary: '¥7,200',
+                     allowance: '¥1,500',
+                     performance: '2500',
+                     subsidy: '300',
+                     remarks: '即将退休'
+                 },
+                 '3': {
+                     name: '王五',
+                     employeeId: '20240003',
+                     department: '行政处',
+                     position: '行政助理',
+                     idCard: '320101199003030003',
+                     hireDate: '2022-01-10',
+                     basicSalary: '¥6,800',
+                     allowance: '¥1,200',
+                     performance: '2000',
+                     subsidy: '200',
+                     remarks: '新入职员工'
+                 }
+             };
+             
+             return mockPersonData[personId] || {
+                 name: '未知',
+                 employeeId: '--',
+                 department: '--',
+                 position: '--',
+                 idCard: '--',
+                 hireDate: '--',
+                 basicSalary: '¥0',
+                 allowance: '¥0',
+                 performance: '0',
+                 subsidy: '0',
+                 remarks: ''
+             };
+         }
+ 
+         // 更新表格中的人员数据显示
+         function updatePersonDataInTable(personId, formData) {
+             // 这里可以实现更新表格显示的逻辑
+             console.log(`更新表格中ID为${personId}的人员数据：`, formData);
+         }
+ 
+         // 点击遮罩层关闭编辑对话框
+         document.getElementById('editPersonOverlay').addEventListener('click', function(e) {
+             if (e.target === this) {
+                 closeEditDialog();
+             }
+         });
      </script>
  </body>
```

---

现在我需要更新UI设计规范文档，添加新功能的设计规范：

---



---

让我继续添加悬浮弹框和编辑对话框的设计规范：

---

Read file: docs/draft/design/ui_design_specification.md

---

```diff
  .data-table tr:hover {
      background: #F8F9FF;
+     cursor: pointer;
  }
  ```
  
- ### 3.7 设置窗口组件
+ #### 表格交互增强（新增）
+ **悬浮提示功能：**
+ - **触发条件**：鼠标悬浮在表格行上
+ - **显示内容**：人员详细信息（姓名、工号、部门、职位、身份证号、入职日期、基本工资、岗位津贴）
+ - **显示形式**：深色半透明悬浮弹框，带模糊背景效果
+ - **位置策略**：跟随鼠标位置，智能避免边界溢出
+ 
+ **双击编辑功能：**
+ - **触发条件**：双击表格行
+ - **编辑内容**：可修改工资表中的相关字段
+ - **显示形式**：模态对话框，表单布局
+ - **数据验证**：必填字段检查、数字格式验证
+ 
+ ### 3.7 悬浮弹框组件（新增）
+ 
+ #### 弹框样式规范
+ ```css
+ .person-info-tooltip {
+     position: absolute;
+     background: rgba(51, 51, 51, 0.95);
+     color: white;
+     border-radius: 8px;
+     padding: 16px;
+     box-shadow: 0 4px 20px rgba(0,0,0,0.3);
+     z-index: 1000;
+     min-width: 280px;
+     max-width: 350px;
+     backdrop-filter: blur(10px);
+ }
+ ```
+ 
+ #### 设计特点
+ - **视觉风格**：深色半透明背景(rgba(51, 51, 51, 0.95))，现代化设计
+ - **模糊效果**：backdrop-filter: blur(10px) 增强视觉层次
+ - **尺寸控制**：最小宽度280px，最大宽度350px，内容自适应
+ - **层级管理**：z-index: 1000，确保在其他元素之上显示
+ - **圆角设计**：8px圆角，与整体设计风格保持一致
+ 
+ #### 信息布局
+ ```css
+ .tooltip-header {
+     font-size: 14px;
+     font-weight: 500;
+     color: #4285F4;           /* Google Blue强调色 */
+     margin-bottom: 12px;
+     padding-bottom: 8px;
+     border-bottom: 1px solid rgba(255,255,255,0.2);
+ }
+ 
+ .tooltip-content {
+     display: flex;
+     flex-direction: column;
+     gap: 8px;
+ }
+ 
+ .info-row {
+     display: flex;
+     align-items: center;
+     font-size: 13px;
+ }
+ 
+ .info-label {
+     color: #ccc;              /* 标签文字为浅灰色 */
+     min-width: 80px;          /* 标签宽度固定对齐 */
+     margin-right: 8px;
+ }
+ 
+ .info-value {
+     color: white;             /* 值文字为白色突出显示 */
+     font-weight: 500;
+ }
+ ```
+ 
+ #### 位置计算策略
+ - **基础位置**：鼠标位置右下方（+15px, -10px偏移）
+ - **边界检测**：检测右边界和下边界溢出
+ - **智能调整**：溢出时自动调整到鼠标左上方
+ - **防抖动**：位置计算考虑弹框实际尺寸
+ 
+ ### 3.8 编辑对话框组件（新增）
+ 
+ #### 对话框容器样式
+ ```css
+ .edit-overlay {
+     position: fixed;
+     top: 0;
+     left: 0;
+     width: 100%;
+     height: 100%;
+     background: rgba(0,0,0,0.5);
+     z-index: 1500;
+ }
+ 
+ .edit-dialog {
+     background: white;
+     border-radius: 12px;
+     width: 600px;
+     max-height: 80vh;
+     box-shadow: 0 20px 40px rgba(0,0,0,0.3);
+     overflow: hidden;
+ }
+ ```
+ 
+ #### 设计规范要点
+ - **层级管理**：z-index: 1500，高于悬浮弹框
+ - **遮罩背景**：半透明黑色遮罩，突出对话框主体
+ - **尺寸控制**：固定宽度600px，最大高度80vh，确保不超出屏幕
+ - **阴影效果**：20px模糊40px扩散的深色阴影，增强层次感
+ - **圆角设计**：12px圆角，比弹框更大，体现重要性差异
+ 
+ #### 表单布局规范
+ ```css
+ .form-grid {
+     display: grid;
+     grid-template-columns: 1fr 1fr;
+     gap: 20px;
+ }
+ 
+ .form-group {
+     display: flex;
+     flex-direction: column;
+ }
+ 
+ .form-group.full-width {
+     grid-column: 1 / -1;      /* 跨两列显示 */
+ }
+ 
+ .form-input {
+     padding: 12px 16px;
+     border: 1px solid #ddd;
+     border-radius: 8px;
+     font-size: 14px;
+     transition: all 0.2s;
+ }
+ 
+ .form-input:focus {
+     border-color: #4285F4;
+     box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
+ }
+ 
+ .form-input:disabled {
+     background: #f5f5f5;
+     color: #999;
+     cursor: not-allowed;
+ }
+ ```
+ 
+ #### 表单字段规划
+ | 字段名称 | 输入类型 | 是否必填 | 是否可编辑 | 布局 |
+ |----------|----------|----------|------------|------|
+ | 姓名 | text | 是 | 否 | 左列 |
+ | 工号 | text | 是 | 否 | 右列 |
+ | 部门 | text | 是 | 是 | 左列 |
+ | 职位 | text | 是 | 是 | 右列 |
+ | 基本工资 | number | 是 | 是 | 左列 |
+ | 岗位津贴 | number | 否 | 是 | 右列 |
+ | 绩效工资 | number | 否 | 是 | 左列 |
+ | 其他补贴 | number | 否 | 是 | 右列 |
+ | 身份证号 | text | 是 | 否 | 跨列 |
+ | 备注 | textarea | 否 | 是 | 跨列 |
+ 
+ #### 对话框头部和底部
+ ```css
+ .dialog-header {
+     height: 60px;
+     background: #f8f9fa;
+     border-bottom: 1px solid #e0e0e0;
+     padding: 0 24px;
+ }
+ 
+ .dialog-footer {
+     height: 70px;
+     background: #f8f9fa;
+     border-top: 1px solid #e0e0e0;
+     padding: 0 24px;
+ }
+ 
+ .btn-save {
+     background: #4285F4;
+     color: white;
+     padding: 10px 20px;
+     border-radius: 6px;
+     font-size: 14px;
+ }
+ 
+ .btn-cancel {
+     background: white;
+     color: #666;
+     border: 1px solid #ddd;
+     padding: 10px 20px;
+     border-radius: 6px;
+     font-size: 14px;
+ }
+ ```
+ 
+ #### 交互行为规范
+ **打开对话框：**
+ - 触发方式：双击表格行
+ - 动画效果：渐显过渡，持续时间200ms
+ - 数据填充：自动填充当前行的人员信息
+ 
+ **关闭对话框：**
+ - 触发方式：点击关闭按钮、取消按钮、点击遮罩层
+ - 确认逻辑：如有未保存更改，弹出确认对话框
+ - 动画效果：渐隐过渡，持续时间200ms
+ 
+ **数据验证：**
+ - 实时验证：输入框失焦时验证格式
+ - 提交验证：保存前验证所有必填字段
+ - 错误提示：在对应字段下方显示具体错误信息
+ 
+ **保存逻辑：**
+ - 验证通过：显示保存成功提示，关闭对话框
+ - 验证失败：显示错误信息，保持对话框打开
+ - 数据更新：保存后更新表格中对应行的显示内容
+ 
+ ### 3.9 设置窗口组件
  
  #### 设置窗口容器
```

---

现在我需要更新交互流程设计文档，添加新功能的交互流程：

---

```diff
  - **状态信息固定**：状态栏位置固定，用户无需在不同位置查找状态信息
  
- ### 2.5 设置功能交互设计（保持不变）
+ ### 2.5 表格交互增强设计（新增）
+ 
+ #### 2.5.1 悬浮弹框交互流程
+ ```mermaid
+ sequenceDiagram
+     participant U as 用户
+     participant T as 表格行
+     participant P as 弹框组件
+     participant D as 数据源
+     
+     U->>T: 鼠标悬浮
+     T->>D: 获取人员详细信息
+     D-->>T: 返回详细数据
+     T->>P: 填充弹框内容
+     P->>P: 计算显示位置
+     P->>P: 检查边界溢出
+     P->>U: 显示弹框
+     
+     U->>T: 鼠标移出
+     T->>P: 隐藏弹框
+     P->>U: 弹框消失
+ ```
+ 
+ **交互细节：**
+ 
+ 1. **悬浮触发**
+    ```
+    鼠标进入表格行 → 获取人员ID → 查询详细信息 → 填充弹框数据 → 计算位置 → 显示弹框
+    ```
+ 
+ 2. **位置计算**
+    ```
+    基础位置：event.pageX + 15, event.pageY - 10
+    
+    边界检测：
+    - 右边界：if (left + 350 > window.innerWidth) left = event.pageX - 365
+    - 下边界：if (top + 200 > window.innerHeight) top = event.pageY - 210
+    ```
+ 
+ 3. **数据展示**
+    ```
+    显示字段：
+    - 基础信息：姓名、工号、部门、职位
+    - 身份信息：身份证号、入职日期
+    - 薪资信息：基本工资、岗位津贴
+    ```
+ 
+ #### 2.5.2 双击编辑交互流程
+ ```mermaid
+ stateDiagram-v2
+     [*] --> 表格显示
+     表格显示 --> 双击检测: 用户双击行
+     双击检测 --> 获取数据: 获取人员ID
+     获取数据 --> 填充表单: 查询详细信息
+     填充表单 --> 显示对话框: 打开编辑界面
+     
+     显示对话框 --> 数据编辑: 用户修改字段
+     数据编辑 --> 实时验证: 字段失焦
+     实时验证 --> 数据编辑: 验证通过
+     实时验证 --> 错误提示: 验证失败
+     错误提示 --> 数据编辑: 用户修正
+     
+     数据编辑 --> 保存验证: 点击保存
+     保存验证 --> 提交保存: 验证通过
+     保存验证 --> 错误提示: 验证失败
+     
+     提交保存 --> 更新表格: 保存成功
+     更新表格 --> 关闭对话框: 显示成功提示
+     关闭对话框 --> [*]
+     
+     显示对话框 --> 取消编辑: 点击取消/关闭
+     取消编辑 --> 确认对话框: 有未保存更改
+     确认对话框 --> 关闭对话框: 确认放弃
+     确认对话框 --> 显示对话框: 继续编辑
+     取消编辑 --> 关闭对话框: 无更改
+ ```
+ 
+ **关键交互节点：**
+ 
+ 1. **双击响应**
+    ```javascript
+    row.ondblclick = function(event) {
+        event.preventDefault();
+        const personId = this.getAttribute('data-person-id');
+        openEditDialog(this);
+    }
+    ```
+ 
+ 2. **表单数据验证**
+    ```
+    必填字段验证：
+    - 部门：不能为空
+    - 职位：不能为空
+    
+    数字字段验证：
+    - 基本工资、岗位津贴、绩效工资、其他补贴：必须为有效数字
+    - 最小值：0，最大值：999999.99
+    ```
+ 
+ 3. **保存确认流程**
+    ```
+    点击保存 → 验证所有字段 → 显示确认信息 → 提交数据 → 更新界面 → 关闭对话框
+    ```
+ 
+ #### 2.5.3 数据关联和同步
+ **数据源管理：**
+ - **原始工资表**：提供人员详细信息，用于悬浮弹框显示
+ - **可修改工资表**：双击编辑时的数据来源和保存目标
+ - **界面状态**：实时同步编辑结果到表格显示
+ 
+ **数据流向：**
+ ```
+ 原始工资表 → 悬浮弹框显示
+     ↓
+ 可修改工资表 → 双击编辑表单 → 保存修改 → 更新表格显示
+     ↓
+ 界面状态同步 → 相关计算更新 → 状态栏信息更新
+ ```
+ 
+ ### 2.6 错误处理和用户反馈
+ 
+ #### 2.6.1 悬浮弹框错误处理
+ **数据加载失败：**
+ - 显示："数据加载中..."
+ - 超时后显示："无法获取详细信息"
+ - 提供重试机制
+ 
+ **边界计算异常：**
+ - 默认显示在鼠标右下方
+ - 位置异常时居中显示
+ - 保证弹框始终在可视区域内
+ 
+ #### 2.6.2 编辑对话框错误处理
+ **表单验证错误：**
+ - 实时验证：字段边框变红，下方显示错误信息
+ - 提交验证：弹出错误汇总对话框
+ - 自动聚焦到第一个错误字段
+ 
+ **数据保存失败：**
+ - 显示具体错误信息
+ - 保持对话框打开状态
+ - 提供重试选项
+ 
+ **网络连接问题：**
+ - 显示网络错误提示
+ - 提供离线编辑模式
+ - 恢复连接后自动同步
+ 
+ ### 2.7 无障碍访问和键盘支持
+ 
+ #### 2.7.1 键盘导航支持
+ **表格导航：**
+ - Tab键：在表格行间导航
+ - Enter键：打开编辑对话框（等同于双击）
+ - Space键：选中当前行
+ 
+ **对话框导航：**
+ - Tab键：在表单字段间切换
+ - Shift+Tab：反向切换
+ - Enter键：提交表单（聚焦在保存按钮时）
+ - Esc键：关闭对话框
+ 
+ #### 2.7.2 屏幕阅读器支持
+ **语义化标签：**
+ - 表格使用完整的table、thead、tbody结构
+ - 表单使用label正确关联输入框
+ - 对话框使用role="dialog"属性
+ 
+ **ARIA属性：**
+ - aria-label：为按钮和输入框提供描述
+ - aria-describedby：关联错误信息
+ - aria-live：动态内容更新通知
+ 
+ #### 2.7.3 高对比度和缩放支持
+ **高对比度模式：**
+ - 确保文字和背景对比度符合WCAG 2.1 AA标准
+ - 弹框和对话框在高对比度模式下清晰可见
+ 
+ **界面缩放：**
+ - 支持浏览器200%缩放
+ - 弹框位置在缩放状态下正确计算
+ - 对话框内容在缩放状态下不出现横向滚动条
+ 
+ ### 2.8 设置功能交互设计（保持不变）
  
- #### 2.5.1 设置菜单交互流程
+ #### 2.8.1 设置菜单交互流程
  ```mermaid
  flowchart LR
```

```diff
  ```
  
- #### 2.5.2 下拉菜单交互设计
+ #### 2.8.2 下拉菜单交互设计
  **菜单结构：**
  ```
```

```diff
  - **外部关闭**：点击其他区域时下拉菜单关闭
  
- #### 2.5.3 独立设置窗口设计
+ #### 2.8.3 独立设置窗口设计
  
  **窗口特点：**
```

```diff
     ```
  
- #### 2.5.4 设置窗口交互逻辑
+ #### 2.8.4 设置窗口交互逻辑
  1. **开关切换**：
     - 点击开关或标签文字都可切换状态
```

```diff
     ```
  
- #### 2.5.5 设置窗口关闭方式
+ #### 2.8.5 设置窗口关闭方式
  - **点击"×"按钮**：等同于取消操作
  - **按Esc键**：快速关闭，等同于取消
  - **点击遮罩层**：点击窗口外部区域关闭
  - **保存后自动关闭**：保存成功后自动关闭窗口
  
- ### 2.6 操作控制区域交互（按钮优化）
+ ### 2.9 操作控制区域交互（按钮优化）
  
- #### 2.6.1 操作控制区域布局（更新）
+ #### 2.9.1 操作控制区域布局（更新）
  ```
  ┌─────────────────────────────────────────────────────────────────────┐
```

```diff
  - **空间合理利用**：三个导出按钮平均分布
  
- #### 2.6.2 按钮名称优化效果（更新）
+ #### 2.9.2 按钮名称优化效果（更新）
  **导出功能按钮对比：**
  | 功能 | 原名称 | 新名称 | 优化效果 |
```

```diff
  | 部务会导出 | (新增) | 导出请示（部务会） | 新增独立导出功能 |
  
- #### 2.6.3 操作流程简化
+ #### 2.9.3 操作流程简化
  ```
  数据加载 → 验证（可选） → 异动处理 → 查看结果 → 导出报表
```

```diff
  - **模态提示**：完成时弹出完成提示
  
- ### 2.7 人员匹配界面交互（保持不变）
+ ### 2.10 人员匹配界面交互（保持不变）
  
- #### 2.7.1 自动匹配过程
+ #### 2.10.1 自动匹配过程
  ```mermaid
  flowchart LR
```

```diff
  ```
  
- #### 2.7.2 手动匹配界面（保持不变）
+ #### 2.10.2 手动匹配界面（保持不变）
  **界面布局：**
  ```
```

```diff
  ```
  
- ### 2.8 异动计算和结果预览（简化后）
+ ### 2.11 异动计算和结果预览（简化后）
  
- #### 2.8.1 计算过程界面
+ #### 2.11.1 计算过程界面
  ```
  初始化计算引擎 → 应用设置规则 → 逐人计算 → 结果验证 → 生成报告
```

```diff
  - **完成提示**：处理完成后弹出模态提示
  
- #### 2.8.2 结果预览界面设计（空间优化）
+ #### 2.11.2 结果预览界面设计（空间优化）
  
  **标签页切换（带查询功能）：**
```

```diff
  - **操作更便捷**：有足够空间放置操作按钮和工具栏
  
- #### 2.8.3 导出操作集成
+ #### 2.11.3 导出操作集成
  **一体化导出流程：**
  ```
```

```diff
  - **功能集中**：所有主要操作都在操作控制区域
  
- ### 2.9 报表生成和结果输出
+ ### 2.12 报表生成和结果输出
  
- #### 2.9.1 导出操作简化
+ #### 2.12.1 导出操作简化
  ```
  ┌─────────────────────────────────────────┐
```

---

现在让我更新README文档，添加新功能的说明：

---

```diff
  - **查询状态**：`系统就绪 | 🔍 查询结果（共找到 X 条记录）`
  
- ### 3. 顶部菜单体系（功能扩充）
+ ### 3. 表格交互增强系统（新增 - v4.0）
+ 
+ #### 3.1 悬浮弹框详情展示
+ **功能描述：**
+ - **触发方式**：鼠标悬浮在表格行上
+ - **显示内容**：完整的人员详细信息，包含原始工资表中的所有相关数据
+ - **视觉设计**：深色半透明弹框，支持模糊背景效果
+ - **智能定位**：自动避免屏幕边界溢出，确保完整显示
+ 
+ **详细信息字段：**
+ - **基础信息**：姓名、工号、部门、职位
+ - **身份信息**：身份证号、入职日期
+ - **薪资信息**：基本工资、岗位津贴
+ - **扩展信息**：绩效工资、其他补贴、员工状态
+ 
+ **技术特性：**
+ - **实时加载**：悬浮时即时从数据源获取最新信息
+ - **位置算法**：基于鼠标位置和弹框尺寸的智能定位
+ - **性能优化**：防抖机制避免频繁触发
+ - **边界检测**：自动调整位置确保在可视区域内完整显示
+ 
+ #### 3.2 双击编辑工资数据
+ **功能描述：**
+ - **触发方式**：双击表格行开启编辑模式
+ - **编辑范围**：可修改工资表中的相关字段
+ - **表单设计**：模态对话框形式，双列网格布局
+ - **数据验证**：实时验证和提交时验证双重保障
+ 
+ **可编辑字段规划：**
+ | 字段类别 | 字段名称 | 编辑权限 | 验证规则 |
+ |----------|----------|----------|----------|
+ | 基础信息 | 姓名 | 只读 | - |
+ | 基础信息 | 工号 | 只读 | - |
+ | 基础信息 | 身份证号 | 只读 | - |
+ | 组织信息 | 部门 | 可编辑 | 必填 |
+ | 组织信息 | 职位 | 可编辑 | 必填 |
+ | 薪资信息 | 基本工资 | 可编辑 | 数字，≥0 |
+ | 薪资信息 | 岗位津贴 | 可编辑 | 数字，≥0 |
+ | 薪资信息 | 绩效工资 | 可编辑 | 数字，≥0 |
+ | 薪资信息 | 其他补贴 | 可编辑 | 数字，≥0 |
+ | 附加信息 | 备注 | 可编辑 | 可选，500字以内 |
+ 
+ **交互流程：**
+ ```
+ 双击表格行 → 加载人员数据 → 打开编辑对话框 → 用户修改字段 → 实时验证 → 提交保存 → 更新表格显示 → 关闭对话框
+ ```
+ 
+ **数据同步机制：**
+ - **数据源**：编辑时从可修改工资表获取数据
+ - **保存目标**：修改结果保存回可修改工资表
+ - **界面更新**：保存成功后实时更新表格显示
+ - **状态同步**：触发相关计算和状态栏信息更新
+ 
+ #### 3.3 交互体验优化
+ **用户体验改进：**
+ - **视觉反馈**：悬浮时行背景高亮，双击时提供即时反馈
+ - **操作提示**：鼠标悬浮显示详情，双击编辑操作直观易懂
+ - **无障碍支持**：键盘导航、屏幕阅读器支持、高对比度模式
+ - **响应式设计**：弹框和对话框在不同屏幕尺寸下正确显示
+ 
+ **错误处理机制：**
+ - **数据加载失败**：显示友好的错误提示和重试选项
+ - **验证错误**：实时显示错误信息，引导用户正确输入
+ - **网络异常**：提供离线编辑模式，恢复连接后自动同步
+ - **权限不足**：清晰提示用户权限限制，避免混淆
+ 
+ ### 4. 顶部菜单体系（功能扩充）
  ```
  菜单栏结构：
```

```diff
  - **退出图标**：× 简洁的叉叉图标，支持快捷退出
  
- ### 4. 操作控制优化（按钮功能细化）
+ ### 5. 操作控制优化（按钮功能细化）
  ```
  操作控制区域：
```

```diff
  | 部务会导出 | (新增) | 导出请示（部务会） | 新增独立导出功能 |
  
- ### 5. 设置功能体系（保持分类）
+ ### 6. 设置功能体系（保持分类）
  ```
  设置菜单 ▼
```

```diff
  ```
  
- ### 6. 数据处理引擎
+ ### 7. 数据处理引擎
  - **智能人员匹配**：工号 → 身份证 → 姓名 三级匹配算法
  - **多类型异动处理**：起薪、停薪、扣款、调整等9种异动类型
  - **实时预览展示**：匹配结果、计算过程、异动汇总、处理日志四大视图
  - **错误处理机制**：完善的异常检测和用户提示
  
- ### 7. 报表生成系统
+ ### 8. 报表生成系统
  - **一键导出功能**：集成在操作控制区，操作连贯
  - **多格式输出**：Excel汇总表、Word请示文档
```

---





---

