<!-- Generated by SpecStory -->

# Improving salary data display methods (2025-06-12 08:21:15)

_**User**_

本来采用列表展示可修改的某人工资表数据，而采用悬浮弹框展示对应人员原始工资表数据，这样可以起到对比参考的效果。
但是经过多次测试，发现悬浮弹框的显示效果不太理想。并且，也不知道这样的效果在tkinter(pyqt5)中是否可以实现同样效果，毕竟，最终实现是在windows10上采用单机版应用。

请你一步一步思考，给出3种解决方案，待我确认后再进行具体实现。

---

_**Assistant**_



---

好的，我完全理解您的顾虑。当前基于Web原型设计的悬浮弹框方案，虽然在Web环境下可以通过精细的优化（如v4.5版的重构）解决技术问题，但在移植到Windows桌面端（如`Tkinter`或`PyQt5`）时，不仅实现复杂，而且最终的用户体验也可能达不到理想效果。桌面应用有其自身的设计范式，生硬地模仿Web交互有时会适得其反。

因此，我们需要寻找更适合桌面环境、更稳定、更清晰的替代方案。

经过一步步思考，我为您设计了以下三种解决方案，旨在有效替代悬浮弹框，以便于您对比查阅“可修改工资表”与“原始工资表”的数据。

---

### **方案一：固定分栏对比视图 (Split-Screen View)**

这个方案是最经典、最稳健的桌面应用布局模式，将屏幕一分为二，清晰地展示列表与详情。

**方案描述：**
主界面将分为左右两个垂直区域。左侧是主数据表格，显示可修改的人员工资数据。右侧是一个固定的“原始数据”面板。当用户在左侧表格中**单击**选中任意一行时，右侧面板会立刻显示并更新为该行对应人员的全部**原始工资表**数据。

**布局示意:**
```
┌─────────────────────────────────────────────┐
│ Header (菜单栏)                             │ 
├──────────────────────┬──────────────────────┤
│ Left (主数据表格)    │ Right (原始数据显示面板) │
│ ┌──────────────────┐ │ ┌──────────────────┐ │
│ │  可修改数据列表  │ │  所选人员的原始数据  │ │
│ │- 张三 (已选中)   │ │- 姓名：张三        │ │
│ │- 李四            │ │- 工号：1001        │ │
│ │- 王五            │ │- 原始基本工资: 8000 │ │
│ │ ...              │ │- ...              │ │
│ └──────────────────┘ │ └──────────────────┘ │
│                      │                      │
└──────────────────────┴──────────────────────┘
```

**Mermaid 流程图:**
```mermaid
graph TD
    A[用户在左侧主表格中单击选中一行] --> B{右侧“原始数据”面板内容更新};
    B --> C[面板中以只读形式<br>清晰展示所选人员的原始工资详情];
    A --> D[被选中的行高亮显示];
```

**优点:**
*   **清晰稳定**：界面布局固定，用户视线无需移动即可完成对比，没有元素遮挡或位置不定的问题。
*   **交互简单**：仅需单击，是桌面应用最基础、最符合用户习惯的交互方式。
*   **易于实现**：在`PyQt5`和`Tkinter`中都是标准布局，实现简单且健壮，几乎没有技术风险。
*   **信息持久**：只要行被选中，原始数据就一直显示，方便用户仔细核对。

**缺点:**
*   **占用空间**：会固定占用一部分屏幕宽度，如果主表格的列数非常多，可能会显得拥挤。

---

### **方案二：行内展开式详情 (Accordion View)**

此方案将详细信息嵌入到表格自身中，保持了数据的上下文关联性。

**方案描述:**
主数据表格的每一行左侧会增加一个可点击的图标（例如 `+` 或 `▶`）。默认情况下，表格只显示核心的可修改字段。当用户点击这个图标时，该行下方会“展开”一个新的区域，用于显示该人员的原始工资数据。再次点击图标（变为 `-` 或 `▼`）则会收起该区域。

**交互示意:**
```
原始状态:
[▶] 张三 | 部门A | 10000.00 | ...

展开后:
[▼] 张三 | 部门A | 10000.00 | ...
    ┌──────────────────────────────┐
    │ 原始数据详情:                │
    │ - 姓名: 张三                 │
    │ - 原始基本工资: 8000         │
    │ - ...                        │
    └──────────────────────────────┘
[▶] 李四 | 部门B | 12000.00 | ...
```

**Mermaid 流程图:**
```mermaid
graph TD
    A[用户点击某行旁边的展开图标(+)] --> B{该行下方动态展开一个子区域};
    B --> C[子区域内显示该人员的原始工资数据];
    D[用户再次点击该行的折叠图标(-)] --> E{平滑地折叠该子区域};
    E --> F[原始工资信息被隐藏，列表恢复紧凑];
```

**优点:**
*   **上下文紧密**：原始数据就近显示在对应条目的正下方，关系一目了然。
*   **空间高效**：只在需要时占用额外空间，默认视图非常紧凑。
*   **支持多行查看**：用户可以同时展开多行进行对比。

**缺点:**
*   **实现稍复杂**：在`Tkinter`中实现平滑的展开/折叠动画需要自定义控件，有一定工作量。`PyQt5`的`QTreeView`可以更好地支持此类结构，但仍比方案一复杂。
*   **布局变化**：展开/折叠会改变下方行的位置，可能会轻微干扰用户的垂直浏览。

---

### **方案三：独立的详情窗口 (Modal/Dockable Window)**

此方案提供了一个独立的窗口来展示详情，给予用户最大的灵活性。

**方案描述:**
为了不与设计文档中已有的“双击编辑”功能冲突，此方案采用**右键单击**表格行的方式。用户右击某一行后，会弹出一个上下文菜单，提供“查看原始数据”选项。点击后，系统会弹出一个**新的、非模态的独立窗口**来显示原始数据。

**交互示意:**
1.  用户在“张三”行上右击。
2.  出现菜单：`[ 编辑数据... | 查看原始数据 ]`
3.  用户点击“查看原始数据”。
4.  屏幕上出现一个可拖动的小窗口，显示张三的原始工资详情。这个小窗口可以一直开着，用户仍可操作主窗口。

**Mermaid 流程图:**
```mermaid
graph TD
    A[用户在某一行上右键单击] --> B[弹出上下文菜单];
    B --> C[用户从菜单中选择“查看原始数据”];
    C --> D{弹出一个新的、可移动的独立窗口};
    D --> E[该窗口中加载并显示对应人员的原始数据];
    E --> F[用户可以将其拖到任意位置，<br>甚至拖到副屏幕，然后关闭它];
```

**优点:**
*   **高度灵活**：用户可以自由移动详情窗口，实现并排、叠加等多种对比方式，尤其适合多显示器用户。
*   **主界面干净**：不占用主界面的任何固定空间。
*   **功能分离**：通过右键菜单触发，将“查看”和“编辑”（双击）操作清晰分开，符合高级用户的操作习惯。
*   **实现可行**：`PyQt5`和`Tkinter`都能轻松创建非模态的子窗口。

**缺点:**
*   **窗口管理**：如果用户打开多个详情窗口，可能会造成桌面混乱。
*   **操作步骤略多**：相比单击或悬浮，需要“右键 -> 单击”两步操作。

---

我已为您呈现了三种各具特色的解决方案。请您仔细评估它们的优缺点，并结合您对最终产品形态的设想，选择一个最合适的方案。

待您确认后，我将为您进一步规划该方案的具体实现细节。

---

_**User**_

看了你给的几个方案，综合考虑，我更倾向于采用方案一，即：
方案一：固定分栏对比视图 (Split-Screen View)

但是，具体实现时，需要考虑以下问题：
1、整个窗体界面的布局：顶部菜单栏、左侧边栏、右侧主界面、底部状态栏。
2、顶部菜单栏的布局：文件、报表、分析、工具、帮助、退出（图标叉叉）。
3、左侧边栏的布局：月度工资表、异动人员表。
以树形展示：
月度工资表
4月
-全部在职人员
-离休人员
-退休人员
-A岗职工
5月
-全部在职人员
-离休人员
-退休人员
-A岗职工

异动人员表
4月
-起薪
-转正定级
-停薪-退休
-停薪-调离
-停薪-其他
-停薪-调整
-其他-考核扣款
-脱产读博
-请假扣款
5月
-起薪
-转正定级
-停薪-退休
-停薪-调离

4、右侧主界面的布局：
顶部区域：
按钮栏（左边：异动处理、数据验证，右边：导出审批表、导出请示（OA流程）、导出请示（部务会））

中部区域：
TAB页切换（异动汇总、计算结果、人员匹配、处理日志）、人员信息查询（输入框、查询按钮）

列表展示区（你上面提到的方案一：固定分栏对比视图）
左边：以列表形式展示选中月度工资表数据或异动人员表数据
右边：选中左边某列表项，展示相应列表项内容（可以双击某字段进行修改）以及对应人员原始工资表数据和差异值（如果可修改列表项某个字段被修改，展示与对应人员原始工资表中相应字段的数值差异）

底部区域：
分页

5、底部状态栏的布局：

请你先复述一下你的想法，待我确认后再进行具体实现。

---

_**Assistant**_



---

好的，非常感谢您的确认和详细的补充说明。选择“方案一：固定分栏对比视图”是一个非常稳健和明智的决定，它完全符合桌面应用程序稳定、清晰的设计原则。

根据您提出的五个方面的具体要求，我将我的理解进行复述和整合，形成一个完整的、集成化的界面布局方案。请您审阅，这是否就是您期望的最终效果。

---

### **整体界面布局构想 (复述)**

我的理解是，我们将构建一个经典的四区布局桌面应用。整个应用程序窗口将由以下四个固定部分组成：

1.  **顶部菜单栏**：位于窗体最上方，提供全局性的功能入口。
2.  **左侧边栏**：位于主窗口左侧，作为数据源的导航和筛选器。
3.  **右侧主界面**：占据窗口大部分中心区域，是所有核心操作和数据显示的地方。
4.  **底部状态栏**：位于窗体最下方，用于显示系统状态和上下文信息。

下面是这个布局的结构示意图，以及我对每个部分具体内容的详细描述：

```
┌──────────────────────────────────────────────────────────────────────────────────────┐
│ 顶部菜单栏: [文件] [报表] [分析] [工具] [帮助]                              [X] │
├──────────────┬─────────────────────────────────────────────────────────────────────┤
│              │ 右侧主界面                                                          │
│              │---------------------------------------------------------------------│
│              │ A. 操作控制区                                                       │
│ 左侧边栏     │  [异动处理] [数据验证]                          [导出审批表] [OA] [部务会] │
│ (树形导航)   │---------------------------------------------------------------------│
│ -月度工资表  │ B. 视图与查询区                                                     │
│  - 5月       │  [异动汇总] [计算结果] [匹配] [日志]  [__________________] [查询] │
│    -在职人员 │---------------------------------------------------------------------│
│ -异动人员表  │ C. 核心数据交互区 (固定分栏对比视图)                                │
│  - 5月       │  ┌───────────────────────────┬───────────────────────────────────┐ │
│    -起薪     │  │ 数据列表 (可修改)         │ 详情与编辑 (对比区)             │ │
│              │  │ [单击选中]                │ - 可编辑信息 (双击修改)         │ │
│              │  │                           │ - 对应原始数据 (只读)           │ │
│              │  │                           │ - 数据差异值 (实时显示)         │ │
│              │  ├───────────────────────────┴───────────────────────────────────┤ │
│              │  │ D. 分页控件                                                   │ │
│              │  │  [<] [1] [2] [...] [>] [显示 X-Y 条，共 Z 条]                  │ │
└──────────────┼─────────────────────────────────────────────────────────────────────┤
│ 底部状态栏:  [系统就绪 | 已加载: 5月工资表 | 共156人, 待处理2人]                   │
└──────────────────────────────────────────────────────────────────────────────────────┘
```

---

### **各区域详细说明**

1.  **顶部菜单栏**
    *   **布局**: 一系列标准的下拉菜单：`文件`、`报表`、`分析`、`工具`、`帮助`。
    *   **退出**: 在菜单栏的最右侧，会有一个独立的、现代风格的“叉”(`×`)图标按钮，用于关闭应用程序。

2.  **左侧边栏 (数据导航树)**
    *   **功能**: 作为核心的数据筛选器。用户在这里的选择将直接决定“右侧主界面”中显示的数据源。
    *   **结构**: 采用**树形视图(Tree View)**，清晰地展示层级关系。
        *   顶级节点为 `月度工资表` 和 `异动人员表`。
        *   二级节点为月份（如 `4月`, `5月`）。
        *   三级叶子节点为具体的表格或异动类型（如 `全部在职人员`, `起薪`）。

3.  **右侧主界面 (核心工作区)**
    这是一个多功能复合区域，从上到下分为四个部分：
    *   **A. 操作控制区**:
        *   顶部的一条按钮栏，按钮按功能和流程分组。
        *   **左侧对齐**: `异动处理`、`数据验证`，代表核心处理流程。
        *   **右侧对齐**: `导出审批表`、`导出请示（OA流程）`、`导出请示（部务会）`，代表最终的成果输出。
    *   **B. 视图与查询区**:
        *   使用**标签页(Tab Control)**来切换不同的数据视图，包括 `异动汇总`、`计算结果`、`人员匹配`、`处理日志`。
        *   标签页的右侧是**人员查询组件**，由一个文本`输入框`和一个`查询`按钮组成，用于在当前加载的数据列表中进行快速搜索。
    *   **C. 核心数据交互区 (您选择的方案一)**:
        *   这是最重要的区域，它被**垂直分割为两栏**。
        *   **左栏：数据列表**
            *   这是一个表格(Table/List View)，其内容由“左侧边栏的选择”和“当前激活的标签页”共同决定。
            *   用户可以**单击**此列表中的任意一行来选中它。
        *   **右栏：详情与编辑区**
            *   当左栏有行被选中时，此区域会立即更新，展示该行人员的详细信息。
            *   **可编辑信息**: 显示该人员在**可修改工资表**中的数据。用户可以**双击**这里的某个字段（如“基本工资”）来进行修改。
            *   **对应原始数据**: 以**只读**形式，并排或上下展示该人员在**原始工资表**中对应的未修改数据，作为核对基准。
            *   **数据差异值**: 这是关键。如果用户修改了“可编辑信息”中的某个数值字段，系统会**实时计算**并在此区域显示一个**差异值**（例如：“基本工资差异：+200.50”或“绩效工资差异：-150.00”）。这提供了即时的、量化的修改反馈。
    *   **D. 分页控件**:
        *   位于“核心数据交互区”的底部，与左侧的“数据列表”直接关联。
        *   当列表中的数据量超过设定值（如每页50条）时，分页控件会自动出现，提供翻页、跳转等功能。

4.  **底部状态栏**
    *   **功能**: 提供全局的、持续的上下文信息反馈。
    *   **布局**: 从左至右分为几个区域。
        *   **左侧**: 显示系统当前的状态，如“系统就绪”、“正在处理，请稍候...”、“处理完成”。
        *   **中间**: 显示当前加载的关键文件信息，如“已加载文件：5月工资表.xlsx, 5月异动人员.xlsx”。
        *   **右侧**: 显示数据统计摘要，如“共 156 条记录 | 匹配成功: 153 | 待处理: 2 | 失败: 1”。

---

**总结一下**：这个设计方案将您所有的需求都整合进了一个逻辑清晰、功能分区明确的界面中。它以“固定分栏对比视图”为核心，解决了数据对比的难题，同时通过树形导航、标签页和状态栏等经典桌面控件，构建了一个专业、高效的财务处理工作台。

如果以上复述和整合的设计构想准确无误地反映了您的想法，请您确认。确认后，我们就可以进入下一步，讨论具体的技术选型（例如使用PyQt5的哪些控件来实现）或者开始着手进行更细致的原型构建。

---

_**User**_

系统名称你觉得放置在哪里比较合适？

---

_**Assistant**_



---

这是一个非常好的问题，它关系到应用的整体性和专业性。对于桌面应用来说，系统名称的摆放位置有几个经典且符合用户习惯的选择。

综合考虑我们已经设计的界面布局，我建议将系统名称放置在以下两个核心位置，以达到最佳效果：

### **1. 应用程序的窗口标题栏 (首选/主要位置)**

这是最标准、最重要，也是最符合用户直觉的位置。

*   **位置**: 操作系统窗口的最顶部。
*   **显示内容**: `月度工资异动处理系统 v1.0` (可以包含版本号)
*   **示意图**:
    ```
    ┌──────────────────────────────────────────────────────────────────────────────────────┐
    │ 🗔 月度工资异动处理系统 v1.0                                                [—][口][X] │ 
    ├──────────────────────────────────────────────────────────────────────────────────────┤
    │ [文件] [报表] [分析] [工具] [帮助]                                                 │
    │ ... (应用其他界面部分) ...                                                          │
    └──────────────────────────────────────────────────────────────────────────────────────┘
    ```
*   **为什么这是最佳位置？**
    *   **行业标准**: 几乎所有的桌面软件（无论是财务、设计还是办公软件）都将应用名称放在这里。用户第一眼就能识别出这是什么程序。
    *   **系统集成**: 当用户使用任务栏、`Alt+Tab`切换窗口或在任务管理器中查看时，这里设置的标题会作为应用的唯一标识，非常清晰。
    *   **不占空间**: 它利用的是操作系统提供的标准区域，不会挤占我们精心设计的内部工作区的任何宝贵空间。

### **2. “关于”对话框 (次要/补充位置)**

这是提供详细版本和版权信息的标准位置。

*   **触发方式**: 通过顶部菜单栏的 `帮助` -> `关于` 选项打开。
*   **显示内容**:
    *   应用 Logo
    *   系统全称：`月度工资异动处理系统`
    *   版本号：`Version 1.0`
    *   版权信息：`© 2024 Your Company. All rights reserved.`
    *   其他技术或团队信息。
*   **为什么需要这个位置？**
    *   **专业性**: 提供一个“关于”窗口是专业软件的标志。
    *   **信息完整**: 它是用户获取详细版本、寻求支持或了解版权信息的官方途径。

### **总结与建议**

我强烈建议**将窗口标题栏作为系统名称的主要展示位置**，并**在“帮助”菜单中创建一个“关于”对话框作为补充**。

这种“主次结合”的方案，既能让用户在日常使用中时刻清晰地识别软件，又提供了获取详细信息的专业渠道，是目前最成熟和用户体验最好的实践。我们无需在应用内部的菜单栏或工具栏上再额外显示名称，从而保持工作区的简洁和高效。

---

_**User**_

@ui_design_specification.md @interaction_flow_design.md @README.md @main_interface_prototype.html
你上面思路我基本同意。请开始修改原型图，修改完成后，更新相关文档。


---

_**Assistant**_



---

我完全理解您的要求。我们将采用您选定的“方案一：固定分栏对比视图”，并对现有的UI原型和设计文档进行全面的更新。这包括修改HTML原型以实现新的布局和交互，然后同步更新所有相关的设计文档（`README.md`, `ui_design_specification.md`, `interaction_flow_design.md`），确保所有材料保持一致性。

我将一步步执行以下操作：

1.  **重构HTML原型 (`main_interface_prototype.html`)**：
    *   将主内容区改造为左右分栏布局。
    *   左栏用于显示数据列表。
    *   右栏用于显示选中人员的详细信息，包括可编辑数据、原始对比数据和实时差异值。
    *   实现新的交互逻辑：单击左侧列表行，在右侧面板显示详情；双击右侧面板中的字段进行编辑。
    *   移除旧的悬浮弹框和双击弹窗编辑的全部代码和逻辑。
2.  **更新项目设计文档 (`README.md`, `ui_design_specification.md`, `interaction_flow_design.md`)**：
    *   在所有文档中，将布局图、组件规范和交互流程图更新为新的分栏设计。
    *   彻底移除关于悬浮弹框（tooltip）和独立编辑窗口（modal）的所有描述和规范。
    *   详细阐述新的“分栏对比视图”的交互模式和设计细节。

我现在开始执行第一步，修改 `main_interface_prototype.html` 文件。这会是一个比较大的改动，但我会确保最终效果符合您的设想。

---



---

我现在开始执行第一步，修改 `main_interface_prototype.html` 文件。这会是一个比较大的改动，但我会确保最终效果符合您的设想。

首先，我需要读取 `main_interface_prototype.html` 的现有内容，以便在其基础上进行重构。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

好的，我已经拿到了 `main_interface_prototype.html` 的第一部分代码。这是一个结构良好、样式丰富的原型文件。为了全面理解其现有的交互逻辑，特别是JavaScript部分，我需要继续读取文件的其余内容。我现在开始读取第二部分。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

好的，第二部分内容已收到。主要是关于界面中各个组件的CSS样式定义。这为我之后的界面重构提供了很好的基础。现在我将继续读取文件的下一部分，以获取更关键的JavaScript交互逻辑代码。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

好的，第三部分的代码已收到，这部分非常关键，它定义了即将被我替换的“悬浮弹框”和“编辑对话框”的样式。这为我后续的界面重构提供了明确的目标。现在我继续读取文件，以获取核心的JavaScript代码。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

第四部分文件内容已收到。这部分主要是关于页脚状态栏、响应式设计以及独立设置窗口的样式。这些信息对于保持界面整体风格一致性非常重要。马上就要到核心的HTML结构和JavaScript部分了，我将继续读取文件。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

太好了！我已经定位到了`<body>`标签和核心HTML结构。我看到了头部、工作区、左侧面板和主工作区的布局定义，以及需要重构的内联`onclick`事件处理器。我需要将现有的`.content-area`拆分成左右两栏，左边放数据列表和分页，右边创建新的详情面板。

接下来，我需要继续读取文件，找到`<script>`标签，分析现有的JavaScript逻辑。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

第六部分文件已收到。太好了，这部分包含了所有设置窗口、完成提示框以及我需要替换的`personInfoTooltip`和`editPersonOverlay`的HTML代码。这为我接下来的重构工作提供了清晰的指引。

现在，我确信JavaScript代码就在文件的剩余部分。我将继续读取，以获取全部脚本内容。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

终于，我找到了`<script>`标签和核心的JavaScript逻辑。

我已经阅读到1750行，看到了`appState`状态管理对象、模拟数据生成、查询框清除按钮逻辑、分页功能函数以及核心的`renderCurrentPageData`渲染函数。我也注意到了需要修改的`ondblclick="openEditDialog(this)"`事件。

现在，我需要读取文件的剩余部分，以了解悬浮提示框 (`positionTooltip`)、编辑功能 (`openEditDialog`, `savePersonEdit`) 及其他事件处理函数的完整实现。

我将读取文件的最后一部分。

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

好的，我已阅读完所有文件内容，完全掌握了当前的实现方式。现在我将开始执行核心的重构工作。

我的计划是：

1.  **修改CSS样式**：我会移除所有关于悬浮弹框和编辑对话框的样式，并添加新的样式来支持左右分栏布局，包括左侧数据列表和右侧详情面板。
2.  **重构HTML结构**：我会修改主工作区的布局，用新的左右分栏结构替换掉原来的单一内容区。同时，移除页面底部的悬浮弹框和编辑对话框的HTML代码。
3.  **重写JavaScript逻辑**：这是最关键的一步。
    *   我会删除所有与旧的弹框和对话框相关的函数。
    *   创建新的函数来处理行选择、在右侧面板中渲染详细信息、以及在右侧面板中实现双击修改、保存和差异计算的功能。
    *   更新事件绑定，用新的单击选择逻辑替换旧的悬浮和双击逻辑。

这是一个比较大的改动，我会谨慎操作，确保代码的清晰和功能的完整。现在，我将为您生成对 `main_interface_prototype.html` 文件的完整修改。

---



---

我已阅读完所有文件内容，完全掌握了当前的实现方式。现在我将开始执行核心的重构工作。

我的计划是：

1.  **修改CSS样式**：我会移除所有关于悬浮弹框和编辑对话框的样式，并添加新的样式来支持左右分栏布局，包括左侧数据列表和右侧详情面板。
2.  **重构HTML结构**：我会修改主工作区的布局，用新的左右分栏结构替换掉原来的单一内容区。同时，移除页面底部的悬浮弹框和编辑对话框的HTML代码。
3.  **重写JavaScript逻辑**：这是最关键的一步。
    *   我会删除所有与旧的弹框和对话框相关的函数。
    *   创建新的函数来处理行选择、在右侧面板中渲染详细信息、以及在右侧面板中实现双击修改、保存和差异计算的功能。
    *   更新事件绑定，用新的单击选择逻辑替换旧的悬浮和双击逻辑。

这是一个比较大的改动，我会谨慎操作，确保代码的清晰和功能的完整。现在，我将为您生成对 `main_interface_prototype.html` 文件的完整修改。

---

