<!-- Generated by SpecStory -->

# 继续更新ui设计规范文档 (2025-06-12 10:37:53)

_**User**_

继续之前的更新文档工作，开始更新文档 @ui_design_specification.md 

---

_**Assistant**_



---

好的，我们继续更新UI设计规范文档 `docs/draft/design/ui_design_specification.md`，使其与 v5.0 的核心设计理念保持一致。

本次更新将遵循 `v5.0` 的核心原则，即采用**固定分栏对比视图**，并废弃所有悬浮式弹窗交互，以提供更稳定、专业的桌面端体验。

我将对文档进行以下修订：

1.  **更新版本标识**: 将文档标题更新为 `v5.0`，并增加版本说明。
2.  **废弃旧组件**: 移除或标记为废弃“智能悬浮弹框”、“编辑对话框”以及旧的“查询结果组件”。
3.  **引入新核心组件**: 详细说明新的“固定分栏对比与编辑系统”的设计规范。
4.  **重构布局规范**: 更新主窗口布局图和功能区域划分，以匹配 `v5.0` 的设计。
5.  **调整交互规范**: 更新交互行为部分，移除旧交互，增加新交互的说明。
6.  **同步开发指导**: 在CSS组织结构中反映组件的变化。

现在，我将开始对 `docs/draft/design/ui_design_specification.md` 文件进行修改。

---



---

现在，我将开始对 `docs/draft/design/ui_design_specification.md` 文件进行修改。

首先，我注意到您想更新的文件是 `ui_design_specification.md`，但在您的工作区中，相关内容位于 `docs/draft/design/README.md`。为了与您的指令保持一致，我将先把 `README.md` 重命名为 `ui_design_specification.md`。

---



---



---

