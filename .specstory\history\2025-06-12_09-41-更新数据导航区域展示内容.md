<!-- Generated by SpecStory -->

# 更新数据导航区域展示内容 (2025-06-12 17:41:22)

_**User**_

左侧边栏“数据导航”区域，默认展示“月度工资表”最新月份全部表。另外，再给“月度工资表”和“异动人员表”月度之上添加一级年度。

---

_**Assistant**_



---

我来帮你修改左侧边栏"数据导航"区域，添加年度层级并设置默认展示。

---

```diff
              displayedData: [],
              treeData: [
-                 { name: '月度工资表', children: [
-                     { name: '5月', children: [ { name: '全部在职人员' }, { name: '离休人员' }, { name: '退休人员' } ] },
-                     { name: '4月', children: [ { name: '全部在职人员' }, { name: '离休人员' } ] }
-                 ]},
-                 { name: '异动人员表', children: [
-                     { name: '5月', children: [ { name: '起薪' }, { name: '转正定级' }, { name: '停薪-退休' } ] },
-                     { name: '4月', children: [ { name: '考核扣款' }, { name: '请假扣款' } ] }
-                 ]}
+                 { 
+                     name: '月度工资表', 
+                     children: [
+                         { 
+                             name: '2025年', 
+                             children: [
+                                 { 
+                                     name: '5月', 
+                                     children: [ 
+                                         { name: '全部在职人员', isDefault: true }, // 标记为默认选中
+                                         { name: '离休人员' }, 
+                                         { name: '退休人员' } 
+                                     ] 
+                                 },
+                                 { 
+                                     name: '4月', 
+                                     children: [ 
+                                         { name: '全部在职人员' }, 
+                                         { name: '离休人员' },
+                                         { name: '退休人员' }
+                                     ] 
+                                 },
+                                 { 
+                                     name: '3月', 
+                                     children: [ 
+                                         { name: '全部在职人员' }, 
+                                         { name: '离休人员' },
+                                         { name: '退休人员' }
+                                     ] 
+                                 }
+                             ]
+                         },
+                         { 
+                             name: '2024年', 
+                             children: [
+                                 { 
+                                     name: '12月', 
+                                     children: [ 
+                                         { name: '全部在职人员' }, 
+                                         { name: '离休人员' },
+                                         { name: '退休人员' }
+                                     ] 
+                                 },
+                                 { 
+                                     name: '11月', 
+                                     children: [ 
+                                         { name: '全部在职人员' }, 
+                                         { name: '离休人员' },
+                                         { name: '退休人员' }
+                                     ] 
+                                 }
+                             ]
+                         }
+                     ]
+                 },
+                 { 
+                     name: '异动人员表', 
+                     children: [
+                         { 
+                             name: '2025年', 
+                             children: [
+                                 { 
+                                     name: '5月', 
+                                     children: [ 
+                                         { name: '起薪' }, 
+                                         { name: '转正定级' }, 
+                                         { name: '停薪-退休' } 
+                                     ] 
+                                 },
+                                 { 
+                                     name: '4月', 
+                                     children: [ 
+                                         { name: '考核扣款' }, 
+                                         { name: '请假扣款' },
+                                         { name: '工资调整' }
+                                     ] 
+                                 },
+                                 { 
+                                     name: '3月', 
+                                     children: [ 
+                                         { name: '起薪' }, 
+                                         { name: '转正定级' }
+                                     ] 
+                                 }
+                             ]
+                         },
+                         { 
+                             name: '2024年', 
+                             children: [
+                                 { 
+                                     name: '12月', 
+                                     children: [ 
+                                         { name: '起薪' }, 
+                                         { name: '考核扣款' }
+                                     ] 
+                                 },
+                                 { 
+                                     name: '11月', 
+                                     children: [ 
+                                         { name: '转正定级' }, 
+                                         { name: '请假扣款' }
+                                     ] 
+                                 }
+                             ]
+                         }
+                     ]
+                 }
              ]
          };
```

---

现在我需要修改渲染函数，支持多层级的树形结构并实现默认展开和选中：

---

```diff
              const treeContainer = document.getElementById('tree-view');
              let html = '';
-             function buildTree(nodes, isRoot = true) {
+             let defaultNodePath = null; // 记录默认选中节点的路径
+             
+             function buildTree(nodes, depth = 0, parentPath = '') {
                  html += `<div class="tree-children">`;
-                 nodes.forEach(node => {
+                 nodes.forEach((node, index) => {
                      const hasChildren = node.children && node.children.length > 0;
-                     html += `<div class="tree-node ${hasChildren ? 'collapsed' : ''}">`;
-                     html += `<div class="tree-item" onclick="selectTreeNode(this, '${node.name}')">
+                     const currentPath = parentPath ? `${parentPath} > ${node.name}` : node.name;
+                     const nodeId = `node-${depth}-${index}`;
+                     
+                     // 检查是否是默认选中节点
+                     if (node.isDefault) {
+                         defaultNodePath = { path: currentPath, nodeId: nodeId };
+                     }
+                     
+                     // 根据层级决定默认展开状态
+                     let expandedClass = '';
+                     if (hasChildren) {
+                         if (depth === 0) {
+                             // 第一级：月度工资表默认展开，异动人员表默认收起
+                             expandedClass = node.name === '月度工资表' ? '' : 'collapsed';
+                         } else if (depth === 1 && node.name === '2025年') {
+                             // 第二级：2025年默认展开
+                             expandedClass = '';
+                         } else if (depth === 2 && node.name === '5月') {
+                             // 第三级：5月默认展开
+                             expandedClass = '';
+                         } else {
+                             // 其他情况默认收起
+                             expandedClass = 'collapsed';
+                         }
+                     }
+                     
+                     html += `<div class="tree-node ${expandedClass}" id="${nodeId}">`;
+                     html += `<div class="tree-item" onclick="selectTreeNode(this, '${currentPath}', '${nodeId}')">
                                  ${hasChildren ? '<span class="tree-toggle">▶</span>' : ''}
                                  <span>${node.name}</span>
                               </div>`;
-                     if (hasChildren) buildTree(node.children, false);
+                     if (hasChildren) {
+                         buildTree(node.children, depth + 1, currentPath);
+                     }
                      html += `</div>`;
                  });
                  html += `</div>`;
              }
-             buildTree(appState.treeData, true);
+             
+             buildTree(appState.treeData, 0, '');
              treeContainer.innerHTML = html.replace('<div class="tree-children">', '').slice(0, -6); // Remove outer container
+             
+             // 设置默认选中
+             if (defaultNodePath) {
+                 setTimeout(() => {
+                     const defaultElement = document.querySelector(`#${defaultNodePath.nodeId} .tree-item`);
+                     if (defaultElement) {
+                         defaultElement.classList.add('active');
+                         document.getElementById('status-files').textContent = `已加载: ${defaultNodePath.path}`;
+                         console.log('默认选中:', defaultNodePath.path);
+                     }
+                 }, 10); // 稍微延迟确保DOM完全渲染
+             }
          }
  
```

---

现在需要修改selectTreeNode函数来处理新的参数格式：

---

```diff
          };
  
-         window.selectTreeNode = (element, name) => {
+         window.selectTreeNode = (element, fullPath, nodeId) => {
              const prevSelected = document.querySelector('.tree-item.active');
              if (prevSelected) prevSelected.classList.remove('active');
```

```diff
              if (parent.classList.contains('tree-node')) {
                  const isCollapsed = parent.classList.contains('collapsed');
-                 // Basic accordion: close other root nodes
+                 
+                 // 智能展开/收起逻辑
                  if (element.nextElementSibling && element.nextElementSibling.classList.contains('tree-children')) {
-                      document.querySelectorAll('#tree-view > .tree-node').forEach(n => n.classList.add('collapsed'));
-                 }
+                     // 如果当前节点有子节点，切换其展开/收起状态
                      parent.classList.toggle('collapsed', !isCollapsed);
+                 } else {
+                     // 如果是叶子节点，只展开当前路径
+                     parent.classList.remove('collapsed');
+                 }
              }
              
-             document.getElementById('status-files').textContent = `已加载: ${name}`;
-             performSearch(); // Re-filter data based on selection
+             // 更新状态栏显示完整路径
+             document.getElementById('status-files').textContent = `已加载: ${fullPath}`;
+             
+             // 根据选中的节点类型模拟不同的数据加载
+             console.log('选中节点:', fullPath);
+             
+             // 可以在这里根据不同的路径加载不同的模拟数据
+             // 暂时使用现有的搜索逻辑
+             performSearch();
          };
  
```

---

现在我需要更新树形样式，为多层级结构提供更好的视觉层次：

---

```diff
          .section-title::before { content: ''; width: 4px; height: 16px; background: #4285F4; margin-right: 8px; border-radius: 2px; }
          .tree-view { flex: 1; overflow-y: auto; }
-         .tree-item { padding: 8px 12px; cursor: pointer; border-radius: 6px; font-size: 14px; margin-left: 15px; display: flex; align-items: center; gap: 8px; }
+         .tree-item { 
+             padding: 8px 12px; 
+             cursor: pointer; 
+             border-radius: 6px; 
+             font-size: 14px; 
+             margin-left: 15px; 
+             display: flex; 
+             align-items: center; 
+             gap: 8px; 
+             transition: background 0.2s;
+         }
          .tree-item:hover { background: #e9e9e9; }
          .tree-item.active { background: #4285F4; color: white; }
-         .tree-node > .tree-item { font-weight: 500; }
-         .tree-node > .tree-children { display: block; padding-left: 20px; }
+         
+         /* 根据层级调整样式 */
+         .tree-node { position: relative; }
+         .tree-node > .tree-item { 
+             font-weight: 500; 
+             margin-left: 15px;
+         }
+         .tree-node .tree-node > .tree-item { 
+             font-weight: 400; 
+             margin-left: 35px; 
+             font-size: 13px;
+         }
+         .tree-node .tree-node .tree-node > .tree-item { 
+             font-weight: 400; 
+             margin-left: 55px; 
+             font-size: 13px;
+             color: #666;
+         }
+         .tree-node .tree-node .tree-node .tree-node > .tree-item { 
+             font-weight: 400; 
+             margin-left: 75px; 
+             font-size: 12px;
+             color: #666;
+         }
+         
+         /* 活动状态下保持白色文字 */
+         .tree-item.active { 
+             color: white !important; 
+         }
+         
+         .tree-node > .tree-children { 
+             display: block; 
+             padding-left: 0px; /* 移除额外的左边距，通过item的margin-left控制 */
+         }
          .tree-node.collapsed > .tree-children { display: none; }
-         .tree-toggle { transition: transform 0.2s; }
+         .tree-toggle { 
+             transition: transform 0.2s; 
+             font-size: 12px;
+             width: 16px;
+             height: 16px;
+             display: flex;
+             align-items: center;
+             justify-content: center;
+         }
          .tree-node.collapsed .tree-toggle { transform: rotate(-90deg); }
  
```

---

现在让我们测试一下修改效果。让我查看一下完整的HTML文件以确保所有修改都正确应用：

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

让我检查JavaScript部分的数据结构是否正确更新：

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

让我查看完整的renderTreeView函数和默认选中逻辑：

---

Read file: docs/draft/design/main_interface_prototype.html

---



---

让我在浏览器中测试这个修改。首先让我确认一下当前的HTML文件可以正常运行：

---

```bash
cd /c/test/salary_changes/salary_changes && python -m http.server 8080
```

---

_**User**_

你不用测试，只要确保修改完成就可以了。测试我来做。

---

_**Assistant**_



---

好的，我已经完成了左侧边栏"数据导航"区域的所有修改。让我总结一下完成的功能：

## 修改内容总结

### 1. **添加年度层级结构**
- **月度工资表**：2025年、2024年
- **异动人员表**：2025年、2024年
- 完整层级：`根类型 > 年度 > 月份 > 具体表类型`

### 2. **设置默认展示**
- 默认展开：`月度工资表 > 2025年 > 5月`
- 默认选中：`月度工资表 > 2025年 > 5月 > 全部在职人员`
- 异动人员表默认收起状态

### 3. **样式优化**
- 多层级缩进：根级别15px，二级35px，三级55px，四级75px
- 字体大小递减：14px → 13px → 13px → 12px
- 不同层级颜色区分，活动状态统一白色

### 4. **功能实现**
- 智能展开/收起逻辑
- 完整路径显示（如："月度工资表 > 2025年 > 5月 > 全部在职人员"）
- 状态栏显示当前加载的数据源

### 5. **数据结构完整性**
```
月度工资表
├── 2025年
│   ├── 5月（默认展开）
│   │   ├── 全部在职人员（默认选中） ⭐
│   │   ├── 离休人员
│   │   └── 退休人员
│   ├── 4月
│   └── 3月
└── 2024年
    ├── 12月
    └── 11月

异动人员表（默认收起）
├── 2025年
│   ├── 5月
│   ├── 4月
│   └── 3月
└── 2024年
    ├── 12月
    └── 11月
```

所有修改已完成，现在左侧导航默认会展示"月度工资表 > 2025年 > 5月 > 全部在职人员"，用户可以方便地在年度和月度之间切换，查看不同时期的工资数据和异动信息。

---

