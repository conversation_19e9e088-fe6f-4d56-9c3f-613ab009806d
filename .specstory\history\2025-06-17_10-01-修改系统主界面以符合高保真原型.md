<!-- Generated by SpecStory -->

# 修改系统主界面以符合高保真原型 (2025-06-17 18:01:56)

_**User**_

系统当前主界面不符合我的预期，我想将目录 @design   (docs/draft/design) 下主界面的高保真原型图作为当前系统的主界面。
请你以 @cursor-memory-bank  memory bank 的思想和几个模式指导我进行修改，修改后的界面符合高保真原型图的功能布局和交互设计，
界面风格和高保真原型图风格保持一致。请你先给出你的思考过程和步骤等，然后待我确认后，再具体进行修改。

---

_**Assistant**_



---

我需要先分析当前系统的主界面结构和高保真原型的设计要求，然后制定一个系统性的修改方案。让我先了解当前项目的主界面实现情况。

---

<details>
            <summary>Searched codebase "main interface GUI primary window layout" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/main_window.py` | L1-37 |
| `src/gui/windows_refactored.py` | L88-130 |
| `src/gui/windows_refactored.py` | L623-670 |
| `src/gui/main_window.py` | L163-214 |
| `src/gui/main_window_refactored.py` | L93-150 |
| `src/gui/main_window.py` | L131-163 |
| `src/gui/windows.py` | L559-614 |
| `src/gui/main_window_refactored.py` | L150-195 |
| `src/gui/windows.py` | L1-24 |
| `src/gui/main_window.py` | L37-82 |
| `src/gui/main_window_refactored.py` | L1-33 |
| `src/gui/windows_refactored.py` | L177-209 |
| `src/gui/dialogs.py` | L311-352 |
| `src/gui/windows.py` | L73-125 |
| `src/gui/main_window.py` | L311-356 |
| `src/gui/main_window.py` | L463-520 |
| `src/gui/windows.py` | L125-168 |
| `src/gui/windows_refactored.py` | L1-31 |
| `main.py` | L176-194 |
| `src/gui/windows.py` | L24-73 |
| `src/gui/main_window_refactored.py` | L292-337 |
| `src/gui/main_window.py` | L412-463 |
| `src/gui/main_window.py` | L82-131 |
| `src/gui/main_window.py` | L214-247 |
| `src/gui/main_window.py` | L247-279 |

</details>

---

<details>
            <summary>Searched codebase "main_window.py" • **9** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: src/gui/main_window.py

---

现在让我更好地了解设计文档中描述的高保真原型的核心特点：

---

Read file: docs/draft/design/README.md

---

现在我基于分析的结果，结合 cursor-memory-bank 的思想，给您一个系统性的修改方案：

## 🎯 基于 Cursor Memory Bank 思想的UI重构分析

### 📊 现状分析与对比

**当前系统特点**：
- PyQt5 传统桌面应用，采用选项卡式布局
- 垂直堆叠的功能区域（欢迎区域 + 选项卡 + 数据显示）
- 分散的功能模块，缺乏统一的导航体验
- 传统的对话框式设置管理

**高保真原型特点**：
- 现代扁平化设计，采用左侧导航 + 右侧主工作区布局
- 树形导航系统，4级层次结构，智能展开策略
- 单面板数据展示，表格内编辑功能
- 简化行展开系统，直观差异对比
- 集成的查询和操作控制面板

### 🎨 设计方案 (CREATIVE 模式)

#### 方案一：渐进式改造 (推荐) ⭐
**核心思路**：保持现有架构基础，逐步重构界面布局
- **优势**：风险低，兼容现有功能模块，开发成本可控
- **改造重点**：
  1. 替换选项卡布局为左右分栏布局
  2. 新增树形导航组件 
  3. 重构数据表格支持行展开和表格内编辑
  4. 优化视觉样式和响应式布局

#### 方案二：全面重构
**核心思路**：完全按照HTML原型重新设计界面架构
- **优势**：完全符合设计规范，用户体验最佳
- **挑战**：开发工作量大，需要重构大量现有组件

### 🔧 具体修改步骤（渐进式改造）

#### 阶段一：布局结构重构
1. **主窗口布局改造**
   - 移除垂直选项卡布局
   - 实现左侧导航面板(320px) + 右侧主工作区的水平分栏
   - 添加顶部操作控制面板和底部状态栏

2. **创建树形导航组件**
   - 实现4级树形结构：月度工资表/异动人员表 → 年份 → 月份 → 人员类型
   - 智能展开策略和交互逻辑
   - 节点选择和状态管理

#### 阶段二：数据展示重构 
1. **表格组件增强**
   - 新增行展开功能（+ 按钮）
   - 实现表格内编辑（双击编辑）
   - 差异值显示和颜色编码

2. **查询和分页系统**
   - 集成查询输入框到操作面板
   - 实现防抖查询和清除功能
   - 智能分页组件

#### 阶段三：视觉样式优化
1. **Material Design 风格**
   - 应用高保真原型的色彩系统
   - 响应式布局适配
   - 现代化的按钮和控件样式

## 🛠️ 推荐实施方案

我建议采用**方案一：渐进式改造**，具体原因：

1. **风险控制**：保持现有业务逻辑不变，仅重构UI层
2. **开发效率**：可以复用现有的数据模型和业务服务
3. **用户体验**：能够快速达到高保真原型的核心体验
4. **维护性**：代码结构更清晰，便于后续维护

### 📝 实施计划

**第一步：创建新的主界面布局组件**
- 创建 `ModernMainWindow` 类
- 实现左右分栏基础架构
- 迁移现有功能到新布局

**第二步：开发核心组件**
- 树形导航组件 `NavigationTreeWidget`
- 增强数据表格 `EnhancedDataTableWidget`
- 操作控制面板 `ControlPanelWidget`

**第三步：集成和优化**
- 连接新界面与现有业务逻辑
- 样式主题化和响应式适配
- 测试和调优

您是否同意这个分析和实施方案？我可以开始具体的代码实现工作。

---

_**User**_

如何在各个模式之间切换你没有说

---

_**Assistant**_



---

您说得对！我遗漏了关键的模式切换机制说明。基于 cursor-memory-bank 的思想，让我补充完整的工作模式体系：

## 🔄 Cursor Memory Bank 工作模式体系

### 🎨 CREATIVE 模式 - 创意设计阶段
**适用场景**：
- 架构设计决策
- UI/UX设计探索  
- 技术方案对比
- 创新功能构思

**切换时机**：当需要进行设计决策、架构选择或解决复杂问题时

**当前状态**：✅ 已完成方案设计分析

---

### ⚙️ IMPLEMENTATION 模式 - 具体实现阶段  
**适用场景**：
- 编写具体代码
- 组件开发
- 功能实现
- 技术细节处理

**切换时机**：设计方案确定后，开始具体编码工作

**待办事项**：等待您确认方案后切换到此模式

---

### 🔍 REVIEW 模式 - 审查优化阶段
**适用场景**：
- 代码质量检查
- 性能优化
- 错误修复
- 重构建议

**切换时机**：实现完成后进行质量保证

---

### 📚 DOCUMENTATION 模式 - 文档维护阶段
**适用场景**：
- 更新设计文档
- 编写技术说明
- 用户指南
- 变更记录

**切换时机**：项目完成或重大变更后

---

## 🎯 当前状态和下一步切换

### 现在处于：🎨 CREATIVE 模式
**已完成的创意工作**：
- ✅ 现状分析（当前PyQt5 vs 高保真原型对比）
- ✅ 设计方案制定（渐进式改造 vs 全面重构）
- ✅ 技术路线规划（三阶段实施计划）
- ✅ 风险评估和优势分析

### 准备切换到：⚙️ IMPLEMENTATION 模式
**切换条件**：等待您

---

