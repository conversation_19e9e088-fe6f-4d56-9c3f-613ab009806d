<!-- Generated by SpecStory -->

# Error during data import in salary system (2025-06-20 22:34:49)

_**User**_

@src 
点击“开始导入”后，弹窗报错，请看图片
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-20 22:24:42.961 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-20 22:24:42.977 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-20 22:24:42.977 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-20 22:24:42.977 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-20 22:24:42.977 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-20 22:24:42.977 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-20 22:24:42.977 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-20 22:24:42.977 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-20 22:24:45.035 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 22:24:45.037 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 22:24:45.040 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-20 22:24:45.041 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-20 22:24:45.042 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-20 22:24:45.092 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-20 22:24:45.136 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-20 22:24:45.137 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-20 22:24:45.146 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-20 22:24:45.147 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-20 22:24:45.148 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1185 | 核心管理器初始化完成。
2025-06-20 22:24:45.218 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-20 22:24:45.220 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-20 22:24:45.321 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-20 22:24:45.323 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-20 22:24:45.433 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:45.444 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:24:45.447 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-20 22:24:45.468 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-20 22:24:46.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-20 22:24:46.709 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-20 22:24:46.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-20 22:24:46.763 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:46.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:46.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.88ms
2025-06-20 22:24:46.769 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:46.840 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:232 | 控制面板按钮信号连接完成
2025-06-20 22:24:46.859 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1175 | 主窗口UI设置完成。
2025-06-20 22:24:46.861 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-20 22:24:46.862 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-20 22:24:46.863 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-20 22:24:46.864 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-20 22:24:46.865 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-20 22:24:46.866 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-20 22:24:46.867 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-20 22:24:46.868 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1205 | 所有信号和槽已连接。
2025-06-20 22:24:46.869 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:24:46.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:46.871 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:46.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.95ms
2025-06-20 22:24:46.874 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:46.876 | INFO     | src.gui.prototype.prototype_main_window:__init__:1139 | 原型主窗口初始化完成
2025-06-20 22:24:47.027 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:47.392 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-20 22:24:47.393 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 22:24:47.402 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 22:24:47.408 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 22:24:47.409 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 22:24:47.451 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-20 22:24:51.955 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:51.955 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:24:51.955 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:24:51.955 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 22:24:51.955 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 22:24:51.971 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:24:51.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:51.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:51.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-20 22:24:51.971 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:53.423 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:53.423 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:24:53.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:24:53.423 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 22:24:53.479 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:777 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 22:24:53.529 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:779 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 22:24:53.530 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 22:24:53.534 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:24:53.546 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:53.547 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:53.548 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.54ms
2025-06-20 22:24:53.548 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:55.418 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:55.434 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 22:24:55.434 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 22:24:55.434 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_a_grade_employees
2025-06-20 22:24:55.434 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_a_grade_employees
2025-06-20 22:24:55.434 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:24:55.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:55.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:55.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-20 22:24:55.450 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:56.259 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:56.259 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 22:24:56.259 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 22:24:56.259 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_active_employees
2025-06-20 22:24:56.259 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_active_employees
2025-06-20 22:24:56.259 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:24:56.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:56.275 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:56.275 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.78ms
2025-06-20 22:24:56.275 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:57.413 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:24:57.413 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:24:57.413 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:24:57.413 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 22:24:57.428 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:777 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 22:24:57.428 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:779 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 22:24:57.428 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 22:24:57.428 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:24:57.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:24:57.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:24:57.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-20 22:24:57.428 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:24:59.582 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:249 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-20 22:24:59.582 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1215 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 退休人员。
打开导入对话框。
2025-06-20 22:24:59.688 | INFO     | src.gui.dialogs:__init__:49 | 数据导入对话框初始化完成。
2025-06-20 22:25:13.335 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-20 22:25:22.151 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 22:25:24.221 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 22:25:40.157 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 22:25:40.157 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 22:25:40.157 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-20 22:25:40.157 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-20 22:25:40.398 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 13行 x 21列
2025-06-20 22:25:40.398 | INFO     | src.modules.data_import.data_validator:_generate_default_rules:446 | 生成默认验证规则: 21个字段
2025-06-20 22:25:40.398 | INFO     | src.modules.data_import.data_validator:validate_dataframe:133 | 开始验证数据: 13行 x 21列
2025-06-20 22:25:40.398 | INFO     | src.modules.data_import.data_validator:validate_dataframe:142 | 数据验证完成: 0个错误
2025-06-20 22:25:42.616 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:1236 | 接收到导入数据: 13 行, 来源: 未知来源
2025-06-20 22:25:42.616 | INFO     | src.gui.prototype.prototype_main_window:set_data:299 | 通过公共接口设置新的表格数据: 13 行
2025-06-20 22:25:42.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 13
2025-06-20 22:25:42.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-20 22:25:42.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 21 列
2025-06-20 22:25:42.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 78.12ms
2025-06-20 22:25:42.711 | INFO     | src.gui.prototype.prototype_main_window:_save_to_database:1293 | 准备将 13 条数据保存到表: salary_data_2025_05_pension_employees
2025-06-20 22:25:42.719 | ERROR    | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:748 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中
缺少必须的员工ID列 (工号/职工编号)
2025-06-20 22:25:42.783 | ERROR    | src.gui.prototype.prototype_main_window:_on_save_finished:1323 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中缺少必须的员
工ID列 (工号/职工编号)
2025-06-20 22:26:02.110 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:249 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-20 22:26:02.110 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1215 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 退休人员。
打开导入对话框。
2025-06-20 22:26:02.150 | INFO     | src.gui.dialogs:__init__:49 | 数据导入对话框初始化完成。
2025-06-20 22:26:09.770 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-20 22:26:34.916 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 22:26:37.665 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 22:26:43.997 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 22:26:43.997 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 22:26:43.997 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-20 22:26:44.013 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-20 22:26:44.209 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-20 22:26:44.225 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-20 22:26:44.225 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.f
loat64(33607.14625)}
2025-06-20 22:26:44.225 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-20 22:26:44.225 | INFO     | src.modules.data_import.data_validator:_generate_default_rules:446 | 生成默认验证规则: 21个字段
2025-06-20 22:26:44.225 | INFO     | src.modules.data_import.data_validator:validate_dataframe:133 | 开始验证数据: 13行 x 21列
2025-06-20 22:26:44.225 | INFO     | src.modules.data_import.data_validator:validate_dataframe:142 | 数据验证完成: 0个错误
2025-06-20 22:26:45.884 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:1236 | 接收到导入数据: 13 行, 来源: 未知来源
2025-06-20 22:26:45.886 | INFO     | src.gui.prototype.prototype_main_window:set_data:299 | 通过公共接口设置新的表格数据: 13 行
2025-06-20 22:26:45.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 13 -> 13
2025-06-20 22:26:45.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-20 22:26:45.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 21 列
2025-06-20 22:26:46.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 138.21ms
2025-06-20 22:26:46.029 | INFO     | src.gui.prototype.prototype_main_window:_save_to_database:1293 | 准备将 13 条数据保存到表: salary_data_2025_05_pension_employees
2025-06-20 22:26:46.047 | ERROR    | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:748 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中
缺少必须的员工ID列 (工号/职工编号)
2025-06-20 22:26:46.103 | ERROR    | src.gui.prototype.prototype_main_window:_on_save_finished:1323 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中缺少必须的员
工ID列 (工号/职工编号)
2025-06-20 22:27:26.305 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:27:26.305 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:27:26.305 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:27:26.305 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 22:27:26.305 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 22:27:26.341 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:27:26.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 13
2025-06-20 22:27:26.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:27:26.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 13, 耗时: 18.06ms
2025-06-20 22:27:26.368 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:27:28.419 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:27:28.419 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:27:28.419 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:27:28.445 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 22:27:28.450 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:777 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 22:27:28.450 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:779 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 22:27:28.450 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 22:27:28.450 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:27:28.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 13
2025-06-20 22:27:28.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:27:28.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-20 22:27:28.450 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:27:31.084 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_a_grade_employees
2025-06-20 22:27:31.091 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_a_grade_employees
2025-06-20 22:27:31.091 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 13
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-20 22:27:31.091 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_active_employees
2025-06-20 22:27:33.052 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_active_employees
2025-06-20 22:27:33.052 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 13
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-20 22:27:33.052 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:27:33.958 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:27:33.958 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:27:33.958 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:27:33.958 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 22:27:33.958 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 22:27:33.968 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:27:33.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 13
2025-06-20 22:27:33.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:27:33.968 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-20 22:27:33.968 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:27:34.708 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:27:34.708 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:27:34.708 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:27:34.724 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
应用程序已退出

---

_**Assistant**_



---

好的，我看到您提供的截图和日志了。

错误信息“无法将数据保存到数据库表 'salary_data_2025_05_pension_employees'。错误: 数据中缺少必须的员工ID列 (工号/职工编号)”清晰地表明，在尝试将导入的Excel数据存入数据库时，程序无法找到一个必需的“员工ID”列。

日志也证实了这一点：
`ERROR | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:748 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)`

这通常意味着导入的Excel文件中，表示员工ID的列名（例如“工号”、“职工编号”等）与程序预期的名称不匹配。

为了解决这个问题，我需要检查一下 `dynamic_table_manager.py` 文件中是如何定义和检查员工ID的。我将首先搜索包含这个错误信息的文件，以定位相关逻辑。

---

<details>
            <summary>Grep search for "数据中缺少必须的员工ID列" • **5** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L692 | `raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号)")` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L3316 | `⟪ 123 characters skipped ⟫lary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L3515 | `⟪ 123 characters skipped ⟫lary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L3516 | `⟪ 111 characters skipped ⟫lary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L3541 | `⟪ 123 characters skipped ⟫lary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L3542 | `⟪ 111 characters skipped ⟫lary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_13-47-error-in-python-application-import-function.md` | L1161 | `根据您提供的截图和终端日志，虽然界面上只显示了“未知错误”，但日志中有一条关键信息：`保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号)`。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_13-47-error-in-python-application-import-function.md` | L1310 | `请您重新运行程序并导入数据。这次，错误弹窗应该会显示“数据中缺少必须的员工ID列 (工号/职工编号)”这个具体的错误信息，而不再是“未知错误”。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_13-47-error-in-python-application-import-function.md` | L1573 | `正如您所见，错误是“数据中缺少必须的员工ID列 (工号/职工编号)”。这清晰地指出了问题的根源：在将数据存入数据库之前，程序没能从导入的Excel数据中找到“工号”或“职工编号”这一关键列。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_13-47-error-in-python-application-import-function.md` | L1624 | `raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号)")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_12-03-%E6%89%BE%E5%87%BA%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%A4%B1%E8%B4%A5%E7%9A%84%E5%8E%9F%E5%9B%A0.md` | L305 | `+             raise ValueError("数据中缺少必须的员工ID列（工号/职工编号）。")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_12-03-%E6%89%BE%E5%87%BA%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%A4%B1%E8%B4%A5%E7%9A%84%E5%8E%9F%E5%9B%A0.md` | L972 | `-             self.logger.error("数据中缺少必须的员工ID列（工号/职工编号）。")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_12-03-%E6%89%BE%E5%87%BA%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%A4%B1%E8%B4%A5%E7%9A%84%E5%8E%9F%E5%9B%A0.md` | L1014 | `+                 raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号)")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L692 | `raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号)")` |

</details>

---



---

搜索结果确认了错误源于 `src/modules/data_storage/dynamic_table_manager.py` 的第692行。

现在，我需要阅读该文件中的 `save_dataframe_to_table` 方法，特别是第692行附近的代码，来理解其内部逻辑。

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---



---

`dynamic_table_manager.py`中的`save_dataframe_to_table`方法代码我已经看过了。

关键逻辑如下：
```python
# ...
# 1a. Standardize employee_id column
employee_id_map = {"工号": "employee_id", "职工编号": "employee_id"}
active_map = {k: v for k, v in employee_id_map.items() if k in df_copy.columns}

if active_map:
    df_copy.rename(columns=active_map, inplace=True)
    self.logger.info(f"列名映射成功: {list(active_map.keys())} -> employee_id")
elif 'employee_id' not in df_copy.columns:
    raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号)")
# ...
```
代码在导入数据后，会查找名为“工号”或“职工编号”的列，并将其重命名为内部统一的`employee_id`。如果两者都找不到，并且数据中也不存在`employee_id`列，就会抛出我们看到的错误。

这证实了我的猜测：导入的“退休人员”Excel文件中，表示唯一身份的列名很可能不是“工号”或“职工编号”。

一个合理的解决办法是增强系统的灵活性，让它能够识别更多可能的ID列名。考虑到不同的人员类型（在职、退休、离休），其ID列的名称可能会有所不同。例如，对于退休人员，列名可能是“编号”。

我将向`employee_id_map`中添加“编号”作为一种可能的ID列名，以兼容更多情况。这能够使系统更加健壮。

现在我来修改`src/modules/data_storage/dynamic_table_manager.py`文件。

---

```diff
              
              # 1a. Standardize employee_id column
-             employee_id_map = {"工号": "employee_id", "职工编号": "employee_id"}
+             employee_id_map = {"工号": "employee_id", "职工编号": "employee_id", "编号": "employee_id"}
              active_map = {k: v for k, v in employee_id_map.items() if k in df_copy.columns}
              
```

```diff
                  self.logger.info(f"列名映射成功: {list(active_map.keys())} -> employee_id")
              elif 'employee_id' not in df_copy.columns:
-                 raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号)")
+                 raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号/编号)")
  
              # 1b. Clean all other column names
```

---



---

我已经将“编号”添加为可接受的员工ID列名，并更新了相应的错误提示信息。

现在，程序应该能够正确识别导入的Excel文件中的ID列了。请您重新运行程序并尝试导入数据，看看问题是否已经解决。

---

```bash
python .\main.py
```

---

_**User**_

又出错，请看图片
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-20 22:38:08.365 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-20 22:38:08.370 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-20 22:38:08.371 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-20 22:38:08.375 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-20 22:38:08.376 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-20 22:38:08.378 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-20 22:38:08.385 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-20 22:38:08.391 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-20 22:38:10.153 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 22:38:10.198 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 22:38:10.199 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-20 22:38:10.206 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-20 22:38:10.208 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-20 22:38:10.248 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-20 22:38:10.271 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-20 22:38:10.272 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-20 22:38:10.277 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-20 22:38:10.287 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-20 22:38:10.297 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1185 | 核心管理器初始化完成。
2025-06-20 22:38:10.348 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-20 22:38:10.467 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-20 22:38:10.615 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-20 22:38:10.648 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-20 22:38:10.792 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:38:10.983 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:38:10.993 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-20 22:38:11.013 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-20 22:38:11.280 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-20 22:38:11.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-20 22:38:11.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-20 22:38:11.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:38:11.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:38:11.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.75ms
2025-06-20 22:38:11.300 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:38:11.372 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:232 | 控制面板按钮信号连接完成
2025-06-20 22:38:11.407 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1175 | 主窗口UI设置完成。
2025-06-20 22:38:11.409 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-20 22:38:11.412 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-20 22:38:11.413 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-20 22:38:11.415 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-20 22:38:11.416 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-20 22:38:11.417 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-20 22:38:11.418 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-20 22:38:11.420 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1205 | 所有信号和槽已连接。
2025-06-20 22:38:11.420 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:38:11.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:38:11.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:38:11.423 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.91ms
2025-06-20 22:38:11.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:38:11.426 | INFO     | src.gui.prototype.prototype_main_window:__init__:1139 | 原型主窗口初始化完成
2025-06-20 22:38:12.143 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-20 22:38:12.341 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 22:38:12.344 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 22:38:12.374 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 22:38:12.500 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 22:38:12.556 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:38:12.615 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-20 22:38:17.049 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:38:17.071 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:38:17.079 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 22:38:17.105 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 22:38:17.118 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 22:38:17.123 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:38:17.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:38:17.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:38:17.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.90ms
2025-06-20 22:38:17.130 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:38:17.888 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 22:38:17.906 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:38:17.907 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 22:38:17.907 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 22:38:17.910 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:763 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 22:38:17.911 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:777 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 22:38:17.933 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 22:38:17.943 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 22:38:17.944 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 22:38:17.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 22:38:17.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 6.36ms
2025-06-20 22:38:17.953 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 22:38:19.219 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:249 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-20 22:38:19.223 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1215 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 退休人员。
打开导入对话框。
2025-06-20 22:38:19.326 | INFO     | src.gui.dialogs:__init__:49 | 数据导入对话框初始化完成。
2025-06-20 22:38:29.976 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-20 22:38:40.427 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 22:38:43.140 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 22:38:51.257 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 22:38:51.258 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 22:38:51.260 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-20 22:38:51.261 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-20 22:38:51.532 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-20 22:38:51.552 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-20 22:38:51.558 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.f
loat64(33607.14625)}
2025-06-20 22:38:51.560 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-20 22:38:51.562 | INFO     | src.modules.data_import.data_validator:_generate_default_rules:446 | 生成默认验证规则: 21个字段
2025-06-20 22:38:51.563 | INFO     | src.modules.data_import.data_validator:validate_dataframe:133 | 开始验证数据: 13行 x 21列
2025-06-20 22:38:51.574 | INFO     | src.modules.data_import.data_validator:validate_dataframe:142 | 数据验证完成: 0个错误
2025-06-20 22:38:53.032 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:1236 | 接收到导入数据: 13 行, 来源: 未知来源
2025-06-20 22:38:53.034 | INFO     | src.gui.prototype.prototype_main_window:set_data:299 | 通过公共接口设置新的表格数据: 13 行
2025-06-20 22:38:53.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 13
2025-06-20 22:38:53.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-20 22:38:53.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 21 列
2025-06-20 22:38:53.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 52.72ms
2025-06-20 22:38:53.106 | INFO     | src.gui.prototype.prototype_main_window:_save_to_database:1293 | 准备将 13 条数据保存到表: salary_data_2025_05_pension_employees
2025-06-20 22:38:53.113 | ERROR    | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:742 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中
缺少必须的员工ID列 (工号/职工编号/编号)
2025-06-20 22:38:53.115 | ERROR    | src.gui.prototype.prototype_main_window:_execute_save:1310 | 异步保存数据到表 salary_data_2025_05_pension_employees 失败: cannot unpack non-i
terable bool object
2025-06-20 22:38:53.181 | ERROR    | src.gui.prototype.prototype_main_window:_on_save_finished:1323 | 保存数据到表 salary_data_2025_05_pension_employees 失败: cannot unpack non-i
terable bool object
2025-06-20 22:39:28.551 | INFO     | __main__:main:296 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_



---

您好，又出现新的错误了。

根据您提供的截图和日志，这次的错误是 `cannot unpack non-iterable bool object`。

日志分析：
1.  `ERROR | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:742 | 保存数据到表 salary_data_2025_05_pension_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号/编号)`
    - 这说明即使我上次增加了“编号”作为可能的ID列，程序在导入的Excel中依然没有找到任何一个匹配的列（工号、职工编号、编号）。这很可能是问题的根源。
2.  `ERROR | src.gui.prototype.prototype_main_window:_execute_save:1310 | 异步保存数据到表 salary_data_2025_05_pension_employees 失败: cannot unpack non-iterable bool object`
    - 这个 `TypeError` 表明，一个函数返回了单个布尔值（`False`），但调用它的代码期望得到一个可以解包的元组（比如 `success, message`）。

综合来看，`dynamic_table_manager.py` 中的 `save_dataframe_to_table` 因为找不到ID列而执行失败，返回了 `False`。而 `prototype_main_window.py` 中的 `_execute_save` 方法没有正确处理这个 `False` 返回值，导致了后续的 `TypeError`。

为了解决这个问题，我将分两步进行：
1.  修改 `dynamic_table_manager.py`，使其在保存失败时返回一个包含错误信息的元组，而不仅仅是 `False`。
2.  修改 `prototype_main_window.py`，以正确处理这个新的返回值格式。

首先，我需要找到 `prototype_main_window.py` 文件。

---

<details>
            <summary>Searched codebase "prototype_main_window.py" • **3** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

好的，文件已找到。

现在，我将修改 `dynamic_table_manager.py` 和 `prototype_main_window.py` 这两个文件。

**第一步：修改 `src/modules/data_storage/dynamic_table_manager.py`**

我将修改 `save_dataframe_to_table` 方法，使其在失败时返回 `(False, error_message)`。

---



---

看来模型未能应用第一次的修改。我将重新尝试修改 `src/modules/data_storage/dynamic_table_manager.py`。

---

```diff
          将DataFrame数据安全地保存到指定表。
          此方法是事务性的，并包含完整的错误处理。
+         Returns:
+             Tuple[bool, str]: A tuple containing a boolean success flag 
+                               and a message string.
          """
          if df is None or df.empty:
              self.logger.warning("输入数据为空，无法保存。")
-             return False, "输入数据为空"
+             return False, "输入数据为空，无法保存。"
  
          conn = None
```

```diff
              conn.execute("COMMIT")
              self.logger.info(f"成功向表 {table_name} 保存 {len(data)} 条数据。")
-             return True, f"成功保存 {len(data)} 条数据"
+             return True, f"成功向表 {table_name} 保存 {len(data)} 条数据。"
  
          except Exception as e:
```

```diff
              else:
                  self.logger.error(f"保存数据到表 {table_name} 失败: {e}", exc_info=True)
-             return False, str(e)
+             return False, f"保存数据到表 {table_name} 失败: {e}"
          finally:
              if conn:
```

---

**第二步：修改 `src/gui/prototype/prototype_main_window.py`**

现在 `save_dataframe_to_table` 返回的是元组，我需要修改 `_execute_save` 方法来正确处理这个元组。我将阅读该文件以了解其结构。

---

Read file: src/gui/prototype/prototype_main_window.py

---

我已经阅读了 `prototype_main_window.py` 的相关代码。`_execute_save` 方法目前已经能处理元组返回了，但为了和 `_on_save_finished` 的期望输入（一个字典）保持一致，我会稍微调整它，确保在任何情况下都返回一个字典。

同时，我也会修改 `save_dataframe_to_table` 方法，使其在数据为空时返回一个更明确的错误信息。

**第一步：再次修改 `dynamic_table_manager.py`**

---



---

模型未能应用修改，我再试一次。

---



---

看起来对 `dynamic_table_manager.py` 的修改没有被正确应用。我会跳过这一步，直接修改 `prototype_main_window.py`，因为这里的逻辑问题更直接。

**第二步：修改 `prototype_main_window.py`**

我将修改 `_execute_save` 方法以确保它总是返回一个字典，从而避免 `TypeError`。

---

```diff
          """在工作线程中执行实际的数据库保存操作，并返回结果。"""
          try:
-             success, error_message = self.dynamic_table_manager.save_dataframe_to_table(df, table_name)
+             success, message = self.dynamic_table_manager.save_dataframe_to_table(df, table_name)
              if success:
-                 return {"success": True, "table_name": table_name}
+                 return {"success": True, "table_name": table_name, "message": message}
              else:
-                 return {"success": False, "table_name": table_name, "error": error_message or "未知数据库错误"}
+                 return {"success": False, "table_name": table_name, "error": message}
          except Exception as e:
+             # This will catch cases where save_dataframe_to_table might not return a tuple
+             # For example if it returns a single boolean False
              self.logger.error(f"异步保存数据到表 {table_name} 失败: {e}", exc_info=True)
-             return {"success": False, "table_name": table_name, "error": str(e)}
+             error_message = str(e) if str(e) else "发生未知异常"
+             # Attempt to unpack if possible, otherwise treat as a single value
+             try:
+                 success, msg = e
+                 error_message = msg
+             except (TypeError, ValueError):
+                 pass
+             return {"success": False, "table_name": table_name, "error": error_message}
  
      @pyqtSlot(dict)
```

---

我已经修改了 `_execute_save` 方法，使其能够更健壮地处理错误。

这次的修改解决了 `cannot unpack non-iterable bool object` 的问题。现在，当保存失败时，您将看到一个更详细的错误信息，而不是程序崩溃。

根本原因——“数据中缺少必须的员工ID列”——仍然存在。**这意味着您尝试导入的退休人员Excel文件中，包含员工唯一标识的列名不是“工号”、“职工编号”或“编号”。**

请您检查一下该Excel文件，确认那一列到底叫什么名字，然后告诉我，我再来更新代码以识别正确的列名。

现在，您可以重新运行程序，导入操作不会再导致程序崩溃，而是会弹出一个内容更详细的错误提示。

---

```bash
python .\main.py
```

---



---

_**User**_

有报错了，请看图片
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-20 23:06:40.368 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-20 23:06:40.374 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-20 23:06:40.375 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-20 23:06:40.376 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-20 23:06:40.401 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-20 23:06:40.403 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-20 23:06:40.403 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-20 23:06:40.404 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-20 23:06:42.412 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 23:06:42.415 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 23:06:42.418 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-20 23:06:42.419 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-20 23:06:42.420 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-20 23:06:42.493 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-20 23:06:42.507 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-20 23:06:42.507 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-20 23:06:42.518 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-20 23:06:42.519 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-20 23:06:42.521 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1185 | 核心管理器初始化完成。
2025-06-20 23:06:42.609 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-20 23:06:42.611 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-20 23:06:42.716 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-20 23:06:42.729 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-20 23:06:42.813 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:06:42.827 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:06:42.832 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-20 23:06:42.854 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-20 23:06:43.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-20 23:06:43.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-20 23:06:43.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-20 23:06:43.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:06:43.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:06:43.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.91ms
2025-06-20 23:06:43.104 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:06:43.172 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:232 | 控制面板按钮信号连接完成
2025-06-20 23:06:43.209 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1175 | 主窗口UI设置完成。
2025-06-20 23:06:43.210 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-20 23:06:43.213 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-20 23:06:43.214 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-20 23:06:43.215 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-20 23:06:43.216 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-20 23:06:43.216 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-20 23:06:43.220 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-20 23:06:43.221 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1205 | 所有信号和槽已连接。
2025-06-20 23:06:43.221 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:06:43.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:06:43.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:06:43.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.96ms
2025-06-20 23:06:43.265 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:06:43.267 | INFO     | src.gui.prototype.prototype_main_window:__init__:1139 | 原型主窗口初始化完成
2025-06-20 23:06:44.008 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-20 23:06:44.384 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 23:06:44.386 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 23:06:44.602 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 23:06:44.604 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 23:06:44.631 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:06:44.682 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-20 23:06:55.640 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:06:55.640 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:06:55.655 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:06:55.669 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_a_grade_employees
2025-06-20 23:06:55.685 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_a_grade_employees
2025-06-20 23:06:55.712 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:06:55.713 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:06:55.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:06:55.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.97ms
2025-06-20 23:06:55.716 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:06:57.138 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:06:57.140 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:06:57.142 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:06:57.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 23:06:57.145 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:766 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 23:06:57.146 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:780 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 23:06:57.188 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 23:06:57.191 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:06:57.192 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:06:57.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:06:57.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.27ms
2025-06-20 23:06:57.196 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:07:00.454 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:07:00.464 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:07:00.470 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:07:00.470 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 23:07:00.482 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 23:07:00.484 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:07:00.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:07:00.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:07:00.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 9.90ms
2025-06-20 23:07:00.716 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:07:01.850 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:07:01.859 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 23:07:01.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 23:07:01.861 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_active_employees
2025-06-20 23:07:01.865 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_active_employees
2025-06-20 23:07:01.866 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:07:01.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:07:01.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:07:01.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.04ms
2025-06-20 23:07:01.874 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:07:05.696 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:07:05.697 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:07:05.698 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:07:05.699 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 23:07:05.701 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 23:07:05.702 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:07:05.703 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:07:05.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:07:05.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 18.06ms
2025-06-20 23:07:05.724 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:07:07.875 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:249 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-20 23:07:07.876 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1215 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 离休人员。
打开导入对话框。
2025-06-20 23:07:07.983 | INFO     | src.gui.dialogs:__init__:49 | 数据导入对话框初始化完成。
2025-06-20 23:07:17.144 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-20 23:07:32.372 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 16列, 3行
2025-06-20 23:07:34.701 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 16列, 3行
2025-06-20 23:07:45.563 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 23:07:45.564 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 23:07:45.564 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-20 23:07:45.570 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-20 23:07:45.810 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-20 23:07:45.812 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-20 23:07:45.851 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float6
4(34405.100000000006)}
2025-06-20 23:07:45.854 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-20 23:07:45.856 | INFO     | src.modules.data_import.data_validator:_generate_default_rules:446 | 生成默认验证规则: 12个字段
2025-06-20 23:07:45.856 | INFO     | src.modules.data_import.data_validator:validate_dataframe:133 | 开始验证数据: 2行 x 12列
2025-06-20 23:07:45.861 | INFO     | src.modules.data_import.data_validator:validate_dataframe:142 | 数据验证完成: 0个错误
2025-06-20 23:07:48.720 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:1236 | 接收到导入数据: 2 行, 来源: 未知来源
2025-06-20 23:07:48.731 | INFO     | src.gui.prototype.prototype_main_window:set_data:299 | 通过公共接口设置新的表格数据: 2 行
2025-06-20 23:07:48.735 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2508 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-20 23:07:48.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 2
2025-06-20 23:07:48.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-20 23:07:48.741 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 2 行, 12 列
2025-06-20 23:07:48.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 35.04ms
2025-06-20 23:07:48.772 | INFO     | src.gui.prototype.prototype_main_window:_save_to_database:1293 | 准备将 2 条数据保存到表: salary_data_2025_05_retired_employees
2025-06-20 23:07:48.845 | ERROR    | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:745 | 保存数据到表 salary_data_2025_05_retired_employees 失败: 数据中
缺少必须的员工ID列 (工号/职工编号/编号)
2025-06-20 23:07:48.886 | ERROR    | src.gui.prototype.prototype_main_window:_on_save_finished:1332 | 保存数据到表 salary_data_2025_05_retired_employees 失败: 保存数据到表 salary
_data_2025_05_retired_employees 失败: 数据中缺少必须的员工ID列 (工号/职工编号/编号)
2025-06-20 23:09:25.778 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:09:25.779 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:09:25.780 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:09:25.781 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 23:09:25.784 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:766 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 23:09:25.785 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:780 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 23:09:25.788 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 23:09:25.791 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:09:25.792 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:09:25.794 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:09:25.803 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 10.98ms
2025-06-20 23:09:25.822 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:09:26.806 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:09:26.817 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:09:26.818 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:09:26.818 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 23:09:26.828 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 23:09:26.829 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:09:26.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:09:26.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:09:26.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 2.01ms
2025-06-20 23:09:26.839 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:09:27.924 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:09:27.926 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 23:09:27.930 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_active_employees
2025-06-20 23:09:27.930 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 23:09:27.931 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_active_employees
2025-06-20 23:09:27.933 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:09:27.933 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:09:27.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:09:27.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 1.01ms
2025-06-20 23:09:27.936 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:09:28.952 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:09:28.954 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:09:28.955 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:09:28.955 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_a_grade_employees
2025-06-20 23:09:28.959 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_a_grade_employees
2025-06-20 23:09:28.960 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:09:28.961 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:09:28.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:09:28.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 9.18ms
2025-06-20 23:09:28.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:09:29.742 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:09:29.743 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:09:29.745 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:09:29.745 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 23:09:29.748 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:766 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 23:09:29.749 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:780 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 23:09:29.750 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 23:09:29.766 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:09:29.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:09:29.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:09:29.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 1.38ms
2025-06-20 23:09:29.770 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:09:32.432 | INFO     | __main__:main:296 | 应用程序正常退出
应用程序已退出

在excel文档sheet中工号对应的是“人员代码”

---

_**Assistant**_



---

好的，非常感谢您提供这个关键信息！

问题果然出在ID列的名称上。程序之前只认识“工号”、“职工编号”和“编号”，不知道“人员代码”也是合法的员工ID。

我现在就来更新代码，让它能够识别“人员代码”。

我将修改 `src/modules/data_storage/dynamic_table_manager.py` 文件。

---

```diff
              
              # 1a. Standardize employee_id column
-             employee_id_map = {"工号": "employee_id", "职工编号": "employee_id", "编号": "employee_id"}
+             employee_id_map = {"工号": "employee_id", "职工编号": "employee_id", "编号": "employee_id", "人员代码": "employee_id"}
              active_map = {k: v for k, v in employee_id_map.items() if k in df_copy.columns}
              
```

```diff
                  self.logger.info(f"列名映射成功: {list(active_map.keys())} -> employee_id")
              elif 'employee_id' not in df_copy.columns:
-                 raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号/编号)")
+                 raise ValueError("数据中缺少必须的员工ID列 (工号/职工编号/编号/人员代码)")
  
              # 1b. Clean all other column names
```

---

我已经更新了代码，现在系统能够正确识别名为“人员代码”的列了。

请您重新运行程序并导入数据，这次应该可以成功保存了。

---

```bash
python .\main.py
```

---

_**User**_

又出错，请看图片
终端日志：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-20 23:20:09.444 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-20 23:20:09.449 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-20 23:20:09.450 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-20 23:20:09.451 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-20 23:20:09.454 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-20 23:20:09.457 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-20 23:20:09.458 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-20 23:20:09.459 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-20 23:20:11.519 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 23:20:11.521 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 23:20:11.524 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-20 23:20:11.532 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-20 23:20:11.533 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-20 23:20:11.578 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-20 23:20:11.579 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-20 23:20:11.581 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-20 23:20:11.589 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-20 23:20:11.590 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-20 23:20:11.591 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1185 | 核心管理器初始化完成。
2025-06-20 23:20:11.644 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-20 23:20:11.729 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-20 23:20:11.860 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-20 23:20:11.909 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-20 23:20:11.987 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:20:11.997 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:20:11.998 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-20 23:20:12.022 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-20 23:20:12.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-20 23:20:12.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-20 23:20:12.345 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-20 23:20:12.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:20:12.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:20:12.360 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 6.83ms
2025-06-20 23:20:12.362 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:20:12.428 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:232 | 控制面板按钮信号连接完成
2025-06-20 23:20:12.452 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1175 | 主窗口UI设置完成。
2025-06-20 23:20:12.453 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-20 23:20:12.514 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-20 23:20:12.530 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-20 23:20:12.534 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-20 23:20:12.535 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-20 23:20:12.537 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-20 23:20:12.538 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-20 23:20:12.540 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1205 | 所有信号和槽已连接。
2025-06-20 23:20:12.541 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:20:12.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:20:12.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:20:12.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 73.20ms
2025-06-20 23:20:12.617 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:20:12.618 | INFO     | src.gui.prototype.prototype_main_window:__init__:1139 | 原型主窗口初始化完成
2025-06-20 23:20:13.484 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-20 23:20:13.487 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 23:20:13.490 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 23:20:13.700 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 23:20:13.746 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 23:20:13.772 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:20:13.833 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-20 23:20:23.655 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:20:23.657 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:20:23.664 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:20:23.668 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 23:20:23.695 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 23:20:23.699 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:20:23.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:20:23.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:20:23.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.95ms
2025-06-20 23:20:23.704 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:20:26.623 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:249 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-20 23:20:26.624 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1215 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 离休人员。
打开导入对话框。
2025-06-20 23:20:26.727 | INFO     | src.gui.dialogs:__init__:49 | 数据导入对话框初始化完成。
2025-06-20 23:20:37.251 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-20 23:20:50.344 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 16列, 3行
2025-06-20 23:20:52.120 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 16列, 3行
2025-06-20 23:20:57.733 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 23:20:57.734 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 23:20:57.735 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-20 23:20:57.735 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-20 23:20:57.992 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-20 23:20:58.006 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-20 23:20:58.033 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float6
4(34405.100000000006)}
2025-06-20 23:20:58.035 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-20 23:20:58.036 | INFO     | src.modules.data_import.data_validator:_generate_default_rules:446 | 生成默认验证规则: 12个字段
2025-06-20 23:20:58.037 | INFO     | src.modules.data_import.data_validator:validate_dataframe:133 | 开始验证数据: 2行 x 12列
2025-06-20 23:20:58.040 | INFO     | src.modules.data_import.data_validator:validate_dataframe:142 | 数据验证完成: 0个错误
2025-06-20 23:21:00.032 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:1236 | 接收到导入数据: 2 行, 来源: 未知来源
2025-06-20 23:21:00.040 | INFO     | src.gui.prototype.prototype_main_window:set_data:299 | 通过公共接口设置新的表格数据: 2 行
2025-06-20 23:21:00.044 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2508 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-20 23:21:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 2
2025-06-20 23:21:00.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-20 23:21:00.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 2 行, 12 列
2025-06-20 23:21:00.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 32.22ms
2025-06-20 23:21:00.077 | INFO     | src.gui.prototype.prototype_main_window:_save_to_database:1293 | 准备将 2 条数据保存到表: salary_data_2025_05_retired_employees
2025-06-20 23:21:00.134 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:694 | 列名映射成功: ['人员代码'] -> employee_id
2025-06-20 23:21:00.187 | ERROR    | src.gui.prototype.prototype_main_window:_execute_save:1312 | 异步保存数据到表 salary_data_2025_05_retired_employees 失败: 'DatabaseManager' o
bject has no attribute 'release_connection'
2025-06-20 23:21:00.193 | ERROR    | src.gui.prototype.prototype_main_window:_on_save_finished:1332 | 保存数据到表 salary_data_2025_05_retired_employees 失败: 'DatabaseManager' o
bject has no attribute 'release_connection'
2025-06-20 23:21:42.434 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:21:42.436 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:21:42.437 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:21:42.438 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 23:21:42.441 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:766 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 23:21:42.446 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:780 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 23:21:42.448 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 23:21:42.449 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:21:42.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:21:42.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:21:42.460 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 10.20ms
2025-06-20 23:21:42.482 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:21:43.860 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:21:43.866 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:21:43.867 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:21:43.867 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_a_grade_employees
2025-06-20 23:21:43.869 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_a_grade_employees
2025-06-20 23:21:43.871 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:21:43.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:21:43.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:21:43.892 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 2.98ms
2025-06-20 23:21:43.894 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:21:45.034 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:21:45.037 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:21:45.039 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:21:45.039 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 23:21:45.041 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:766 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 23:21:45.043 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:780 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 23:21:45.045 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 23:21:45.046 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:21:45.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:21:45.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:21:45.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 0.98ms
2025-06-20 23:21:45.060 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:21:45.995 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:21:46.016 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:21:46.017 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-20 23:21:46.018 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_retired_employees
2025-06-20 23:21:46.020 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_retired_employees
2025-06-20 23:21:46.021 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:21:46.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:21:46.026 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:21:46.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 10.63ms
2025-06-20 23:21:46.035 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:21:46.957 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:21:46.959 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 23:21:46.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-20 23:21:46.960 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_active_employees
2025-06-20 23:21:46.962 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_active_employees
2025-06-20 23:21:46.963 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:21:46.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-20 23:21:46.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:21:46.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 14.45ms
2025-06-20 23:21:46.980 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:21:49.135 | INFO     | __main__:main:296 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_



---

好的，我们又遇到了一个新问题。

根据截图和日志，这次的错误是 `AttributeError: 'DatabaseManager' object has no attribute 'release_connection'`。

日志显示，在成功识别“人员代码”并准备保存数据时，程序在 `dynamic_table_manager.py` 的 `save_dataframe_to_table` 方法中尝试调用 `self.db_manager.release_connection()`，但 `DatabaseManager` 类中并不存在这个方法。

这是一个方法命名错误。我需要找到 `database_manager.py` 并查看其中管理数据库连接的正确方法名是什么，然后修正这个调用。

首先，我来查找 `database_manager.py` 文件。

---

<details>
            <summary>Searched codebase "database_manager.py" • **5** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

文件找到了。现在我来阅读 `src/modules/data_storage/database_manager.py` 的内容，以确定正确的连接释放方法。

---

Read file: src/modules/data_storage/database_manager.py

---

我已阅读 `database_manager.py` 文件。

问题很清楚，`DatabaseManager` 中用于关闭连接的方法是 `close_connection`，而不是 `release_connection`。这是一个简单的拼写错误。

我现在就去修改 `src/modules/data_storage/dynamic_table_manager.py`，将错误的 `release_connection` 调用更正为 `close_connection`。

---

```diff
          finally:
              if conn:
-                 self.db_manager.release_connection()
+                 self.db_manager.close_connection()
  
      @staticmethod
```

---

好了，我已经修正了 `dynamic_table_manager.py` 中的方法名错误。

现在，数据导入和保存的整个流程应该是通畅的了。请您最后一次尝试重新运行程序并导入数据。

---

```bash
python .\main.py
```

---

_**User**_

又出错了，请看图片
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-20 23:26:11.988 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-20 23:26:12.000 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-20 23:26:12.001 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-20 23:26:12.002 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-20 23:26:12.023 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-20 23:26:12.024 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-20 23:26:12.025 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-20 23:26:12.026 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-20 23:26:14.130 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 23:26:14.133 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-20 23:26:14.135 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-20 23:26:14.135 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-20 23:26:14.137 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-20 23:26:14.201 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-20 23:26:14.209 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-20 23:26:14.210 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-20 23:26:14.221 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-20 23:26:14.223 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-20 23:26:14.225 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1185 | 核心管理器初始化完成。
2025-06-20 23:26:14.319 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-20 23:26:14.331 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-20 23:26:14.482 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-20 23:26:14.491 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-20 23:26:14.581 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-20 23:26:14.615 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-20 23:26:14.809 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-20 23:26:14.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-20 23:26:14.825 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-20 23:26:14.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:26:14.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:26:14.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 9.79ms
2025-06-20 23:26:14.843 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:26:14.941 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:232 | 控制面板按钮信号连接完成
2025-06-20 23:26:14.979 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1175 | 主窗口UI设置完成。
2025-06-20 23:26:14.983 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-20 23:26:14.986 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-20 23:26:15.012 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-20 23:26:15.016 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-20 23:26:15.030 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-20 23:26:15.038 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-20 23:26:15.039 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-20 23:26:15.040 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1205 | 所有信号和槽已连接。
2025-06-20 23:26:15.040 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:26:15.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:26:15.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:26:15.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.97ms
2025-06-20 23:26:15.044 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:26:15.046 | INFO     | src.gui.prototype.prototype_main_window:__init__:1139 | 原型主窗口初始化完成
2025-06-20 23:26:15.831 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-20 23:26:15.926 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 23:26:15.932 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 23:26:15.943 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-20 23:26:15.951 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:903 | MainWorkspaceArea 响应式适配: sm
2025-06-20 23:26:15.981 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:26:16.036 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-20 23:26:33.251 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:26:33.254 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:26:33.259 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-20 23:26:33.266 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_pension_employees
2025-06-20 23:26:33.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:766 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-20 23:26:33.279 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:780 | 表 salary_data_2025_05_pension_employees 为空。
2025-06-20 23:26:33.301 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1278 | 数据库表不存在或为空: salary_data_2025_05_pension_employees
2025-06-20 23:26:33.317 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:26:33.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-20 23:26:33.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:26:33.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.97ms
2025-06-20 23:26:33.328 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:26:35.018 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:249 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-20 23:26:35.019 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1215 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 退休人员。
打开导入对话框。
2025-06-20 23:26:35.118 | INFO     | src.gui.dialogs:__init__:49 | 数据导入对话框初始化完成。
2025-06-20 23:26:45.128 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-20 23:26:58.531 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 23:27:01.551 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-20 23:27:07.848 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 23:27:07.849 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-20 23:27:07.857 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-20 23:27:07.859 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-20 23:27:08.134 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-20 23:27:08.148 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-20 23:27:08.184 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.f
loat64(33607.14625)}
2025-06-20 23:27:08.186 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-20 23:27:08.188 | INFO     | src.modules.data_import.data_validator:_generate_default_rules:446 | 生成默认验证规则: 21个字段
2025-06-20 23:27:08.189 | INFO     | src.modules.data_import.data_validator:validate_dataframe:133 | 开始验证数据: 13行 x 21列
2025-06-20 23:27:08.189 | INFO     | src.modules.data_import.data_validator:validate_dataframe:142 | 数据验证完成: 0个错误
2025-06-20 23:27:10.600 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:1236 | 接收到导入数据: 13 行, 来源: 未知来源
2025-06-20 23:27:10.602 | INFO     | src.gui.prototype.prototype_main_window:set_data:299 | 通过公共接口设置新的表格数据: 13 行
2025-06-20 23:27:10.606 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 13
2025-06-20 23:27:10.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-20 23:27:10.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 21 列
2025-06-20 23:27:10.660 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 53.81ms
2025-06-20 23:27:10.662 | INFO     | src.gui.prototype.prototype_main_window:_save_to_database:1293 | 准备将 13 条数据保存到表: salary_data_2025_05_pension_employees
2025-06-20 23:27:10.750 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:694 | 列名映射成功: ['人员代码'] -> employee_id
2025-06-20 23:27:10.881 | ERROR    | src.gui.prototype.prototype_main_window:_execute_save:1312 | 异步保存数据到表 salary_data_2025_05_pension_employees 失败: '_GeneratorContextM
anager' object has no attribute 'execute'
2025-06-20 23:27:10.883 | ERROR    | src.gui.prototype.prototype_main_window:_on_save_finished:1332 | 保存数据到表 salary_data_2025_05_pension_employees 失败: '_GeneratorContextM
anager' object has no attribute 'execute'
2025-06-20 23:27:30.839 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-20 23:27:30.850 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1251 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:27:30.852 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-20 23:27:30.852 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data:1268 | 开始从数据库加载数据: salary_data_2025_05_a_grade_employees
2025-06-20 23:27:30.864 | WARNING  | src.gui.prototype.prototype_main_window:_load_database_data:1272 | 数据库表不存在或为空: salary_data_2025_05_a_grade_employees
2025-06-20 23:27:30.866 | WARNING  | src.gui.prototype.prototype_main_window:set_data:295 | 尝试设置空数据，将显示提示信息。
2025-06-20 23:27:30.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 13
2025-06-20 23:27:30.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-20 23:27:30.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 13, 耗时: 19.56ms
2025-06-20 23:27:30.895 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:755 | 表格已初始化为空白状态，等待用户导入数据
2025-06-20 23:27:34.152 | INFO     | __main__:main:296 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_



---

