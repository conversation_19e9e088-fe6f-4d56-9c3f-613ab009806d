<!-- Generated by SpecStory -->

# 数据导入后未显示的问题 (2025-06-21 22:55:56)

_**User**_

在数据导入窗体中进行一系列导入操作后，点击“开始导入”按钮，提示导入成功，但是，在主界面列表区域没有显示数据，通过左侧数据导航进行切换，主界面列表区域也没有显示数据。
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-21 22:56:24.623 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-21 22:56:24.623 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-21 22:56:24.623 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-21 22:56:24.623 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-21 22:56:24.623 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-21 22:56:24.623 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-21 22:56:24.623 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-21 22:56:24.623 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-21 22:56:26.192 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 22:56:26.192 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 22:56:26.192 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-21 22:56:26.192 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-21 22:56:26.192 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-21 22:56:26.252 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-21 22:56:26.254 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-21 22:56:26.255 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-21 22:56:26.263 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-21 22:56:26.269 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-21 22:56:26.270 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1195 | 核心管理器初始化完成。
2025-06-21 22:56:26.312 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-21 22:56:26.314 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-21 22:56:26.401 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-21 22:56:26.412 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-21 22:56:26.482 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:56:26.490 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:56:26.491 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-21 22:56:26.513 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-21 22:56:26.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-21 22:56:26.679 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-21 22:56:26.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-21 22:56:26.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:56:26.689 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:56:26.694 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 5.84ms
2025-06-21 22:56:26.695 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:56:26.752 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:237 | 控制面板按钮信号连接完成
2025-06-21 22:56:26.777 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1185 | 主窗口UI设置完成。
2025-06-21 22:56:26.780 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-21 22:56:26.781 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-21 22:56:26.783 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-21 22:56:26.785 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-21 22:56:26.786 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-21 22:56:26.787 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-21 22:56:26.791 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-21 22:56:26.792 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1215 | 所有信号和槽已连接。
2025-06-21 22:56:26.794 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:1306 | 已加载 1 个表的字段映射信息
2025-06-21 22:56:26.829 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:56:26.831 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:56:26.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:56:26.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.94ms
2025-06-21 22:56:26.836 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:56:26.837 | INFO     | src.gui.prototype.prototype_main_window:__init__:1149 | 原型主窗口初始化完成
2025-06-21 22:56:27.326 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-21 22:56:27.336 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-21 22:56:27.339 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:910 | MainWorkspaceArea 响应式适配: sm
2025-06-21 22:56:27.346 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-21 22:56:27.347 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:910 | MainWorkspaceArea 响应式适配: sm
2025-06-21 22:56:27.389 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-21 22:56:27.543 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:56:42.432 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:56:42.434 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 22:56:42.437 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 22:56:42.441 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_a_grade_employees 尚未创建，请导入
数据后重试
2025-06-21 22:56:42.442 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:56:42.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:56:42.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:56:42.445 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.93ms
2025-06-21 22:56:42.452 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:56:43.103 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:56:43.104 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:56:43.106 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:56:43.106 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_pension_employees 尚未创建，请导入
数据后重试
2025-06-21 22:56:43.109 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:56:43.110 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:56:43.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:56:43.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.95ms
2025-06-21 22:56:43.114 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:56:44.619 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:254 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-21 22:56:44.620 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1225 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 退休人员。
打开导入对话框。
2025-06-21 22:56:44.624 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 22:56:44.625 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 22:56:44.626 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-21 22:56:44.626 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-21 22:56:44.628 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-21 22:56:44.633 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-21 22:56:44.633 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-21 22:56:44.635 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 22:56:44.638 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-21 22:56:44.639 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:56 | 多Sheet导入器初始化完成
2025-06-21 22:56:44.699 | INFO     | src.gui.dialogs:_init_field_mapping:1404 | 初始化字段映射表格: 17 个默认字段
2025-06-21 22:56:44.763 | INFO     | src.gui.dialogs:__init__:53 | 数据导入对话框初始化完成。
2025-06-21 22:56:53.599 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-21 22:57:15.017 | INFO     | src.gui.dialogs:_on_import_mode_changed:1558 | 切换到多Sheet导入模式
2025-06-21 22:57:15.017 | INFO     | src.gui.dialogs:_on_import_mode_changed:1558 | 切换到多Sheet导入模式
2025-06-21 22:57:26.168 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-21 22:57:36.376 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-21 22:57:37.938 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-21 22:57:59.486 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 22:57:59.486 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:158 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 22:57:59.486 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 22:57:59.878 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-21 22:57:59.878 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:169 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工
资表', 'A岗职工']
2025-06-21 22:57:59.878 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 22:57:59.878 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 22:57:59.878 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 22:58:00.085 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-21 22:58:00.101 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 22:58:00.116 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float6
4(34405.100000000006)}
2025-06-21 22:58:00.119 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-21 22:58:00.130 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:396 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-21 22:58:00.141 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:405 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-21 22:58:00.161 | WARNING  | src.modules.data_storage.dynamic_table_manager:create_salary_table:258 | 列名冲突，跳过自定义列: employee_name
2025-06-21 22:58:00.185 | ERROR    | src.modules.data_storage.database_manager:get_connection:186 | 数据库连接使用失败: UNIQUE constraint failed: table_metadata.table_name
2025-06-21 22:58:00.198 | ERROR    | src.modules.data_storage.database_manager:execute_update:328 | 更新执行失败: UNIQUE constraint failed: table_metadata.table_name
2025-06-21 22:58:00.199 | ERROR    | src.modules.data_storage.dynamic_table_manager:_record_table_metadata:437 | 记录表元信息失败: UNIQUE constraint failed: table_metadata.table_
name
2025-06-21 22:58:00.200 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_table:275 | 工资数据表创建成功: salary_data_2025_05
2025-06-21 22:58:00.204 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['人员代码'] -> ['employee_id']
2025-06-21 22:58:00.207 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 basic_info_2025_05 不存在，将根据模板创建...
2025-06-21 22:58:00.210 | WARNING  | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:793 | 表 basic_info_2025_05 无法匹配具体模板，使用默认salary_data模板
2025-06-21 22:58:00.237 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 2 条数据。
2025-06-21 22:58:00.257 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 22:58:00.259 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 22:58:00.260 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 22:58:00.490 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-21 22:58:00.505 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 22:58:00.505 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.f
loat64(33607.14625)}
2025-06-21 22:58:00.505 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-21 22:58:00.505 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:396 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-21 22:58:00.505 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:405 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-21 22:58:00.505 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_table:242 | 表 salary_data_2025_05 已存在
2025-06-21 22:58:00.505 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['人员代码', '津贴', '应发工资'] -> ['employee_id'
, 'allowance', 'total_salary']
2025-06-21 22:58:00.536 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 13 条数据。
2025-06-21 22:58:00.540 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 22:58:00.541 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 22:58:00.542 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 22:58:00.822 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-21 22:58:00.822 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 22:58:00.822 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3
971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(1
4240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.
0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.in
t64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-21 22:58:00.838 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-21 22:58:00.853 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:405 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-21 22:58:00.861 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_table:242 | 表 salary_data_2025_05 已存在
2025-06-21 22:58:00.864 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['津贴', '应发工资'] -> ['allowance', 'total_salar
y']
2025-06-21 22:58:00.904 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 1396 条数据。
2025-06-21 22:58:00.914 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 22:58:00.915 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 22:58:00.916 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 22:58:01.137 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-21 22:58:01.153 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 22:58:01.153 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)
}
2025-06-21 22:58:01.153 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-21 22:58:01.153 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:405 | Sheet A岗职工 数据处理完成: 62 行
2025-06-21 22:58:01.168 | INFO     | src.modules.data_storage.dynamic_table_manager:create_salary_table:242 | 表 salary_data_2025_05 已存在
2025-06-21 22:58:01.173 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['津贴', '应发工资'] -> ['allowance', 'total_salar
y']
2025-06-21 22:58:01.197 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 62 条数据。
2025-06-21 22:58:01.200 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:188 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'succe
ss': True, 'message': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 b
asic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396
 条数据。', 'table_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_inf
o_2025_05', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 2}
, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls'
, 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'
]}
2025-06-21 22:58:01.206 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1119 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'messag
e': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05
保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396 条数据。', 'table
_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_info_2025_05', 'recor
ds': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 2}, 'file_info': {'p
ath': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.165
0390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode':
'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-05', 'data_description': '2025年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2
025年5月份正式工工资（报财务)  最终版.xls'}
2025-06-21 22:58:14.243 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:58:14.243 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 22:58:14.243 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 22:58:14.243 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_a_grade_employees 尚未创建，请导入
数据后重试
2025-06-21 22:58:14.274 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:58:14.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:58:14.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:58:14.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-21 22:58:14.274 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_pension_employees 尚未创建，请导入
数据后重试
2025-06-21 22:58:16.233 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:58:16.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-21 22:58:16.248 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_retired_employees 尚未创建，请导入
数据后重试
2025-06-21 22:58:17.035 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-21 22:58:17.035 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:58:19.255 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:58:19.259 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-21 22:58:19.260 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-21 22:58:19.261 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_active_employees 尚未创建，请导入
数据后重试
2025-06-21 22:58:19.263 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:58:19.264 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:58:19.265 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:58:19.266 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.98ms
2025-06-21 22:58:19.268 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:58:21.208 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 22:58:21.210 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:58:21.211 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 22:58:21.211 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_pension_employees 尚未创建，请导入
数据后重试
2025-06-21 22:58:21.214 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 22:58:21.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 22:58:21.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 22:58:21.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.95ms
2025-06-21 22:58:21.219 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 22:58:27.253 | INFO     | __main__:main:296 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_

根据终端日志分析，我发现了问题的核心：数据导入成功了，但是导入的数据存储在表 `basic_info_2025_05` 中，而主界面在查找数据时却在寻找其他表名（如 `salary_data_2025_05_a_grade_employees`、`salary_data_2025_05_pension_employees` 等）。

让我先查看相关代码来确认问题的具体位置：

---

<details>
            <summary>Searched codebase "_load_database_data_with_mapping 数据表尚未创建" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/prototype_main_window.py` | L1377-1404 |
| `src/modules/data_storage/database_manager.py` | L91-131 |
| `src/gui/prototype/prototype_main_window.py` | L1265-1313 |
| `src/modules/change_detection/baseline_manager.py` | L369-406 |
| `src/gui/prototype/prototype_main_window.py` | L1313-1343 |
| `src/gui/prototype/prototype_main_window.py` | L1404-1438 |
| `src/modules/data_storage/dynamic_table_manager.py` | L58-118 |
| `src/gui/prototype/prototype_main_window.py` | L1343-1377 |
| `docs/draft/原型-误解/database_design.md` | L364-415 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L543-586 |
| `src/modules/data_storage/database_manager.py` | L131-160 |
| `src/modules/data_storage/dynamic_table_manager.py` | L1-47 |
| `docs/draft/原型-误解/database_design.md` | L120-159 |
| `src/modules/data_storage/database_manager.py` | L43-91 |
| `memory_bank/plan-technical-design.md` | L527-584 |
| `src/gui/prototype/prototype_main_window.py` | L1472-1504 |
| `src/modules/change_detection/baseline_manager.py` | L91-122 |
| `src/modules/data_storage/dynamic_table_manager.py` | L386-437 |
| `logs/salary_system.log` | L456-467 |
| `memory_bank/plan-technical-design.md` | L584-634 |
| `logs/salary_system.log` | L1559-1570 |
| `src/gui/dialogs.py` | L2141-2176 |
| `docs/draft/原型-误解/database_design.md` | L159-202 |
| `src/gui/dialogs.py` | L985-1012 |
| `src/modules/data_storage/database_manager.py` | L1-43 |

</details>

---

现在让我查看多Sheet导入器的相关代码，了解数据是如何存储的：

---

<details>
            <summary>Searched codebase "multi_sheet_importer basic_info_2025_05 保存数据" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/modules/data_import/multi_sheet_importer.py` | L258-279 |
| `src/modules/data_import/multi_sheet_importer.py` | L329-358 |
| `src/modules/data_import/multi_sheet_importer.py` | L215-258 |
| `examples/multi_sheet_import_example.py` | L49-68 |
| `src/modules/data_import/multi_sheet_importer.py` | L1-56 |
| `src/modules/data_import/multi_sheet_importer.py` | L78-120 |
| `src/modules/data_import/multi_sheet_importer.py` | L141-189 |
| `src/modules/data_import/multi_sheet_importer.py` | L189-215 |
| `src/modules/data_import/multi_sheet_importer.py` | L358-405 |
| `src/modules/data_import/multi_sheet_importer.py` | L279-329 |
| `src/modules/data_import/multi_sheet_importer.py` | L405-457 |
| `src/gui/dialogs.py` | L1082-1125 |
| `src/modules/data_import/multi_sheet_importer.py` | L56-78 |
| `src/modules/data_import/multi_sheet_importer.py` | L589-645 |
| `examples/multi_sheet_import_example.py` | L102-121 |
| `logs/salary_system.log` | L1723-1734 |
| `examples/multi_sheet_import_example.py` | L68-102 |
| `logs/salary_system.log` | L1025-1037 |
| `logs/salary_system.log` | L1763-1768 |
| `src/gui/dialogs.py` | L1182-1211 |
| `src/gui/dialogs.py` | L1211-1252 |
| `src/modules/data_import/multi_sheet_importer.py` | L457-507 |
| `src/gui/dialogs.py` | L31-53 |
| `src/modules/data_import/multi_sheet_importer.py` | L551-589 |
| `logs/salary_system.log` | L1752-1763 |

</details>

---

现在我已经找到了问题的根本原因。让我查看动态表管理器中 `save_dataframe_to_table` 方法的具体实现：

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---

现在我明白了问题所在。让我查看主界面的 `_generate_table_name_from_path` 方法，看看它是如何生成表名的：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我完全明白了问题的原因。让我总结一下：

## 问题分析

**根本原因：表名不匹配**

1. **数据导入时的表名**：多Sheet导入器将所有数据都保存到了 `basic_info_2025_05` 表中
2. **主界面查找的表名**：主界面根据导航路径生成的表名是 `salary_data_2025_05_a_grade_employees`、`salary_data_2025_05_pension_employees` 等

**具体问题**：
- 多Sheet导入器在分离策略下，应该为每个Sheet创建对应的表，但由于配置问题，所有数据都被保存到了 `basic_info_2025_05` 这个默认表中
- 主界面的 `_generate_table_name_from_path` 方法生成的表名与实际存储的表名不匹配

## 解决方案

我需要修复多Sheet导入器中的表名生成逻辑，使其与主界面期望的表名格式一致。

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

现在我需要查看 `_get_sheet_data_type` 方法的实现：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

现在我明白了问题所在。让我检查一下动态表管理器的 `save_dataframe_to_table` 方法中的表名处理逻辑：

---

<details>
            <summary>Searched codebase "save_dataframe_to_table basic_info 表名处理" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/modules/data_storage/dynamic_table_manager.py` | L873-923 |
| `src/modules/data_storage/dynamic_table_manager.py` | L809-838 |
| `src/modules/data_storage/dynamic_table_manager.py` | L704-752 |
| `src/modules/data_storage/dynamic_table_manager.py` | L752-770 |
| `src/modules/data_storage/dynamic_table_manager.py` | L838-873 |
| `src/modules/data_storage/dynamic_table_manager.py` | L770-809 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L511-543 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L543-586 |
| `src/gui/prototype/prototype_main_window.py` | L1438-1472 |
| `src/modules/data_import/multi_sheet_importer.py` | L258-279 |
| `src/modules/data_storage/dynamic_table_manager.py` | L386-437 |
| `src/modules/data_storage/dynamic_table_manager.py` | L579-617 |
| `src/gui/prototype/prototype_main_window.py` | L1313-1343 |
| `src/modules/data_storage/dynamic_table_manager.py` | L270-323 |
| `src/gui/prototype/prototype_main_window.py` | L1343-1377 |
| `src/modules/data_import/multi_sheet_importer.py` | L329-358 |
| `src/modules/data_storage/dynamic_table_manager.py` | L219-270 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L698-745 |
| `memory_bank/plan-technical-design.md` | L527-584 |
| `src/gui/prototype/prototype_main_window.py` | L1472-1504 |
| `src/gui/prototype/prototype_main_window.py` | L1265-1313 |
| `src/modules/data_storage/dynamic_table_manager.py` | L170-200 |
| `src/gui/dialogs.py` | L1483-1513 |
| `src/gui/prototype/prototype_main_window.py` | L1377-1404 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L663-698 |

</details>

---

现在我明白了问题的根本原因。从日志和代码分析可以看出：

1. **多Sheet导入器**使用了 `_get_sheet_data_type` 方法正确识别了Sheet类型（如`salary_data_retired_employees`、`salary_data_pension_employees`等）
2. **表名生成**在 `_import_separate_strategy` 方法中是正确的，应该生成如 `salary_data_2025_05_retired_employees` 这样的表名
3. **但是**在 `save_dataframe_to_table` 方法中，由于某种原因，最终所有数据都被保存到了 `basic_info_2025_05` 表

让我仔细检查一下导入过程中实际传递给 `save_dataframe_to_table` 的表名：

---

<details>
            <summary>Grep search for "basic_info_2025_05" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1730 | `2025-06-21 22:58:00.207 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 basic_info_2025_05 不存在，将根据模板创建...` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1731 | `2025-06-21 22:58:00.210 | WARNING  | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:793 | 表 basic_info_2025_05 无法匹配具体模板，使用默认salary_data模板` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1732 | `2025-06-21 22:58:00.237 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 2 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1744 | `2025-06-21 22:58:00.536 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 13 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1755 | `2025-06-21 22:58:00.904 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 1396 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1766 | `2025-06-21 22:58:01.197 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 62 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1767 | `⟪ 154 characters skipped ⟫资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396 条数据。', 'table_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_info_2025_05', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 2}, 'file_info'` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1768 | `⟪ 136 characters skipped ⟫资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396 条数据。', 'table_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_info_2025_05', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 2}, 'file_info'` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L140 | `2025-06-21 22:58:00.207 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 basic_info_2025_05 不存在，将根据模板创建...` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L141 | `2025-06-21 22:58:00.210 | WARNING  | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:793 | 表 basic_info_2025_05 无法匹配具体模板，使用默认salary_data模板` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L142 | `2025-06-21 22:58:00.237 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 2 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L157 | `2025-06-21 22:58:00.536 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 13 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L174 | `2025-06-21 22:58:00.904 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 1396 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L188 | `2025-06-21 22:58:01.197 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 basic_info_2025_05 保存 62 条数据。` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L190 | `ss': True, 'message': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 b` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L191 | `asic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L192 | `条数据。', 'table_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_inf` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L198 | `e': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L199 | `保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396 条数据。', 'table` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L200 | `_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_info_2025_05', 'recor` |

</details>

---

这说明问题出现在多Sheet导入器中，所有的数据都被保存到了 `basic_info_2025_05` 表，而不是期望的分离表名。让我检查一下是否在某个地方表名被重写了。

从日志可以看出，问题出现在多Sheet导入器的 `_import_separate_strategy` 方法中。让我检查一下这个方法中是否有问题：

---

<details>
            <summary>Grep search for "table_name.*basic_info" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1767 | `⟪ 186 characters skipped ⟫: '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'succe` |
| `file:///c%3A/test/salary_changes/salary_changes/logs/salary_system.log` | L1768 | `⟪ 168 characters skipped ⟫: '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'succe` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L190 | `ss': True, 'message': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 b` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L191 | `asic_info_2025_05 保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L192 | `条数据。', 'table_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_inf` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L198 | `e': '成功向表 basic_info_2025_05 保存 2 条数据。', 'table_name': 'basic_info_2025_05', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L199 | `保存 13 条数据。', 'table_name': 'basic_info_2025_05', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 1396 条数据。', 'table` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-21_14-55-%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%90%8E%E6%9C%AA%E6%98%BE%E7%A4%BA%E7%9A%84%E9%97%AE%E9%A2%98.md` | L200 | `_name': 'basic_info_2025_05', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 basic_info_2025_05 保存 62 条数据。', 'table_name': 'basic_info_2025_05', 'recor` |

</details>

---

现在我需要检查一下动态表管理器的 `save_dataframe_to_table` 方法，看看为什么传入的正确表名会被改成 `basic_info_2025_05`：

---

<details>
            <summary>Searched codebase "basic_info table_name 表名生成" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `memory_bank/plan-technical-design.md` | L470-527 |
| `memory_bank/plan-technical-design.md` | L527-584 |
| `src/modules/data_storage/dynamic_table_manager.py` | L809-838 |
| `docs/draft/原型-误解/database_design.md` | L159-202 |
| `src/modules/data_storage/dynamic_table_manager.py` | L170-200 |
| `memory_bank/plan-technical-design.md` | L410-470 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L543-586 |
| `src/modules/data_storage/dynamic_table_manager.py` | L386-437 |
| `docs/draft/原型-误解/database_design.md` | L75-120 |
| `docs/prd/数据库集成设计补充方案.md` | L78-106 |
| `src/modules/data_storage/dynamic_table_manager.py` | L219-270 |
| `docs/draft/原型-误解/database_design.md` | L120-159 |
| `src/modules/data_storage/dynamic_table_manager.py` | L118-142 |
| `src/modules/data_storage/database_manager.py` | L91-131 |
| `src/modules/data_storage/dynamic_table_manager.py` | L58-118 |
| `src/modules/data_storage/dynamic_table_manager.py` | L47-58 |
| `src/modules/data_storage/dynamic_table_manager.py` | L668-704 |
| `src/modules/data_storage/dynamic_table_manager.py` | L770-809 |
| `docs/draft/原型-误解/database_design.md` | L364-415 |
| `src/modules/data_storage/dynamic_table_manager.py` | L200-219 |
| `src/modules/data_storage/dynamic_table_manager.py` | L270-323 |
| `src/gui/prototype/prototype_main_window.py` | L1472-1504 |
| `docs/draft/月度工资异动处理系统详细设计方案-部分代码参考.md` | L511-543 |
| `logs/salary_system.log` | L1723-1734 |
| `src/gui/dialogs.py` | L2072-2110 |

</details>

---

现在我找到了问题的根源！从日志中可以看到：

1. 多Sheet导入器正确创建了 `salary_data_2025_05` 表
2. 但在调用 `save_dataframe_to_table` 时，传递的表名变成了 `basic_info_2025_05`

这说明问题出现在多Sheet导入器的 `_import_separate_strategy` 方法中。让我仔细检查这个方法的实现，特别是表名生成的部分：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

现在我发现了关键问题！在 `_import_separate_strategy` 方法中，有两个问题：

1. **表名生成正确**：`table_name = f"salary_data_{year}_{month:02d}_{employee_type}"` 这部分是正确的
2. **但是调用 `create_salary_table` 时传递的参数不对**：`self.table_manager.create_salary_table(f"{month:02d}", year, custom_columns)` 这里传递的是 `f"{month:02d}"` 而不是完整的表名

让我查看 `create_salary_table` 方法的实现，看看它是如何生成表名的：

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---

现在我明白了问题所在！从 `create_salary_table` 方法可以看出：

```python
table_name = f"salary_data_{year}_{month.replace('-', '_')}"
```

这个方法只会创建形如 `salary_data_2025_05` 的表名，而不会包含人员类型后缀（如 `_retired_employees`）。

但是在多Sheet导入器中，我们希望创建的表名是 `salary_data_2025_05_retired_employees` 这样的格式。

问题在于：
1. 多Sheet导入器调用 `create_salary_table(f"{month:02d}", year, custom_columns)` 创建了 `salary_data_2025_05` 表
2. 然后尝试将数据保存到 `salary_data_2025_05_retired_employees` 表
3. 但这个表不存在，所以 `save_dataframe_to_table` 方法尝试创建一个新表，但由于表名不匹配模板，最终使用了默认的 `basic_info` 前缀

## 解决方案

我需要修复多Sheet导入器中的表创建逻辑。有两种解决方案：

**方案1：修改 `create_salary_table` 方法，支持传入完整表名**
**方案2：直接在多Sheet导入器中使用 `save_dataframe_to_table` 的自动表创建功能**

我选择方案2，因为它更简单且不会影响现有的 `create_salary_table` 方法。

---



---

让我更精确地定位需要修改的代码行：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

感谢您提供的详细对话总结。我已经完全理解了这个数据导入问题的根本原因：**表名不匹配导致数据被保存到错误的表中**。

让我立即修复这个问题：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

我看到代码已经被修改过了！在第329行，代码已经正确地移除了 `create_salary_table` 调用，直接使用 `save_dataframe_to_table` 方法。这正是我们需要的修复。

让我检查一下这个修改是否完整和正确：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

很好！我看到代码已经正确修复了。让我再确认一下 `_get_sheet_data_type` 方法的实现，确保它能正确识别Sheet类型：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

完美！代码修复已经完成。让我总结一下修复的内容：

## 问题修复总结

### 修复内容
在 `src/modules/data_import/multi_sheet_importer.py` 文件的 `_import_separate_strategy` 方法中：

1. **移除了错误的表创建调用**：
   ```python
   # 移除了这行：
   # create_salary_table(f"{month:02d}", year, custom_columns)
   ```

2. **保留了正确的表名生成逻辑**（第314-324行）：
   ```python
   # 根据不同的数据类型生成不同的表名格式
   if sheet_type.startswith("salary_data_"):
       # 提取人员类型后缀
       employee_type = sheet_type.replace("salary_data_", "")
       table_name = f"salary_data_{year}_{month:02d}_{employee_type}"
   elif sheet_type == "salary_data":
       # 通用工资数据表
       table_name = f"salary_data_{year}_{month:02d}"
   else:
       # 其他类型保持原格式
       table_name = f"{sheet_type}_{year}_{month:02d}"
   ```

3. **直接使用自动表创建功能**（第327行）：
   ```python
   # 直接保存数据到表，让save_dataframe_to_table自动创建表
   success, message = self.table_manager.save_dataframe_to_table(processed_df, table_name)
   ```

### 修复原理
- **问题根因**：之前的代码先调用 `create_salary_table` 创建了通用的 `salary_data_2025_05` 表，然后试图将数据保存到具体的 `salary_data_2025_05_retired_employees` 等表中，但这些具体表并不存在
- **修复方案**：移除不必要的 `create_salary_table` 调用，直接使用 `save_dataframe_to_table` 的自动表创建功能，让系统根据正确的表名（包含人员类型后缀）自动创建对应的表

### 表名映射关系
修复后，系统将正确创建以下表名：
- `离休人员工资表` → `salary_data_2025_05_retired_employees`
- `退休人员工资表` → `salary_data_2025_05_pension_employees`  
- `全部在职人员工资表` → `salary_data_2025_05_active_employees`
- `A岗职工` → `salary_data_2025_05_a_grade_employees`

这些表名与主界面根据导航路径生成的表名完全匹配，解决了数据无法显示的问题。

### 验证Sheet类型识别
从 `_get_sheet_data_type` 方法（第526-556行）可以看到，系统能正确识别各种Sheet类型：
- 离休人员 → `salary_data_retired_employees`
- 退休人员 → `salary_data_pension_employees`
- A岗职工 → `salary_data_a_grade_employees`
- 在职人员 → `salary_data_active_employees`

## 测试建议
现在可以重新测试多Sheet导入功能：
1. 启动应用程序
2. 进行多Sheet Excel文件导入
3. 检查主界面左侧导航是否能正确切换并显示数据
4. 验证数据是否被保存到正确的表中

修复已完成，问题应该得到解决！

---

_**User**_

按上面修改后，再次重启系统进行数据导入的一系列操作，最后提示导入成功。经过在左侧数据导航进行切换，主界面列表展示出了数据，但是切换到“A岗职工”时，主界面列表展示区域没有显示数据。我记得导入数据时有个弹窗信息可供你参考，请看图片
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-21 23:34:25.578 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-21 23:34:25.578 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-21 23:34:25.578 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-21 23:34:25.578 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-21 23:34:25.594 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-21 23:34:25.594 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-21 23:34:25.594 | INFO     | __main__:setup_app_logging:202 | 月度工资异动处理系统 v1.0.0 启动
2025-06-21 23:34:25.594 | INFO     | __main__:main:266 | 应用程序启动中...
2025-06-21 23:34:27.242 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 23:34:27.351 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 23:34:27.351 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-21 23:34:27.367 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-21 23:34:27.367 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-21 23:34:27.477 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-21 23:34:27.488 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-21 23:34:27.489 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-21 23:34:27.498 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-21 23:34:27.498 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-21 23:34:27.498 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:1195 | 核心管理器初始化完成。
2025-06-21 23:34:27.601 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:148 | 导航状态加载成功
2025-06-21 23:34:27.604 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-21 23:34:27.683 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-21 23:34:27.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-21 23:34:27.763 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:34:27.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 23:34:27.777 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:654 | 恢复导航状态: 7个展开项
2025-06-21 23:34:27.795 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:436 | 增强导航面板初始化完成
2025-06-21 23:34:27.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-21 23:34:27.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-21 23:34:27.972 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-21 23:34:27.974 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 23:34:27.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 23:34:27.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.81ms
2025-06-21 23:34:27.984 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 23:34:28.062 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:237 | 控制面板按钮信号连接完成
2025-06-21 23:34:28.150 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:1185 | 主窗口UI设置完成。
2025-06-21 23:34:28.153 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: header_widget (MaterialHeaderWidget)
2025-06-21 23:34:28.155 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: navigation_panel (EnhancedNavigationPanel)
2025-06-21 23:34:28.157 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: main_workspace (MainWorkspaceArea)
2025-06-21 23:34:28.158 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: footer_widget (MaterialFooterWidget)
2025-06-21 23:34:28.160 | INFO     | src.gui.prototype.managers.communication_manager:register_component:151 | 注册组件: layout_manager (ResponsiveLayoutManager)
2025-06-21 23:34:28.162 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:197 | 开始建立组件连接
2025-06-21 23:34:28.163 | INFO     | src.gui.prototype.managers.communication_manager:setup_all_connections:206 | 所有组件连接建立完成
2025-06-21 23:34:28.165 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:1215 | 所有信号和槽已连接。
2025-06-21 23:34:28.167 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:1306 | 已加载 1 个表的字段映射信息
2025-06-21 23:34:28.168 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 23:34:28.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 23:34:28.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 23:34:28.175 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 5.83ms
2025-06-21 23:34:28.177 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 23:34:28.183 | INFO     | src.gui.prototype.prototype_main_window:__init__:1149 | 原型主窗口初始化完成
2025-06-21 23:34:28.808 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-21 23:34:28.813 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-21 23:34:28.815 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:910 | MainWorkspaceArea 响应式适配: sm
2025-06-21 23:34:28.823 | INFO     | src.gui.prototype.managers.communication_manager:_on_breakpoint_changed:296 | 断点变化: sm
2025-06-21 23:34:28.827 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:910 | MainWorkspaceArea 响应式适配: sm
2025-06-21 23:34:28.900 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:34:28.946 | INFO     | __main__:main:287 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-21 23:34:36.002 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:34:36.002 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 23:34:36.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 23:34:36.002 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_pension_employees 尚未创建，请导入
数据后重试
2025-06-21 23:34:36.017 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 23:34:36.021 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-21 23:34:36.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 23:34:36.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.92ms
2025-06-21 23:34:36.026 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 23:34:51.831 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:254 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-21 23:34:51.831 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:1225 | 接收到数据导入请求，当前导航路径: 工资表 > 2025年 > 5月 > 退休人员。
打开导入对话框。
2025-06-21 23:34:51.831 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 23:34:51.831 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 23:34:51.831 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-21 23:34:51.831 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-21 23:34:51.831 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-21 23:34:51.831 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-21 23:34:51.847 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-21 23:34:51.847 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-21 23:34:51.847 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-21 23:34:51.847 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:56 | 多Sheet导入器初始化完成
2025-06-21 23:34:51.964 | INFO     | src.gui.dialogs:_init_field_mapping:1404 | 初始化字段映射表格: 17 个默认字段
2025-06-21 23:34:52.025 | INFO     | src.gui.dialogs:__init__:53 | 数据导入对话框初始化完成。
2025-06-21 23:35:01.423 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-21 23:35:43.161 | INFO     | src.gui.dialogs:_on_import_mode_changed:1558 | 切换到多Sheet导入模式
2025-06-21 23:35:43.161 | INFO     | src.gui.dialogs:_on_import_mode_changed:1558 | 切换到多Sheet导入模式
2025-06-21 23:35:54.946 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-21 23:35:57.327 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 14行
2025-06-21 23:36:06.258 | INFO     | src.gui.dialogs:_setup_field_mapping:685 | 开始加载Excel列信息: C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财
务)  最终版.xls, 工作表: 退休人员工资表
2025-06-21 23:36:06.481 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 10行
2025-06-21 23:36:06.481 | INFO     | src.gui.dialogs:_setup_field_mapping:697 | 成功加载Excel列: 27 个列
2025-06-21 23:36:06.496 | INFO     | src.gui.dialogs:_update_excel_columns_in_combos:737 | 成功更新下拉框Excel列选项: 27 个列
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行9, 列津贴, 错误: 6
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行9, 列津贴, 错误: 6
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行12, 列应发工资, 错误: 23
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行12, 列应发工资, 错误: 23
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行14, 列公积, 错误: 24
2025-06-21 23:36:06.496 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行14, 列公积, 错误: 24
2025-06-21 23:36:06.513 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行15, 列保险扣款, 错误: 25
2025-06-21 23:36:06.513 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行15, 列保险扣款, 错误: 25
2025-06-21 23:36:06.513 | INFO     | src.gui.dialogs:_auto_map_fields:824 | 智能映射完成: 6 个字段
2025-06-21 23:36:08.212 | INFO     | src.gui.dialogs:_setup_field_mapping:685 | 开始加载Excel列信息: C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财
务)  最终版.xls, 工作表: 退休人员工资表
2025-06-21 23:36:08.469 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 27列, 10行
2025-06-21 23:36:08.471 | INFO     | src.gui.dialogs:_setup_field_mapping:697 | 成功加载Excel列: 27 个列
2025-06-21 23:36:08.474 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-21 23:36:08.478 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-21 23:36:08.484 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行9, 列津贴, 错误: 6
2025-06-21 23:36:08.488 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行12, 列应发工资, 错误: 23
2025-06-21 23:36:08.490 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行14, 列公积, 错误: 24
2025-06-21 23:36:08.493 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行15, 列保险扣款, 错误: 25
2025-06-21 23:36:08.496 | INFO     | src.gui.dialogs:_update_excel_columns_in_combos:737 | 成功更新下拉框Excel列选项: 27 个列
2025-06-21 23:36:08.499 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-21 23:36:08.501 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-21 23:36:08.504 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行9, 列津贴, 错误: 6
2025-06-21 23:36:08.506 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行12, 列应发工资, 错误: 23
2025-06-21 23:36:08.516 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行14, 列公积, 错误: 24
2025-06-21 23:36:08.521 | ERROR    | src.gui.dialogs:_update_preview_for_row:902 | 更新预览失败: 行15, 列保险扣款, 错误: 25
2025-06-21 23:36:08.524 | INFO     | src.gui.dialogs:_auto_map_fields:824 | 智能映射完成: 6 个字段
2025-06-21 23:36:14.621 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 23:36:14.621 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:158 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 23:36:14.621 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 23:36:15.078 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-21 23:36:15.078 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:169 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工
资表', 'A岗职工']
2025-06-21 23:36:15.078 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 23:36:15.078 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 23:36:15.078 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 23:36:15.314 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-21 23:36:15.329 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 23:36:15.329 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float6
4(34405.100000000006)}
2025-06-21 23:36:15.329 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-21 23:36:15.329 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:377 | 未找到工作表 离休人员工资表 的配置，使用默认处理
2025-06-21 23:36:15.345 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_
name']
2025-06-21 23:36:15.345 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 salary_data_2025_05_retired_employees 不存在，将根据模板创建.
..
2025-06-21 23:36:15.398 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 salary_data_2025_05_retired_employees 保存 2 条数据。
2025-06-21 23:36:15.426 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 23:36:15.428 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 23:36:15.429 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 23:36:15.659 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-21 23:36:15.659 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 23:36:15.659 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.f
loat64(33607.14625)}
2025-06-21 23:36:15.659 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-21 23:36:15.659 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:377 | 未找到工作表 退休人员工资表 的配置，使用默认处理
2025-06-21 23:36:15.675 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['empl
oyee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-21 23:36:15.680 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 salary_data_2025_05_pension_employees 不存在，将根据模板创建.
..
2025-06-21 23:36:15.701 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 salary_data_2025_05_pension_employees 保存 13 条数据。
2025-06-21 23:36:15.704 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 23:36:15.705 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 23:36:15.706 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 23:36:15.988 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-21 23:36:16.004 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 23:36:16.004 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3
971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(1
4240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.
0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.in
t64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-21 23:36:16.004 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-21 23:36:16.004 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:377 | 未找到工作表 全部在职人员工资表 的配置，使用默认处理
2025-06-21 23:36:16.004 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee
_id', 'employee_name', 'allowance', 'total_salary']
2025-06-21 23:36:16.019 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 salary_data_2025_05_active_employees 不存在，将根据模板创建..
.
2025-06-21 23:36:16.076 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 salary_data_2025_05_active_employees 保存 1396 条数据。
2025-06-21 23:36:16.088 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-21 23:36:16.089 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-21 23:36:16.092 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xl
s: read
2025-06-21 23:36:16.324 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-21 23:36:16.324 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-21 23:36:16.324 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)
}
2025-06-21 23:36:16.339 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-21 23:36:16.339 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:377 | 未找到工作表 A岗职工 的配置，使用默认处理
2025-06-21 23:36:16.339 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:761 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee
_id', 'employee_name', 'allowance', 'total_salary']
2025-06-21 23:36:16.339 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:774 | 表 misc_data_2025_05 不存在，将根据模板创建...
2025-06-21 23:36:16.385 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:872 | 成功向表 misc_data_2025_05 保存 62 条数据。
2025-06-21 23:36:16.393 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:188 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'succe
ss': True, 'message': '成功向表 salary_data_2025_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_05_retired_employees', 'records': 2}, '退休人员工资表': {'
success': True, 'message': '成功向表 salary_data_2025_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_05_pension_employees', 'records': 13}, '全部在职人员
工资表': {'success': True, 'message': '成功向表 salary_data_2025_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_05_active_employees', 'records': 1396},
'A岗职工': {'success': True, 'message': '成功向表 misc_data_2025_05 保存 62 条数据。', 'table_name': 'misc_data_2025_05', 'records': 62}}, 'total_records': 1473, 'import_stats':
{'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\d
ata\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'e
rror_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-21 23:36:16.403 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1119 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'messag
e': '成功向表 salary_data_2025_05_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_05_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'm
essage': '成功向表 salary_data_2025_05_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_05_pension_employees', 'records': 13}, '全部在职人员工资表': {'success
': True, 'message': '成功向表 salary_data_2025_05_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_05_active_employees', 'records': 1396}, 'A岗职工': {'succe
ss': True, 'message': '成功向表 misc_data_2025_05 保存 62 条数据。', 'table_name': 'misc_data_2025_05', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4
, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025
年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': No
ne}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-
05', 'data_description': '2025年5月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls'}
2025-06-21 23:36:59.733 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:36:59.733 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-21 23:36:59.733 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-21 23:36:59.747 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:905 | 正在从表 salary_data_2025_05_retired_employees 获取数据...
2025-06-21 23:36:59.747 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:916 | 成功从表 salary_data_2025_05_retired_employees 获取 2 行数据。
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1393 | 成功从数据库加载 2 行数据
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1355 | 表 salary_data_2025_05_retired_employees 没有保存的字段映射
信息
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1398 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.prototype_main_window:set_data:304 | 通过公共接口设置新的表格数据: 2 行
2025-06-21 23:36:59.747 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2508 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 2
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-21 23:36:59.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 2 行, 16 列
2025-06-21 23:36:59.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 31.35ms
2025-06-21 23:37:03.283 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:37:03.283 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 23:37:03.299 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 退休人员
2025-06-21 23:37:03.299 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:905 | 正在从表 salary_data_2025_05_pension_employees 获取数据...
2025-06-21 23:37:03.299 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:916 | 成功从表 salary_data_2025_05_pension_employees 获取 13 行数据。
2025-06-21 23:37:03.314 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1393 | 成功从数据库加载 13 行数据
2025-06-21 23:37:03.314 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1355 | 表 salary_data_2025_05_pension_employees 没有保存的字段映射
信息
2025-06-21 23:37:03.314 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1398 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-21 23:37:03.314 | INFO     | src.gui.prototype.prototype_main_window:set_data:304 | 通过公共接口设置新的表格数据: 13 行
2025-06-21 23:37:03.330 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 2 -> 13
2025-06-21 23:37:03.330 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-21 23:37:03.330 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 16 列
2025-06-21 23:37:03.346 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 15.63ms
2025-06-21 23:37:10.739 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:37:10.764 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 离休人员
2025-06-21 23:37:10.764 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 离休人员
2025-06-21 23:37:10.764 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:905 | 正在从表 salary_data_2025_05_retired_employees 获取数据...
2025-06-21 23:37:10.770 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:916 | 成功从表 salary_data_2025_05_retired_employees 获取 2 行数据。
2025-06-21 23:37:10.770 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1393 | 成功从数据库加载 2 行数据
2025-06-21 23:37:10.770 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1355 | 表 salary_data_2025_05_retired_employees 没有保存的字段映射
信息
2025-06-21 23:37:10.770 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1398 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-21 23:37:10.770 | INFO     | src.gui.prototype.prototype_main_window:set_data:304 | 通过公共接口设置新的表格数据: 2 行
2025-06-21 23:37:10.770 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2508 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-21 23:37:10.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 13 -> 2
2025-06-21 23:37:11.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-21 23:37:11.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 2 行, 16 列
2025-06-21 23:37:11.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 246.07ms
2025-06-21 23:37:12.898 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:37:12.898 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 23:37:12.898 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 23:37:12.898 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_a_grade_employees 尚未创建，请导入
数据后重试
2025-06-21 23:37:12.907 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 23:37:12.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 2
2025-06-21 23:37:12.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 23:37:12.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 2, 耗时: 0.00ms
2025-06-21 23:37:12.907 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 23:37:16.994 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:37:16.994 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-21 23:37:16.994 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > 全部在职人员
2025-06-21 23:37:16.994 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:905 | 正在从表 salary_data_2025_05_active_employees 获取数据...
2025-06-21 23:37:17.036 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:916 | 成功从表 salary_data_2025_05_active_employees 获取 1396 行数据
。
2025-06-21 23:37:17.045 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1393 | 成功从数据库加载 1396 行数据
2025-06-21 23:37:17.048 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:1352 | 表 salary_data_2025_05_active_employees 的列名已经是中文，无
需重命名
2025-06-21 23:37:17.062 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1398 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-21 23:37:17.065 | INFO     | src.gui.prototype.prototype_main_window:set_data:304 | 通过公共接口设置新的表格数据: 1396 行
2025-06-21 23:37:17.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 2 -> 500
2025-06-21 23:37:17.110 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(1396)自动调整最大可见行数为: 500
2025-06-21 23:37:17.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 1396 行, 16 列
2025-06-21 23:37:17.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 1396 行, 最大可见行数: 500, 耗时: 242.69ms
2025-06-21 23:38:08.624 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['path_9', 'path_8', 'path_7', 'path_6', 'path_5']
2025-06-21 23:38:08.624 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:1364 | 导航变化: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 23:38:08.637 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:750 | 导航选择: 工资表 > 2025年 > 5月 > A岗职工
2025-06-21 23:38:08.637 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:1384 | 数据表 salary_data_2025_05_a_grade_employees 尚未创建，请导入
数据后重试
2025-06-21 23:38:08.662 | WARNING  | src.gui.prototype.prototype_main_window:set_data:300 | 尝试设置空数据，将显示提示信息。
2025-06-21 23:38:08.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 500
2025-06-21 23:38:08.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-21 23:38:09.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 500, 耗时: 461.92ms
2025-06-21 23:38:09.140 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:762 | 表格已初始化为空白状态，等待用户导入数据
2025-06-21 23:38:22.862 | INFO     | __main__:main:296 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_

我从您提供的图片和日志中发现了问题的根本原因！

## 问题分析

从导入成功的弹窗信息可以看到：
- **A岗职工** → `misc_data_2025_05`: 62 行

但从日志中可以看到，当切换到"A岗职工"时：
```
数据表 salary_data_2025_05_a_grade_employees 尚未创建，请导入数据后重试
```

**问题根因**：`_get_sheet_data_type` 方法对"A岗职工"的识别逻辑有问题，它没有正确识别为 `salary_data_a_grade_employees`，而是被识别为了 `misc_data`，导致数据被保存到了 `misc_data_2025_05` 表中，而不是期望的 `salary_data_2025_05_a_grade_employees` 表中。

让我检查并修复这个问题：

---

Read file: src/modules/data_import/multi_sheet_importer.py

---

找到问题了！在 `_get_sheet_data_type` 方法的第534行，识别"A岗职工"的逻辑有问题。当前的逻辑是：

```python
elif "a岗" in sheet_name or "a_grade" in sheet_name_lower or "a级" in sheet_name:
    return "salary_data_a_grade_employees"
```

但是"A岗职工"中的"A"是大写的，而检查条件是小写"a岗"，所以没有匹配成功，最终被归类为 `misc_data`。

让我修复这个问题：

---

```diff
          elif "退休" in sheet_name or "pension" in sheet_name_lower:
              return "salary_data_pension_employees"
-         elif "a岗" in sheet_name or "a_grade" in sheet_name_lower or "a级" in sheet_name:
+         elif "a岗" in sheet_name_lower or "a_grade" in sheet_name_lower or "a级" in sheet_name_lower:
              return "salary_data_a_grade_employees"
          elif "全部在职" in sheet_name or "active" in sheet_name_lower or "在职" in sheet_name:
```

---

很好！现在修复已经完成。问题在于原来的代码中，对"A岗职工"的识别使用了 `"a岗" in sheet_name`，这会导致大写的"A岗职工"无法匹配。

修改后的逻辑使用了 `"a岗" in sheet_name_lower`，这样无论是"A岗职工"还是"a岗职工"都能正确识别为 `salary_data_a_grade_employees` 类型。

## 修复总结

**问题根因**：
- "A岗职工"的Sheet类型识别逻辑中，使用了大小写敏感的字符串匹配
- `"a岗" in sheet_name` 无法匹配 "A岗职工"（大写A）
- 导致"A岗职工"被识别为 `misc_data` 而不是 `salary_data_a_grade_employees`
- 数据被保存到了 `misc_data_2025_05` 表而不是期望的 `salary_data_2025_05_a_grade_employees` 表

**修复内容**：
- 将 `"a岗" in sheet_name` 改为 `"a岗" in sheet_name_lower`
- 将 `"a级" in sheet_name` 改为 `"a级" in sheet_name_lower`
- 确保所有A岗相关的匹配都使用小写字符串进行比较

**修复效果**：
- 现在"A岗职工"会被正确识别为 `salary_data_a_grade_employees` 类型
- 数据将被保存到 `salary_data_2025_05_a_grade_employees` 表中
- 主界面导航切换到"A岗职工"时能正确显示数据

## 测试建议

现在需要重新进行数据导入测试：

1. **清理旧数据**：可以删除之前错误创建的 `misc_data_2025_05` 表
2. **重新导入**：使用修复后的代码重新导入多Sheet Excel文件
3. **验证结果**：确认"A岗职工"的数据被正确保存到 `salary_data_2025_05_a_grade_employees` 表中
4. **界面测试**：验证主界面能正确显示"A岗职工"的数据

修复完成！现在"A岗职工"应该能正确显示数据了。

---

