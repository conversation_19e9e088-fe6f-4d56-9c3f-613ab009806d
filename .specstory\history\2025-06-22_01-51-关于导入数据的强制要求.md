<!-- Generated by SpecStory -->

# 关于导入数据的强制要求 (2025-06-22 09:51:45)

_**User**_

@src 
我有个疑问，系统为什么在主界面点击“导入数据”按钮时强制要求”请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然
后再导入数据“？请看图片

---

_**Assistant**_

我来帮您分析这个问题。首先让我查看当前系统的导入数据相关代码，理解为什么会有这个强制要求。

---

<details>
            <summary>Searched codebase "导入数据 按钮 目标位置 左侧导航栏 选择" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/dialogs.py` | L53-96 |
| `src/gui/dialogs.py` | L148-193 |
| `src/gui/prototype/prototype_main_window.py` | L1195-1229 |
| `src/gui/modern_main_window.py` | L162-211 |
| `src/gui/dialogs.py` | L96-148 |
| `src/gui/main_window.py` | L215-248 |
| `src/gui/dialogs.py` | L234-275 |
| `src/gui/prototype/prototype_main_window.py` | L1265-1313 |
| `src/gui/dialogs.py` | L1439-1483 |
| `src/gui/main_window.py` | L563-603 |
| `src/gui/data_import_integration.py` | L336-368 |
| `src/gui/data_import_integration.py` | L498-525 |
| `src/gui/dialogs.py` | L402-436 |
| `src/gui/dialogs.py` | L1483-1513 |
| `src/gui/context_menu_system.py` | L167-210 |
| `src/gui/prototype/prototype_main_window.py` | L1229-1265 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L563-593 |
| `src/gui/main_window_refactored.py` | L195-228 |
| `src/gui/data_import_integration.py` | L368-408 |
| `src/gui/dialogs.py` | L1125-1152 |
| `src/gui/dialogs.py` | L31-53 |
| `src/gui/dialogs.py` | L364-402 |
| `src/gui/dialogs.py` | L1712-1759 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L531-563 |
| `src/gui/dialogs.py` | L320-364 |

</details>

---

现在让我继续搜索更多相关代码，特别是关于导航路径验证的逻辑：

---

<details>
            <summary>Grep search for "请先在左侧导航栏中选择一个具体的目标位置" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L1220 | `self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_13-47-error-in-python-application-import-function.md` | L9 | `self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_12-40-%E5%A4%84%E7%90%86%E5%B7%A5%E8%B5%84%E7%B3%BB%E7%BB%9F%E5%90%AF%E5%8A%A8%E9%94%99%E8%AF%AF.md` | L748 | `+             self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_12-40-%E5%A4%84%E7%90%86%E5%B7%A5%E8%B5%84%E7%B3%BB%E7%BB%9F%E5%90%AF%E5%8A%A8%E9%94%99%E8%AF%AF.md` | L1419 | `self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")` |
| `file:///c%3A/test/salary_changes/salary_changes/.specstory/history/2025-06-20_12-40-%E5%A4%84%E7%90%86%E5%B7%A5%E8%B5%84%E7%B3%BB%E7%BB%9F%E5%90%AF%E5%8A%A8%E9%94%99%E8%AF%AF.md` | L1784 | `self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")` |

</details>

---

现在让我查看具体的验证逻辑：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在让我查看导航路径验证的具体逻辑：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在让我查看导航面板的数据结构，了解路径层级：

---

Read file: src/gui/prototype/widgets/enhanced_navigation_panel.py

---

现在我明白了问题的根源。让我分析一下系统的设计逻辑：

## 问题分析

根据代码分析，我发现了系统强制要求选择具体目标位置的原因：

### 1. **路径层级验证逻辑**

在 `src/gui/prototype/prototype_main_window.py` 的第1220行，系统有这样的验证：

```python
if not self.current_nav_path or len(self.current_nav_path) < 3:
    self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")
    return
```

### 2. **导航结构层级**

从导航面板的数据结构可以看出，系统设计了4级导航结构：
- **第1级**：模块类型（如"工资表"、"异动人员表"）
- **第2级**：年份（如"2025年"、"2024年"）  
- **第3级**：月份（如"5月"、"4月"）
- **第4级**：人员类别（如"全部在职人员"、"离休人员"、"退休人员"、"A岗职工"）

### 3. **数据存储策略**

系统采用了基于导航路径的数据库表命名策略（第1474行）：

```python
def _generate_table_name_from_path(self, path_list: list[str]) -> str:
    """根据导航路径列表生成数据库表名"""
    if len(path_list) < 4:
        self.logger.warning(f"导航路径不完整，无法生成表名: {path_list}")
        return ""
    
    # 生成类似 "salary_data_2025_05_active_employees" 的表名
```

### 4. **设计原因分析**

这种强制要求的设计有以下考虑：

#### **数据组织需要**
- 系统需要知道导入的数据属于哪个年份、月份、人员类别
- 每个具体的数据表都有独立的存储空间
- 避免数据混乱和覆盖

#### **表结构管理**
- 不同类别的人员可能有不同的字段结构
- 系统需要为每个具体分类创建对应的数据库表
- 确保数据的完整性和一致性

#### **业务逻辑需求**
- 工资数据通常按月份和人员类别进行管理
- 便于后续的异动检测和报告生成
- 符合实际的业务流程

## 改进建议

虽然这种设计有其合理性，但确实可能影响用户体验。以下是一些改进建议：

### **方案1：智能路径推断**
- 在用户未选择完整路径时，提供默认值或智能推断
- 例如：自动选择当前月份和"全部在职人员"

### **方案2：导入时选择目标**
- 允许在导入对话框中选择目标位置
- 提供年份、月份、人员类别的下拉选择

### **方案3：分步导入流程**
- 第一步：选择文件并预览
- 第二步：选择目标位置
- 第三步：配置字段映射
- 第四步：执行导入

如果您希望修改这个行为，我可以帮您实现相应的改进方案。您倾向于哪种处理方式？

---

_**User**_

我希望将方案1和方案2结合起来，而且要允许新增年份、月份、人员类别等，毕竟是要导入下月数据的，而数据导航栏是不可能先新增下月数据再去导入的。请你重新梳理思路，给出新方案，暂不给出具体实现代码，待我确认后，再开始具体实现。

---

_**Assistant**_

您的想法非常合理！确实，实际业务中需要导入新月份的数据，而不可能先在导航栏中手动添加。让我重新梳理一个更实用的方案。

## 新方案设计思路

### **核心理念**
将"数据导入"从"必须先选择目标"转变为"导入时智能确定目标"，同时支持动态创建新的年份、月份、人员类别。

### **方案概述：智能导入 + 动态目标选择**

#### **1. 导入触发方式优化**
- **当前选择了完整路径**：直接使用选中的路径作为默认目标
- **当前选择了部分路径**：基于选择进行智能推断和补全
- **当前未选择任何路径**：提供完整的目标选择界面

#### **2. 智能路径推断规则**
- **选择到年份级别**：默认推断为当前月份 + "全部在职人员"
- **选择到月份级别**：默认推断为"全部在职人员"
- **选择到模块级别**：推断为当前年份 + 当前月份 + "全部在职人员"
- **未选择**：推断为当前年份 + 当前月份 + "全部在职人员"

#### **3. 导入对话框增强**
在原有导入对话框基础上，新增"目标位置选择"区域：

```
┌─────────────────────────────────────┐
│ 📍 数据目标位置                      │
├─────────────────────────────────────┤
│ 数据模块: [工资表 ▼] [异动人员表]    │
│ 年    份: [2025 ▼] [+ 新增]         │
│ 月    份: [06 ▼] [+ 新增]           │
│ 人员类别: [全部在职人员 ▼] [+ 新增]   │
├─────────────────────────────────────┤
│ 📊 完整路径预览:                     │
│ 工资表 > 2025年 > 6月 > 全部在职人员  │
└─────────────────────────────────────┘
```

#### **4. 动态选项管理**
- **年份选择**：显示已有年份 + 当前年份 + 下一年份，支持手动输入
- **月份选择**：显示1-12月，支持中文显示（如"6月"）
- **人员类别**：显示常用类别 + 支持自定义新增
- **数据模块**：工资表、异动人员表等

#### **5. 新增功能设计**

##### **年份新增**
- 点击"+ 新增"按钮
- 弹出输入框，默认值为下一年
- 验证年份合理性（如2020-2030范围）

##### **月份新增**
- 实际上月份是固定的1-12，这里主要是选择
- 可以考虑支持季度、半年等特殊周期

##### **人员类别新增**
- 点击"+ 新增"按钮
- 弹出对话框输入新类别名称和描述
- 支持图标选择
- 自动更新到导航栏

#### **6. 导航栏动态更新**
- 导入成功后，自动在导航栏中创建对应的节点
- 如果是新年份/月份/类别，自动添加到导航树
- 保持导航树的实时性和完整性

### **用户交互流程**

#### **流程A：有明确选择时**
1. 用户在导航栏选择了"工资表 > 2025年 > 5月"
2. 点击"导入数据"按钮
3. 导入对话框显示，目标位置默认为"工资表 > 2025年 > 5月 > 全部在职人员"
4. 用户可以修改人员类别或其他参数
5. 执行导入

#### **流程B：需要导入新月份数据时**
1. 用户点击"导入数据"按钮（无论是否有选择）
2. 导入对话框显示，智能推断目标位置
3. 用户修改月份为"6月"（新月份）
4. 系统提示"将创建新的数据分类：工资表 > 2025年 > 6月 > 全部在职人员"
5. 执行导入后，导航栏自动添加新节点

#### **流程C：需要新增人员类别时**
1. 用户在目标位置选择中点击人员类别的"+ 新增"
2. 输入新类别名称，如"临时工"
3. 选择图标，如"👷"
4. 确认后，新类别出现在选择列表中
5. 执行导入，导航栏自动更新

### **技术实现要点**

#### **1. 数据结构调整**
- 导航数据从硬编码改为动态配置
- 支持运行时添加新节点
- 持久化保存导航结构

#### **2. 表名生成策略**
- 保持现有的表名生成逻辑
- 支持新类别的英文名自动生成或用户指定
- 避免表名冲突

#### **3. 导入对话框重构**
- 在现有基础上增加目标选择区域
- 实现智能推断逻辑
- 添加新增功能的UI组件

#### **4. 导航栏动态更新**
- 实现导航树的动态添加功能
- 保持展开状态和选择状态
- 数据导入后的自动刷新

### **优势分析**

1. **用户体验友好**：不再强制要求预先选择完整路径
2. **业务流程合理**：支持导入新月份数据的实际需求
3. **灵活性强**：支持动态添加新的分类和结构
4. **向后兼容**：保持现有的数据存储和管理逻辑
5. **智能化程度高**：自动推断和补全，减少用户操作

### **潜在问题和解决方案**

1. **数据一致性**：新增类别时需要验证名称唯一性
2. **权限控制**：可以考虑添加新增权限的控制
3. **数据迁移**：现有硬编码的导航结构需要迁移到动态配置
4. **性能考虑**：导航树的动态更新不应影响界面响应速度

这个方案如何？是否符合您的期望？有哪些地方需要进一步调整或补充？

---

_**User**_

开始具体实现

---

_**Assistant**_

好的，我开始具体实现这个新方案。我会按照以下步骤进行：

1. 首先修改导入数据的触发逻辑，移除强制路径要求
2. 创建动态目标选择组件
3. 增强导入对话框
4. 实现导航栏动态更新功能
5. 添加新增年份、月份、人员类别的功能

让我开始实现：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我开始实现新方案。首先，我需要创建一个动态目标选择组件，然后修改相关的逻辑。

## 步骤1：创建动态目标选择组件

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 动态目标选择组件
+ 
+ 用于数据导入时选择或创建目标位置（年份、月份、人员类别等）。
+ 支持智能推断、动态新增和实时预览。
+ """
+ 
+ import os
+ import json
+ from datetime import datetime
+ from typing import Dict, List, Optional, Tuple
+ from PyQt5.QtWidgets import (
+     QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
+     QComboBox, QPushButton, QLabel, QLineEdit, QDialog, QDialogButtonBox,
+     QMessageBox, QFrame, QTextEdit, QCheckBox
+ )
+ from PyQt5.QtCore import pyqtSignal, Qt
+ from PyQt5.QtGui import QFont, QPalette
+ 
+ from src.utils.log_config import setup_logger
+ 
+ 
+ class NewCategoryDialog(QDialog):
+     """新增人员类别对话框"""
+     
+     def __init__(self, parent=None):
+         super().__init__(parent)
+         self.logger = setup_logger(__name__)
+         self.category_name = ""
+         self.category_icon = ""
+         self.category_description = ""
+         self._init_ui()
+     
+     def _init_ui(self):
+         """初始化界面"""
+         self.setWindowTitle("新增人员类别")
+         self.setModal(True)
+         self.resize(400, 300)
+         
+         layout = QVBoxLayout(self)
+         
+         # 表单区域
+         form_layout = QFormLayout()
+         
+         # 类别名称
+         self.name_edit = QLineEdit()
+         self.name_edit.setPlaceholderText("例如: 临时工、实习生等")
+         form_layout.addRow("类别名称*:", self.name_edit)
+         
+         # 图标选择
+         icon_layout = QHBoxLayout()
+         self.icon_combo = QComboBox()
+         self.icon_combo.addItems([
+             "👥 默认", "👷 临时工", "🎓 实习生", "👨‍💼 管理层", 
+             "👩‍🏫 教师", "👨‍🔬 科研", "🏥 医务", "🔧 技术"
+         ])
+         icon_layout.addWidget(self.icon_combo)
+         form_layout.addRow("显示图标:", icon_layout)
+         
+         # 类别描述
+         self.description_edit = QTextEdit()
+         self.description_edit.setMaximumHeight(80)
+         self.description_edit.setPlaceholderText("可选：描述该类别的特点或用途")
+         form_layout.addRow("类别描述:", self.description_edit)
+         
+         layout.addLayout(form_layout)
+         
+         # 按钮区域
+         button_box = QDialogButtonBox(
+             QDialogButtonBox.Ok | QDialogButtonBox.Cancel
+         )
+         button_box.accepted.connect(self._on_accept)
+         button_box.rejected.connect(self.reject)
+         layout.addWidget(button_box)
+     
+     def _on_accept(self):
+         """确认新增"""
+         name = self.name_edit.text().strip()
+         if not name:
+             QMessageBox.warning(self, "输入错误", "请输入类别名称")
+             return
+         
+         self.category_name = name
+         self.category_icon = self.icon_combo.currentText().split()[0]
+         self.category_description = self.description_edit.toPlainText().strip()
+         
+         self.accept()
+     
+     def get_category_info(self) -> Dict[str, str]:
+         """获取类别信息"""
+         return {
+             'name': self.category_name,
+             'icon': self.category_icon,
+             'description': self.category_description
+         }
+ 
+ 
+ class TargetSelectionWidget(QWidget):
+     """动态目标选择组件"""
+     
+     # 信号定义
+     target_changed = pyqtSignal(dict)  # 目标位置变化信号
+     new_category_added = pyqtSignal(dict)  # 新增类别信号
+     
+     def __init__(self, parent=None):
+         super().__init__(parent)
+         self.logger = setup_logger(__name__)
+         
+         # 配置文件路径
+         self.config_dir = "state/data"
+         self.config_file = os.path.join(self.config_dir, "navigation_config.json")
+         
+         # 当前选择的目标信息
+         self.current_target = {
+             'module': '工资表',
+             'year': str(datetime.now().year),
+             'month': str(datetime.now().month),
+             'category': '全部在职人员'
+         }
+         
+         # 可选项配置
+         self.available_options = self._load_available_options()
+         
+         self._init_ui()
+         self._load_initial_target()
+     
+     def _init_ui(self):
+         """初始化界面"""
+         layout = QVBoxLayout(self)
+         layout.setContentsMargins(5, 5, 5, 5)
+         
+         # 创建目标选择组
+         target_group = QGroupBox("📍 数据目标位置")
+         target_group.setStyleSheet("""
+             QGroupBox {
+                 font-weight: bold;
+                 border: 2px solid #0078d4;
+                 border-radius: 8px;
+                 margin-top: 10px;
+                 padding-top: 10px;
+             }
+             QGroupBox::title {
+                 subcontrol-origin: margin;
+                 left: 10px;
+                 padding: 0 5px 0 5px;
+                 color: #0078d4;
+             }
+         """)
+         target_layout = QFormLayout(target_group)
+         target_layout.setSpacing(12)
+         
+         # 数据模块选择
+         module_layout = QHBoxLayout()
+         self.module_combo = QComboBox()
+         self.module_combo.addItems(self.available_options['modules'])
+         self.module_combo.currentTextChanged.connect(self._on_module_changed)
+         module_layout.addWidget(self.module_combo)
+         target_layout.addRow("数据模块:", module_layout)
+         
+         # 年份选择
+         year_layout = QHBoxLayout()
+         self.year_combo = QComboBox()
+         self.year_combo.setEditable(True)
+         self._populate_year_options()
+         self.year_combo.currentTextChanged.connect(self._on_year_changed)
+         year_layout.addWidget(self.year_combo)
+         
+         self.add_year_btn = QPushButton("+ 新增年份")
+         self.add_year_btn.setMaximumWidth(100)
+         self.add_year_btn.clicked.connect(self._add_new_year)
+         year_layout.addWidget(self.add_year_btn)
+         target_layout.addRow("年    份:", year_layout)
+         
+         # 月份选择
+         month_layout = QHBoxLayout()
+         self.month_combo = QComboBox()
+         self._populate_month_options()
+         self.month_combo.currentTextChanged.connect(self._on_month_changed)
+         month_layout.addWidget(self.month_combo)
+         target_layout.addRow("月    份:", month_layout)
+         
+         # 人员类别选择
+         category_layout = QHBoxLayout()
+         self.category_combo = QComboBox()
+         self._populate_category_options()
+         self.category_combo.currentTextChanged.connect(self._on_category_changed)
+         category_layout.addWidget(self.category_combo)
+         
+         self.add_category_btn = QPushButton("+ 新增类别")
+         self.add_category_btn.setMaximumWidth(100)
+         self.add_category_btn.clicked.connect(self._add_new_category)
+         category_layout.addWidget(self.add_category_btn)
+         target_layout.addRow("人员类别:", category_layout)
+         
+         layout.addWidget(target_group)
+         
+         # 路径预览区域
+         preview_group = QGroupBox("📊 完整路径预览")
+         preview_group.setStyleSheet("""
+             QGroupBox {
+                 border: 1px solid #dee2e6;
+                 border-radius: 6px;
+                 margin-top: 10px;
+                 padding-top: 10px;
+             }
+             QGroupBox::title {
+                 subcontrol-origin: margin;
+                 left: 10px;
+                 padding: 0 5px 0 5px;
+                 color: #495057;
+             }
+         """)
+         preview_layout = QVBoxLayout(preview_group)
+         
+         self.path_preview_label = QLabel()
+         self.path_preview_label.setStyleSheet("""
+             QLabel {
+                 background-color: #f8f9fa;
+                 border: 1px solid #dee2e6;
+                 border-radius: 4px;
+                 padding: 10px;
+                 font-size: 12px;
+                 color: #0078d4;
+                 font-weight: bold;
+             }
+         """)
+         self.path_preview_label.setWordWrap(True)
+         preview_layout.addWidget(self.path_preview_label)
+         
+         # 状态提示
+         self.status_label = QLabel()
+         self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")
+         preview_layout.addWidget(self.status_label)
+         
+         layout.addWidget(preview_group)
+         
+         # 更新预览
+         self._update_path_preview()
+     
+     def _load_available_options(self) -> Dict[str, List[str]]:
+         """加载可用选项配置"""
+         try:
+             os.makedirs(self.config_dir, exist_ok=True)
+             
+             if os.path.exists(self.config_file):
+                 with open(self.config_file, 'r', encoding='utf-8') as f:
+                     config = json.load(f)
+                 self.logger.info("成功加载导航配置")
+                 return config
+             else:
+                 # 默认配置
+                 default_config = {
+                     'modules': ['工资表', '异动人员表'],
+                     'years': [str(year) for year in range(2020, 2030)],
+                     'categories': [
+                         {'name': '全部在职人员', 'icon': '👥'},
+                         {'name': '离休人员', 'icon': '🏖️'},
+                         {'name': '退休人员', 'icon': '🏠'},
+                         {'name': 'A岗职工', 'icon': '🏢'}
+                     ]
+                 }
+                 self._save_config(default_config)
+                 return default_config
+                 
+         except Exception as e:
+             self.logger.error(f"加载配置失败: {e}")
+             return {
+                 'modules': ['工资表', '异动人员表'],
+                 'years': [str(year) for year in range(2020, 2030)],
+                 'categories': [
+                     {'name': '全部在职人员', 'icon': '👥'},
+                     {'name': '离休人员', 'icon': '🏖️'},
+                     {'name': '退休人员', 'icon': '🏠'},
+                     {'name': 'A岗职工', 'icon': '🏢'}
+                 ]
+             }
+     
+     def _save_config(self, config: Dict):
+         """保存配置"""
+         try:
+             with open(self.config_file, 'w', encoding='utf-8') as f:
+                 json.dump(config, f, ensure_ascii=False, indent=2)
+             self.logger.info("配置保存成功")
+         except Exception as e:
+             self.logger.error(f"保存配置失败: {e}")
+     
+     def _populate_year_options(self):
+         """填充年份选项"""
+         current_year = datetime.now().year
+         years = []
+         
+         # 添加已有年份
+         if 'years' in self.available_options:
+             years.extend(self.available_options['years'])
+         
+         # 确保包含当前年份和下一年
+         for year in [current_year - 1, current_year, current_year + 1]:
+             if str(year) not in years:
+                 years.append(str(year))
+         
+         # 排序并去重
+         years = sorted(list(set(years)), reverse=True)
+         
+         self.year_combo.clear()
+         self.year_combo.addItems(years)
+         
+         # 设置默认为当前年份
+         current_year_str = str(current_year)
+         if current_year_str in years:
+             self.year_combo.setCurrentText(current_year_str)
+     
+     def _populate_month_options(self):
+         """填充月份选项"""
+         months = [
+             "1", "2", "3", "4", "5", "6",
+             "7", "8", "9", "10", "11", "12"
+         ]
+         
+         self.month_combo.clear()
+         self.month_combo.addItems(months)
+         
+         # 设置默认为当前月份
+         current_month = str(datetime.now().month)
+         self.month_combo.setCurrentText(current_month)
+     
+     def _populate_category_options(self):
+         """填充人员类别选项"""
+         self.category_combo.clear()
+         
+         if 'categories' in self.available_options:
+             for category in self.available_options['categories']:
+                 if isinstance(category, dict):
+                     display_text = f"{category.get('icon', '👥')} {category['name']}"
+                     self.category_combo.addItem(display_text, category['name'])
+                 else:
+                     self.category_combo.addItem(f"👥 {category}", category)
+     
+     def _load_initial_target(self):
+         """加载初始目标设置"""
+         # 设置默认值
+         self.module_combo.setCurrentText(self.current_target['module'])
+         self.year_combo.setCurrentText(self.current_target['year'])
+         self.month_combo.setCurrentText(self.current_target['month'])
+         
+         # 查找并设置类别
+         for i in range(self.category_combo.count()):
+             item_data = self.category_combo.itemData(i)
+             if item_data == self.current_target['category']:
+                 self.category_combo.setCurrentIndex(i)
+                 break
+     
+     def _on_module_changed(self, text: str):
+         """模块变化处理"""
+         self.current_target['module'] = text
+         self._update_path_preview()
+         self._emit_target_changed()
+     
+     def _on_year_changed(self, text: str):
+         """年份变化处理"""
+         self.current_target['year'] = text
+         self._update_path_preview()
+         self._emit_target_changed()
+     
+     def _on_month_changed(self, text: str):
+         """月份变化处理"""
+         self.current_target['month'] = text
+         self._update_path_preview()
+         self._emit_target_changed()
+     
+     def _on_category_changed(self, text: str):
+         """类别变化处理"""
+         # 获取实际的类别名称（去掉图标）
+         current_index = self.category_combo.currentIndex()
+         if current_index >= 0:
+             category_name = self.category_combo.itemData(current_index)
+             if category_name:
+                 self.current_target['category'] = category_name
+             else:
+                 # 如果没有itemData，从显示文本中提取
+                 display_text = self.category_combo.currentText()
+                 if ' ' in display_text:
+                     self.current_target['category'] = display_text.split(' ', 1)[1]
+                 else:
+                     self.current_target['category'] = display_text
+         
+         self._update_path_preview()
+         self._emit_target_changed()
+     
+     def _add_new_year(self):
+         """新增年份"""
+         next_year = str(datetime.now().year + 1)
+         
+         # 检查是否已存在
+         for i in range(self.year_combo.count()):
+             if self.year_combo.itemText(i) == next_year:
+                 self.year_combo.setCurrentText(next_year)
+                 QMessageBox.information(self, "提示", f"年份 {next_year} 已存在，已自动选择")
+                 return
+         
+         # 添加新年份
+         self.year_combo.addItem(next_year)
+         self.year_combo.setCurrentText(next_year)
+         
+         # 更新配置
+         if next_year not in self.available_options['years']:
+             self.available_options['years'].append(next_year)
+             self._save_config(self.available_options)
+         
+         QMessageBox.information(self, "成功", f"已添加新年份: {next_year}")
+         self.logger.info(f"新增年份: {next_year}")
+     
+     def _add_new_category(self):
+         """新增人员类别"""
+         dialog = NewCategoryDialog(self)
+         if dialog.exec_() == QDialog.Accepted:
+             category_info = dialog.get_category_info()
+             
+             # 检查是否已存在
+             for category in self.available_options['categories']:
+                 if isinstance(category, dict) and category['name'] == category_info['name']:
+                     QMessageBox.warning(self, "错误", f"类别 '{category_info['name']}' 已存在")
+                     return
+                 elif isinstance(category, str) and category == category_info['name']:
+                     QMessageBox.warning(self, "错误", f"类别 '{category_info['name']}' 已存在")
+                     return
+             
+             # 添加新类别
+             new_category = {
+                 'name': category_info['name'],
+                 'icon': category_info['icon'],
+                 'description': category_info['description']
+             }
+             
+             self.available_options['categories'].append(new_category)
+             self._save_config(self.available_options)
+             
+             # 更新界面
+             display_text = f"{category_info['icon']} {category_info['name']}"
+             self.category_combo.addItem(display_text, category_info['name'])
+             self.category_combo.setCurrentText(display_text)
+             
+             # 发射新增类别信号
+             self.new_category_added.emit(new_category)
+             
+             QMessageBox.information(self, "成功", f"已添加新类别: {category_info['name']}")
+             self.logger.info(f"新增人员类别: {category_info}")
+     
+     def _update_path_preview(self):
+         """更新路径预览"""
+         path_parts = [
+             self.current_target['module'],
+             f"{self.current_target['year']}年",
+             f"{self.current_target['month']}月",
+             self.current_target['category']
+         ]
+         
+         path_str = " > ".join(path_parts)
+         self.path_preview_label.setText(path_str)
+         
+         # 检查是否为新建路径
+         if self._is_new_path():
+             self.status_label.setText("✨ 将创建新的数据分类")
+             self.status_label.setStyleSheet("color: #ffc107; font-size: 11px;")
+         else:
+             self.status_label.setText("✓ 使用现有数据分类")
+             self.status_label.setStyleSheet("color: #28a745; font-size: 11px;")
+     
+     def _is_new_path(self) -> bool:
+         """检查是否为新路径（简化版，实际应该检查数据库）"""
+         # 这里可以添加实际的数据库检查逻辑
+         # 暂时返回False，表示都是现有路径
+         return False
+     
+     def _emit_target_changed(self):
+         """发射目标变化信号"""
+         self.target_changed.emit(self.current_target.copy())
+     
+     def set_target_from_path(self, path_str: str):
+         """从路径字符串设置目标"""
+         if not path_str:
+             return
+         
+         parts = path_str.split(' > ')
+         if len(parts) >= 4:
+             self.current_target['module'] = parts[0]
+             self.current_target['year'] = parts[1].replace('年', '')
+             self.current_target['month'] = parts[2].replace('月', '')
+             self.current_target['category'] = parts[3]
+             
+             # 更新界面
+             self.module_combo.setCurrentText(self.current_target['module'])
+             self.year_combo.setCurrentText(self.current_target['year'])
+             self.month_combo.setCurrentText(self.current_target['month'])
+             
+             # 设置类别
+             for i in range(self.category_combo.count()):
+                 item_data = self.category_combo.itemData(i)
+                 if item_data == self.current_target['category']:
+                     self.category_combo.setCurrentIndex(i)
+                     break
+             
+             self._update_path_preview()
+             self.logger.info(f"从路径设置目标: {path_str}")
+     
+     def get_current_target(self) -> Dict[str, str]:
+         """获取当前目标信息"""
+         return self.current_target.copy()
+     
+     def get_target_path_list(self) -> List[str]:
+         """获取目标路径列表"""
+         return [
+             self.current_target['module'],
+             f"{self.current_target['year']}年",
+             f"{self.current_target['month']}月",
+             self.current_target['category']
+         ]
+     
+     def get_target_path_string(self) -> str:
+         """获取目标路径字符串"""
+         return " > ".join(self.get_target_path_list()) 
```

---

## 步骤2：修改导入对话框，集成目标选择组件

---

Read file: src/gui/dialogs.py

---

现在我需要修改导入对话框，集成目标选择组件：

---

```diff
  
  import os
- from typing import Dict, Any, Optional, List
+ import re
+ import json
+ import traceback
+ from datetime import datetime
+ from typing import Dict, List, Optional, Any, Tuple
+ from pathlib import Path
+ 
  from PyQt5.QtWidgets import (
-     QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
-     QLabel, QPushButton, QLineEdit, QTextEdit, QFileDialog,
-     QProgressBar, QCheckBox, QComboBox, QSpinBox, QTabWidget,
-     QGroupBox, QMessageBox, QDialogButtonBox, QWidget, QTableWidget,
-     QTableWidgetItem, QHeaderView, QSplitter, QFrame, QButtonGroup,
-     QRadioButton, QScrollArea
+     QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
+     QGroupBox, QTabWidget, QWidget, QLabel, QLineEdit, QPushButton,
+     QComboBox, QSpinBox, QCheckBox, QTableWidget, QTableWidgetItem,
+     QHeaderView, QTextEdit, QProgressBar, QDialogButtonBox,
+     QMessageBox, QFileDialog, QScrollArea, QFrame, QSplitter,
+     QApplication, QSlider
  )
- from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
- from PyQt5.QtGui import QFont, QPixmap, QIcon, QStandardItemModel, QStandardItem, QColor
- from datetime import datetime
- import re
+ from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
+ from PyQt5.QtGui import QFont, QPixmap, QIcon
+ 
+ import pandas as pd
  
  from src.utils.log_config import setup_logger
  from src.modules.data_import.excel_importer import ExcelImporter
  from src.modules.data_import.data_validator import DataValidator
  from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
+ from src.gui.widgets.target_selection_widget import TargetSelectionWidget
  
  
```

```diff
          self.imported_data = {}
          self.is_multi_sheet_mode = False  # 标记是否为多Sheet模式
+         
+         # 目标选择组件
+         self.target_selection_widget = None
+         self.final_target_path = ""  # 最终确定的目标路径
+         
          self._init_ui()
          self._connect_signals()
```

```diff
          """初始化界面"""
          self.setWindowTitle("数据导入")
-         self.setMinimumSize(1000, 800)  # 增加最小尺寸
-         self.resize(1000, 800)  # 增加默认尺寸
+         self.setMinimumSize(1200, 900)  # 增加尺寸以容纳目标选择组件
+         self.resize(1200, 900)
          
          # 主布局
          main_layout = QVBoxLayout(self)
-         main_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
-         
-         # 添加目标路径显示（固定在顶部，不滚动）
-         target_path_group = QGroupBox("导入目标位置")
-         target_path_layout = QHBoxLayout()
-         self.target_path_label = QLabel(f"<b>{self.target_path or '未选择'}</b>")
-         self.target_path_label.setWordWrap(True)
-         target_path_layout.addWidget(self.target_path_label)
-         target_path_group.setLayout(target_path_layout)
-         main_layout.addWidget(target_path_group)
+         main_layout.setContentsMargins(8, 8, 8, 8)
+         
+         # 创建目标选择组件（固定在顶部）
+         self.target_selection_widget = TargetSelectionWidget(self)
+         
+         # 如果有传入的目标路径，设置到目标选择组件
+         if self.target_path:
+             self.target_selection_widget.set_target_from_path(self.target_path)
+             self.final_target_path = self.target_path
+         else:
+             # 使用智能推断的默认路径
+             self.final_target_path = self.target_selection_widget.get_target_path_string()
+         
+         main_layout.addWidget(self.target_selection_widget)
+         
+         # 创建分隔线
+         separator = QFrame()
+         separator.setFrameShape(QFrame.HLine)
+         separator.setFrameShadow(QFrame.Sunken)
+         main_layout.addWidget(separator)
          
          # 创建滚动区域
```

---

现在我需要继续修改导入对话框，添加目标选择组件的信号连接和相关处理逻辑：

---

Read file: src/gui/dialogs.py

---

现在我需要修改_connect_signals方法，添加目标选择组件的信号连接，并且修改一些相关的方法来移除对旧目标路径标签的引用：

---

```diff
          """设置并更新目标路径显示"""
          self.target_path = path
-         self.target_path_label.setText(f"<b>{self.target_path or '未选择'}</b>")
+         if self.target_selection_widget:
+             self.target_selection_widget.set_target_from_path(path)
+         self.final_target_path = path
          self.logger.info(f"数据导入目标路径已设置为: {path}")
  
      def _connect_signals(self):
          """连接信号和槽"""
+         # 连接目标选择组件的信号
+         if self.target_selection_widget:
+             self.target_selection_widget.target_changed.connect(self._on_target_changed)
+             self.target_selection_widget.new_category_added.connect(self._on_new_category_added)
+         
          # The "Import" and "Cancel" buttons are connected directly in _create_button_area.
          # There is no self.button_box in this dialog.
```

```diff
          if hasattr(self, 'skip_empty_check'):
              self.skip_empty_check.stateChanged.connect(self._on_options_changed)
+     
+     def _on_target_changed(self, target_info: Dict[str, str]):
+         """处理目标位置变化"""
+         self.final_target_path = self.target_selection_widget.get_target_path_string()
+         self.logger.info(f"目标位置已更新: {self.final_target_path}")
+         
+         # 可以在这里添加其他需要响应目标变化的逻辑
+         # 例如：更新数据期间字段、调整字段映射等
+         if hasattr(self, 'data_period_edit'):
+             # 根据目标信息自动填充数据期间
+             year = target_info.get('year', '')
+             month = target_info.get('month', '').zfill(2)
+             if year and month:
+                 self.data_period_edit.setText(f"{year}-{month}")
+     
+     def _on_new_category_added(self, category_info: Dict[str, str]):
+         """处理新增人员类别"""
+         self.logger.info(f"新增人员类别: {category_info}")
+         # 这里可以添加通知主窗口更新导航栏的逻辑
+         # 暂时只记录日志
  
      def accept(self):
```

---

## 步骤3：修改主窗口的导入数据请求处理逻辑

现在我需要修改主窗口的导入数据请求处理逻辑，移除强制路径要求：

---

```diff
      def _on_import_data_requested(self):
          """响应数据导入请求，打开导入对话框。"""
-         if not self.current_nav_path or len(self.current_nav_path) < 3:
-             self._show_error_message("导入错误", "请先在左侧导航栏中选择一个具体的目标位置（精确到月份或人员类别），然后再导入数据。")
-             return
+         # 智能推断目标路径
+         suggested_path = self._get_suggested_target_path()
          
-         current_path_str = " > ".join(self.current_nav_path)
-         self.logger.info(f"接收到数据导入请求，当前导航路径: {current_path_str}。打开导入对话框。")
+         self.logger.info(f"接收到数据导入请求，推断的目标路径: {suggested_path}。打开导入对话框。")
  
-         dialog = DataImportDialog(self, target_path=current_path_str)
+         dialog = DataImportDialog(self, target_path=suggested_path)
          dialog.data_imported.connect(self._handle_data_imported)
          dialog.exec_()
+     
+     def _get_suggested_target_path(self) -> str:
+         """获取智能推断的目标路径"""
+         from datetime import datetime
+         
+         # 如果当前有选择的路径，基于此进行推断
+         if self.current_nav_path and len(self.current_nav_path) > 0:
+             if len(self.current_nav_path) == 1:
+                 # 只选择了模块，推断为当前年月 + 默认类别
+                 current_year = datetime.now().year
+                 current_month = datetime.now().month
+                 return f"{self.current_nav_path[0]} > {current_year}年 > {current_month}月 > 全部在职人员"
+             elif len(self.current_nav_path) == 2:
+                 # 选择了模块和年份，推断为当前月 + 默认类别
+                 current_month = datetime.now().month
+                 return f"{self.current_nav_path[0]} > {self.current_nav_path[1]} > {current_month}月 > 全部在职人员"
+             elif len(self.current_nav_path) == 3:
+                 # 选择了模块、年份、月份，推断为默认类别
+                 return f"{self.current_nav_path[0]} > {self.current_nav_path[1]} > {self.current_nav_path[2]} > 全部在职人员"
+             elif len(self.current_nav_path) == 4:
+                 # 已经是完整路径，直接使用
+                 return " > ".join(self.current_nav_path)
+         
+         # 如果没有选择任何路径，使用默认推断
+         current_year = datetime.now().year
+         current_month = datetime.now().month
+         default_path = f"工资表 > {current_year}年 > {current_month}月 > 全部在职人员"
+         
+         self.logger.info(f"使用默认推断路径: {default_path}")
+         return default_path
  
      @pyqtSlot(dict)
```

---

## 步骤4：修改数据导入处理逻辑，使用最终目标路径

现在我需要修改_handle_data_imported方法，使其使用导入对话框确定的最终目标路径：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我需要修改_handle_data_imported方法，使其能够处理从导入对话框返回的最终目标路径：

---

```diff
          source_file = import_result.get("source_file", "未知来源")
          field_mapping = import_result.get("field_mapping", {})
+         target_path_str = import_result.get("target_path", "")  # 获取最终目标路径
          
          if df is None or df.empty:
              self._show_warning_message("导入结果", "导入成功，但未返回有效数据。")
              return
              
-         self.logger.info(f"接收到导入数据: {len(df)} 行, 来源: {source_file}")
+         self.logger.info(f"接收到导入数据: {len(df)} 行, 来源: {source_file}, 目标路径: {target_path_str}")
+         
+         # 从目标路径字符串解析路径列表
+         if target_path_str:
+             target_path_list = target_path_str.split(' > ')
+         else:
+             # 如果没有目标路径，使用当前导航路径作为后备
+             target_path_list = self.current_nav_path if self.current_nav_path else []
+             
+         if not target_path_list or len(target_path_list) < 4:
+             self._show_error_message("保存失败", f"目标路径不完整: {target_path_str}")
+             return
          
          # 保存字段映射信息，确保表头正确显示
-         if field_mapping and self.current_nav_path:
-             table_name = self._generate_table_name_from_path(self.current_nav_path)
+         if field_mapping:
+             table_name = self._generate_table_name_from_path(target_path_list)
              
              self.logger.info(f"保存字段映射信息到表 {table_name}: {field_mapping}")
```

```diff
              self.logger.info(f"使用中文表头: {chinese_headers}")
  
-         if self.current_nav_path:
+         # 保存到数据库
+         self._save_to_database(df, target_path_list)
+         
+         # 如果导入的路径与当前选择的路径一致，刷新显示
+         current_path_str = " > ".join(self.current_nav_path) if self.current_nav_path else ""
+         if target_path_str == current_path_str:
              # 设置数据时传递中文表头信息
              self.main_workspace.set_data(df, preserve_headers=True)
-             self._save_to_database(df, self.current_nav_path)
+             self.logger.info("导入路径与当前路径一致，已刷新显示")
          else:
-             self._show_error_message("保存失败", "无法确定保存位置，当前导航路径丢失。")
+             # 导入到了不同的位置，提示用户
+             self._show_info_message("导入成功", 
+                 f"数据已成功导入到: {target_path_str}\n如需查看，请在左侧导航栏中选择对应位置。")
+         
+         # 通知导航面板更新（如果导入了新的年份、月份或类别）
+         self._update_navigation_if_needed(target_path_list)
+     
+     def _update_navigation_if_needed(self, target_path_list: List[str]):
+         """如果需要，更新导航面板（添加新的年份、月份或类别）"""
+         try:
+             # 这里可以添加逻辑来检查是否需要在导航面板中添加新节点
+             # 由于导航面板目前是静态的，这个功能可以在后续实现
+             self.logger.info(f"检查是否需要更新导航面板: {target_path_list}")
+             
+             # TODO: 实现动态更新导航面板的逻辑
+             # 1. 检查目标路径是否在当前导航树中存在
+             # 2. 如果不存在，添加相应的节点
+             # 3. 保存导航配置
+             
+         except Exception as e:
+             self.logger.error(f"更新导航面板失败: {e}")
+             # 不影响主要功能，只记录错误
  
      def _save_field_mappings_to_file(self):
```

---

## 步骤5：修改导入对话框的导入结果返回

现在我需要修改导入对话框，确保在导入成功时返回最终的目标路径信息：

---

Read file: src/gui/dialogs.py

---

现在我需要修改_execute_single_sheet_import方法，在返回结果中包含最终目标路径：

---

```diff
              data = df.to_dict('records')
              
+             # 获取最终目标路径
+             final_target_path = ""
+             if self.target_selection_widget:
+                 final_target_path = self.target_selection_widget.get_target_path_string()
+             
              # 保存导入的数据
              result = {
```

```diff
                  'data_description': self.data_description_edit.text().strip() or f"{year}年{month}月工资数据",
                  'target_table_name': target_table_name,
+                 'target_path': final_target_path,  # 添加最终目标路径
                  'create_new_table': not self.target_table_combo.currentText(),
                  'use_sheet_name': self.create_table_with_sheet_name.isChecked(),
```

---

## 步骤6：创建测试文件验证功能

现在让我创建一个简单的测试文件来验证新功能：

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 目标选择组件测试
+ 
+ 测试动态目标选择组件的功能，包括智能推断、动态新增等。
+ """
+ 
+ import sys
+ import os
+ from pathlib import Path
+ 
+ # 添加项目根目录到Python路径
+ project_root = Path(__file__).parent
+ sys.path.insert(0, str(project_root))
+ 
+ from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
+ from PyQt5.QtCore import Qt
+ 
+ from src.gui.widgets.target_selection_widget import TargetSelectionWidget
+ from src.gui.dialogs import DataImportDialog
+ 
+ 
+ class TestMainWindow(QMainWindow):
+     """测试主窗口"""
+     
+     def __init__(self):
+         super().__init__()
+         self.setWindowTitle("目标选择组件测试")
+         self.setGeometry(100, 100, 800, 600)
+         
+         # 创建中央部件
+         central_widget = QWidget()
+         self.setCentralWidget(central_widget)
+         
+         layout = QVBoxLayout(central_widget)
+         
+         # 添加说明标签
+         info_label = QLabel("""
+ 目标选择组件测试
+ 
+ 功能测试：
+ 1. 智能推断当前年月
+ 2. 动态新增年份
+ 3. 动态新增人员类别
+ 4. 路径预览实时更新
+ 5. 配置文件自动保存加载
+ 
+ 操作说明：
+ - 修改任意选择项，观察路径预览变化
+ - 点击"+ 新增年份"添加下一年
+ - 点击"+ 新增类别"添加自定义人员类别
+ - 点击"测试导入对话框"查看集成效果
+         """)
+         info_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
+         layout.addWidget(info_label)
+         
+         # 添加目标选择组件
+         self.target_widget = TargetSelectionWidget()
+         self.target_widget.target_changed.connect(self._on_target_changed)
+         self.target_widget.new_category_added.connect(self._on_new_category_added)
+         layout.addWidget(self.target_widget)
+         
+         # 添加当前选择显示
+         self.current_selection_label = QLabel("当前选择: 未初始化")
+         self.current_selection_label.setStyleSheet("font-weight: bold; color: #0078d4;")
+         layout.addWidget(self.current_selection_label)
+         
+         # 添加测试按钮
+         test_dialog_btn = QPushButton("测试导入对话框")
+         test_dialog_btn.clicked.connect(self._test_import_dialog)
+         layout.addWidget(test_dialog_btn)
+         
+         # 添加路径设置测试按钮
+         test_path_btn = QPushButton("测试路径设置")
+         test_path_btn.clicked.connect(self._test_path_setting)
+         layout.addWidget(test_path_btn)
+         
+         # 初始化显示
+         self._update_current_selection()
+     
+     def _on_target_changed(self, target_info):
+         """处理目标变化"""
+         print(f"目标变化: {target_info}")
+         self._update_current_selection()
+     
+     def _on_new_category_added(self, category_info):
+         """处理新增类别"""
+         print(f"新增类别: {category_info}")
+     
+     def _update_current_selection(self):
+         """更新当前选择显示"""
+         target = self.target_widget.get_current_target()
+         path_str = self.target_widget.get_target_path_string()
+         
+         display_text = f"""
+ 当前选择:
+   模块: {target['module']}
+   年份: {target['year']}
+   月份: {target['month']}
+   类别: {target['category']}
+   
+ 完整路径: {path_str}
+         """.strip()
+         
+         self.current_selection_label.setText(display_text)
+     
+     def _test_import_dialog(self):
+         """测试导入对话框"""
+         current_path = self.target_widget.get_target_path_string()
+         dialog = DataImportDialog(self, target_path=current_path)
+         dialog.exec_()
+     
+     def _test_path_setting(self):
+         """测试路径设置功能"""
+         test_paths = [
+             "工资表 > 2024年 > 12月 > 退休人员",
+             "异动人员表 > 2025年 > 6月 > 起薪",
+             "工资表 > 2025年 > 7月 > 全部在职人员"
+         ]
+         
+         import random
+         test_path = random.choice(test_paths)
+         print(f"设置测试路径: {test_path}")
+         
+         self.target_widget.set_target_from_path(test_path)
+         self._update_current_selection()
+ 
+ 
+ def main():
+     """主函数"""
+     app = QApplication(sys.argv)
+     
+     # 设置应用程序属性
+     app.setApplicationName("目标选择组件测试")
+     app.setApplicationVersion("1.0.0")
+     
+     # 创建主窗口
+     window = TestMainWindow()
+     window.show()
+     
+     # 运行应用程序
+     return app.exec_()
+ 
+ 
+ if __name__ == "__main__":
+     exit_code = main()
+     sys.exit(exit_code) 
```

---

## 步骤7：创建widgets目录

我需要确保widgets目录存在：

---

```bash
mkdir -p src/gui/widgets
```

```
Windows PowerShell
版权所有 (C) Microsoft Corporation。保留所有权利。
```

---

现在让我创建__init__.py文件：

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ GUI组件包
+ 
+ 包含各种自定义的GUI组件和控件。
+ """
+ 
+ from .target_selection_widget import TargetSelectionWidget, NewCategoryDialog
+ 
+ __all__ = [
+     'TargetSelectionWidget',
+     'NewCategoryDialog'
+ ] 
```

---

## 步骤8：运行测试验证功能

现在让我运行测试来验证新功能：

---

```bash
python test_target_selection.py
```

```
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python test_target_selection.py
```

---

测试程序似乎启动了但没有输出。让我检查是否有错误，并且运行主程序来验证集成效果：

---

```bash
python main.py
```

---

_**User**_

运行测试脚本出错了，终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python test_target_selection.py
2025-06-22 10:32:22.369 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-22 10:32:22.369 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-22 10:32:22.370 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-22 10:32:22.371 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-22 10:32:22.372 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-22 10:32:22.373 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\test_target_selection.py", line 20, in <module>
    from src.gui.widgets.target_selection_widget import TargetSelectionWidget
  File "C:\test\salary_changes\salary_changes\src\gui\__init__.py", line 16, in <module>
    from .modern_main_window import ModernMainWindow as MainWindow
  File "C:\test\salary_changes\salary_changes\src\gui\modern_main_window.py", line 38, in <module>
    from .widgets import DataTableWidget
ImportError: cannot import name 'DataTableWidget' from 'src.gui.widgets' (C:\test\salary_changes\salary_changes\src\gui\widgets\__init__.py)

---

_**Assistant**_

我看到了问题的根源。错误是因为在导入`TargetSelectionWidget`时，触发了`src.gui.__init__.py`的导入，而这个文件试图导入`DataTableWidget`，但是我们新创建的`src/gui/widgets/__init__.py`文件中没有包含这个类。

让我检查一下相关的文件结构和导入问题：

---

Read file: src/gui/__init__.py

---

<details>
            <summary>Listed directory src/gui/widgets • **3** results</summary>
        
| Name |
|-------|
| 📁 `__pycache__` |
| 📄 `__init__.py` |
| 📄 `target_selection_widget.py` |

</details>

---

<details>
            <summary>Listed directory src/gui • **31** results</summary>
        
| Name |
|-------|
| 📁 `widgets` |
| 📁 `__pycache__` |
| 📄 `dialogs.py` |
| 📁 `prototype` |
| 📁 `.pytest_cache` |
| 📄 `main_window.py` |
| 📄 `change_detection_dialog.py` |
| 📄 `context_menu_system.py` |
| 📄 `modern_main_window.py` |
| 📄 `__init__.py` |
| 📄 `main_window_refactored.py` |
| 📄 `data_import_integration.py` |
| 📄 `response_optimizer.py` |
| 📄 `render_optimizer.py` |
| 📄 `large_data_optimizer.py` |
| 📄 `preferences_dialog.py` |
| 📄 `state_manager.py` |
| 📄 `keyboard_shortcuts.py` |
| 📄 `modern_table_editor.py` |
| 📄 `report_generation_integration.py` |
| 📄 `change_detection_integration.py` |
| 📄 `batch_operations.py` |
| 📄 `drag_drop_table.py` |
| 📄 `expandable_table_widget.py` |
| 📄 `modern_demo.py` |
| 📄 `toast_system.py` |
| 📄 `modern_style.py` |
| 📄 `modern_style_poc.py` |
| 📄 `windows_refactored.py` |
| 📄 `widgets.py` |
| 📄 `windows.py` |

</details>

---

现在我明白了问题所在。`src/gui/__init__.py`文件试图从`widgets`模块导入`DataTableWidget`等组件，但是我们新创建的`src/gui/widgets/__init__.py`文件只包含了`TargetSelectionWidget`，没有包含其他原有的组件。

让我检查一下原有的widgets.py文件：

---

Read file: src/gui/widgets.py

---

现在我明白了问题。我们需要更新`src/gui/widgets/__init__.py`文件，使其包含原有的组件。让我修复这个问题：

---

```diff
  """
  
+ # 导入原有的组件
+ from ..widgets import (
+     DataTableWidget,
+     ChangeDetectionWidget,
+     ReportPreviewWidget
+ )
+ 
+ # 导入新的目标选择组件
  from .target_selection_widget import TargetSelectionWidget, NewCategoryDialog
  
  __all__ = [
+     # 原有组件
+     'DataTableWidget',
+     'ChangeDetectionWidget', 
+     'ReportPreviewWidget',
+     # 新组件
      'TargetSelectionWidget',
      'NewCategoryDialog'
```

---

现在让我们再次运行测试脚本：

---

```bash
python test_target_selection.py
```

---



---

_**User**_



---

