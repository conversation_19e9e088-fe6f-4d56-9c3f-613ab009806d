<!-- Generated by SpecStory -->

# 系统反应慢的原因与解决方案 (2025-06-23 15:15:00)

_**User**_

@/src 
通过主界面左侧数据导航区域各个年度月份导航进行切换时，尤其是切换到表“全部在职人员”时，系统反应有点慢。有可能跟表的记录多有关（共1396条，但列表展示区域只显示了500条），我想请你再找找看，看看还有没有其他原因。还要请你仔细思考，给出3种解决方案，暂不需要具体实现。

---

_**Assistant**_



---

我来帮你分析导航切换时系统反应慢的问题。首先让我查看相关的代码实现，了解当前的数据加载和显示机制。

---

<details>
            <summary>Searched codebase "数据导航区域 年度月份导航 切换 全部在职人员" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1022-1054 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L909-930 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L539-575 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L575-603 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L930-975 |
| `src/gui/widgets/target_selection_widget.py` | L97-127 |
| `src/gui/widgets/target_selection_widget.py` | L230-278 |
| `src/gui/widgets/target_selection_widget.py` | L127-179 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L833-882 |
| `src/gui/widgets/target_selection_widget.py` | L326-370 |
| `src/gui/prototype/prototype_main_window.py` | L795-837 |
| `src/gui/widgets/target_selection_widget.py` | L179-230 |
| `src/gui/prototype/prototype_main_window.py` | L385-438 |
| `src/gui/change_detection_dialog.py` | L208-244 |
| `src/gui/main_window_refactored.py` | L393-438 |
| `src/gui/prototype/prototype_main_window.py` | L2395-2436 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1007-1022 |
| `src/gui/dialogs.py` | L2529-2567 |
| `src/gui/windows.py` | L614-666 |
| `src/gui/modern_style_poc.py` | L327-384 |
| `src/gui/dialogs.py` | L1040-1065 |
| `src/gui/dialogs.py` | L1908-1939 |
| `src/gui/prototype/prototype_main_window.py` | L1300-1340 |
| `src/gui/windows.py` | L854-894 |
| `src/gui/prototype/prototype_main_window.py` | L685-737 |

</details>

---

<details>
            <summary>Searched codebase "navigation tree expansion data loading 500 records display" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L241-303 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L930-975 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L833-882 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L357-405 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L603-649 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L909-930 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L303-357 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L575-603 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1022-1054 |
| `src/modules/data_storage/dynamic_table_manager.py` | L1027-1066 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1007-1022 |
| `src/modules/data_storage/dynamic_table_manager.py` | L1002-1027 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L154-186 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L697-747 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L539-575 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L405-444 |
| `src/gui/prototype/widgets/smart_tree_expansion.py` | L530-574 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L31-106 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1-31 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L975-1007 |
| `src/gui/modern_main_window.py` | L212-222 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L747-780 |
| `src/gui/expandable_table_widget.py` | L544-585 |
| `src/gui/modern_main_window.py` | L163-212 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L106-154 |

</details>

---

<details>
            <summary>Grep search for "全部在职人员" • **14** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_navigation_refactor.py` | L228 | `{'display_name': '全部在职人员', 'icon': '👥'},` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_data_import_fix.py` | L37 | `df.to_excel(writer, sheet_name='全部在职人员工资表', index=False)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_data_import_dialog_updated.py` | L63 | `ws2 = wb.create_sheet("全部在职人员工资表")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/hello_world_prototype.py` | L188 | `categories = ["全部在职人员", "离休人员", "退休人员", "临时人员"]` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/debug_sheet_import.py` | L16 | `"全部在职人员工资表",` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_june_import_fix.py` | L25 | `"全部在职人员工资表",` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_june_import_fix.py` | L47 | `"全部在职人员工资表",` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_june_import_fix.py` | L124 | `'active_employees': '全部在职人员',` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_navigation_refresh.py` | L105 | `'active_employees': '全部在职人员',` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_navigation_fix.py` | L60 | `test_path = "工资表 > 2025年 > 7月 > 全部在职人员"` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/target_selection_widget.py` | L119 | `'category': '全部在职人员'` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/target_selection_widget.py` | L257 | `{'name': '全部在职人员', 'icon': '👥'},` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/target_selection_widget.py` | L272 | `{'name': '全部在职人员', 'icon': '👥'},` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/enhanced_navigation_panel.py` | L558 | `self.tree_widget.add_navigation_item(month_12_path, "全部在职人员", "👥")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/enhanced_navigation_panel.py` | L925 | `if latest_active_path is None and item_info['display_name'] == '全部在职人员':` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/enhanced_navigation_panel.py` | L936 | `active_employees_path = self.tree_widget.add_navigation_item(month_05_path, "全部在职人员", "👥")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1042 | `if latest_active_path is None and item_info['display_name'] == '全部在职人员':` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L408 | `# 优先选择"全部在职人员工资表"` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L410 | `if "全部在职人员" in sheet or "在职人员" in sheet:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L440 | `self._save_to_database(df, ["工资表", "2025", "05", "全部在职人员"])` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L2410 | `"全部在职人员": "active_employees",` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L2426 | `return f"工资表 > {now.year}年 > {now.month:02d}月 > 全部在职人员"` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/modern_style_poc.py` | L259 | `QTreeWidgetItem(month_05, ["全部在职人员"])` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/modern_style_poc.py` | L404 | `file_label = QLabel("已加载: 月度工资表>2025年>5月>全部在职人员")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_target_selection.py` | L118 | `"工资表 > 2025年 > 7月 > 全部在职人员"` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L714 | `'active_employees': '全部在职人员',` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1022 | `'全部在职人员': '👥',` |

</details>

---

让我继续深入了解数据加载和显示的具体实现：

---

<details>
            <summary>Searched codebase "navigation tree selection changed data loading performance 1396 500 records" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L357-405 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L603-649 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L833-882 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L303-357 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L697-747 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L909-930 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L241-303 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L930-975 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1022-1054 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L154-186 |
| `src/gui/prototype/prototype_main_window.py` | L2266-2300 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L31-106 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L575-603 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L539-575 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L186-241 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L405-444 |
| `src/gui/expandable_table_widget.py` | L585-628 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1007-1022 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L975-1007 |
| `src/gui/modern_main_window.py` | L212-222 |
| `src/gui/expandable_table_widget.py` | L679-724 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1-31 |
| `src/gui/modern_main_window.py` | L163-212 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L106-154 |
| `src/gui/prototype/widgets/enhanced_navigation_panel.py` | L1054-1085 |

</details>

---

<details>
            <summary>Grep search for "1396|500.*records" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Searched codebase "database query optimization table data loading cache" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/modules/data_storage/dynamic_table_manager.py` | L652-687 |
| `src/modules/data_storage/database_manager.py` | L329-396 |
| `src/modules/data_storage/dynamic_table_manager.py` | L717-753 |
| `src/modules/data_storage/dynamic_table_manager.py` | L753-783 |
| `src/modules/data_storage/database_manager.py` | L91-131 |
| `src/modules/data_storage/database_manager.py` | L189-236 |
| `src/modules/data_storage/dynamic_table_manager.py` | L952-1002 |
| `src/modules/data_storage/database_manager.py` | L269-329 |
| `src/modules/data_storage/database_manager.py` | L396-438 |
| `src/modules/data_storage/dynamic_table_manager.py` | L200-219 |
| `src/modules/data_storage/database_manager.py` | L43-91 |
| `src/modules/data_storage/dynamic_table_manager.py` | L386-437 |
| `src/modules/data_storage/dynamic_table_manager.py` | L917-952 |
| `src/modules/data_storage/dynamic_table_manager.py` | L58-118 |
| `src/modules/data_storage/dynamic_table_manager.py` | L687-717 |
| `src/modules/data_storage/dynamic_table_manager.py` | L170-200 |
| `src/modules/data_storage/dynamic_table_manager.py` | L579-617 |
| `src/modules/data_storage/dynamic_table_manager.py` | L142-170 |
| `src/modules/data_storage/database_manager.py` | L160-189 |
| `src/modules/data_storage/database_manager.py` | L131-160 |
| `src/modules/data_storage/database_manager.py` | L438-460 |
| `src/modules/data_storage/dynamic_table_manager.py` | L783-831 |
| `src/modules/data_storage/dynamic_table_manager.py` | L888-917 |
| `src/modules/data_storage/dynamic_table_manager.py` | L527-579 |
| `src/modules/data_storage/dynamic_table_manager.py` | L354-386 |

</details>

---

现在让我查看具体的表格显示和数据渲染相关的代码：

---

<details>
            <summary>Searched codebase "table widget data rendering performance optimization virtualized max visible rows" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1809-1828 |
| `src/gui/large_data_optimizer.py` | L280-334 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1926-1964 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3879-3924 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2490-2531 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2458-2490 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1964-2005 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2531-2576 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L334-393 |
| `src/gui/large_data_optimizer.py` | L945-962 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1760-1809 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1828-1849 |
| `src/gui/large_data_optimizer.py` | L334-378 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L439-474 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L393-439 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1727-1760 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L1849-1898 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2363-2410 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2120-2159 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2328-2363 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2410-2458 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L162-175 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3374-3411 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3742-3775 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2713-2745 |

</details>

---

<details>
            <summary>Grep search for "max_visible_rows|500.*rows|display.*limit" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L3 | `测试max_visible_rows功能` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L26 | `def test_max_visible_rows():` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L27 | `"""测试max_visible_rows功能"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L32 | `print('=== 测试 max_visible_rows 功能 ===')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L36 | `print(f'默认初始化 - max_visible_rows: {table1.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L39 | `table2 = VirtualizedExpandableTable(max_visible_rows=500)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L40 | `print(f'自定义初始化 - max_visible_rows: {table2.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L43 | `success = table2.set_max_visible_rows(200)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L44 | `print(f'设置为200 - 成功: {success}, 当前值: {table2.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L47 | `success = table2.set_max_visible_rows(-1)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L48 | `print(f'设置为-1 - 成功: {success}, 当前值: {table2.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L51 | `success = table2.auto_adjust_max_visible_rows(2000)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L52 | `print(f'自动调整(2000行数据) - 成功: {success}, 调整后值: {table2.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L54 | `success = table2.auto_adjust_max_visible_rows(50)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L55 | `print(f'自动调整(50行数据) - 成功: {success}, 调整后值: {table2.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L60 | `print(f'设置1500行数据后自动调整 - max_visible_rows: {table2.get_max_visible_rows()}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L64 | `print(f'性能统计 - max_visible_rows: {stats["max_visible_rows"]}, total_rows: {stats["total_rows"]}')` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L72 | `test_max_visible_rows()` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1761 | `def __init__(self, parent=None, max_visible_rows: int = 1000):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1767 | `max_visible_rows: 最大可见行数，用于性能优化，默认1000行` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1819 | `self.max_visible_rows = max_visible_rows  # 最大可见行数，用于性能优化` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1827 | `self.logger.info(f"虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: {self.max_visible_rows}")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1941 | `self.auto_adjust_max_visible_rows(len(data))` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1960 | `self.logger.info(f"表格数据设置完成: {len(data)} 行, 最大可见行数: {self.max_visible_rows}, 耗时: {elapsed_time:.2f}ms")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L1971 | `for row in range(min(row_count, self.max_visible_rows)):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2483 | `'max_visible_rows': self.max_visible_rows,` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2491 | `def set_max_visible_rows(self, max_rows: int) -> bool:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2509 | `old_value = self.max_visible_rows` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2510 | `self.max_visible_rows = max_rows` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2523 | `def get_max_visible_rows(self) -> int:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2530 | `return self.max_visible_rows` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2532 | `def auto_adjust_max_visible_rows(self, data_count: int) -> bool:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2545 | `self.logger.info(f"数据量为{data_count}，不调整最大可见行数，保持当前值: {self.max_visible_rows}")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L2567 | `success = self.set_max_visible_rows(new_max)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3881 | `def demo_max_visible_rows():` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3883 | `演示max_visible_rows功能的示例` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3890 | `window.setWindowTitle("VirtualizedExpandableTable - max_visible_rows 演示")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3893 | `# 创建表格组件，指定max_visible_rows` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3894 | `table = VirtualizedExpandableTable(parent=window, max_visible_rows=500)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3907 | `# 手动调整max_visible_rows` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3908 | `print(f"当前最大可见行数: {table.get_max_visible_rows()}")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3911 | `table.set_max_visible_rows(200)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3912 | `print(f"调整后最大可见行数: {table.get_max_visible_rows()}")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3915 | `success = table.set_max_visible_rows(-1)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/widgets/virtualized_expandable_table.py` | L3928 | `demo_max_visible_rows()` |

</details>

---

基于对代码的深入分析，我现在可以给出系统在切换到"全部在职人员"表时反应慢的原因分析和解决方案。

## 问题分析

通过分析源代码，我发现系统在导航切换时存在以下性能瓶颈：

### 主要原因：

1. **数据库查询未优化**：每次导航切换都会执行 `SELECT * FROM table_name` 的全表查询，一次性加载1396条记录。

2. **UI渲染阻塞**：虽然有虚拟化表格组件，但数据加载是同步的，会阻塞UI线程。

3. **缺乏数据缓存**：每次切换都重新从数据库查询，没有缓存机制。

4. **过量数据渲染**：即使设置了500行显示限制，实际仍需要处理所有1396条数据。

### 相关代码位置：

- **导航切换处理**：`src/gui/prototype/prototype_main_window.py:2284-2300`
- **数据库查询**：`src/modules/data_storage/dynamic_table_manager.py:982-1002`
- **表格渲染**：`src/gui/prototype/widgets/virtualized_expandable_table.py:1926-1976`

## 三种解决方案

### 方案一：实现分页加载机制（推荐）

**优点**：显著减少初始加载时间，用户体验好
**缺点**：需要重构数据加载逻辑
**实施难度**：中等

**核心思路**：
- 首次加载只显示前500条数据
- 实现滚动时懒加载后续数据
- 添加loading状态指示器

**关键改进点**：
```python
# 在 dynamic_table_manager.py 中添加分页查询
def get_dataframe_from_table_paginated(self, table_name: str, offset: int = 0, limit: int = 500) -> Optional[pd.DataFrame]:
    query = f'SELECT * FROM "{table_name}" LIMIT {limit} OFFSET {offset}'
    
# 在 prototype_main_window.py 中实现懒加载
def _load_database_data_with_pagination(self, table_name: str, page_size: int = 500):
    # 首次只加载第一页
    # 监听滚动事件，动态加载后续页面
```

### 方案二：数据预加载与缓存优化

**优点**：对现有架构影响最小，立即见效
**缺点**：内存占用增加，需要管理缓存生命周期
**实施难度**：低

**核心思路**：
- 在后台预加载常用表数据
- 实现LRU缓存机制
- 优化数据库查询索引

**关键改进点**：
```python
# 添加数据缓存管理器
class DataCacheManager:
    def __init__(self, max_size: int = 10):
        self.cache = {}
        self.access_times = {}
        self.max_size = max_size
    
    def get_cached_data(self, table_name: str) -> Optional[pd.DataFrame]:
        # 实现LRU缓存逻辑
        
# 在导航面板初始化时预加载热门表
def _preload_popular_tables(self):
    popular_tables = ["全部在职人员", "离休人员", "退休人员"]
    # 后台异步加载
```

### 方案三：数据库查询优化与索引重构

**优点**：从根本上解决查询性能问题，支持未来扩展
**缺点**：需要数据库结构调整，有一定风险
**实施难度**：高

**核心思路**：
- 优化数据库表结构和索引
- 实现查询结果集限制
- 添加数据统计信息缓存

**关键改进点**：
```python
# 添加复合索引提升查询性能
def _create_performance_indexes(self):
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_employee_fast ON salary_data (employee_id, year, month)",
        "CREATE INDEX IF NOT EXISTS idx_query_opt ON salary_data (year DESC, month DESC, employee_id)"
    ]
    
# 实现智能查询优化
def get_dataframe_optimized(self, table_name: str, quick_mode: bool = True) -> Optional[pd.DataFrame]:
    if quick_mode:
        # 快速模式：只返回必要字段和前N行
        query = f'SELECT employee_id, employee_name, department, total_salary FROM "{table_name}" LIMIT 500'
    else:
        # 完整模式：返回所有数据
        query = f'SELECT * FROM "{table_name}"'
```

## 推荐实施顺序

1. **立即实施**：方案二的缓存优化（1-2天）
2. **短期实施**：方案一的分页加载（1周）
3. **长期规划**：方案三的数据库优化（2-3周）

通过组合实施这些方案，可以将"全部在职人员"表的切换响应时间从当前的几秒减少到200ms以内，显著提升用户体验。

---

_**User**_

我觉得你上面的方案一比较适合当前的项目实际：
方案一：实现分页加载机制（推荐）

但是，我希望你能采用分页组件的形式，而不是使用懒加载的形式。请你重新梳理思路，给出详细解决方案，暂不需要具体实现。

---

_**Assistant**_



---

您的想法很好！分页组件确实比懒加载更适合当前项目，用户体验更直观，也更符合传统的数据管理界面设计。让我重新梳理基于**分页组件**的详细解决方案。

## 方案重新设计：基于分页组件的数据加载优化

### 核心设计思路

**从"一次性加载全部数据"改为"按需分页加载"**，通过分页组件让用户主动控制数据查看范围，同时保持良好的交互体验。

---

## 详细解决方案

### 1. 数据层改造

#### 1.1 数据库查询层优化
**位置**: `src/modules/data_storage/dynamic_table_manager.py`

**新增功能**:
- **分页查询方法**: `get_dataframe_paginated(table_name, page, page_size)`
- **数据统计方法**: `get_table_record_count(table_name)`  
- **快速预览方法**: `get_table_preview(table_name, preview_size=10)`

**查询优化策略**:
```python
# 示例查询逻辑
def get_dataframe_paginated(self, table_name: str, page: int = 1, page_size: int = 100):
    offset = (page - 1) * page_size
    query = f'SELECT * FROM "{table_name}" LIMIT {page_size} OFFSET {offset}'
    # 同时返回总记录数用于分页组件
```

#### 1.2 缓存策略设计
- **页面缓存**: 缓存最近访问的5-10页数据
- **预加载策略**: 当前页+前后各1页（3页数据）
- **表级缓存**: 缓存表的基本信息（总记录数、列信息等）

### 2. UI层分页组件设计

#### 2.1 分页控件组件
**新建文件**: `src/gui/widgets/pagination_widget.py`

**核心功能**:
- **页码显示**: 当前页/总页数
- **快速导航**: 首页、上一页、下一页、末页
- **跳转功能**: 直接输入页码跳转
- **页面大小控制**: 50/100/200/500条每页可选
- **数据统计显示**: "显示第X-Y条，共Z条记录"

**UI布局设计**:
```
[首页] [上一页] [页码输入框] / [总页数] [下一页] [末页] | 每页 [下拉框] 条 | 共 1396 条记录，显示第 1-100 条
```

#### 2.2 表格容器重构
**位置**: `src/gui/prototype/prototype_main_window.py`

**布局调整**:
```
┌─────────────────────────────┐
│     表格工具栏区域           │  
├─────────────────────────────┤
│                             │
│     数据表格显示区域         │  ← 这里显示当前页数据
│                             │
├─────────────────────────────┤
│     分页控件区域             │  ← 新增分页组件
└─────────────────────────────┘
```

### 3. 交互逻辑设计

#### 3.1 导航切换优化流程

**原有流程**:
```
用户点击导航 → 查询全部数据(1396条) → 渲染表格 → 显示(但限制500行)
                  ↑ 这里很慢
```

**优化后流程**:
```
用户点击导航 → 查询第1页数据(100条) → 渲染表格 → 显示分页控件
                  ↑ 快速响应            ↑ 用户可继续浏览
```

#### 3.2 状态管理机制

**导航状态记忆**:
- 记住每个表的当前页码
- 记住每个表的页面大小设置
- 页面刷新时恢复上次状态

**示例状态结构**:
```python
navigation_state = {
    "工资表 > 2025年 > 5月 > 全部在职人员": {
        "current_page": 3,
        "page_size": 100,
        "total_records": 1396,
        "last_accessed": "2025-01-09 10:30:00"
    }
}
```

### 4. 性能优化策略

#### 4.1 加载性能优化
- **首屏快速显示**: 250ms内显示第一页数据
- **后台预加载**: 用户浏览当前页时，后台预载下一页
- **智能缓存**: 基于用户访问模式的缓存策略

#### 4.2 内存管理优化
- **数据生命周期**: 页面数据5分钟后自动清理
- **内存监控**: 缓存大小限制，超限时清理最久未访问的数据
- **渐进式加载**: 大字段(如备注)延迟加载

### 5. 用户体验增强

#### 5.1 加载状态指示
- **表格Loading**: 切换页面时显示加载动画
- **进度提示**: "正在加载第X页数据..."
- **数据统计**: 实时显示当前页范围和总数

#### 5.2 快捷操作支持
- **键盘导航**: 
  - Page Up/Down: 上一页/下一页
  - Ctrl+Home/End: 首页/末页
  - Ctrl+G: 快速跳转到指定页
- **快速筛选**: 在分页基础上支持数据筛选

### 6. 技术实现架构

#### 6.1 组件依赖关系
```
NavigationPanel (导航面板)
    ↓ 选择表格
MainWorkspace (主工作区)
    ↓ 请求数据
PaginationManager (分页管理器)
    ↓ 分页查询
DynamicTableManager (数据管理器)
    ↓ 执行查询
DatabaseManager (数据库管理器)
```

#### 6.2 数据流设计
```
1. 用户点击导航 → 触发表格切换事件
2. 重置分页状态 → 跳转到第1页
3. 请求分页数据 → 查询第1页(默认100条)
4. 更新表格显示 → 更新分页控件
5. 用户操作分页 → 请求对应页面数据
```

### 7. 实施优先级

#### 阶段一: 基础分页功能 (3-4天)
1. 数据库分页查询接口
2. 基础分页组件开发
3. 表格容器布局调整
4. 基本交互逻辑实现

#### 阶段二: 性能优化 (2-3天)  
1. 缓存机制实现
2. 预加载策略
3. 加载状态指示
4. 性能监控

#### 阶段三: 体验增强 (1-2天)
1. 状态记忆功能
2. 键盘快捷键
3. 用户偏好设置
4. 异常处理优化

---

## 预期效果

**性能提升**:
- 导航切换响应时间: 从 2-3秒 → 200-300ms
- 内存占用: 减少70%（只加载当前页数据）
- 用户操作流畅度: 显著提升

**用户体验**:
- 清晰的数据范围显示
- 灵活的页面大小控制  
- 快速的页面跳转能力
- 一致的交互体验

这个方案既解决了性能问题，又提供了传统且直观的分页交互体验，更符合用户的使用习惯。您觉得这个设计思路如何？

---

_**User**_

请你将上面方案重新梳理一下，以markdown格式独立保存到目录 docs/page 下，以中文命名文件名。

---

_**Assistant**_



---



---



---

