<!-- Generated by SpecStory -->

# 继续讨论 (2025-06-23 19:02:20)

_**User**_

继续

---

_**Assistant**_



---

我继续解决分页组件的问题。从上次的对话中，我看到最后遇到了`DynamicTableManager`的`AttributeError`问题，以及分页组件只在"全部在职人员"时显示的问题。让我先检查当前系统状态并解决这些问题。

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---

Read file: src/gui/prototype/prototype_main_window.py

---

Read file: src/gui/widgets/pagination_widget.py

---

现在我检查了系统的当前状态。我发现：

1. `DynamicTableManager` 已经实现了分页查询的方法
2. `PaginationWidget` 组件看起来是完整的
3. 主窗口已经有了分页相关的逻辑

让我检查分页组件是否在所有表中都显示的逻辑：

---

Read file: src/gui/prototype/prototype_main_window.py

---

```bash
cd "C:\test\salary_changes\salary_changes" && python temp/check_db.py
```

---

_**User**_

测试脚本运行结果如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/check_db.py
数据库中的表:
  - system_config
  - sqlite_sequence
  - table_metadata
  - backup_records
  - salary_data_2025_05_retired_employees
  - salary_data_2025_05_pension_employees
  - salary_data_2025_05_active_employees
  - salary_data_2025_05_a_grade_employees
  - salary_data_2025_06_active_employees
  - salary_data_2025_06_retired_employees
  - salary_data_2025_06_pension_employees
  - salary_data_2025_06_a_grade_employees
  - salary_data_2025_07_retired_employees
  - salary_data_2025_07_pension_employees
  - salary_data_2025_07_active_employees
  - salary_data_2025_07_a_grade_employees
  - salary_data_2025_08_retired_employees
  - salary_data_2025_08_pension_employees
  - salary_data_2025_08_active_employees
  - salary_data_2025_08_a_grade_employees
  - salary_data_2025_09_retired_employees
  - salary_data_2025_09_pension_employees
  - salary_data_2025_09_active_employees
  - salary_data_2025_09_a_grade_employees
  - salary_data_2025_04_retired_employees
  - salary_data_2025_04_pension_employees
  - salary_data_2025_04_active_employees
  - salary_data_2025_04_a_grade_employees
  - salary_data_2025_03_retired_employees
  - salary_data_2025_03_pension_employees
  - salary_data_2025_03_active_employees
  - salary_data_2025_03_a_grade_employees
  - salary_data_2025_02_retired_employees
  - salary_data_2025_02_pension_employees
  - salary_data_2025_02_active_employees
  - salary_data_2025_02_a_grade_employees
  - salary_data_2025_01_retired_employees
  - salary_data_2025_01_pension_employees
  - salary_data_2025_01_active_employees
  - salary_data_2025_01_a_grade_employees
  - salary_data_2024_11_retired_employees
  - salary_data_2024_11_pension_employees
  - salary_data_2024_11_active_employees
  - salary_data_2024_11_a_grade_employees
  - salary_data_2024_10_retired_employees
  - salary_data_2024_10_pension_employees
  - salary_data_2024_10_active_employees
  - salary_data_2024_10_a_grade_employees
  - salary_data_2023_12_retired_employees
  - salary_data_2023_12_pension_employees
  - salary_data_2023_12_active_employees
  - salary_data_2023_12_a_grade_employees
  - salary_data_2023_08_retired_employees
  - salary_data_2023_08_pension_employees
  - salary_data_2023_08_active_employees
  - salary_data_2023_08_a_grade_employees
  - salary_data_2022_05_retired_employees
  - salary_data_2022_05_pension_employees
  - salary_data_2022_05_active_employees
  - salary_data_2022_05_a_grade_employees

检查basic_info_2025_06表:
查询失败: Execution failed on sql 'SELECT COUNT(*) as count FROM basic_info_2025_06': no such table: basic_info_2025_06

---

_**Assistant**_



---

看到测试结果了！数据库中确实有很多表，但是脚本在查询 `basic_info_2025_06` 表时失败了，因为该表不存在。让我创建一个新的脚本来检查这些工资数据表的记录数量，特别是查看为什么分页组件只在"全部在职人员"表中显示。

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 检查工资数据表记录数量脚本
+ 
+ 用于分析各个工资数据表的记录数量，帮助理解分页组件显示逻辑
+ """
+ 
+ import sqlite3
+ import os
+ import sys
+ 
+ # 添加项目根目录到路径
+ sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
+ 
+ def check_salary_table_records():
+     """检查所有工资数据表的记录数量"""
+     
+     db_path = "data/db/salary_system.db"
+     
+     if not os.path.exists(db_path):
+         print(f"数据库文件不存在: {db_path}")
+         return
+     
+     try:
+         conn = sqlite3.connect(db_path)
+         cursor = conn.cursor()
+         
+         # 获取所有以salary_data开头的表
+         cursor.execute("""
+             SELECT name FROM sqlite_master 
+             WHERE type='table' AND name LIKE 'salary_data_%'
+             ORDER BY name DESC
+         """)
+         
+         salary_tables = cursor.fetchall()
+         
+         print("工资数据表记录数量统计:")
+         print("=" * 80)
+         
+         # 按年月分组统计
+         table_data = {}
+         
+         for table_name, in salary_tables:
+             try:
+                 # 获取记录数
+                 cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
+                 count = cursor.fetchone()[0]
+                 
+                 # 解析表名
+                 parts = table_name.split('_')
+                 if len(parts) >= 5:
+                     year = parts[2]
+                     month = parts[3]
+                     category = '_'.join(parts[4:])
+                     
+                     key = f"{year}_{month}"
+                     if key not in table_data:
+                         table_data[key] = {}
+                     
+                     table_data[key][category] = count
+                     
+             except Exception as e:
+                 print(f"查询表 {table_name} 失败: {e}")
+         
+         # 输出结果
+         for period in sorted(table_data.keys(), reverse=True):
+             year, month = period.split('_')
+             print(f"\n{year}年{month}月数据:")
+             print("-" * 40)
+             
+             categories = table_data[period]
+             for category, count in categories.items():
+                 # 转换为中文显示
+                 category_map = {
+                     'active_employees': '全部在职人员',
+                     'retired_employees': '退休人员', 
+                     'pension_employees': '离休人员',
+                     'a_grade_employees': 'A岗职工'
+                 }
+                 
+                 display_name = category_map.get(category, category)
+                 print(f"  {display_name}: {count:,}条记录")
+         
+         print("\n" + "=" * 80)
+         print("分页显示阈值分析:")
+         print("- 分页阈值: 100条记录")
+         print("- 超过100条的表将显示分页组件")
+         print("- 100条以下的表将隐藏分页组件")
+         
+         # 分析哪些表会显示分页
+         print("\n需要分页的表:")
+         for period in sorted(table_data.keys(), reverse=True):
+             year, month = period.split('_')
+             categories = table_data[period]
+             
+             for category, count in categories.items():
+                 if count > 100:
+                     category_map = {
+                         'active_employees': '全部在职人员',
+                         'retired_employees': '退休人员', 
+                         'pension_employees': '离休人员',
+                         'a_grade_employees': 'A岗职工'
+                     }
+                     display_name = category_map.get(category, category)
+                     print(f"  {year}年{month}月 {display_name}: {count:,}条 ✓")
+         
+         conn.close()
+         
+     except Exception as e:
+         print(f"检查数据库失败: {e}")
+ 
+ if __name__ == "__main__":
+     check_salary_table_records() 
```

---

```bash
python temp/check_salary_table_records.py
```

---

_**User**_

测试脚本运行结果如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/check_salary_table_records.py
工资数据表记录数量统计:
================================================================================

2025年09月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年08月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年07月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年06月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年05月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年04月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年03月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年02月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2025年01月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2024年11月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2024年10月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2023年12月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2023年08月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

2022年05月数据:
----------------------------------------
  退休人员: 2条记录
  离休人员: 13条记录
  全部在职人员: 1,396条记录
  A岗职工: 62条记录

================================================================================
分页显示阈值分析:
- 分页阈值: 100条记录
- 超过100条的表将显示分页组件
- 100条以下的表将隐藏分页组件

需要分页的表:
  2025年09月 全部在职人员: 1,396条 ✓
  2025年08月 全部在职人员: 1,396条 ✓
  2025年07月 全部在职人员: 1,396条 ✓
  2025年06月 全部在职人员: 1,396条 ✓
  2025年05月 全部在职人员: 1,396条 ✓
  2025年04月 全部在职人员: 1,396条 ✓
  2025年03月 全部在职人员: 1,396条 ✓
  2025年02月 全部在职人员: 1,396条 ✓
  2025年01月 全部在职人员: 1,396条 ✓
  2024年11月 全部在职人员: 1,396条 ✓
  2024年10月 全部在职人员: 1,396条 ✓
  2023年12月 全部在职人员: 1,396条 ✓
  2023年08月 全部在职人员: 1,396条 ✓
  2022年05月 全部在职人员: 1,396条 ✓

---

_**Assistant**_



---

现在我明白问题所在了！从测试结果可以清楚地看出：

## 问题分析

**分页组件显示逻辑完全正常**，分页组件只在"全部在职人员"表中显示是因为：

1. **只有"全部在职人员"表数据量大**：1,396条记录，超过了100条的分页阈值
2. **其他表数据量都很小**：
   - 退休人员：2条记录
   - 离休人员：13条记录  
   - A岗职工：62条记录

3. **当前分页阈值设置**：100条记录
   - 超过100条 → 显示分页组件
   - 100条以下 → 隐藏分页组件

## 优化建议

根据实际使用情况，我建议几个优化方案：

### 方案1：降低分页阈值（推荐）
将分页阈值从100条降低到30条，这样A岗职工(62条)也能显示分页，提供更一致的用户体验。

### 方案2：为所有表显示分页组件
无论数据量多少，都显示分页组件，让用户有统一的界面体验。

### 方案3：智能分页阈值
根据不同表的特点设置不同的阈值。

让我实施方案1，将分页阈值降低到30条：

---



---

现在让我们测试一下新的分页阈值。创建一个测试脚本来验证哪些表现在会显示分页：

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 测试新分页阈值脚本
+ 
+ 验证将分页阈值从100条降低到30条后，哪些表会显示分页组件
+ """
+ 
+ import sqlite3
+ import os
+ import sys
+ 
+ # 添加项目根目录到路径
+ sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
+ 
+ def test_new_pagination_threshold():
+     """测试新的分页阈值"""
+     
+     NEW_THRESHOLD = 30  # 新的分页阈值
+     
+     db_path = "data/db/salary_system.db"
+     
+     if not os.path.exists(db_path):
+         print(f"数据库文件不存在: {db_path}")
+         return
+     
+     try:
+         conn = sqlite3.connect(db_path)
+         cursor = conn.cursor()
+         
+         # 获取所有以salary_data开头的表
+         cursor.execute("""
+             SELECT name FROM sqlite_master 
+             WHERE type='table' AND name LIKE 'salary_data_%'
+             ORDER BY name DESC
+         """)
+         
+         salary_tables = cursor.fetchall()
+         
+         print(f"新分页阈值测试 (阈值: {NEW_THRESHOLD}条)")
+         print("=" * 80)
+         
+         # 按年月分组统计
+         table_data = {}
+         
+         for table_name, in salary_tables:
+             try:
+                 # 获取记录数
+                 cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
+                 count = cursor.fetchone()[0]
+                 
+                 # 解析表名
+                 parts = table_name.split('_')
+                 if len(parts) >= 5:
+                     year = parts[2]
+                     month = parts[3]
+                     category = '_'.join(parts[4:])
+                     
+                     key = f"{year}_{month}"
+                     if key not in table_data:
+                         table_data[key] = {}
+                     
+                     table_data[key][category] = count
+                     
+             except Exception as e:
+                 print(f"查询表 {table_name} 失败: {e}")
+         
+         # 分析最新的几个月的数据
+         recent_periods = sorted(table_data.keys(), reverse=True)[:3]
+         
+         print("最近3个月的分页显示情况:")
+         print("-" * 80)
+         
+         for period in recent_periods:
+             year, month = period.split('_')
+             print(f"\n{year}年{month}月:")
+             
+             categories = table_data[period]
+             for category, count in categories.items():
+                 # 转换为中文显示
+                 category_map = {
+                     'active_employees': '全部在职人员',
+                     'retired_employees': '退休人员', 
+                     'pension_employees': '离休人员',
+                     'a_grade_employees': 'A岗职工'
+                 }
+                 
+                 display_name = category_map.get(category, category)
+                 
+                 # 判断是否显示分页
+                 will_paginate = count > NEW_THRESHOLD
+                 pagination_status = "✓ 显示分页" if will_paginate else "✗ 隐藏分页"
+                 
+                 print(f"  {display_name}: {count:,}条 → {pagination_status}")
+         
+         print("\n" + "=" * 80)
+         print("分页显示统计:")
+         
+         # 统计会显示分页的表
+         paginated_tables = []
+         non_paginated_tables = []
+         
+         for period in table_data.keys():
+             year, month = period.split('_')
+             categories = table_data[period]
+             
+             for category, count in categories.items():
+                 category_map = {
+                     'active_employees': '全部在职人员',
+                     'retired_employees': '退休人员', 
+                     'pension_employees': '离休人员',
+                     'a_grade_employees': 'A岗职工'
+                 }
+                 display_name = category_map.get(category, category)
+                 
+                 if count > NEW_THRESHOLD:
+                     if display_name not in [item[0] for item in paginated_tables]:
+                         paginated_tables.append((display_name, count))
+                 else:
+                     if display_name not in [item[0] for item in non_paginated_tables]:
+                         non_paginated_tables.append((display_name, count))
+         
+         print(f"\n将显示分页组件的表类型 (>{NEW_THRESHOLD}条):")
+         for table_type, count in paginated_tables:
+             print(f"  ✓ {table_type}: {count:,}条")
+         
+         print(f"\n将隐藏分页组件的表类型 (≤{NEW_THRESHOLD}条):")
+         for table_type, count in non_paginated_tables:
+             print(f"  ✗ {table_type}: {count:,}条")
+         
+         print(f"\n改进效果:")
+         print(f"  - 原阈值(100条): 只有1种表类型显示分页")
+         print(f"  - 新阈值({NEW_THRESHOLD}条): {len(paginated_tables)}种表类型显示分页")
+         print(f"  - 改进程度: {len(paginated_tables) - 1}种表类型新增分页功能")
+         
+         conn.close()
+         
+     except Exception as e:
+         print(f"测试失败: {e}")
+ 
+ if __name__ == "__main__":
+     test_new_pagination_threshold() 
```

---

现在让我们测试新的分页阈值设置：

---

```bash
python temp/test_new_pagination_threshold.py
```

---

_**User**_

测试脚本运行结果如下“
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/test_new_pagination_threshold.py
新分页阈值测试 (阈值: 30条)
================================================================================
最近3个月的分页显示情况:
--------------------------------------------------------------------------------

2025年09月:
  退休人员: 2条 → ✗ 隐藏分页
  离休人员: 13条 → ✗ 隐藏分页
  全部在职人员: 1,396条 → ✓ 显示分页
  A岗职工: 62条 → ✓ 显示分页

2025年08月:
  退休人员: 2条 → ✗ 隐藏分页
  离休人员: 13条 → ✗ 隐藏分页
  全部在职人员: 1,396条 → ✓ 显示分页
  A岗职工: 62条 → ✓ 显示分页

2025年07月:
  退休人员: 2条 → ✗ 隐藏分页
  离休人员: 13条 → ✗ 隐藏分页
  全部在职人员: 1,396条 → ✓ 显示分页
  A岗职工: 62条 → ✓ 显示分页

================================================================================
分页显示统计:

将显示分页组件的表类型 (>30条):
  ✓ 全部在职人员: 1,396条
  ✓ A岗职工: 62条

将隐藏分页组件的表类型 (≤30条):
  ✗ 退休人员: 2条
  ✗ 离休人员: 13条

改进效果:
  - 原阈值(100条): 只有1种表类型显示分页
  - 新阈值(30条): 2种表类型显示分页
  - 改进程度: 1种表类型新增分页功能

---

_**Assistant**_



---

太好了！分页阈值优化非常成功！🎉

## 优化效果总结

### ✅ 成功改进
- **原来**：只有"全部在职人员"(1,396条)显示分页组件
- **现在**：**2种表类型**都显示分页组件：
  - ✓ 全部在职人员：1,396条 → 显示分页
  - ✓ **A岗职工：62条 → 显示分页**（新增）

### ✅ 合理保持
- ✗ 退休人员：2条 → 隐藏分页（合理，数据量很小）
- ✗ 离休人员：13条 → 隐藏分页（合理，数据量很小）

## 用户体验提升

1. **更一致的界面体验**：现在用户在查看A岗职工数据时也能看到分页组件
2. **合理的阈值设置**：30条是一个很好的平衡点
3. **性能优化**：小数据量表（退休、离休人员）仍然使用快速加载模式

## 测试建议

现在请您测试一下系统，验证分页组件是否正常工作：

1. **启动系统**：`python main.py`
2. **测试场景**：
   - 浏览"全部在职人员" → 应该显示分页组件
   - 浏览"A岗职工" → **应该显示分页组件**（新功能）
   - 浏览"退休人员" → 应该隐藏分页组件
   - 浏览"离休人员" → 应该隐藏分页组件

## 进一步优化选项（可选）

如果您希望更加统一的用户体验，我们还可以考虑：

### 选项1：进一步降低阈值到15条
这样"离休人员"(13条)也会隐藏分页，但用户体验更统一

### 选项2：为所有表显示分页组件
无论数据量多少都显示分页，但对于很小的数据量可能显得冗余

### 选项3：当前设置已经很好
30条的阈值在功能性和合理性之间取得了很好的平衡

您觉得当前的30条阈值设置如何？需要进一步调整吗，还是现在就测试一下系统看看实际效果？

---

_**User**_

尽管你将阈值设置为30，但是分页组件还是以100条记录为一页。另外，从表”全部工作人员“切换到表”A岗职工“还是很慢。
终端日志如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
2025-06-23 19:26:52.278 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-23 19:26:52.279 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-23 19:26:52.280 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-23 19:26:52.280 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-23 19:26:52.281 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-23 19:26:52.283 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-23 19:26:54.779 | INFO     | __main__:setup_app_logging:210 | 月度工资异动处理系统 v1.0.0 启动
2025-06-23 19:26:54.779 | INFO     | __main__:main:274 | 初始化核心管理器...
2025-06-23 19:26:54.779 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-23 19:26:54.779 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-23 19:26:54.779 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-23 19:26:54.779 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-23 19:26:54.779 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-23 19:26:54.834 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-23 19:26:54.834 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\sal
ary_system.db
2025-06-23 19:26:54.835 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-23 19:26:54.836 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-23 19:26:54.836 | INFO     | __main__:main:279 | 核心管理器初始化完成。
2025-06-23 19:26:55.691 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-23 19:26:55.691 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-23 19:26:55.691 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-23 19:26:55.691 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2098 | 管理器设置完成
2025-06-23 19:26:55.751 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-23 19:26:55.751 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-23 19:26:55.861 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-23 19:26:55.861 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-23 19:26:56.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:865 | 开始从元数据动态加载工资数据...
2025-06-23 19:26:56.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:872 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-23 19:26:56.600 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:603 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-23 19:26:56.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:638 | 恢复导航状态: 40个展开项
2025-06-23 19:26:56.622 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['异动人员表 > 2025年', '异动人员表 > 2025年 > 4月
', '异动人员表 > 2025年 > 5月', '工资表 > 2025年', '工资表']
2025-06-23 19:26:56.622 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:444 | 增强导航面板初始化完成
2025-06-23 19:26:56.688 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1250 | 快捷键注册完成: 18/18 个
2025-06-23 19:26:56.690 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1461 | 拖拽排序管理器初始化完成
2025-06-23 19:26:56.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1828 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数:
1000
2025-06-23 19:26:56.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-23 19:26:56.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-23 19:26:56.709 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.73ms
2025-06-23 19:26:56.711 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1062 | 表格已初始化为空白状态，等待用户导入数据
2025-06-23 19:26:56.730 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-23 19:26:56.792 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:372 | 控制面板按钮信号连接完成
2025-06-23 19:26:57.265 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1459 | 菜单栏创建完成
2025-06-23 19:26:57.266 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_p
references.json
2025-06-23 19:26:57.267 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.js
on
2025-06-23 19:26:57.268 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-23 19:26:57.269 | INFO     | src.gui.prototype.prototype_main_window:__init__:1438 | 菜单栏管理器初始化完成
2025-06-23 19:26:57.269 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2085 | 快捷键设置完成
2025-06-23 19:26:57.270 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2074 | 主窗口UI设置完成。
2025-06-23 19:26:57.270 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2123 | 信号连接设置完成
2025-06-23 19:26:57.271 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2464 | 已加载 1 个表的字段映射信息
2025-06-23 19:26:57.272 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2464 | 已加载 1 个表的字段映射信息
2025-06-23 19:26:57.272 | WARNING  | src.gui.prototype.prototype_main_window:set_data:453 | 尝试设置空数据，将显示提示信息。
2025-06-23 19:26:57.273 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-23 19:26:57.273 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-23 19:26:57.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.03ms
2025-06-23 19:26:57.275 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1062 | 表格已初始化为空白状态，等待用户导入数据
2025-06-23 19:26:57.280 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:26:57.280 | INFO     | src.gui.prototype.prototype_main_window:__init__:2026 | 原型主窗口初始化完成
2025-06-23 19:26:57.803 | INFO     | __main__:main:301 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-23 19:26:57.927 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:887 | 执行延迟的工资数据加载...
2025-06-23 19:26:57.928 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:985 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-23 19:26:57.929 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:993 | 找到工资表节点: � 工资表
2025-06-23 19:26:58.213 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 56 个匹配类型 'salary_data' 的表
2025-06-23 19:26:58.217 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1029 | 创建年份节点: 2025年，包含 9 个月份
2025-06-23 19:26:58.219 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1029 | 创建年份节点: 2024年，包含 2 个月份
2025-06-23 19:26:58.253 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1029 | 创建年份节点: 2023年，包含 2 个月份
2025-06-23 19:26:58.255 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1029 | 创建年份节点: 2022年，包含 1 个月份
2025-06-23 19:26:58.255 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1049 | 工资数据导航已强制刷新: 4 个年份, 14 个月份
2025-06-23 19:26:58.256 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1052 | 默认路径: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-23 19:26:58.257 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-23 19:26:58.262 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1210 | MainWorkspaceArea 响应式适配: sm
2025-06-23 19:27:10.018 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '异动人员表 > 2025年', '异动人
员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月', '工资表']
2025-06-23 19:27:10.033 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年
2025-06-23 19:27:10.033 | WARNING  | src.gui.prototype.prototype_main_window:set_data:453 | 尝试设置空数据，将显示提示信息。
2025-06-23 19:27:10.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-23 19:27:10.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-23 19:27:10.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-23 19:27:10.033 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1062 | 表格已初始化为空白状态，等待用户导入数据
2025-06-23 19:27:10.049 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:10.049 | WARNING  | src.gui.prototype.prototype_main_window:set_data:453 | 尝试设置空数据，将显示提示信息。
2025-06-23 19:27:10.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-23 19:27:10.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-23 19:27:10.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-23 19:27:10.049 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1062 | 表格已初始化为空白状态，等待用户导入数据
2025-06-23 19:27:10.049 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:10.049 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '异动人员表 > 2025年', '异动人
员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月', '工资表']
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月
2025-06-23 19:27:13.471 | WARNING  | src.gui.prototype.prototype_main_window:set_data:453 | 尝试设置空数据，将显示提示信息。
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1062 | 表格已初始化为空白状态，等待用户导入数据
2025-06-23 19:27:13.471 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:13.471 | WARNING  | src.gui.prototype.prototype_main_window:set_data:453 | 尝试设置空数据，将显示提示信息。
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2546 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 0 行, 7 列
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1062 | 表格已初始化为空白状态，等待用户导入数据
2025-06-23 19:27:13.471 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:13.471 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月
2025-06-23 19:27:22.852 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月', '异动
人员表 > 2025年', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月']
2025-06-23 19:27:22.852 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:27:22.852 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:22.868 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2564 | 数据量大(62条)，启用分页模式
2025-06-23 19:27:22.868 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2584 | 使用分页模式加载 salary_data_2025_01_a_grade_employees，第1页，每页
100条
2025-06-23 19:27:22.868 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2627 | 缓存未命中，从数据库加载: salary_data_2025_01_a_grade_employees 第1
页
2025-06-23 19:27:22.868 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:27:22.868 | INFO     | src.gui.prototype.prototype_main_window:run:120 | 开始加载表 salary_data_2025_01_a_grade_employees 第1页数据，每页100条
2025-06-23 19:27:22.868 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_01_a_grade_employees 分页获取数据: 第
1页, 每页100条
2025-06-23 19:27:22.908 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_01_a_grade_employees 获取第1页数据: 6
2 行，总计62行
2025-06-23 19:27:22.909 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_a_grade_employees 没有保存的字段映射
信息
2025-06-23 19:27:22.910 | INFO     | src.gui.prototype.prototype_main_window:run:132 | 成功加载 62 条数据，总记录数: 62
2025-06-23 19:27:22.911 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2657 | 分页数据加载成功（数据库）: 62条数据，第1页，总计62条
2025-06-23 19:27:22.918 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 62 行
2025-06-23 19:27:22.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 1000 -> 62
2025-06-23 19:27:22.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(62)自动调整最大可见行数为: 62
2025-06-23 19:27:22.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 62 行, 16 列
2025-06-23 19:27:22.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 62 行, 最大可见行数: 62, 耗时: 22.84ms
2025-06-23 19:27:22.957 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:27:22.957 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:27:38.794 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月', '异动
人员表 > 2025年', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月']
2025-06-23 19:27:38.794 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > 退休人员
2025-06-23 19:27:38.796 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:38.796 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2567 | 数据量小(13条)，使用普通模式
2025-06-23 19:27:38.798 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > 退休人员
2025-06-23 19:27:38.798 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_01_pension_employees 获取数据...
2025-06-23 19:27:38.800 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_01_pension_employees 获取 13 行数据。
2025-06-23 19:27:38.801 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2720 | 成功从数据库加载 13 行数据
2025-06-23 19:27:38.801 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_pension_employees 没有保存的字段映射
信息
2025-06-23 19:27:38.802 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2725 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-23 19:27:38.812 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 13 行
2025-06-23 19:27:40.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 62 -> 13
2025-06-23 19:27:40.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-23 19:27:40.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 16 列
2025-06-23 19:27:40.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 1630.69ms
2025-06-23 19:27:40.448 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月', '异动
人员表 > 2025年', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月']
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > 离休人员
2025-06-23 19:27:45.558 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2567 | 数据量小(2条)，使用普通模式
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > 离休人员
2025-06-23 19:27:45.558 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_01_retired_employees 获取数据...
2025-06-23 19:27:45.558 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_01_retired_employees 获取 2 行数据。
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2720 | 成功从数据库加载 2 行数据
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_retired_employees 没有保存的字段映射
信息
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2725 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-23 19:27:45.558 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 2 行
2025-06-23 19:27:45.558 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2508 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-23 19:27:45.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 13 -> 2
2025-06-23 19:27:45.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-23 19:27:45.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 2 行, 16 列
2025-06-23 19:27:45.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 91.66ms
2025-06-23 19:27:45.649 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > A岗职
工', '工资表 > 2025年 > 1月', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:27:49.375 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2564 | 数据量大(62条)，启用分页模式
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2584 | 使用分页模式加载 salary_data_2025_01_a_grade_employees，第1页，每页
100条
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2593 | 缓存命中: salary_data_2025_01_a_grade_employees 第1页
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_a_grade_employees 没有保存的字段映射
信息
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2657 | 分页数据加载成功（缓存）: 62条数据，第1页，总计62条
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 62 行
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 2 -> 62
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(62)自动调整最大可见行数为: 62
2025-06-23 19:27:49.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 62 行, 16 列
2025-06-23 19:27:49.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 62 行, 最大可见行数: 62, 耗时: 15.67ms
2025-06-23 19:27:49.391 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:27:49.391 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:27:49.391 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > A岗职
工', '工资表 > 2025年 > 1月', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-23 19:28:01.938 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2564 | 数据量大(1396条)，启用分页模式
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2584 | 使用分页模式加载 salary_data_2025_01_active_employees，第1页，每页1
00条
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2627 | 缓存未命中，从数据库加载: salary_data_2025_01_active_employees 第1
页
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.prototype_main_window:run:120 | 开始加载表 salary_data_2025_01_active_employees 第1页数据，每页100条
2025-06-23 19:28:01.938 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_01_active_employees 分页获取数据: 第1
页, 每页100条
2025-06-23 19:28:01.938 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_01_active_employees 获取第1页数据: 10
0 行，总计1396行
2025-06-23 19:28:01.938 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_active_employees 没有保存的字段映射信
息
2025-06-23 19:28:01.954 | INFO     | src.gui.prototype.prototype_main_window:run:132 | 成功加载 100 条数据，总记录数: 1396
2025-06-23 19:28:01.954 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2657 | 分页数据加载成功（数据库）: 100条数据，第1页，总计1396条
2025-06-23 19:28:01.954 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_01_active_employees 分页获取数据: 第2
页, 每页100条
2025-06-23 19:28:01.954 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 100 行
2025-06-23 19:28:01.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 62 -> 100
2025-06-23 19:28:01.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(100)自动调整最大可见行数为: 100
2025-06-23 19:28:01.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 100 行, 16 列
2025-06-23 19:28:01.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 100 行, 最大可见行数: 100, 耗时: 29.10ms
2025-06-23 19:28:02.000 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 100
2025-06-23 19:28:02.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_01_active_employees 获取第2页数据: 10
0 行，总计1396行
2025-06-23 19:28:02.004 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > A岗职
工', '工资表 > 2025年 > 1月 > 退休人员', '工资表 > 2025年 > 1月', '异动人员表 > 2025年']
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > 退休人员
2025-06-23 19:28:22.076 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2567 | 数据量小(13条)，使用普通模式
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > 退休人员
2025-06-23 19:28:22.076 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_01_pension_employees 获取数据...
2025-06-23 19:28:22.076 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_01_pension_employees 获取 13 行数据。
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2720 | 成功从数据库加载 13 行数据
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_pension_employees 没有保存的字段映射
信息
2025-06-23 19:28:22.076 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2725 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-23 19:28:22.092 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 13 行
2025-06-23 19:28:24.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 100 -> 13
2025-06-23 19:28:24.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-23 19:28:24.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 13 行, 16 列
2025-06-23 19:28:24.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 2597.76ms
2025-06-23 19:28:24.720 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-23 19:28:31.982 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > 退休
人员', '工资表 > 2025年 > 1月 > A岗职工', '工资表 > 2025年 > 1月 > 离休人员', '工资表 > 2025年 > 1月']
2025-06-23 19:28:31.986 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > 离休人员
2025-06-23 19:28:31.987 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:28:31.989 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2567 | 数据量小(2条)，使用普通模式
2025-06-23 19:28:31.989 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > 离休人员
2025-06-23 19:28:31.990 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_01_retired_employees 获取数据...
2025-06-23 19:28:31.993 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_01_retired_employees 获取 2 行数据。
2025-06-23 19:28:31.996 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2720 | 成功从数据库加载 2 行数据
2025-06-23 19:28:31.996 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_retired_employees 没有保存的字段映射
信息
2025-06-23 19:28:31.997 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2725 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name',
'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-23 19:28:31.998 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 2 行
2025-06-23 19:28:32.000 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2508 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-23 19:28:32.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 13 -> 2
2025-06-23 19:28:32.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-23 19:28:32.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 2 行, 16 列
2025-06-23 19:28:32.092 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 91.79ms
2025-06-23 19:28:32.094 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 1月 > A岗职工', '工资表 > 2025
年', '工资表 > 2025年 > 1月 > 离休人员', '工资表 > 2025年 > 1月 > 退休人员', '工资表 > 2025年 > 1月']
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:28:34.526 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2564 | 数据量大(62条)，启用分页模式
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2584 | 使用分页模式加载 salary_data_2025_01_a_grade_employees，第1页，每页
100条
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2593 | 缓存命中: salary_data_2025_01_a_grade_employees 第1页
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_a_grade_employees 没有保存的字段映射
信息
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2657 | 分页数据加载成功（缓存）: 62条数据，第1页，总计62条
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 62 行
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 2 -> 62
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(62)自动调整最大可见行数为: 62
2025-06-23 19:28:34.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 62 行, 16 列
2025-06-23 19:28:34.542 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 62 行, 最大可见行数: 62, 耗时: 15.67ms
2025-06-23 19:28:34.542 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:28:34.542 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:28:34.542 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 1月 > A岗职工', '工资表 > 2025
年', '工资表 > 2025年 > 1月 > 离休人员', '工资表 > 2025年 > 1月 > 退休人员', '工资表 > 2025年 > 1月 > 全部在职人员']
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-23 19:28:41.484 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2564 | 数据量大(1396条)，启用分页模式
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2584 | 使用分页模式加载 salary_data_2025_01_active_employees，第1页，每页1
00条
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2593 | 缓存命中: salary_data_2025_01_active_employees 第1页
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_active_employees 没有保存的字段映射信
息
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2657 | 分页数据加载成功（缓存）: 100条数据，第1页，总计1396条
2025-06-23 19:28:41.484 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 100 行
2025-06-23 19:28:41.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 62 -> 100
2025-06-23 19:28:41.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(100)自动调整最大可见行数为: 100
2025-06-23 19:28:41.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 100 行, 16 列
2025-06-23 19:28:41.522 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 100 行, 最大可见行数: 100, 耗时: 22.58ms
2025-06-23 19:28:41.523 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 100
2025-06-23 19:28:41.524 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-23 19:28:41.527 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-23 19:28:48.757 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 1月 > A岗职工', '工资表 > 2025
年', '工资表 > 2025年 > 1月 > 全部在职人员', '工资表 > 2025年 > 1月 > 离休人员', '工资表 > 2025年 > 1月 > 退休人员']
2025-06-23 19:28:48.757 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2522 | 导航变化: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:28:48.757 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-23 19:28:48.773 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2564 | 数据量大(62条)，启用分页模式
2025-06-23 19:28:48.773 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2584 | 使用分页模式加载 salary_data_2025_01_a_grade_employees，第1页，每页
100条
2025-06-23 19:28:48.773 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2593 | 缓存命中: salary_data_2025_01_a_grade_employees 第1页
2025-06-23 19:28:48.773 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2513 | 表 salary_data_2025_01_a_grade_employees 没有保存的字段映射
信息
2025-06-23 19:28:48.773 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2657 | 分页数据加载成功（缓存）: 62条数据，第1页，总计62条
2025-06-23 19:28:48.773 | INFO     | src.gui.prototype.prototype_main_window:set_data:460 | 通过公共接口设置新的表格数据: 62 行
2025-06-23 19:29:00.357 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2517 | 最大可见行数已更新: 100 -> 62
2025-06-23 19:29:00.357 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2570 | 根据数据量(62)自动调整最大可见行数为: 62
2025-06-23 19:29:00.357 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:388 | 表格数据已设置: 62 行, 16 列
2025-06-23 19:29:00.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1961 | 表格数据设置完成: 62 行, 最大可见行数: 62, 耗时: 11600.02ms
2025-06-23 19:29:00.373 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:29:00.373 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-23 19:29:00.373 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:734 | 导航选择: 工资表 > 2025年 > 1月 > A岗职工
2025-06-23 19:29:14.990 | INFO     | __main__:main:306 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_



---

通过分析日志，我发现了两个主要问题：

## 问题1：分页组件仍然以100条为一页

虽然我们将**显示阈值**从100改为30，但**每页记录数**仍然是100条。从日志可以看到：
- `每页100条` 
- `第1页，每页100条`

## 问题2：切换仍然很慢

日志显示切换时间仍然很长：
- `耗时: 11600.02ms` (约11.6秒)
- `耗时: 2597.76ms` (约2.6秒)

让我解决这些问题：

### 修复1：调整分页组件的默认页面大小

---

Read file: src/gui/widgets/pagination_widget.py

---

现在我找到了问题！分页组件的默认页面大小和页面大小选项都设置得过大。让我修复这些设置：

---



---



---

现在还需要检查主窗口中分页加载的默认页面大小：

---

<details>
            <summary>Grep search for "page_size=100" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test_pagination_simple.py` | L140 | `page_size=100,` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L105 | `def __init__(self, table_manager, table_name, page=1, page_size=100, apply_mapping_func=None):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L2564 | `self._load_data_with_pagination(table_name, page=1, page_size=100)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L105 | `def __init__(self, table_manager, table_name, page=1, page_size=100, apply_mapping_func=None):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L2564 | `self._load_data_with_pagination(table_name, page=1, page_size=100)` |

</details>

---

找到了！主窗口中还有硬编码的`page_size=100`。让我修复这些：

---



---



---

Read file: src/gui/prototype/prototype_main_window.py

---



---

现在修复主窗口中的硬编码page_size：

---



---



---



---



---

现在还需要检查`_load_data_with_pagination`方法的默认参数：

---

Read file: src/gui/prototype/prototype_main_window.py

---



---



---



---

现在我需要解决性能问题。从日志看，主要的性能瓶颈在于表格渲染。让我检查表格渲染的相关代码并优化：

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 测试优化后的分页设置
+ 
+ 验证分页阈值30条、页面大小50条的设置是否正常工作
+ """
+ 
+ import sys
+ import os
+ sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
+ 
+ from src.gui.widgets.pagination_widget import PaginationWidget, PaginationState
+ from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
+ from PyQt5.QtCore import Qt
+ 
+ def test_pagination_settings():
+     """测试分页组件的优化设置"""
+     
+     app = QApplication(sys.argv)
+     
+     # 创建测试窗口
+     window = QMainWindow()
+     window.setWindowTitle("分页组件优化测试")
+     window.setGeometry(100, 100, 800, 200)
+     
+     central_widget = QWidget()
+     window.setCentralWidget(central_widget)
+     
+     layout = QVBoxLayout(central_widget)
+     
+     # 添加说明标签
+     info_label = QLabel("分页组件优化测试 - 新设置验证")
+     info_label.setAlignment(Qt.AlignCenter)
+     layout.addWidget(info_label)
+     
+     # 创建分页组件
+     pagination_widget = PaginationWidget()
+     layout.addWidget(pagination_widget)
+     
+     # 测试结果标签
+     result_label = QLabel()
+     result_label.setAlignment(Qt.AlignCenter)
+     layout.addWidget(result_label)
+     
+     # 验证设置
+     state = pagination_widget.get_current_state()
+     default_page_size = pagination_widget.default_page_size
+     page_size_options = pagination_widget.page_size_options
+     
+     results = []
+     results.append(f"✓ 默认页面大小: {default_page_size} (期望: 50)")
+     results.append(f"✓ 页面大小选项: {page_size_options} (期望: [20, 50, 100, 200])")
+     results.append(f"✓ 初始状态页面大小: {state.page_size} (期望: 50)")
+     
+     # 验证是否符合预期
+     all_correct = True
+     if default_page_size != 50:
+         results[0] = f"✗ 默认页面大小: {default_page_size} (期望: 50)"
+         all_correct = False
+     
+     if page_size_options != [20, 50, 100, 200]:
+         results[1] = f"✗ 页面大小选项: {page_size_options} (期望: [20, 50, 100, 200])"
+         all_correct = False
+     
+     if state.page_size != 50:
+         results[2] = f"✗ 初始状态页面大小: {state.page_size} (期望: 50)"
+         all_correct = False
+     
+     # 测试模拟数据
+     print("=" * 60)
+     print("分页组件优化测试结果:")
+     print("=" * 60)
+     
+     for result in results:
+         print(result)
+     
+     # 测试不同数据量的分页显示逻辑
+     test_cases = [
+         (20, "小数据量", "应该隐藏分页"),
+         (50, "中等数据量", "应该显示分页"),
+         (100, "大数据量", "应该显示分页"),
+         (1396, "超大数据量", "应该显示分页")
+     ]
+     
+     print("\n" + "-" * 60)
+     print("不同数据量的分页显示逻辑测试 (阈值: 30条):")
+     print("-" * 60)
+     
+     PAGINATION_THRESHOLD = 30
+     
+     for total_records, description, expected in test_cases:
+         will_show_pagination = total_records > PAGINATION_THRESHOLD
+         status = "显示分页" if will_show_pagination else "隐藏分页"
+         
+         if (will_show_pagination and "显示" in expected) or (not will_show_pagination and "隐藏" in expected):
+             result_icon = "✓"
+         else:
+             result_icon = "✗"
+             all_correct = False
+         
+         print(f"{result_icon} {total_records:4d}条 ({description:8s}) → {status:8s} ({expected})")
+     
+     # 计算分页数量
+     print("\n" + "-" * 60)
+     print("分页数量计算测试 (页面大小: 50条):")
+     print("-" * 60)
+     
+     PAGE_SIZE = 50
+     for total_records, description, _ in test_cases:
+         total_pages = max(1, (total_records + PAGE_SIZE - 1) // PAGE_SIZE)
+         print(f"✓ {total_records:4d}条记录 → {total_pages:2d}页 (每页{PAGE_SIZE}条)")
+     
+     print("\n" + "=" * 60)
+     
+     if all_correct:
+         print("🎉 所有测试通过！分页组件优化设置正确。")
+         result_text = "🎉 所有测试通过！分页组件优化设置正确。"
+         result_label.setStyleSheet("color: green; font-weight: bold; font-size: 14px;")
+     else:
+         print("❌ 部分测试失败，请检查配置。")
+         result_text = "❌ 部分测试失败，请检查配置。"
+         result_label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
+     
+     result_label.setText(result_text)
+     
+     # 测试实际分页功能
+     pagination_widget.set_total_records(1396)  # 模拟全部在职人员数据
+     
+     print(f"\n设置总记录数为1396条后的状态:")
+     updated_state = pagination_widget.get_current_state()
+     print(f"✓ 当前页: {updated_state.current_page}")
+     print(f"✓ 页面大小: {updated_state.page_size}")
+     print(f"✓ 总页数: {updated_state.total_pages}")
+     print(f"✓ 显示范围: 第{updated_state.start_record}-{updated_state.end_record}条")
+     
+     window.show()
+     return app.exec_()
+ 
+ if __name__ == "__main__":
+     test_pagination_settings() 
```

---



---

现在让我们测试优化后的设置：

---

```bash
python temp/test_optimized_pagination.py
```

---

_**User**_

测试脚本运行结果如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/test_optimized_pagination.py
2025-06-23 19:38:33.199 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-23 19:38:33.200 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-23 19:38:33.200 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-23 19:38:33.202 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-23 19:38:33.203 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-23 19:38:33.204 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-23 19:38:35.601 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
============================================================
分页组件优化测试结果:
============================================================
✓ 默认页面大小: 50 (期望: 50)
✓ 页面大小选项: [20, 50, 100, 200] (期望: [20, 50, 100, 200])
✓ 初始状态页面大小: 50 (期望: 50)

------------------------------------------------------------
不同数据量的分页显示逻辑测试 (阈值: 30条):
------------------------------------------------------------
✓   20条 (小数据量    ) → 隐藏分页     (应该隐藏分页)
✓   50条 (中等数据量   ) → 显示分页     (应该显示分页)
✓  100条 (大数据量    ) → 显示分页     (应该显示分页)
✓ 1396条 (超大数据量   ) → 显示分页     (应该显示分页)

------------------------------------------------------------
分页数量计算测试 (页面大小: 50条):
------------------------------------------------------------
✓   20条记录 →  1页 (每页50条)
✓   50条记录 →  1页 (每页50条)
✓  100条记录 →  2页 (每页50条)
✓ 1396条记录 → 28页 (每页50条)

============================================================
� 所有测试通过！分页组件优化设置正确。
2025-06-23 19:38:36.176 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396

设置总记录数为1396条后的状态:
✓ 当前页: 1
✓ 页面大小: 50
✓ 总页数: 28
✓ 显示范围: 第1-50条
2025-06-23 19:39:26.792 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2

---

_**Assistant**_



---

