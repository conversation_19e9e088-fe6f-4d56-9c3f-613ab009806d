<!-- Generated by SpecStory -->

# Header change not reflected in data import (2025-06-24 15:15:59)

_**User**_

@/src 
在数据导入窗体，新增2021年12月数据导入，提示导入成功后，在主界面数据导航区域有新增导航，在列表展示区域也显示了新导入数据。在表头部分双击“employee_name"，更改为”姓名“，提示修改表头成功。但是，表头还是显示英文名”employee_name"，请看图片
终端日志如下：
2025-06-24 15:13:54.771 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新:
工资表 > 2022年 > 1月 > 全部在职人员
2025-06-24 15:14:02.079 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新:
工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:09.723 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173
 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-24 15:14:26.093 | INFO     | src.gui.dialogs:_on_import_mode_changed:1953 | 切换到多She
et导入模式
2025-06-24 15:14:40.154 | INFO     | src.modules.data_import.excel_importer:preview_data:221 |
数据预览完成: 27列, 14行
2025-06-24 15:14:41.394 | INFO     | src.modules.data_import.excel_importer:preview_data:221 |
数据预览完成: 27列, 14行
2025-06-24 15:14:48.804 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:14:48.804 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_
file:165 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:14:48.804 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:14:49.005 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173
 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-24 15:14:49.005 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_
file:176 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-24 15:14:49.020 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:14:49.020 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:14:49.025 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:14:49.138 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 3行 x 12列
2025-06-24 15:14:49.153 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:14:49.153 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.1000
00000006)}
2025-06-24 15:14:49.153 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-24 15:14:49.153 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 离休人员工资表 的配置，使用默认处理
2025-06-24 15:14:49.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-24 15:14:49.153 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2022_01_retired_employees 不存在，将根据模板创建...
2025-06-24 15:14:49.190 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2022_01_retired_employees 保存 2 条数据。
2025-06-24 15:14:49.192 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:14:49.192 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:14:49.192 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:14:49.330 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 14行 x 21列
2025-06-24 15:14:49.333 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:14:49.333 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607
.14625)}
2025-06-24 15:14:49.334 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-24 15:14:49.334 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 退休人员工资表 的配置，使用默认处理
2025-06-24 15:14:49.335 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'e
mployee_name', 'allowance', 'total_salary']
2025-06-24 15:14:49.336 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2022_01_pension_employees 不存在，将根据模板创建...
2025-06-24 15:14:49.341 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2022_01_pension_employees 保存 13 条数据。
2025-06-24 15:14:49.341 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:14:49.341 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:14:49.341 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:14:49.499 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 1397行 x 23列
2025-06-24 15:14:49.499 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:14:49.499 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71),
'2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int6
4(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.
float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144),
 '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64
(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.f
loat64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(196
7911.5299999986)}
2025-06-24 15:14:49.499 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-24 15:14:49.499 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 全部在职人员工资表 的配置，使用默认处理
2025-06-24 15:14:49.499 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'emplo
yee_name', 'allowance', 'total_salary']
2025-06-24 15:14:49.499 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2022_01_active_employees 不存在，将根据模板创建...
2025-06-24 15:14:49.531 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2022_01_active_employees 保存 1396 条数据。
2025-06-24 15:14:49.531 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:14:49.531 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:14:49.531 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:14:49.657 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 63行 x 17列
2025-06-24 15:14:49.657 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:14:49.657 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-24 15:14:49.673 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-24 15:14:49.674 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 A岗职工 的配置，使用默认处理
2025-06-24 15:14:49.674 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'emplo
yee_name', 'allowance', 'total_salary']
2025-06-24 15:14:49.674 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2022_01_a_grade_employees 不存在，将根据模板创建...
2025-06-24 15:14:49.689 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2022_01_a_grade_employees 保存 62 条数据。
2025-06-24 15:14:49.691 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_
file:195 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, '
message': '成功向表 salary_data_2022_01_retired_employees 保存 2 条数据。', 'table_name': 'sala
ry_data_2022_01_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message
': '成功向表 salary_data_2022_01_pension_employees 保存 13 条数据。', 'table_name': 'salary_dat
a_2022_01_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message'
: '成功向表 salary_data_2022_01_active_employees 保存 1396 条数据。', 'table_name': 'salary_dat
a_2022_01_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向
表 salary_data_2022_01_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2022_01_
a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4,
 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'f
ile_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工
工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.
1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets':
['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-24 15:14:49.693 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1425 | 多Sheet
导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向
表 salary_data_2022_01_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2022_01_r
etired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 sala
ry_data_2022_01_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2022_01_pension
_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salar
y_data_2022_01_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2022_01_active_
employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_202
2_01_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2022_01_a_grade_employees'
, 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets
': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path'
: 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终
版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extens
ion': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表',
'退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': '
separate_tables', 'data_period': '2022-01', 'data_description': '2022年1月工资数据', 'file_path
': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xl
s', 'target_path': '工资表 > 2022年 > 1月 > 退休人员'}
2025-06-24 15:14:49.696 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_impor
ted:2331 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True,
'message': '成功向表 salary_data_2022_01_retired_employees 保存 2 条数据。', 'table_name': 'sal
ary_data_2022_01_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'messag
e': '成功向表 salary_data_2022_01_pension_employees 保存 13 条数据。', 'table_name': 'salary_da
ta_2022_01_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message
': '成功向表 salary_data_2022_01_active_employees 保存 1396 条数据。', 'table_name': 'salary_da
ta_2022_01_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向
表 salary_data_2022_01_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2022_01_
a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4,
 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'f
ile_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工
工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.
1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets':
['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sh
eet', 'strategy': 'separate_tables', 'data_period': '2022-01', 'data_description': '2022年1月工
资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资
（报财务)  最终版.xls', 'target_path': '工资表 > 2022年 > 1月 > 退休人员'}
2025-06-24 15:14:49.698 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_impor
ted:2342 | 导入模式: multi_sheet, 目标路径: '工资表 > 2022年 > 1月 > 退休人员'
2025-06-24 15:14:49.698 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_impor
ted:2350 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:49.699 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2399 | 检查是否需要更新导航面板: ['工资表', '2022年', '1月', '退休人员']
2025-06-24 15:14:49.702 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2403 | 检测到工资数据导入，开始刷新导航面板
2025-06-24 15:14:49.703 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2407 | 使用强制刷新方法
2025-06-24 15:14:49.703 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:985 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-24 15:14:49.704 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:993 | 找到工资表节点: � 工资表
2025-06-24 15:14:49.716 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > 退休人员', '工资
表 > 2022年', '工资表 > 2025年 > 1月 > 离休人员', '工资表']
2025-06-24 15:14:49.717 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表
2025-06-24 15:14:49.717 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:14:49.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-24 15:14:49.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:14:49.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 1.06ms
2025-06-24 15:14:49.720 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:14:49.722 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:14:49.722 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:14:49.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-24 15:14:49.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:14:49.722 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-24 15:14:49.722 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:14:49.722 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:14:49.722 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表
2025-06-24 15:14:49.771 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_l
ist:702 | 找到 60 个匹配类型 'salary_data' 的表
2025-06-24 15:14:49.773 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2025年，包含 9 个月份
2025-06-24 15:14:49.775 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2024年，包含 2 个月份
2025-06-24 15:14:49.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2023年，包含 2 个月份
2025-06-24 15:14:49.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2022年，包含 2 个月份
2025-06-24 15:14:49.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1049 | 工资数据导航已强制刷新: 4 个年份, 15 个月份
2025-06-24 15:14:49.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1052 | 默认路径: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-24 15:14:49.776 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2412 | 将在800ms后导航到: 工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:50.588 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_impor
ted_path:2445 | 尝试导航到新导入的路径: 工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:50.600 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > 退休人员', '工资
表 > 2022年', '工资表 > 2025年 > 1月 > 离休人员', '工资表']
2025-06-24 15:14:50.602 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:50.603 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:14:50.604 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_da
ta_with_pagination:2602 | 数据量小(13条)，使用普通模式
2025-06-24 15:14:50.606 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:50.611 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_impor
ted_path:2450 | 已成功导航到新导入的路径: 工资表 > 2022年 > 1月 > 退休人员
2025-06-24 15:14:50.612 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_from_table:984 | 正在从表 salary_data_2022_01_pension_employees 获取数据...
2025-06-24 15:14:50.614 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_from_table:995 | 成功从表 salary_data_2022_01_pension_employees 获取 13 行数据。
2025-06-24 15:14:50.615 | INFO     | src.gui.prototype.prototype_main_window:_load_database_dat
a_with_mapping:2755 | 成功从数据库加载 13 行数据
2025-06-24 15:14:50.618 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mappi
ng_to_dataframe:2548 | 表 salary_data_2022_01_pension_employees 没有保存的字段映射信息
2025-06-24 15:14:50.620 | INFO     | src.gui.prototype.prototype_main_window:_load_database_dat
a_with_mapping:2760 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', '
department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'ded
uction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-24 15:14:50.628 | INFO     | src.gui.prototype.prototype_main_window:set_data:464 | 通
过公共接口设置新的表格数据: 13 行
2025-06-24 15:14:50.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_max_visible_rows:2542 | 最大可见行数已更新: 50 -> 13
2025-06-24 15:14:50.630 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2595 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-24 15:14:50.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 13 行, 16 列
2025-06-24 15:14:50.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 21.51ms
2025-06-24 15:14:50.653 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 |
总记录数设置为: 13
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 1月 > 退休人员', '工资
表 > 2022年', '工资表 > 2025年 > 1月 > 离休人员', '工资表']
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表 > 2022年 > 1月 > A岗职工
2025-06-24 15:15:05.127 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_da
ta_with_pagination:2599 | 数据量大(62条)，启用分页模式
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pa
gination:2619 | 使用分页模式加载 salary_data_2022_01_a_grade_employees，第1页，每页50条
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pa
gination:2662 | 缓存未命中，从数据库加载: salary_data_2022_01_a_grade_employees 第1页
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表 > 2022年 > 1月 > A岗职工
2025-06-24 15:15:05.127 | INFO     | src.gui.prototype.prototype_main_window:run:124 | 开始加载
表 salary_data_2022_01_a_grade_employees 第1页数据，每页50条
2025-06-24 15:15:05.127 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1016 | 正在从表 salary_data_2022_01_a_grade_employees 分页获取数据: 第1页, 每页50
条
2025-06-24 15:15:05.127 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1039 | 成功从表 salary_data_2022_01_a_grade_employees 获取第1页数据: 50 行，总计62
行
2025-06-24 15:15:05.143 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mappi
ng_to_dataframe:2548 | 表 salary_data_2022_01_a_grade_employees 没有保存的字段映射信息
2025-06-24 15:15:05.143 | INFO     | src.gui.prototype.prototype_main_window:run:136 | 成功加载
 50 条数据，总记录数: 62
2025-06-24 15:15:05.143 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_dat
a_loaded:2692 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-24 15:15:05.143 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1016 | 正在从表 salary_data_2022_01_a_grade_employees 分页获取数据: 第2页, 每页50
条
2025-06-24 15:15:05.143 | INFO     | src.gui.prototype.prototype_main_window:set_data:464 | 通
过公共接口设置新的表格数据: 50 行
2025-06-24 15:15:05.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_max_visible_rows:2542 | 最大可见行数已更新: 13 -> 50
2025-06-24 15:15:05.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2595 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-24 15:15:05.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-24 15:15:05.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-24 15:15:05.159 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 |
总记录数设置为: 50
2025-06-24 15:15:05.159 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1039 | 成功从表 salary_data_2022_01_a_grade_employees 获取第2页数据: 12 行，总计62
行
2025-06-24 15:15:05.159 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 |
总记录数设置为: 62
2025-06-24 15:15:20.939 | INFO     | src.modules.data_import.config_sync_manager:update_mapping
:203 | 表 salary_data_2022_01_a_grade_employees 配置不存在，创建新的字段映射配置
2025-06-24 15:15:20.939 | INFO     | src.modules.data_import.config_sync_manager:update_mapping
:222 | 字段 employee_name 在表 salary_data_2022_01_a_grade_employees 中不存在，创建新字段映射
2025-06-24 15:15:20.939 | INFO     | src.modules.data_import.config_sync_manager:update_mapping
:257 | 字段映射更新成功: salary_data_2022_01_a_grade_employees.employee_name -> 姓名
2025-06-24 15:15:20.939 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dial
og:115 | 字段编辑成功: employee_name -> 姓名
2025-06-24 15:15:20.939 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on
_header_double_clicked:3906 | 表头编辑成功: 列2, employee_name -> employee_name
2025-06-24 15:15:46.862 | INFO     | __main__:main:306 | 应用程序正常退出
应用程序已退出
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python .\main.py
2025-06-24 15:16:59.616 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系
统初始化完成
2025-06-24 15:16:59.616 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级
别: INFO
2025-06-24 15:16:59.616 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台
输出: True
2025-06-24 15:16:59.616 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输
出: True
2025-06-24 15:16:59.616 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文
件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-24 15:16:59.616 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
正在启动 月度工资异动处理系统 v1.0.0...
2025-06-24 15:17:00.541 | INFO     | __main__:setup_app_logging:210 | 月度工资异动处理系统 v1.0
.0 启动
2025-06-24 15:17:00.541 | INFO     | __main__:main:274 | 初始化核心管理器...
2025-06-24 15:17:00.541 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配
置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-24 15:17:00.541 | INFO     | src.modules.system_config.config_manager:load_config:338 |
 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-24 15:17:00.541 | INFO     | src.modules.system_config.config_manager:_generate_validat
ion_result:252 | 配置文件验证通过
2025-06-24 15:17:00.541 | INFO     | src.modules.system_config.config_manager:load_config:350 |
 配置文件加载成功
2025-06-24 15:17:00.541 | INFO     | src.modules.data_storage.database_manager:_initialize_data
base:101 | 开始初始化数据库...
2025-06-24 15:17:00.541 | INFO     | src.modules.data_storage.database_manager:_initialize_data
base:156 | 数据库初始化完成
2025-06-24 15:17:00.541 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数
据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.d
b
2025-06-24 15:17:00.541 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配
置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-24 15:17:00.541 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:10
2 | 动态表管理器初始化完成
2025-06-24 15:17:00.541 | INFO     | __main__:main:279 | 核心管理器初始化完成。
2025-06-24 15:17:01.008 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分
页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-24 15:17:01.008 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__ini
t__:121 | 响应式布局管理器初始化完成
2025-06-24 15:17:01.009 | INFO     | src.gui.prototype.managers.communication_manager:__init__:
134 | 组件通信管理器初始化完成
2025-06-24 15:17:01.009 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始
化完成
2025-06-24 15:17:01.011 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:21
25 | 管理器设置完成，包含增强版表头管理器
2025-06-24 15:17:01.013 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_
state:151 | 导航状态加载成功
2025-06-24 15:17:01.014 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:3
68 | 智能防抖搜索算法初始化完成
2025-06-24 15:17:01.055 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:
556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-24 15:17:01.055 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:14
8 | 智能树形展开算法初始化完成
2025-06-24 15:17:01.307 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_
dynamic_salary_data:865 | 开始从元数据动态加载工资数据...
2025-06-24 15:17:01.307 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_
dynamic_salary_data:872 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-24 15:17:01.311 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup
_navigation_data:603 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-24 15:17:01.322 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_resto
re_state:638 | 恢复导航状态: 44个展开项
2025-06-24 15:17:01.330 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月', '异
动人员表 > 2025年', '工资表 > 2025年', '工资表']
2025-06-24 15:17:01.331 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init
__:444 | 增强导航面板初始化完成
2025-06-24 15:17:01.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_re
gister_shortcuts:1263 | 快捷键注册完成: 18/18 个
2025-06-24 15:17:01.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__i
nit__:1474 | 拖拽排序管理器初始化完成
2025-06-24 15:17:01.377 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 |
配置同步管理器初始化完成
2025-06-24 15:17:01.377 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 |
表头编辑管理器初始化完成
2025-06-24 15:17:01.380 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__i
nit__:1850 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-24 15:17:01.381 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-24 15:17:01.381 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:17:01.396 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.66ms
2025-06-24 15:17:01.398 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:17:01.412 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初
始化完成
2025-06-24 15:17:01.464 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_p
anel_signals:376 | 控制面板按钮信号连接完成
2025-06-24 15:17:01.501 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1
475 | 菜单栏创建完成
2025-06-24 15:17:01.502 | INFO     | src.modules.system_config.user_preferences:__init__:61 |
用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.
json
2025-06-24 15:17:01.503 | INFO     | src.modules.system_config.user_preferences:load_preference
s:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-24 15:17:01.506 | INFO     | src.modules.system_config.user_preferences:load_preference
s:193 | 偏好设置加载成功
2025-06-24 15:17:01.509 | INFO     | src.gui.prototype.prototype_main_window:__init__:1454 | 菜
单栏管理器初始化完成
2025-06-24 15:17:01.510 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2
101 | 快捷键设置完成
2025-06-24 15:17:01.513 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2090 |
主窗口UI设置完成。
2025-06-24 15:17:01.514 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections
:2150 | 信号连接设置完成
2025-06-24 15:17:01.515 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappin
gs_from_file:2499 | 已加载 3 个表的字段映射信息
2025-06-24 15:17:01.516 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappin
gs_from_file:2499 | 已加载 3 个表的字段映射信息
2025-06-24 15:17:01.516 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:17:01.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-24 15:17:01.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:17:01.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.04ms
2025-06-24 15:17:01.519 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:17:01.522 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:17:01.526 | INFO     | src.gui.prototype.prototype_main_window:__init__:2042 | 原
型主窗口初始化完成
2025-06-24 15:17:02.045 | INFO     | __main__:main:301 | 应用程序启动成功
月度工资异动处理系统 启动成功!
2025-06-24 15:17:02.049 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_appl
y_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-24 15:17:02.050 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_
change:1226 | MainWorkspaceArea 响应式适配: sm
2025-06-24 15:17:02.108 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delay
ed_load_salary_data:887 | 执行延迟的工资数据加载...
2025-06-24 15:17:02.109 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:985 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-24 15:17:02.111 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:993 | 找到工资表节点: � 工资表
2025-06-24 15:17:02.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_l
ist:702 | 找到 60 个匹配类型 'salary_data' 的表
2025-06-24 15:17:02.154 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2025年，包含 9 个月份
2025-06-24 15:17:02.155 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2024年，包含 2 个月份
2025-06-24 15:17:02.156 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2023年，包含 2 个月份
2025-06-24 15:17:02.157 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2022年，包含 2 个月份
2025-06-24 15:17:02.158 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1049 | 工资数据导航已强制刷新: 4 个年份, 15 个月份
2025-06-24 15:17:02.158 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1052 | 默认路径: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-24 15:17:06.879 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月', '异
动人员表 > 2025年', '工资表 > 2025年', '工资表']
2025-06-24 15:17:06.879 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表 > 2022年
2025-06-24 15:17:06.880 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:17:06.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-24 15:17:06.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:17:06.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.04ms
2025-06-24 15:17:06.882 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:17:06.884 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:17:06.884 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:17:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-24 15:17:06.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:17:06.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.05ms
2025-06-24 15:17:06.886 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:17:06.887 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:17:06.887 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表 > 2022年
2025-06-24 15:17:16.495 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:39
3 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-24 15:17:16.495 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_re
quested:2318 | 接收到数据导入请求，推断的目标路径: 工资表 > 2022年。打开导入对话框。
2025-06-24 15:17:16.495 | INFO     | src.modules.data_import.auto_field_mapping_generator:__ini
t__:60 | 自动字段映射生成器初始化完成
2025-06-24 15:17:16.511 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 |
配置同步管理器初始化完成
2025-06-24 15:17:16.511 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:63 |
 多Sheet导入器初始化完成
2025-06-24 15:17:16.511 | INFO     | src.gui.widgets.target_selection_widget:_load_available_op
tions:250 | 成功加载导航配置
2025-06-24 15:17:16.526 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_l
ist:702 | 找到 60 个匹配类型 'salary_data' 的表
2025-06-24 15:17:16.558 | INFO     | src.gui.dialogs:_init_field_mapping:1760 | 初始化字段映射
表格: 17 个默认字段
2025-06-24 15:17:16.655 | INFO     | src.gui.dialogs:__init__:67 | 数据导入对话框初始化完成。
2025-06-24 15:17:24.364 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新:
工资表 > 2021年 > 6月 > 全部在职人员
2025-06-24 15:17:27.186 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新:
工资表 > 2021年 > 12月 > 全部在职人员
2025-06-24 15:17:30.799 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新:
工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:17:42.068 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173
 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-24 15:17:55.126 | INFO     | src.gui.dialogs:_on_import_mode_changed:1953 | 切换到多She
et导入模式
2025-06-24 15:18:01.974 | INFO     | src.modules.data_import.excel_importer:preview_data:221 |
数据预览完成: 21列, 20行
2025-06-24 15:18:03.289 | INFO     | src.modules.data_import.excel_importer:preview_data:221 |
数据预览完成: 21列, 20行
2025-06-24 15:18:14.974 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173
 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-24 15:18:58.591 | INFO     | src.gui.dialogs:_setup_field_mapping:985 | 开始加载Excel列
信息: C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.
xls, 工作表: A岗职工
2025-06-24 15:18:58.720 | INFO     | src.modules.data_import.excel_importer:preview_data:221 |
数据预览完成: 21列, 10行
2025-06-24 15:18:58.720 | INFO     | src.gui.dialogs:_setup_field_mapping:997 | 成功加载Excel列
: 21 个列
2025-06-24 15:18:58.720 | INFO     | src.gui.dialogs:_update_excel_columns_in_combos:1037 | 成
功更新下拉框Excel列选项: 21 个列
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行0, 列工号, 错误: 1
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行0, 列工号, 错误: 1
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行1, 列姓名, 错误: 2
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行1, 列姓名, 错误: 2
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行3, 列部门名称, 错误: 3
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行3, 列部门名称, 错误: 3
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行4, 列2025年岗位工资, 错误: 6
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行4, 列2025年岗位工资, 错误: 6
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行6, 列2025年岗位工资, 错误: 6
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行6, 列2025年岗位工资, 错误: 6
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行9, 列津贴, 错误: 8
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行9, 列津贴, 错误: 8
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行11, 列2025年奖励性绩效预发, 错误: 14
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行11, 列2025年奖励性绩效预发, 错误: 14
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行12, 列应发工资, 错误: 17
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行12, 列应发工资, 错误: 17
2025-06-24 15:18:58.720 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行14, 列2025公积金, 错误: 18
2025-06-24 15:18:58.736 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行14, 列2025公积金, 错误: 18
2025-06-24 15:18:58.736 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行15, 列保险扣款, 错误: 19
2025-06-24 15:18:58.736 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行15, 列保险扣款, 错误: 19
2025-06-24 15:18:58.736 | INFO     | src.gui.dialogs:_auto_map_fields:1124 | 智能映射完成: 10 
个字段
2025-06-24 15:19:00.335 | INFO     | src.gui.dialogs:_setup_field_mapping:985 | 开始加载Excel列
信息: C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.
xls, 工作表: A岗职工
2025-06-24 15:19:00.457 | INFO     | src.modules.data_import.excel_importer:preview_data:221 |
数据预览完成: 21列, 10行
2025-06-24 15:19:00.457 | INFO     | src.gui.dialogs:_setup_field_mapping:997 | 成功加载Excel列
: 21 个列
2025-06-24 15:19:00.458 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行0, 列工号, 错误: 1
2025-06-24 15:19:00.459 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行1, 列姓名, 错误: 2
2025-06-24 15:19:00.459 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行3, 列部门名称, 错误: 3
2025-06-24 15:19:00.460 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行4, 列2025年岗位工资, 错误: 6
2025-06-24 15:19:00.460 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行6, 列2025年岗位工资, 错误: 6
2025-06-24 15:19:00.461 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行9, 列津贴, 错误: 8
2025-06-24 15:19:00.462 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行11, 列2025年奖励性绩效预发, 错误: 14
2025-06-24 15:19:00.462 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行12, 列应发工资, 错误: 17
2025-06-24 15:19:00.463 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行14, 列2025公积金, 错误: 18
2025-06-24 15:19:00.464 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行15, 列保险扣款, 错误: 19
2025-06-24 15:19:00.464 | INFO     | src.gui.dialogs:_update_excel_columns_in_combos:1037 | 成
功更新下拉框Excel列选项: 21 个列
2025-06-24 15:19:00.465 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行0, 列工号, 错误: 1
2025-06-24 15:19:00.466 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行1, 列姓名, 错误: 2
2025-06-24 15:19:00.468 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行3, 列部门名称, 错误: 3
2025-06-24 15:19:00.468 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行4, 列2025年岗位工资, 错误: 6
2025-06-24 15:19:00.469 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行6, 列2025年岗位工资, 错误: 6
2025-06-24 15:19:00.469 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行9, 列津贴, 错误: 8
2025-06-24 15:19:00.470 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行11, 列2025年奖励性绩效预发, 错误: 14
2025-06-24 15:19:00.470 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行12, 列应发工资, 错误: 17
2025-06-24 15:19:00.470 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行14, 列2025公积金, 错误: 18
2025-06-24 15:19:00.471 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行15, 列保险扣款, 错误: 19
2025-06-24 15:19:00.472 | INFO     | src.gui.dialogs:_auto_map_fields:1124 | 智能映射完成: 10 
个字段
2025-06-24 15:19:25.230 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行0, 列工号, 错误: 1
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行1, 列姓名, 错误: 2
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行3, 列部门名称, 错误: 3
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行4, 列2025年岗位工资, 错误: 6
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行6, 列2025年岗位工资, 错误: 6
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行9, 列津贴, 错误: 8
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行11, 列2025年奖励性绩效预发, 错误: 14
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行12, 列应发工资, 错误: 17
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行14, 列2025公积金, 错误: 18
2025-06-24 15:19:25.245 | ERROR    | src.gui.dialogs:_update_preview_for_row:1202 | 更新预览失
败: 行15, 列保险扣款, 错误: 19
2025-06-24 15:19:25.245 | INFO     | src.gui.dialogs:_auto_map_fields:1124 | 智能映射完成: 10 
个字段
2025-06-24 15:19:40.745 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:19:40.745 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_
file:165 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:19:40.745 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:19:40.960 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173
 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-24 15:19:40.960 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_
file:176 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职
工']
2025-06-24 15:19:40.960 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:19:40.960 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:19:40.960 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:19:41.080 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 3行 x 12列
2025-06-24 15:19:41.083 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:19:41.083 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.1000
00000006)}
2025-06-24 15:19:41.084 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-24 15:19:41.085 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 离休人员工资表 的配置，使用默认处理
2025-06-24 15:19:41.085 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-24 15:19:41.086 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2021_12_retired_employees 不存在，将根据模板创建...
2025-06-24 15:19:41.102 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2021_12_retired_employees 保存 2 条数据。
2025-06-24 15:19:41.102 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:19:41.102 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:19:41.102 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:19:41.226 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 14行 x 21列
2025-06-24 15:19:41.226 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:19:41.243 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607
.14625)}
2025-06-24 15:19:41.243 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-24 15:19:41.243 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 退休人员工资表 的配置，使用默认处理
2025-06-24 15:19:41.243 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'e
mployee_name', 'allowance', 'total_salary']
2025-06-24 15:19:41.243 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2021_12_pension_employees 不存在，将根据模板创建...
2025-06-24 15:19:41.266 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2021_12_pension_employees 保存 13 条数据。
2025-06-24 15:19:41.268 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:19:41.269 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:19:41.270 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:19:41.403 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 1397行 x 23列
2025-06-24 15:19:41.418 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:19:41.418 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71),
'2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int6
4(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.
float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144),
 '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64
(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.f
loat64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(196
7911.5299999986)}
2025-06-24 15:19:41.418 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-24 15:19:41.418 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 全部在职人员工资表 的配置，使用默认处理
2025-06-24 15:19:41.418 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'emplo
yee_name', 'allowance', 'total_salary']
2025-06-24 15:19:41.418 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2021_12_active_employees 不存在，将根据模板创建...
2025-06-24 15:19:41.460 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2021_12_active_employees 保存 1396 条数据。
2025-06-24 15:19:41.460 | INFO     | src.modules.data_import.excel_importer:validate_file:111 |
 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-24 15:19:41.460 | INFO     | src.modules.data_import.excel_importer:import_data:260 |
开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-24 15:19:41.460 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\
salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-24 15:19:41.581 | INFO     | src.modules.data_import.excel_importer:_import_normal_file
:315 | 导入完成: 63行 x 17列
2025-06-24 15:19:41.584 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_dat
a:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-24 15:19:41.584 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-24 15:19:41.585 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_dat
a:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-24 15:19:41.586 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_shee
t_data:384 | 未找到工作表 A岗职工 的配置，使用默认处理
2025-06-24 15:19:41.586 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'emplo
yee_name', 'allowance', 'total_salary']
2025-06-24 15:19:41.587 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:853 | 表 salary_data_2021_12_a_grade_employees 不存在，将根据模板创建...
2025-06-24 15:19:41.591 | INFO     | src.modules.data_storage.dynamic_table_manager:save_datafr
ame_to_table:951 | 成功向表 salary_data_2021_12_a_grade_employees 保存 62 条数据。
2025-06-24 15:19:41.591 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_
file:195 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, '
message': '成功向表 salary_data_2021_12_retired_employees 保存 2 条数据。', 'table_name': 'sala
ry_data_2021_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message
': '成功向表 salary_data_2021_12_pension_employees 保存 13 条数据。', 'table_name': 'salary_dat
a_2021_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message'
: '成功向表 salary_data_2021_12_active_employees 保存 1396 条数据。', 'table_name': 'salary_dat
a_2021_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向
表 salary_data_2021_12_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2021_12_
a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4,
 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'f
ile_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工
工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.
1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets':
['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-24 15:19:41.591 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1425 | 多Sheet
导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向
表 salary_data_2021_12_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2021_12_r
etired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 sala
ry_data_2021_12_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2021_12_pension
_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salar
y_data_2021_12_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2021_12_active_
employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_202
1_12_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2021_12_a_grade_employees'
, 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets
': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path'
: 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终
版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extens
ion': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表',
'退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': '
separate_tables', 'data_period': '2021-12', 'data_description': '2021年12月工资数据', 'file_pat
h': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.x
ls', 'target_path': '工资表 > 2021年 > 12月 > A岗职工'}
2025-06-24 15:19:41.591 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_impor
ted:2331 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True,
'message': '成功向表 salary_data_2021_12_retired_employees 保存 2 条数据。', 'table_name': 'sal
ary_data_2021_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'messag
e': '成功向表 salary_data_2021_12_pension_employees 保存 13 条数据。', 'table_name': 'salary_da
ta_2021_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message
': '成功向表 salary_data_2021_12_active_employees 保存 1396 条数据。', 'table_name': 'salary_da
ta_2021_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向
表 salary_data_2021_12_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2021_12_
a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4,
 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'f
ile_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工
工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.
1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets':
['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sh
eet', 'strategy': 'separate_tables', 'data_period': '2021-12', 'data_description': '2021年12月
工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工
资（报财务)  最终版.xls', 'target_path': '工资表 > 2021年 > 12月 > A岗职工'}
2025-06-24 15:19:41.591 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_impor
ted:2342 | 导入模式: multi_sheet, 目标路径: '工资表 > 2021年 > 12月 > A岗职工'
2025-06-24 15:19:41.591 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_impor
ted:2350 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:19:41.606 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2399 | 检查是否需要更新导航面板: ['工资表', '2021年', '12月', 'A岗职工']
2025-06-24 15:19:41.607 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2403 | 检测到工资数据导入，开始刷新导航面板
2025-06-24 15:19:41.607 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2407 | 使用强制刷新方法
2025-06-24 15:19:41.607 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:985 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-24 15:19:41.607 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:993 | 找到工资表节点: � 工资表
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2022年', '异动人员表 > 2025年 > 4月', '异
动人员表 > 2025年 > 5月', '异动人员表 > 2025年']
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表
2025-06-24 15:19:41.622 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:19:41.622 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:19:41.622 | WARNING  | src.gui.prototype.prototype_main_window:set_data:457 | 尝
试设置空数据，将显示提示信息。
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2571 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_
with_prompt:1078 | 表格已初始化为空白状态，等待用户导入数据
2025-06-24 15:19:41.622 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:19:41.622 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表
2025-06-24 15:19:41.658 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_l
ist:702 | 找到 64 个匹配类型 'salary_data' 的表
2025-06-24 15:19:41.658 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2025年，包含 9 个月份
2025-06-24 15:19:41.658 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2024年，包含 2 个月份
2025-06-24 15:19:41.673 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2023年，包含 2 个月份
2025-06-24 15:19:41.673 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2022年，包含 2 个月份
2025-06-24 15:19:41.673 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1029 | 创建年份节点: 2021年，包含 1 个月份
2025-06-24 15:19:41.673 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1049 | 工资数据导航已强制刷新: 5 个年份, 16 个月份
2025-06-24 15:19:41.673 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_
refresh_salary_data:1052 | 默认路径: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-24 15:19:41.673 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation
_if_needed:2412 | 将在800ms后导航到: 工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:19:42.474 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_impor
ted_path:2445 | 尝试导航到新导入的路径: 工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:19:42.484 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2022年', '异动人员表 > 2025年 > 4月', '异
动人员表 > 2025年 > 5月', '异动人员表 > 2025年']
2025-06-24 15:19:42.486 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:19:42.487 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:19:42.488 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_da
ta_with_pagination:2599 | 数据量大(62条)，启用分页模式
2025-06-24 15:19:42.489 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pa
gination:2619 | 使用分页模式加载 salary_data_2021_12_a_grade_employees，第1页，每页50条
2025-06-24 15:19:42.489 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pa
gination:2662 | 缓存未命中，从数据库加载: salary_data_2021_12_a_grade_employees 第1页
2025-06-24 15:19:42.490 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:19:42.490 | INFO     | src.gui.prototype.prototype_main_window:run:124 | 开始加载
表 salary_data_2021_12_a_grade_employees 第1页数据，每页50条
2025-06-24 15:19:42.492 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1016 | 正在从表 salary_data_2021_12_a_grade_employees 分页获取数据: 第1页, 每页50
条
2025-06-24 15:19:42.497 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_impor
ted_path:2450 | 已成功导航到新导入的路径: 工资表 > 2021年 > 12月 > A岗职工
2025-06-24 15:19:42.503 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1039 | 成功从表 salary_data_2021_12_a_grade_employees 获取第1页数据: 50 行，总计62
行
2025-06-24 15:19:42.503 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mappi
ng_to_dataframe:2548 | 表 salary_data_2021_12_a_grade_employees 没有保存的字段映射信息
2025-06-24 15:19:42.504 | INFO     | src.gui.prototype.prototype_main_window:run:136 | 成功加载
 50 条数据，总记录数: 62
2025-06-24 15:19:42.515 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_dat
a_loaded:2692 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-24 15:19:42.520 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1016 | 正在从表 salary_data_2021_12_a_grade_employees 分页获取数据: 第2页, 每页50
条
2025-06-24 15:19:42.520 | INFO     | src.gui.prototype.prototype_main_window:set_data:464 | 通
过公共接口设置新的表格数据: 50 行
2025-06-24 15:19:42.524 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_max_visible_rows:2542 | 最大可见行数已更新: 1000 -> 50
2025-06-24 15:19:42.528 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_paginated:1039 | 成功从表 salary_data_2021_12_a_grade_employees 获取第2页数据: 12 行，总计62
行
2025-06-24 15:19:42.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2595 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-24 15:19:42.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-24 15:19:42.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 39.78ms
2025-06-24 15:19:42.565 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 |
总记录数设置为: 50
2025-06-24 15:19:42.566 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 |
总记录数设置为: 62
2025-06-24 15:20:15.234 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_exp
ansion_paths:214 | 预测展开路径: ['工资表 > 2022年', '工资表', '工资表 > 2021年 > 12月 > A岗职
工', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年 > 5月']
2025-06-24 15:20:15.234 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_cha
nged:2557 | 导航变化: 工资表 > 2021年 > 12月 > 离休人员
2025-06-24 15:20:15.250 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重
置
2025-06-24 15:20:15.250 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_da
ta_with_pagination:2602 | 数据量小(2条)，使用普通模式
2025-06-24 15:20:15.250 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tr
ee_selection_changed:734 | 导航选择: 工资表 > 2021年 > 12月 > 离休人员
2025-06-24 15:20:15.250 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_from_table:984 | 正在从表 salary_data_2021_12_retired_employees 获取数据...
2025-06-24 15:20:15.250 | INFO     | src.modules.data_storage.dynamic_table_manager:get_datafra
me_from_table:995 | 成功从表 salary_data_2021_12_retired_employees 获取 2 行数据。
2025-06-24 15:20:15.250 | INFO     | src.gui.prototype.prototype_main_window:_load_database_dat
a_with_mapping:2755 | 成功从数据库加载 2 行数据
2025-06-24 15:20:15.250 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mappi
ng_to_dataframe:2548 | 表 salary_data_2021_12_retired_employees 没有保存的字段映射信息
2025-06-24 15:20:15.250 | INFO     | src.gui.prototype.prototype_main_window:_load_database_dat
a_with_mapping:2760 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', '
department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'ded
uction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-24 15:20:15.250 | INFO     | src.gui.prototype.prototype_main_window:set_data:464 | 通
过公共接口设置新的表格数据: 2 行
2025-06-24 15:20:15.250 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set
_max_visible_rows:2533 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-24 15:20:15.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_max_visible_rows:2542 | 最大可见行数已更新: 50 -> 2
2025-06-24 15:20:15.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:aut
o_adjust_max_visible_rows:2595 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-24 15:20:15.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:401 | 表格数据已设置: 2 行, 16 列
2025-06-24 15:20:15.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set
_data:1986 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 263.95ms
2025-06-24 15:20:15.514 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 |
总记录数设置为: 2
2025-06-24 15:20:28.042 | INFO     | src.modules.data_import.config_sync_manager:update_mapping
:203 | 表 salary_data_2021_12_retired_employees 配置不存在，创建新的字段映射配置
2025-06-24 15:20:28.043 | INFO     | src.modules.data_import.config_sync_manager:update_mapping
:222 | 字段 employee_name 在表 salary_data_2021_12_retired_employees 中不存在，创建新字段映射
2025-06-24 15:20:28.046 | INFO     | src.modules.data_import.config_sync_manager:update_mapping
:257 | 字段映射更新成功: salary_data_2021_12_retired_employees.employee_name -> 姓名
2025-06-24 15:20:28.046 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dial
og:115 | 字段编辑成功: employee_name -> 姓名
2025-06-24 15:20:28.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on
_header_double_clicked:3906 | 表头编辑成功: 列2, employee_name -> employee_name
2025-06-24 15:21:08.274 | INFO     | __main__:main:306 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_



---

根据你的描述和日志分析，问题是：字段映射配置已经成功保存（日志显示"字段映射更新成功"），但是表头显示没有立即更新。这是因为在字段编辑成功后，没有触发界面刷新来应用新的映射配置。

让我检查相关代码并修复这个问题：

---

Read file: src/gui/prototype/widgets/virtualized_expandable_table.py

---

现在我看到了问题所在。在`_on_header_double_clicked`方法中，编辑成功后调用了`_refresh_header_display()`，但是这个方法的逻辑有问题。让我检查一下这个方法的实现：

---

Read file: src/gui/prototype/widgets/virtualized_expandable_table.py

---

我发现问题了！在`_refresh_header_display()`方法中，代码使用的逻辑是根据当前表头的文本来查找映射，但是这里有一个循环逻辑问题。我们需要检查主窗口中如何处理字段映射的应用。让我检查主窗口的字段映射处理逻辑：

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在我需要检查主窗口是否有监听字段映射更新信号的逻辑：

---

<details>
            <summary>Grep search for "field_mapping_updated" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "header_edited" • **0** files</summary>
        
No results found
</details>

---

问题找到了！主窗口没有监听表格组件发出的字段映射更新信号，所以当用户修改字段映射后，主窗口不知道需要刷新显示。我们需要修复这个问题：

---

```diff
          """设置信号连接"""
          try:
-             # 连接数据导入请求信号
+             # 1. Header组件信号连接
+             self.header_widget.settings_clicked.connect(self._on_settings_clicked)
+             
+             # 2. 主工作区信号连接
              self.main_workspace.import_requested.connect(self._on_import_data_requested)
              
-             # 连接导航变化信号
-             self.navigation_panel.navigation_changed.connect(self._on_navigation_changed)
+             # 3. 响应式布局信号连接
+             self.responsive_layout_manager.layout_changed.connect(self._on_responsive_layout_changed)
              
-             # 连接窗口大小变化信号
-             self.window_resized.connect(self.responsive_manager.handle_resize)
-             
-             # 连接响应式管理器信号
-             self.responsive_manager.breakpoint_changed.connect(self._on_responsive_layout_changed)
+             # 4. 菜单栏信号连接
+             self.menu_manager.visibility_changed.connect(self._on_menu_visibility_changed)
+             self.menu_manager.menu_action_triggered.connect(self._on_menu_action_triggered)
              
-             # 连接Header设置按钮信号
-             self.header_widget.settings_clicked.connect(self._on_settings_clicked)
+             # 5. 导航面板信号连接
+             self.navigation_panel.navigation_changed.connect(self._on_navigation_changed)
              
-             # 连接菜单栏管理器信号
-             if self.menu_bar_manager:
-                 self.menu_bar_manager.visibility_changed.connect(self._on_menu_visibility_changed)
-                 self.menu_bar_manager.menu_action_triggered.connect(self._on_menu_action_triggered)
+             # 6. 表格组件信号连接
+             if hasattr(self.main_workspace, 'data_table') and self.main_workspace.data_table:
+                 # 连接字段映射更新信号
+                 self.main_workspace.data_table.field_mapping_updated.connect(self._on_field_mapping_updated)
+                 # 连接表头编辑信号
+                 self.main_workspace.data_table.header_edited.connect(self._on_header_edited)
              
              self.logger.info("信号连接设置完成")
              
          except Exception as e:
-             self.logger.error(f"信号连接设置失败: {e}")
+             self.logger.error(f"设置信号连接失败: {e}", exc_info=True)
      
      @pyqtSlot()
```

---

现在需要添加处理字段映射更新和表头编辑的槽函数：

---



---



---

