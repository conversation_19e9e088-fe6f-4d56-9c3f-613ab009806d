<!-- Generated by SpecStory -->

# 解决数据导入窗体弹窗重复问题 (2025-06-24 21:38:39)

_**User**_

@/src 
在数据导入窗体，进行数据预览时，图片中的信息弹窗会出现两次。请找出根本原因，并给出有效解决办法

---

_**Assistant**_



---

我来帮您查找数据导入窗体中数据预览弹窗重复出现的问题。让我先检查相关的代码文件。

---

<details>
            <summary>Searched codebase "数据导入 数据预览 弹窗 信息" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/dialogs.py` | L934-968 |
| `src/gui/data_import_integration.py` | L566-610 |
| `src/gui/dialogs.py` | L968-1012 |
| `src/gui/dialogs.py` | L436-485 |
| `src/gui/dialogs.py` | L913-934 |
| `src/gui/dialogs.py` | L1749-1793 |
| `src/gui/dialogs.py` | L1608-1637 |
| `src/gui/dialogs.py` | L298-341 |
| `src/gui/dialogs.py` | L1166-1202 |
| `src/gui/dialogs.py` | L39-67 |
| `src/gui/dialogs.py` | L117-171 |
| `src/gui/dialogs.py` | L1121-1147 |
| `src/gui/data_import_integration.py` | L498-525 |
| `src/gui/dialogs.py` | L871-913 |
| `src/gui/dialogs.py` | L390-436 |
| `src/gui/data_import_integration.py` | L610-658 |
| `src/gui/data_import_integration.py` | L525-566 |
| `src/gui/dialogs.py` | L341-390 |
| `src/gui/main_window.py` | L563-603 |
| `src/gui/dialogs.py` | L1202-1247 |
| `src/gui/modern_main_window.py` | L680-712 |
| `src/gui/dialogs.py` | L1870-1908 |
| `src/gui/dialogs.py` | L1361-1382 |
| `src/gui/dialogs.py` | L1708-1749 |
| `src/gui/data_import_integration.py` | L658-702 |

</details>

---

<details>
            <summary>Grep search for "已预览.*行数据" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L963 | `QMessageBox.information(self, "预览完成", f"已预览 {len(data)} 行数据")` |

</details>

---

现在让我进一步搜索具体的预览数据函数，看看是否有重复调用的情况：

---

Read file: src/gui/dialogs.py

---

<details>
            <summary>Grep search for "_preview_data.*def" • **0** files</summary>
        
No results found
</details>

---

让我搜索是否有重复调用的情况：

---

<details>
            <summary>Grep search for "def _preview_data" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L932 | `def _preview_data(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/data_import_integration.py` | L567 | `def _preview_data(self):` |

</details>

---

<details>
            <summary>Grep search for "clicked\.connect.*_preview_data" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/data_import_integration.py` | L508 | `self.preview_btn.clicked.connect(self._preview_data)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L334 | `self.preview_btn.clicked.connect(self._preview_data)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L1897 | `self.preview_btn.clicked.connect(self._preview_data)` |

</details>

---

Read file: src/gui/dialogs.py

---

Read file: src/gui/dialogs.py

---

现在我找到了问题的根本原因！通过分析代码，我发现预览按钮的点击信号被**重复连接**了两次：

1. 在 `_create_excel_import_tab()` 函数的第334行连接了一次
2. 在 `_connect_signals()` 函数的第1897行又连接了一次

这就导致点击预览按钮时，`_preview_data()` 函数被调用两次，从而弹出两次相同的信息窗。

让我修复这个问题：

---



---

现在我再检查一下是否还有其他重复连接的情况：

---

<details>
            <summary>Grep search for "\.clicked\.connect" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L151 | `self.salary_browse_btn.clicked.connect(lambda: self._browse_file(self.salary_file_edit, "Excel文件 (*.xlsx *.xls)"))` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L305 | `self.config_sheets_btn.clicked.connect(self._configure_sheet_mappings)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L309 | `self.export_config_btn.clicked.connect(self._export_sheet_config)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L381 | `self.auto_map_btn.clicked.connect(self._auto_map_fields)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L386 | `self.add_field_btn.clicked.connect(self._add_field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L391 | `self.remove_field_btn.clicked.connect(self._remove_field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L396 | `self.reset_mapping_btn.clicked.connect(self._reset_field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L493 | `import_btn.clicked.connect(self._start_import)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L497 | `cancel_btn.clicked.connect(self.reject)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L1893 | `# self.salary_browse_btn.clicked.connect(lambda: self._browse_file(self.salary_file_edit, "Excel文件 (*.xlsx *.xls)"))` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L1897 | `self.preview_btn.clicked.connect(self._preview_data)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L1899 | `self.mapping_btn.clicked.connect(self._setup_field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2120 | `db_browse_btn.clicked.connect(self._browse_db_path)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2196 | `button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_settings)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2313 | `cancel_btn.clicked.connect(self._cancel)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2397 | `ok_btn.clicked.connect(self.accept)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2443 | `reset_btn.clicked.connect(self._reset_to_defaults)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2451 | `ok_btn.clicked.connect(self.accept)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2455 | `cancel_btn.clicked.connect(self.reject)` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2504 | `add_mapping_btn.clicked.connect(lambda: self._add_mapping_row(mapping_table))` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/dialogs.py` | L2508 | `remove_mapping_btn.clicked.connect(lambda: self._remove_mapping_row(mapping_table))` |

</details>

---



---

我来验证一下修复是否完整有效，让我创建一个测试文件来检验：

---

```diff
- 
+ """
+ 测试数据导入对话框预览功能修复
+ 验证预览弹窗是否只出现一次
+ """
+ 
+ import sys
+ import os
+ from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
+ from PyQt5.QtCore import pyqtSignal
+ 
+ # 添加项目根目录到路径
+ sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
+ 
+ from src.gui.dialogs import DataImportDialog
+ from src.utils.log_config import setup_logger
+ 
+ class TestMainWindow(QMainWindow):
+     """测试主窗口"""
+     
+     def __init__(self):
+         super().__init__()
+         self.logger = setup_logger(__name__)
+         self.init_ui()
+         
+     def init_ui(self):
+         """初始化界面"""
+         self.setWindowTitle("数据导入对话框预览功能测试")
+         self.setGeometry(100, 100, 400, 200)
+         
+         central_widget = QWidget()
+         self.setCentralWidget(central_widget)
+         
+         layout = QVBoxLayout(central_widget)
+         
+         # 测试按钮
+         test_btn = QPushButton("打开数据导入对话框")
+         test_btn.clicked.connect(self.open_import_dialog)
+         layout.addWidget(test_btn)
+         
+         # 说明标签
+         from PyQt5.QtWidgets import QLabel
+         info_label = QLabel("""
+ 测试说明：
+ 1. 点击按钮打开数据导入对话框
+ 2. 选择一个Excel文件（项目中的测试文件）
+ 3. 点击"数据预览"按钮
+ 4. 检查是否只弹出一个信息窗（修复前会弹出两个相同的窗口）
+ 
+ 测试文件位置：
+ - data/工资表/2025年5月份正式工工资（报财务)  最终版.xls
+ - data/异动人员/5月异动人员5.7.xlsx
+         """)
+         info_label.setWordWrap(True)
+         info_label.setStyleSheet("QLabel { padding: 10px; background-color: #f0f0f0; border-radius: 5px; }")
+         layout.addWidget(info_label)
+         
+     def open_import_dialog(self):
+         """打开数据导入对话框"""
+         try:
+             dialog = DataImportDialog(self)
+             
+             # 记录对话框打开
+             self.logger.info("数据导入对话框已打开，请测试预览功能")
+             
+             # 显示对话框
+             result = dialog.exec_()
+             
+             if result == dialog.Accepted:
+                 QMessageBox.information(self, "测试结果", "导入对话框正常关闭")
+             else:
+                 QMessageBox.information(self, "测试结果", "导入对话框被取消")
+                 
+         except Exception as e:
+             self.logger.error(f"打开导入对话框失败: {e}")
+             QMessageBox.critical(self, "错误", f"打开导入对话框失败: {e}")
+ 
+ def main():
+     """主函数"""
+     app = QApplication(sys.argv)
+     
+     # 设置应用信息
+     app.setApplicationName("数据导入预览功能测试")
+     app.setApplicationVersion("1.0")
+     
+     # 创建主窗口
+     window = TestMainWindow()
+     window.show()
+     
+     # 显示测试说明
+     QMessageBox.information(
+         window, "测试说明", 
+         "这是一个用于验证数据导入对话框预览功能修复的测试程序。\n\n"
+         "修复内容：\n"
+         "- 移除了预览按钮的重复信号连接\n"
+         "- 现在预览时只会弹出一个信息窗口\n\n"
+         "请按照窗口中的说明进行测试。"
+     )
+     
+     sys.exit(app.exec_())
+ 
+ if __name__ == '__main__':
+     main() 
```

---



---

现在让我运行测试来验证修复效果：

---



---



---

