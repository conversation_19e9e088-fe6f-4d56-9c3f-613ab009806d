<!-- Generated by SpecStory -->

# 数据导入后表头未更新 (2025-06-25 22:50:43)

_**User**_

启动系统后，新增2026年3月数据导入，提示导入成功，但是，在主界面右侧列表展示区域新导入数据的表头还是英文，修改没有起效。
终端日志如下：
2025-06-25 22:44:12.041 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-25 22:44:14.626 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2025年', '工资表 > 2026年 > 2月 > 离休人员']
2025-06-25 22:44:14.626 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2025年 > 6月
2025-06-25 22:44:14.626 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:14.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 2
2025-06-25 22:44:14.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 2, 耗时: 15.70ms
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:14.641 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:14.641 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 2
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 2, 耗时: 0.00ms
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:14.641 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:14.641 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2025年 > 6月
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2025年', '工资表 > 2026年 > 2月 > 离休人员']
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2025年 > 6月 >
 离休人员
2025-06-25 22:44:16.897 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(2条)，使用
普通模式
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2025年 > 6月 > 离休人员
2025-06-25 22:44:16.897 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:44:16.897 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025
_06_retired_employees 获取数据...
2025-06-25 22:44:16.897 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025
_06_retired_employees 获取 2 行数据。
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 2 行数
据，16 个字段
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2025_06
_retired_employees 没有保存的字段映射信息
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 2 行
2025-06-25 22:44:16.897 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2582 | 最大可见行数过小可能
影响性能，建议至少设置为10，当前值: 2
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
2 -> 2
2025-06-25 22:44:16.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(2
)自动调整最大可见行数为: 2
2025-06-25 22:44:16.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 2 行, 16 列
2025-06-25 22:44:16.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 2 行, 最大可见
行数: 2, 耗时: 15.68ms
2025-06-25 22:44:16.913 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-25 22:44:18.173 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2025年', '工资表 > 2026年 > 2月 > 离休人员']
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2025年 > 6月 >
 退休人员
2025-06-25 22:44:18.188 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(13条)，使
用普通模式
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2025年 > 6月 > 退休人员
2025-06-25 22:44:18.188 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:44:18.188 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025
_06_pension_employees 获取数据...
2025-06-25 22:44:18.188 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025
_06_pension_employees 获取 13 行数据。
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 13 行
数据，16 个字段
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2025_06
_pension_employees 没有保存的字段映射信息
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 13 行
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
2 -> 13
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(1
3)自动调整最大可见行数为: 13
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 13 行, 16 列
2025-06-25 22:44:18.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 13 行, 最大可
见行数: 13, 耗时: 0.00ms
2025-06-25 22:44:18.188 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2025年', '工资表 > 2026年 > 2月 > 离休人员']
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2025年 > 6月 >
 A岗职工
2025-06-25 22:44:19.708 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(62条)，启
用分页模式
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2025_06_a_grade_employees，第1页，每页50条
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2783 | 缓存未命中，从数据库加载: sal
ary_data_2025_06_a_grade_employees 第1页
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2025年 > 6月 > A岗职工
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2025_06_a_grade_employees 第1
页数据，每页50条
2025-06-25 22:44:19.708 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:19.708 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 分页加载所有字段
2025-06-25 22:44:19.708 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025
_06_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-25 22:44:19.708 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025
_06_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-25 22:44:19.724 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2025_06
_a_grade_employees 没有保存的字段映射信息
2025-06-25 22:44:19.757 | INFO     | src.gui.prototype.prototype_main_window:run:147 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-25 22:44:19.772 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（数据库）: 5
0条数据，第1页，总计62条
2025-06-25 22:44:19.816 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025
_06_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-25 22:44:19.816 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:44:19.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
13 -> 50
2025-06-25 22:44:19.824 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025
_06_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-25 22:44:19.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:44:19.850 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:44:19.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 52.34ms
2025-06-25 22:44:19.917 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:44:19.923 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-25 22:44:29.730 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2025年', '工资表 > 2026年 > 2月 > 离休人员']
2025-06-25 22:44:29.730 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年
2025-06-25 22:44:29.730 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:29.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:29.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 30.83ms
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:29.761 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:29.761 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:29.761 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:29.761 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 1月
2025-06-25 22:44:33.142 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:33.142 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:33.142 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:44:33.142 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:33.142 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:33.157 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 1月
2025-06-25 22:44:34.095 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 2月
2025-06-25 22:44:34.111 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:34.111 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:34.111 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:34.111 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:34.111 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 2月
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 1月 >
 A岗职工
2025-06-25 22:44:34.311 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(62条)，启
用分页模式
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2026_01_a_grade_employees，第1页，每页50条
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2749 | 缓存命中: salary_data_2026_01
_a_grade_employees 第1页
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01
_a_grade_employees 没有保存的字段映射信息
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（缓存）: 50
条数据，第1页，总计62条
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:44:34.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 50
2025-06-25 22:44:34.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:44:34.327 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:44:34.345 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 33.62ms
2025-06-25 22:44:34.365 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:44:34.368 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-25 22:44:34.379 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 1月 > A岗职工
2025-06-25 22:44:37.983 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:37.983 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 11月
2025-06-25 22:44:37.983 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:37.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:37.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:37.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 15.66ms
2025-06-25 22:44:37.999 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:37.999 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:37.999 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:37.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:37.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:37.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:44:37.999 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:37.999 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:38.014 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 11月
2025-06-25 22:44:39.563 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:39.563 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 11月
> 退休人员
2025-06-25 22:44:39.563 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:39.563 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(13条)，使
用普通模式
2025-06-25 22:44:39.563 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 11月 > 退休人员
2025-06-25 22:44:39.563 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:39.578 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:44:39.578 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026
_11_pension_employees 获取数据...
2025-06-25 22:44:39.578 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026
_11_pension_employees 获取 13 行数据。
2025-06-25 22:44:39.578 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 13 行
数据，16 个字段
2025-06-25 22:44:39.578 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_11
_pension_employees 无需字段重命名
2025-06-25 22:44:39.578 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:44:39.578 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 13 行
2025-06-25 22:44:39.597 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 13
2025-06-25 22:44:39.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(1
3)自动调整最大可见行数为: 13
2025-06-25 22:44:39.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:44:39.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 13 行, 16 列
2025-06-25 22:44:39.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 13 行, 最大可
见行数: 13, 耗时: 60.94ms
2025-06-25 22:44:39.671 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 11月
> 离休人员
2025-06-25 22:44:41.929 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(2条)，使用
普通模式
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 11月 > 离休人员
2025-06-25 22:44:41.929 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:44:41.929 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026
_11_retired_employees 获取数据...
2025-06-25 22:44:41.929 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026
_11_retired_employees 获取 2 行数据。
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 2 行数
据，16 个字段
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_11
_retired_employees 无需字段重命名
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:44:41.929 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 2 行
2025-06-25 22:44:41.929 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2582 | 最大可见行数过小可能
影响性能，建议至少设置为10，当前值: 2
2025-06-25 22:44:42.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
13 -> 2
2025-06-25 22:44:42.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(2
)自动调整最大可见行数为: 2
2025-06-25 22:44:42.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:44:42.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 2 行, 16 列
2025-06-25 22:44:42.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 2 行, 最大可见
行数: 2, 耗时: 131.91ms
2025-06-25 22:44:42.060 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-25 22:44:43.587 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:43.587 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 11月
> 退休人员
2025-06-25 22:44:43.587 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:43.587 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(13条)，使
用普通模式
2025-06-25 22:44:43.587 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 11月 > 退休人员
2025-06-25 22:44:43.587 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:43.587 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:44:43.587 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026
_11_pension_employees 获取数据...
2025-06-25 22:44:43.603 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026
_11_pension_employees 获取 13 行数据。
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 13 行
数据，16 个字段
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_11
_pension_employees 无需字段重命名
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 13 行
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
2 -> 13
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(1
3)自动调整最大可见行数为: 13
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 13 行, 16 列
2025-06-25 22:44:43.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 13 行, 最大可
见行数: 13, 耗时: 0.00ms
2025-06-25 22:44:43.603 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 11月
> A岗职工
2025-06-25 22:44:44.957 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(62条)，启
用分页模式
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2026_11_a_grade_employees，第1页，每页50条
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2783 | 缓存未命中，从数据库加载: sal
ary_data_2026_11_a_grade_employees 第1页
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 11月 > A岗职工
2025-06-25 22:44:44.957 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_11_a_grade_employees 第1
页数据，每页50条
2025-06-25 22:44:44.957 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:44:44.972 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 分页加载所有字段
2025-06-25 22:44:44.972 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_11_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-25 22:44:44.972 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_11_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-25 22:44:44.972 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salar
y_data_2026_11_a_grade_employees: 2 个字段重命名
2025-06-25 22:44:44.972 | INFO     | src.gui.prototype.prototype_main_window:run:147 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-25 22:44:44.972 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（数据库）: 5
0条数据，第1页，总计62条
2025-06-25 22:44:45.027 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_11_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-25 22:44:45.028 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:44:45.038 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_11_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-25 22:44:45.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
13 -> 50
2025-06-25 22:44:45.071 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:44:45.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:44:45.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:44:45.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 53.88ms
2025-06-25 22:44:45.097 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:44:45.099 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-25 22:44:50.693 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:555 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-25 22:44:50.693 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_11_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-25 22:44:50.693 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_11_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-25 22:44:51.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 12
2025-06-25 22:44:51.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(1
2)自动调整最大可见行数为: 12
2025-06-25 22:44:51.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:44:51.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 12 行, 16 列
2025-06-25 22:44:51.856 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 12 行, 最大可
见行数: 12, 耗时: 1163.31ms
2025-06-25 22:44:51.856 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记
录
2025-06-25 22:44:51.856 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-25 22:44:53.905 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:555 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-25 22:44:53.905 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_11_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-25 22:44:53.907 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_11_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-25 22:44:53.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
12 -> 50
2025-06-25 22:44:53.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:44:53.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:44:53.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:44:53.921 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 11.71ms
2025-06-25 22:44:53.921 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记
录
2025-06-25 22:44:53.921 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-25 22:44:58.511 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:44:58.511 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 1月
2025-06-25 22:44:58.512 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:58.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:58.513 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 7
个表头
2025-06-25 22:44:58.513 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:58.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 14.63ms
2025-06-25 22:44:58.527 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:58.527 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:58.528 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:44:58.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:44:58.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 7
个表头
2025-06-25 22:44:58.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:44:58.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.52ms
2025-06-25 22:44:58.530 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:44:58.531 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:44:58.532 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 1月
2025-06-25 22:45:00.223 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:45:00.224 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 2月
2025-06-25 22:45:00.225 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:45:00.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:45:00.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 7
个表头
2025-06-25 22:45:00.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:45:00.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 1.03ms
2025-06-25 22:45:00.227 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:45:00.227 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:45:00.227 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:45:00.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:45:00.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 7
个表头
2025-06-25 22:45:00.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:45:00.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 1.02ms
2025-06-25 22:45:00.229 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:45:00.230 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:45:00.230 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 2月
2025-06-25 22:45:00.461 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:45:00.462 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 1月 >
 A岗职工
2025-06-25 22:45:00.463 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:45:00.467 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(62条)，启
用分页模式
2025-06-25 22:45:00.468 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2026_01_a_grade_employees，第1页，每页50条
2025-06-25 22:45:00.468 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2749 | 缓存命中: salary_data_2026_01
_a_grade_employees 第1页
2025-06-25 22:45:00.469 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01
_a_grade_employees 没有保存的字段映射信息
2025-06-25 22:45:00.470 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（缓存）: 50
条数据，第1页，总计62条
2025-06-25 22:45:00.472 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:45:00.475 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 50
2025-06-25 22:45:00.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:45:00.477 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:45:00.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 22.42ms
2025-06-25 22:45:00.512 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:45:00.515 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-25 22:45:00.532 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 1月 > A岗职工
2025-06-25 22:45:01.890 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:45:01.890 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 2月
2025-06-25 22:45:01.890 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:45:01.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:45:01.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 32.28ms
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:45:01.922 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:45:01.922 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 0.00ms
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:45:01.922 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:45:01.922 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 2月
2025-06-25 22:45:05.424 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:406 | 数据导入功能被触发，发出 import_requested
 信号。
2025-06-25 22:45:05.440 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标
路径: 工资表 > 2026年 > 2月。打开导入对话框。
2025-06-25 22:45:05.440 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-25 22:45:05.440 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:45:05.440 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-25 22:45:05.440 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-25 22:45:05.482 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 60 个匹配类型 'salary_data' 的
表
2025-06-25 22:45:05.517 | INFO     | src.gui.dialogs:_init_field_mapping:1760 | 初始化字段映射表格: 17 个默认字段
2025-06-25 22:45:05.603 | INFO     | src.gui.dialogs:__init__:67 | 数据导入对话框初始化完成。
2025-06-25 22:45:10.462 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新: 工资表 > 2026年 > 6月 > 全部在职人员
2025-06-25 22:45:13.151 | INFO     | src.gui.dialogs:_on_target_changed:1915 | 目标位置已更新: 工资表 > 2026年 > 3月 > 全部在职人员
2025-06-25 22:45:25.267 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员
工资表', '全部在职人员工资表', 'A岗职工']
2025-06-25 22:45:36.993 | INFO     | src.gui.dialogs:_on_import_mode_changed:1953 | 切换到多Sheet导入模式
2025-06-25 22:45:43.490 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 23列, 20行
2025-06-25 22:46:00.131 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)
  最终版.xls (1.17MB)
2025-06-25 22:46:00.131 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5
月份正式工工资（报财务)  最终版.xls
2025-06-25 22:46:00.131 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)
  最终版.xls (1.17MB)
2025-06-25 22:46:00.387 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员
工资表', '全部在职人员工资表', 'A岗职工']
2025-06-25 22:46:00.387 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资
表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-25 22:46:00.387 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)
  最终版.xls (1.17MB)
2025-06-25 22:46:00.387 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财
务)  最终版.xls
2025-06-25 22:46:00.387 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\202
5年5月份正式工工资（报财务)  最终版.xls: read
2025-06-25 22:46:00.531 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-25 22:46:00.531 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-25 22:46:00.531 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数
据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-25 22:46:00.531 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无
效记录，有效记录2条
2025-06-25 22:46:00.531 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配
置，使用智能默认处理
2025-06-25 22:46:00.531 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '离休人员工资
表' 生成智能默认配置: 1 个必需字段
2025-06-25 22:46:00.531 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:82 | 开始为表 'salary_data_2026_0
3_retired_employees' 生成字段映射，共 12 个字段
2025-06-25 22:46:00.551 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:126 | 字段映射生成完成，详细信息:
2025-06-25 22:46:00.559 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_03_re
tired_employees
2025-06-25 22:46:00.559 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_03_retired
_employees 自动生成字段映射: 12 个字段
2025-06-25 22:46:00.560 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总
体评分=1.00
2025-06-25 22:46:00.560 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 离休人员工资表 存在 1 个验
证错误
2025-06-25 22:46:00.565 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 离休人员工资表 数据处理完
成: 2 行
2025-06-25 22:46:00.579 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码',
 '姓名', '部门'] -> ['employee_id', 'employee_name', 'department']
2025-06-25 22:46:00.619 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_03_ret
ired_employees 不存在，将根据模板创建...
2025-06-25 22:46:00.639 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_
03_retired_employees 保存 2 条数据。
2025-06-25 22:46:00.640 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)
  最终版.xls (1.17MB)
2025-06-25 22:46:00.642 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财
务)  最终版.xls
2025-06-25 22:46:00.642 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\202
5年5月份正式工工资（报财务)  最终版.xls: read
2025-06-25 22:46:00.815 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-25 22:46:00.855 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-25 22:46:00.856 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 
数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-25 22:46:00.857 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无
效记录，有效记录13条
2025-06-25 22:46:00.858 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配
置，使用智能默认处理
2025-06-25 22:46:00.859 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '退休人员工资
表' 生成智能默认配置: 1 个必需字段
2025-06-25 22:46:00.860 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:82 | 开始为表 'salary_data_2026_0
3_pension_employees' 生成字段映射，共 21 个字段
2025-06-25 22:46:00.884 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:126 | 字段映射生成完成，详细信息:
2025-06-25 22:46:00.901 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_03_pe
nsion_employees
2025-06-25 22:46:00.904 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_03_pension
_employees 自动生成字段映射: 21 个字段
2025-06-25 22:46:00.916 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总
体评分=1.00
2025-06-25 22:46:00.933 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 退休人员工资表 存在 1 个验
证错误
2025-06-25 22:46:00.943 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 退休人员工资表 数据处理完
成: 13 行
2025-06-25 22:46:00.952 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码',
 '姓名', '部门'] -> ['employee_id', 'employee_name', 'department']
2025-06-25 22:46:00.960 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_03_pen
sion_employees 不存在，将根据模板创建...
2025-06-25 22:46:00.966 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_
03_pension_employees 保存 13 条数据。
2025-06-25 22:46:00.966 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)
  最终版.xls (1.17MB)
2025-06-25 22:46:00.966 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财
务)  最终版.xls
2025-06-25 22:46:00.966 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\202
5年5月份正式工工资（报财务)  最终版.xls: read
2025-06-25 22:46:01.178 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-25 22:46:01.195 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-25 22:46:01.195 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空,
 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np
.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float6
4(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发'
: np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公
积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-25 22:46:01.201 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条
无效记录，有效记录1396条
2025-06-25 22:46:01.201 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表
的配置，使用智能默认处理
2025-06-25 22:46:01.202 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet '全部在职人员
工资表' 生成智能默认配置: 2 个必需字段
2025-06-25 22:46:01.202 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:82 | 开始为表 'salary_data_2026_0
3_active_employees' 生成字段映射，共 23 个字段
2025-06-25 22:46:01.213 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:126 | 字段映射生成完成，详细信息:
2025-06-25 22:46:01.234 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_03_ac
tive_employees
2025-06-25 22:46:01.251 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_03_active_
employees 自动生成字段映射: 23 个字段
2025-06-25 22:46:01.259 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总
体评分=1.00
2025-06-25 22:46:01.263 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet 全部在职人员工资表 存在 2
个验证错误
2025-06-25 22:46:01.271 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet 全部在职人员工资表 数据处
理完成: 1396 行
2025-06-25 22:46:01.275 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓
名', '部门'] -> ['employee_id', 'employee_name', 'department']
2025-06-25 22:46:01.279 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_03_act
ive_employees 不存在，将根据模板创建...
2025-06-25 22:46:01.327 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_
03_active_employees 保存 1396 条数据。
2025-06-25 22:46:01.383 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)
  最终版.xls (1.17MB)
2025-06-25 22:46:01.383 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财
务)  最终版.xls
2025-06-25 22:46:01.384 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\202
5年5月份正式工工资（报财务)  最终版.xls: read
2025-06-25 22:46:01.540 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-25 22:46:01.542 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-25 22:46:01.543 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 
数据: {'应发工资': np.float64(471980.9)}
2025-06-25 22:46:01.545 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无
效记录，有效记录62条
2025-06-25 22:46:01.545 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使
用智能默认处理
2025-06-25 22:46:01.546 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:706 | 为Sheet 'A岗职工' 生
成智能默认配置: 2 个必需字段
2025-06-25 22:46:01.546 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:82 | 开始为表 'salary_data_2026_0
3_a_grade_employees' 生成字段映射，共 17 个字段
2025-06-25 22:46:01.554 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:126 | 字段映射生成完成，详细信息:
2025-06-25 22:46:01.562 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:143 | 字段映射保存成功: salary_data_2026_03_a_
grade_employees
2025-06-25 22:46:01.563 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:467 | 为表 salary_data_2026_03_a_grade
_employees 自动生成字段映射: 17 个字段
2025-06-25 22:46:01.564 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | 映射质量评估: 覆盖率=100.0%, 总
体评分=1.00
2025-06-25 22:46:01.565 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:494 | Sheet A岗职工 存在 2 个验证错误
2025-06-25 22:46:01.581 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:503 | Sheet A岗职工 数据处理完成: 62 
行
2025-06-25 22:46:01.608 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓
名', '部门'] -> ['employee_id', 'employee_name', 'department']
2025-06-25 22:46:01.619 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_03_a_g
rade_employees 不存在，将根据模板创建...
2025-06-25 22:46:01.638 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_
03_a_grade_employees 保存 62 条数据。
2025-06-25 22:46:01.640 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True,
 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_03_retired_employees 保存 2 条数据。', 'table_name':
'salary_data_2026_03_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_03_pension
_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_03_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True,
 'message': '成功向表 salary_data_2026_03_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_03_active_employees', 'recor
ds': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_03_a_grade_employees 保存 62 条数据。', 'table_name': 'salary
_data_2026_03_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_
records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\
\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.
xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-25 22:46:01.640 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1425 | 多Sheet导入成功: {'success': True, 'results': {'离休
人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_03_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_
03_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_03_pension_employees 保存 13
 条数据。', 'table_name': 'salary_data_2026_03_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功
向表 salary_data_2026_03_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_03_active_employees', 'records': 1396}, 'A岗
职工': {'success': True, 'message': '成功向表 salary_data_2026_03_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_03_a_
grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473,
'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式
工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid'
: True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'm
ulti_sheet', 'strategy': 'separate_tables', 'data_period': '2026-03', 'data_description': '2026年3月工资数据', 'file_path': 'C:/test/salary_
changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 3月 > 全部在职人员'}
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True
, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_03_retired_employees 保存 2 条数据。', 'table_name':
 'salary_data_2026_03_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_03_pensio
n_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_03_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True
, 'message': '成功向表 salary_data_2026_03_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_03_active_employees', 'reco
rds': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_03_a_grade_employees 保存 62 条数据。', 'table_name': 'salar
y_data_2026_03_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total
_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表
\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '
.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'],
'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-03', 'data_description': '2026年3月工资数据', 'file_path':
 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 3月 > 全
部在职人员'}
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '
工资表 > 2026年 > 3月 > 全部在职人员'
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目
标路径: 工资表 > 2026年 > 3月 > 全部在职人员
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: [
'工资表', '2026年', '3月', '全部在职人员']
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷
新导航面板
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数
据导航... (尝试 1/3)
2025-06-25 22:46:01.640 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: �
 工资表
2025-06-25 22:46:01.656 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:01.669 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 2月
2025-06-25 22:46:01.671 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:46:01.671 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:46:01.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:46:01.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 39.28ms
2025-06-25 22:46:01.717 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:46:01.746 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:01.747 | WARNING  | src.gui.prototype.prototype_main_window:set_data:470 | 尝试设置空数据，将显示提示信息。
2025-06-25 22:46:01.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2620 | 数据量为0，
不调整最大可见行数，保持当前值: 50
2025-06-25 22:46:01.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 0 行, 7 列
2025-06-25 22:46:01.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 0 行, 最大可见
行数: 50, 耗时: 7.62ms
2025-06-25 22:46:01.755 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等
待用户导入数据
2025-06-25 22:46:01.755 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:01.755 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 2月
2025-06-25 22:46:01.812 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 64 个匹配类型 'salary_data' 的
表
2025-06-25 22:46:01.857 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026
年，包含 4 个月份
2025-06-25 22:46:01.859 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025
年，包含 3 个月份
2025-06-25 22:46:01.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024
年，包含 1 个月份
2025-06-25 22:46:01.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023
年，包含 1 个月份
2025-06-25 22:46:01.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022
年，包含 1 个月份
2025-06-25 22:46:01.861 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021
年，包含 1 个月份
2025-06-25 22:46:01.867 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020
年，包含 1 个月份
2025-06-25 22:46:01.868 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019
年，包含 1 个月份
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018
年，包含 1 个月份
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017
年，包含 1 个月份
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016
年，包含 1 个月份
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制
刷新: 11 个年份, 16 个月份
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 >
 2026年 > 1月 > 全部在职人员
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_sala
ry_data 执行完成
2025-06-25 22:46:01.869 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 >
 2026年 > 3月 > 全部在职人员
2025-06-25 22:46:02.669 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资
表 > 2026年 > 3月 > 全部在职人员
2025-06-25 22:46:02.682 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:02.686 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 3月 >
 全部在职人员
2025-06-25 22:46:02.697 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:02.699 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(1396条)，
启用分页模式
2025-06-25 22:46:02.699 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2026_03_active_employees，第1页，每页50条
2025-06-25 22:46:02.700 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2783 | 缓存未命中，从数据库加载: sal
ary_data_2026_03_active_employees 第1页
2025-06-25 22:46:02.701 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 3月 > 全部在职人员
2025-06-25 22:46:02.739 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工
资表 > 2026年 > 3月 > 全部在职人员
2025-06-25 22:46:02.741 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_03_active_employees 第1页
数据，每页50条
2025-06-25 22:46:02.750 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:46:02.751 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 分页加载所有字段
2025-06-25 22:46:02.768 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_03_active_employees 分页获取数据: 第1页, 每页50条
2025-06-25 22:46:02.776 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_03_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-25 22:46:02.825 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_03
_active_employees 无需字段重命名
2025-06-25 22:46:02.833 | INFO     | src.gui.prototype.prototype_main_window:run:147 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-25 22:46:02.839 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（数据库）: 5
0条数据，第1页，总计1396条
2025-06-25 22:46:02.952 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_03_active_employees 分页获取数据: 第2页, 每页50条
2025-06-25 22:46:02.952 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:46:02.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 50
2025-06-25 22:46:02.961 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:46:02.961 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_03_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-25 22:46:02.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:46:02.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:46:03.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 103.49ms
2025-06-25 22:46:03.066 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:46:03.067 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 3月 >
 A岗职工
2025-06-25 22:46:09.992 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(62条)，启
用分页模式
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2026_03_a_grade_employees，第1页，每页50条
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2783 | 缓存未命中，从数据库加载: sal
ary_data_2026_03_a_grade_employees 第1页
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 3月 > A岗职工
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_03_a_grade_employees 第1
页数据，每页50条
2025-06-25 22:46:09.992 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:46:09.992 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 分页加载所有字段
2025-06-25 22:46:09.992 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_03_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-25 22:46:10.008 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_03_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-25 22:46:10.008 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_03
_a_grade_employees 无需字段重命名
2025-06-25 22:46:10.008 | INFO     | src.gui.prototype.prototype_main_window:run:147 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-25 22:46:10.008 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（数据库）: 5
0条数据，第1页，总计62条
2025-06-25 22:46:10.076 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026
_03_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-25 22:46:10.076 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:46:10.090 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026
_03_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-25 22:46:10.107 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 50
2025-06-25 22:46:10.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:46:10.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:46:10.149 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:46:14.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 4818.46ms
2025-06-25 22:46:14.924 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:46:14.924 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-25 22:46:14.992 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:15.021 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 3月 >
 退休人员
2025-06-25 22:46:15.022 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:15.023 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(13条)，使
用普通模式
2025-06-25 22:46:15.027 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 3月 > 退休人员
2025-06-25 22:46:15.027 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:46:15.027 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:46:15.027 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026
_03_pension_employees 获取数据...
2025-06-25 22:46:15.027 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026
_03_pension_employees 获取 13 行数据。
2025-06-25 22:46:15.027 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 13 行
数据，16 个字段
2025-06-25 22:46:15.027 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_03
_pension_employees 无需字段重命名
2025-06-25 22:46:15.027 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:46:15.062 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 13 行
2025-06-25 22:46:16.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
50 -> 13
2025-06-25 22:46:16.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(1
3)自动调整最大可见行数为: 13
2025-06-25 22:46:16.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:46:16.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 13 行, 16 列
2025-06-25 22:46:16.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 13 行, 最大可
见行数: 13, 耗时: 1270.88ms
2025-06-25 22:46:16.400 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 3月 >
 离休人员
2025-06-25 22:46:29.310 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(2条)，使用
普通模式
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 3月 > 离休人员
2025-06-25 22:46:29.310 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:46:29.310 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026
_03_retired_employees 获取数据...
2025-06-25 22:46:29.310 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026
_03_retired_employees 获取 2 行数据。
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 2 行数
据，16 个字段
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_03
_retired_employees 无需字段重命名
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:46:29.310 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 2 行
2025-06-25 22:46:29.310 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2582 | 最大可见行数过小可能
影响性能，建议至少设置为10，当前值: 2
2025-06-25 22:46:29.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
13 -> 2
2025-06-25 22:46:29.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(2
)自动调整最大可见行数为: 2
2025-06-25 22:46:29.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:46:29.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 2 行, 16 列
2025-06-25 22:46:29.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 2 行, 最大可见
行数: 2, 耗时: 111.00ms
2025-06-25 22:46:29.421 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-25 22:46:54.115 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:54.116 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 3月 >
 退休人员
2025-06-25 22:46:54.116 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:54.117 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2723 | 数据量小(13条)，使
用普通模式
2025-06-25 22:46:54.118 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 3月 > 退休人员
2025-06-25 22:46:54.118 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-25 22:46:54.120 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2882 | 加载所有字段
2025-06-25 22:46:54.120 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026
_03_pension_employees 获取数据...
2025-06-25 22:46:54.121 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026
_03_pension_employees 获取 13 行数据。
2025-06-25 22:46:54.122 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2890 | 成功从数据库加载 13 行
数据，16 个字段
2025-06-25 22:46:54.122 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_03
_pension_employees 无需字段重命名
2025-06-25 22:46:54.123 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2895 | 应用字段映射后的表头: [
'id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance',
 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-25 22:46:54.123 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 13 行
2025-06-25 22:46:54.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
2 -> 13
2025-06-25 22:46:54.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(1
3)自动调整最大可见行数为: 13
2025-06-25 22:46:54.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:46:54.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 13 行, 16 列
2025-06-25 22:46:54.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 13 行, 最大可
见行数: 13, 耗时: 5.60ms
2025-06-25 22:46:54.130 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2
026年 > 1月', '工资表 > 2026年 > 2月', '工资表 > 2025年 > 2月', '工资表 > 2026年', '工资表 > 2025年']
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2675 | 导航变化: 工资表 > 2026年 > 3月 >
 A岗职工
2025-06-25 22:46:55.793 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2720 | 数据量大(62条)，启
用分页模式
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2740 | 使用分页模式加载 salary_data_
2026_03_a_grade_employees，第1页，每页50条
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2749 | 缓存命中: salary_data_2026_03
_a_grade_employees 第1页
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_03
_a_grade_employees 无需字段重命名
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2813 | 分页数据加载成功（缓存）: 50
条数据，第1页，总计62条
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.prototype_main_window:set_data:477 | 通过公共接口设置新的表格数据: 50 行
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2591 | 最大可见行数已更新:
13 -> 50
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2644 | 根据数据量(5
0)自动调整最大可见行数为: 50
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2033 | 表头映射应用完成: 16
 个表头
2025-06-25 22:46:55.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:401 | 表格数据已设置: 50 行, 16 列
2025-06-25 22:46:55.809 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1998 | 表格数据设置完成: 50 行, 最大可
见行数: 50, 耗时: 15.66ms
2025-06-25 22:46:55.809 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-25 22:46:55.809 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-25 22:46:55.829 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 >
 2026年 > 3月 > A岗职工
2025-06-25 22:47:03.919 | INFO     | __main__:main:302 | 应用程序正常退出
应用程序已退出

---

_**Assistant**_



---

根据日志分析，问题出现在字段映射功能上。虽然2026年3月数据导入成功，但表头显示仍为英文。让我检查字段映射相关的代码逻辑。

---

<details>
            <summary>Searched codebase "字段映射 表头映射 field mapping header mapping" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2001-2038 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L4046-4091 |
| `src/gui/prototype/prototype_main_window.py` | L2311-2350 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L167-216 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L4007-4046 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L117-167 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3921-3967 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L37-88 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3987-4007 |
| `docs/fields_map/表头英文显示问题完整解决方案.md` | L1-56 |
| `docs/fields_map/字段映射机制深度解析与优化方案.md` | L191-233 |
| `temp/test_phase1_field_mapping_integration.py` | L718-750 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L255-313 |
| `src/modules/data_import/auto_field_mapping_generator.py` | L84-126 |
| `diagnose_header_mapping.py` | L70-106 |
| `test_header_mapping_quick.py` | L1-56 |
| `docs/draft/表头映射与自定义功能使用指南.md` | L61-138 |
| `src/modules/data_import/field_mapper.py` | L1-61 |
| `diagnose_header_mapping.py` | L106-158 |
| `temp/test_phase1_field_mapping_integration.py` | L387-439 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L88-117 |
| `src/gui/dialogs.py` | L1749-1793 |
| `src/gui/prototype/prototype_main_window.py` | L2586-2631 |
| `docs/fields_map/字段映射显示问题深度分析与完整解决方案.md` | L55-131 |
| `src/gui/dialogs.py` | L390-436 |

</details>

---

现在让我查看自动字段映射生成器的逻辑，了解新导入数据的字段映射是如何生成的：

---

Read file: src/modules/data_import/auto_field_mapping_generator.py

---

现在让我查看配置文件的实际内容，了解当前的字段映射配置：

---

Could not find file 'config/field_mappings.json' in the workspace.

---

让我查看实际的配置文件位置：

---

<details>
            <summary>Searched codebase "field_mappings.json" • **6** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

现在查看实际的字段映射配置文件：

---

Read file: state/data/field_mappings.json

---

让我查看2026年3月相关的字段映射配置：

---

<details>
            <summary>Grep search for "salary_data_2026_03" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/state/data/field_mappings.json` | L1106 | `"salary_data_2026_03_retired_employees": {` |
| `file:///c%3A/test/salary_changes/salary_changes/state/data/field_mappings.json` | L1129 | `"salary_data_2026_03_pension_employees": {` |
| `file:///c%3A/test/salary_changes/salary_changes/state/data/field_mappings.json` | L1161 | `"salary_data_2026_03_active_employees": {` |
| `file:///c%3A/test/salary_changes/salary_changes/state/data/field_mappings.json` | L1195 | `"salary_data_2026_03_a_grade_employees": {` |

</details>

---

Read file: state/data/field_mappings.json

---



---

我发现了问题所在！从配置文件可以看到，2026年3月的数据确实有字段映射配置，但是存在映射键不匹配的问题：

**问题分析：**
1. 配置文件中存储的是：`Excel列名 → 显示名` 的映射（如 "工号": "工号", "姓名": "姓名"）
2. 但主界面显示时从数据库读取的是数据库字段名（如 employee_id, employee_name）
3. 系统用数据库字段名去查找映射配置，但配置中保存的是Excel列名的映射，所以找不到匹配项

从日志可以看到显示的是英文字段名：
```
['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
```

让我创建一个修复脚本来解决这个问题：

---



---

```diff
  import sys
  import json
+ import time
  from pathlib import Path
  from typing import Dict, Any
```

```diff
          return False
      
-     # 备份原配置文件
-     backup_file = config_file.with_suffix(f".backup_fix_2026_03_{int(time.time())}.json")
-     
      try:
          # 读取配置文件
```

```diff
          
          # 备份
-         import time
          backup_file = config_file.parent / f"field_mappings_before_2026_03_fix_{int(time.time())}.json"
          with open(backup_file, 'w', encoding='utf-8') as f:
```

---

现在运行修复脚本：

---

```bash
cd /c/test/salary_changes/salary_changes && python temp/fix_2026_03_field_mapping.py
```

---



---

_**User**_

测试脚本运行结果如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/fix_2026_03_field_mapping.py
开始修复2026年3月数据的字段映射配置...
✅ 已备份原配置到: C:\test\salary_changes\salary_changes\state\data\field_mappings_before_2026_03_fix_1750864440.json

修复表 salary_data_2026_03_retired_employees:
  修复表 salary_data_2026_03_retired_employees 的字段映射:
    序号 -> id: 序号
    人员代码 -> employee_id: 人员代码
    姓名 -> employee_name: 姓名
    部门名称 -> department: 部门
    基本
离休费 -> basic_salary: 基本
离休费
    结余
津贴 -> allowance: 结余
津贴
    生活
补贴 -> allowance: 生活
补贴
    住房
补贴 -> allowance: 住房
补贴
    物业
补贴 -> allowance: 物业
补贴
    离休
补贴 -> allowance: 离休
补贴
    护理费 -> allowance: 护理费
    合计 -> total_salary: 合计
❌ 修复过程中出错: Invalid format string
Traceback (most recent call last):
  File "C:\test\salary_changes\salary_changes\temp\fix_2026_03_field_mapping.py", line 164, in main
    table_config["metadata"]["last_modified"] = time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: Invalid format string

❌ 字段映射修复失败！

---

_**Assistant**_



---

看到错误了，问题出在时间格式化上。`time.strftime()` 不支持 `%f`（微秒）格式，这是 `datetime` 模块的格式。让我修复这个问题：

---



---



---



---

现在时间格式化问题已经修复，请重新运行修复脚本：

---

```bash
python temp/fix_2026_03_field_mapping.py
```

---

_**User**_

测试脚本运行结果如下：
(salary_system_py39) PS C:\test\salary_changes\salary_changes> python temp/fix_2026_03_field_mapping.py
开始修复2026年3月数据的字段映射配置...
✅ 已备份原配置到: C:\test\salary_changes\salary_changes\state\data\field_mappings_before_2026_03_fix_1750864600.json

修复表 salary_data_2026_03_retired_employees:
  修复表 salary_data_2026_03_retired_employees 的字段映射:
    序号 -> id: 序号
    人员代码 -> employee_id: 人员代码
    姓名 -> employee_name: 姓名
    部门名称 -> department: 部门
    基本
离休费 -> basic_salary: 基本
离休费
    结余
津贴 -> allowance: 结余
津贴
    生活
补贴 -> allowance: 生活
补贴
    住房
补贴 -> allowance: 住房
补贴
    物业
补贴 -> allowance: 物业
补贴
    离休
补贴 -> allowance: 离休
补贴
    护理费 -> allowance: 护理费
    合计 -> total_salary: 合计
  ✅ 已修复

修复表 salary_data_2026_03_pension_employees:
  修复表 salary_data_2026_03_pension_employees 的字段映射:
    序号 -> id: 序号
    人员代码 -> employee_id: 人员代码
    姓名 -> employee_name: 姓名
    部门名称 -> department: 部门
    人员类别代码 -> position: 人员类别代码
    基本退休费 -> basic_salary: 基本退休费
    津贴 -> allowance: 津贴补贴
    结余津贴 -> allowance: 结余津贴
    离退休生活补贴 -> allowance: 离退休生活补贴
    物业补贴 -> allowance: 物业补贴
    住房补贴 -> allowance: 住房补贴
    增资预付 -> overtime_pay: 增资预付
    2016待遇调整 -> allowance: 2016待遇调整
    2017待遇调整 -> allowance: 2017待遇调整
    2018待遇调整 -> allowance: 2018待遇调整
    2019待遇调整 -> allowance: 2019待遇调整
    2020待遇调整 -> allowance: 2020待遇调整
    2021待遇调整 -> allowance: 2021待遇调整
    2022待遇调整 -> allowance: 2022待遇调整
    2023待遇调整 -> allowance: 2023待遇调整
    应发工资 -> total_salary: 应发合计
  ✅ 已修复

修复表 salary_data_2026_03_active_employees:
  修复表 salary_data_2026_03_active_employees 的字段映射:
    序号 -> id: 序号
    工号 -> employee_id: 工号
    姓名 -> employee_name: 姓名
    部门名称 -> department: 部门
    人员类别代码 -> position: 人员类别代码
    人员类别 -> position: 人员类别
    2025年岗位工资 -> basic_salary: 岗位工资
    2025年薪级工资 -> basic_salary: 薪级工资
    津贴 -> allowance: 津贴补贴
    结余津贴 -> allowance: 结余津贴
    2025年基础性绩效 -> performance_bonus: 2025年基础性绩效
    卫生费 -> allowance: 卫生费
    交通补贴 -> allowance: 交通补贴
    物业补贴 -> allowance: 物业补贴
    住房补贴 -> allowance: 住房补贴
    车补 -> allowance: 车补
    通讯补贴 -> allowance: 通讯补贴
    2025年奖励性绩效预发 -> performance_bonus: 绩效工资
    补发 -> overtime_pay: 补发
    借支 -> deduction: 借支
    应发工资 -> total_salary: 应发合计
    2025公积金 -> deduction: 2025公积金
    代扣代存养老保险 -> deduction: 代扣代存养老保险
  ✅ 已修复

修复表 salary_data_2026_03_a_grade_employees:
  修复表 salary_data_2026_03_a_grade_employees 的字段映射:
    序号 -> id: 序号
    工号 -> employee_id: 工号
    姓名 -> employee_name: 姓名
    部门名称 -> department: 部门
    人员类别 -> position: 人员类别
    人员类别代码 -> position: 人员类别代码
    2025年岗位工资 -> basic_salary: 岗位工资
    2025年校龄工资 -> basic_salary: 2025年校龄工资
    津贴 -> allowance: 津贴补贴
    结余津贴 -> allowance: 结余津贴
    2025年基础性绩效 -> performance_bonus: 2025年基础性绩效
    卫生费 -> allowance: 卫生费
    2025年生活补贴 -> allowance: 2025年生活补贴
    2025年奖励性绩效预发 -> performance_bonus: 绩效工资
    借支 -> deduction: 借支
    应发工资 -> total_salary: 应发合计
    2025公积金 -> deduction: 2025公积金
  ✅ 已修复

✅ 修复完成！共修复了 4 个表的字段映射
✅ 配置已保存到: C:\test\salary_changes\salary_changes\state\data\field_mappings.json
✅ 备份文件: C:\test\salary_changes\salary_changes\state\data\field_mappings_before_2026_03_fix_1750864600.json

修复后请重启系统，新的字段映射将生效。

� 字段映射修复成功！

---

_**Assistant**_



---

太好了！🎉 字段映射修复脚本成功运行了！

从输出结果可以看到：

## 修复成果 ✅

1. **成功修复了4个表的字段映射**：
   - `salary_data_2026_03_retired_employees` (离休人员)
   - `salary_data_2026_03_pension_employees` (退休人员) 
   - `salary_data_2026_03_active_employees` (全部在职人员)
   - `salary_data_2026_03_a_grade_employees` (A岗职工)

2. **映射转换成功**：
   - 原来：`"工号": "工号"` (Excel列名 → 显示名)
   - 现在：`employee_id: "工号"` (数据库字段名 → 显示名)

3. **配置文件已更新**，并创建了备份文件。

## 下一步操作

现在请按以下步骤验证修复效果：

1. **重启系统**：
   ```bash
   python main.py
   ```

2. **导航验证**：
   - 启动后，导航到：`工资表 > 2026年 > 3月`
   - 分别查看各个分类（全部在职人员、A岗职工、退休人员、离休人员）

3. **预期效果**：
   - 表头应该显示为中文：`工号`、`姓名`、`部门`、`岗位工资`、`应发合计` 等
   - 不再显示英文字段名：`employee_id`、`employee_name` 等

## 修复原理说明

**问题根源**：
- 导入时：Excel列名 → 数据库字段名 → 保存映射为 `Excel列名: 显示名`
- 显示时：从数据库读取字段名 → 查找映射时用数据库字段名查找
- **不匹配**：查找键(数据库字段名) ≠ 配置键(Excel列名)

**修复方案**：
- 将映射配置转换为：`数据库字段名: 显示名`
- 确保查找键与配置键一致

请重启系统并验证表头是否正常显示中文了！如果还有问题，请告诉我。

---

_**User**_

你上面的方法只能针对已导入的表，我想在数据导入窗体中进行导入操作的时候，就实现这种字段映射的转换，请你给出详细解决方案以及产生上述问题的原因。暂不需具体实现。

---

_**Assistant**_



---

优秀的思考！你说得对，我刚才的修复方案只是"治标不治本"，只能解决已导入数据的问题。真正的解决方案应该是在数据导入时就确保字段映射的正确性。

## 问题产生的根本原因分析

### 1. 数据导入流程中的映射逻辑断层

**当前有问题的流程**：
```
Excel导入 → 字段映射生成(基于Excel列名) → 数据存储(转换为DB字段名) → 显示查找(用DB字段名查找)
     ↓                    ↓                        ↓                    ↓
"工号","姓名"        "工号":"工号"              employee_id,employee_name    查找employee_id → ❌找不到
```

**正确的流程应该是**：
```
Excel导入 → 字段映射生成(DB字段名为键) → 数据存储(DB字段名) → 显示查找(DB字段名查找)
     ↓                    ↓                    ↓                ↓
"工号","姓名"        employee_id:"工号"        employee_id,employee_name    查找employee_id → ✅找到
```

### 2. 字段映射生成器设计缺陷

**问题代码位置**：`src/modules/data_import/auto_field_mapping_generator.py`

```python
# 当前错误的逻辑
for excel_col in excel_columns:
    mapping_result[excel_col] = display_name  # ❌ 用Excel列名作为键
```

**应该是**：
```python
# 正确的逻辑  
for excel_col in excel_columns:
    db_field_name = self._map_excel_to_db_field(excel_col)
    mapping_result[db_field_name] = display_name  # ✅ 用数据库字段名作为键
```

### 3. 多层映射转换缺失

**当前缺失的映射层**：
- **Excel列名 → 数据库字段名** 的映射关系
- **数据库字段名 → 显示名** 的映射关系

## 详细解决方案

### 方案1：在导入窗体中实现双重映射机制

#### A. 增强字段映射生成器

**文件**：`src/modules/data_import/auto_field_mapping_generator.py`

```python
class AutoFieldMappingGenerator:
    def generate_mapping(self, excel_columns: List[str], table_name: str) -> Dict[str, Any]:
        """生成完整的字段映射信息
        
        Returns:
            {
                "excel_to_db": {"工号": "employee_id", "姓名": "employee_name"},
                "db_to_display": {"employee_id": "工号", "employee_name": "姓名"},
                "excel_to_display": {"工号": "工号", "姓名": "姓名"}  # 用于导入预览
            }
        """
        
    def _map_excel_to_db_field(self, excel_col: str) -> str:
        """将Excel列名映射为标准数据库字段名"""
        
    def _generate_display_name(self, excel_col: str, db_field: str) -> str:
        """生成用户友好的显示名称"""
```

#### B. 修改数据导入对话框

**文件**：`src/gui/dialogs.py`

```python
class DataImportDialog:
    def _execute_import(self):
        """执行导入时确保映射正确性"""
        
        # 1. 生成完整映射信息
        mapping_info = self.auto_mapping_generator.generate_mapping(
            excel_columns, table_name
        )
        
        # 2. 保存正确的数据库字段映射
        self.config_sync_manager.save_mapping(
            table_name=table_name,
            mapping=mapping_info["db_to_display"]  # ✅ 用数据库字段名作为键
        )
        
        # 3. 数据存储时使用Excel到DB的映射
        processed_data = self._transform_data_fields(
            raw_data, mapping_info["excel_to_db"]
        )
```

#### C. 增强ConfigSyncManager

**文件**：`src/modules/data_import/config_sync_manager.py`

```python
class ConfigSyncManager:
    def save_complete_mapping(self, table_name: str, mapping_info: Dict[str, Any]):
        """保存完整的映射信息"""
        config = {
            "metadata": {...},
            "excel_to_db_mapping": mapping_info["excel_to_db"],
            "field_mappings": mapping_info["db_to_display"],  # 主要用于显示
            "excel_preview_mapping": mapping_info["excel_to_display"]  # 用于预览
        }
```

### 方案2：导入窗体中的字段映射预览与确认

#### A. 增强导入预览功能

```python
class DataImportDialog:
    def _show_field_mapping_preview(self, excel_columns: List[str], table_name: str):
        """显示字段映射预览，允许用户确认或调整"""
        
        # 显示三列对比：
        # Excel列名 | 数据库字段名 | 显示名称
        # 工号      | employee_id  | 工号
        # 姓名      | employee_name| 姓名
        
    def _allow_user_mapping_adjustment(self):
        """允许用户手动调整映射关系"""
```

#### B. 实时映射验证

```python
def _validate_mapping_consistency(self, mapping_info: Dict[str, Any]) -> bool:
    """验证映射的一致性"""
    
    # 1. 检查Excel列名是否都有对应的数据库字段
    # 2. 检查数据库字段名是否都有对应的显示名
    # 3. 检查是否存在重复映射
```

### 方案3：标准化字段名映射规则库

#### A. 建立映射规则库

**文件**：`config/field_mapping_rules.json`

```json
{
  "standard_mappings": {
    "employee_info": {
      "工号": "employee_id",
      "员工号": "employee_id", 
      "职工编号": "employee_id",
      "姓名": "employee_name",
      "员工姓名": "employee_name"
    },
    "salary_fields": {
      "基本工资": "basic_salary",
      "岗位工资": "basic_salary",
      "应发工资": "total_salary"
    }
  },
  "table_specific_rules": {
    "salary_data_*_active_employees": {
      "2025年岗位工资": "basic_salary",
      "2025年奖励性绩效预发": "performance_bonus"
    }
  }
}
```

#### B. 智能映射推荐

```python
class SmartFieldMapper:
    def recommend_mapping(self, excel_columns: List[str], table_type: str) -> Dict[str, str]:
        """基于规则库智能推荐字段映射"""
        
    def get_mapping_confidence(self, excel_col: str, db_field: str) -> float:
        """计算映射的置信度"""
```

## 实施优先级建议

### 阶段1：核心修复（高优先级）
1. 修改`AutoFieldMappingGenerator`生成正确的数据库字段映射
2. 确保`ConfigSyncManager`保存的是数据库字段名为键的映射
3. 在导入流程中添加映射转换验证

### 阶段2：用户体验优化（中优先级）
1. 在导入对话框中显示字段映射预览
2. 允许用户手动调整映射关系
3. 添加映射冲突检测和提醒

### 阶段3：智能化增强（低优先级）
1. 建立标准映射规则库
2. 实现智能映射推荐
3. 添加映射学习和优化机制

## 关键修改点总结

1. **`auto_field_mapping_generator.py`**：修改映射生成逻辑，确保生成数据库字段名为键的映射
2. **`dialogs.py`**：在导入对话框中添加映射预览和验证
3. **`config_sync_manager.py`**：确保保存正确格式的映射配置
4. **`multi_sheet_importer.py`**：在导入流程中添加字段名转换逻辑

这样从源头解决问题，新导入的数据就不会再出现表头显示英文的问题了。

---

_**User**_

请将你给出的方案和问题原因相关内容进行梳理，以 markdown 格式独立保存到目录 docs/fields_map 下，以中文命名文件名。

---

_**Assistant**_



---

