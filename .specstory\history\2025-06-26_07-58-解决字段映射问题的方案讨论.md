<!-- Generated by SpecStory -->

# 解决字段映射问题的方案讨论 (2025-06-26 15:58:02)

_**User**_

@/src 
之前，对于“字段映射”相关内容做了些修改，当时在方案中提出：
从excel文档导入各个sheet数据时，顺便将sheet中表头（中文名）作为映射名，便于在主界面右侧列表展示区域中表头也显示一致的表头名。

经过一番修改后，现在系统还是没有完全达到效果。目前，新增数据导入后，在主界面列表展示区域，第一页表头及字段数据看上去正常，但是，跳转到下一页后，字段数据就为空了（主要是中文表头的字段）。

请你仔细阅读项目代码，一步一步思考，找出根源，并给出有效解决方案，暂不需要具体实现。

---

_**Assistant**_



---

我来仔细分析这个字段映射和分页显示的问题。让我先查看相关的代码文件，理解当前的实现逻辑。

---

<details>
            <summary>Searched codebase "字段映射 field mapping 表头 header mapping pagination" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `temp/diagnose_pagination_field_issue.py` | L68-94 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L4047-4092 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2002-2039 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L117-167 |
| `src/gui/prototype/prototype_main_window.py` | L2311-2350 |
| `temp/test_pagination_field_consistency.py` | L192-220 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L167-216 |
| `docs/fields_map/表头英文显示问题完整解决方案.md` | L1-56 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L4008-4047 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L37-88 |
| `docs/fields_map/数据导入字段映射根本问题分析与解决方案.md` | L209-242 |
| `src/gui/prototype/prototype_main_window.py` | L130-168 |
| `temp/test_field_mapping_fix_complete.py` | L260-292 |
| `docs/draft/表头映射与自定义功能使用指南.md` | L61-138 |
| `temp/test_pagination_fix.py` | L115-154 |
| `temp/test_pagination_fix_integration.py` | L67-98 |
| `state/data/field_mappings.json` | L1518-1581 |
| `temp/test_pagination_field_consistency.py` | L220-248 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L88-117 |
| `test/test_pagination_fix_comprehensive.py` | L147-192 |
| `temp/test_field_mapping_fix_complete.py` | L217-260 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3922-3968 |
| `docs/fields_map/字段映射机制深度解析与优化方案.md` | L191-233 |
| `test/test_real_world_pagination_scenarios.py` | L204-236 |
| `temp/test_pagination_fix_integration.py` | L144-168 |

</details>

---

<details>
            <summary>Grep search for "field_mapping" • **13** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L81 | `field_mapping = config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L84 | `has_mapping = field_mapping is not None and len(field_mapping) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L91 | `print(f"   映射规则数: {len(field_mapping)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L113 | `df_page, field_mapping, table_preference` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L206 | `def simulate_display_layer_processing(df, field_mapping, table_preference):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L209 | `if field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L212 | `if col in field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L213 | `column_rename_map[col] = field_mapping[col]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L227 | `elif field_mapping and pref_field in field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L228 | `mapped_name = field_mapping[pref_field]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L62 | `def test_02_field_mapping_configuration(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L72 | `field_mapping = self.config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L73 | `has_mapping = field_mapping is not None and len(field_mapping) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L79 | `print(f"    映射数量: {len(field_mapping)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L81 | `self.assertIsInstance(field_mapping, dict)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L82 | `self.assertGreater(len(field_mapping), 0)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L153 | `def test_05_field_mapping_application(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L168 | `field_mapping = self.config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L169 | `self.assertIsNotNone(field_mapping, "字段映射不存在")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L172 | `print(f"  映射规则数: {len(field_mapping)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L177 | `if col in field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L178 | `mapped_columns.append(field_mapping[col])` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L257 | `field_mapping = self.config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L258 | `if field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L260 | `df_page1_mapped = self._apply_field_mapping(df_page1, field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L261 | `df_page2_mapped = self._apply_field_mapping(df_page2, field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L294 | `def _apply_field_mapping(self, df: pd.DataFrame, field_mapping: dict) -> pd.DataFrame:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L296 | `if not field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L301 | `if col in field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L302 | `column_rename_map[col] = field_mapping[col]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L37 | `from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L65 | `self.test_auto_field_mapping_generator()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L74 | `self.test_import_with_field_mapping()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L89 | `def test_auto_field_mapping_generator(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L209 | `def test_import_with_field_mapping(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_gui_dialogs_comprehensive.py` | L105 | `def test_field_mapping_tab_components(self, qapp):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L21 | `from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L170 | `def test_field_mapping_save_logic(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L176 | `user_field_mapping = {` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L188 | `db_field_mapping = generator.generate_mapping(excel_columns, table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L189 | `print(f"自动生成的DB字段映射: {db_field_mapping}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L193 | `for excel_col, display_name in user_field_mapping.items():` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_field_mapping_fix.py` | L201 | `final_mapping.update(db_field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_data_import_fix.py` | L97 | `dialog._setup_field_mapping()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L63 | `field_mapping = config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L64 | `has_mapping = field_mapping is not None and len(field_mapping) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L69 | `print(f"    映射字段数: {len(field_mapping)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L70 | `print(f"    前3个映射: {dict(list(field_mapping.items())[:3])}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L139 | `print("✅ _apply_field_mapping_to_dataframe() - 在显示层应用字段映射")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_complete_functionality.py` | L31 | `from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L64 | `field_mappings = config_sync.get_mapping(test_table)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L65 | `if field_mappings:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L66 | `print(f"   映射数量: {len(field_mappings)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L67 | `print(f"   前3个映射: {dict(list(field_mappings.items())[:3])}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L70 | `field_mappings = {}` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L86 | `field_mappings=field_mappings` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase2_header_editing.py` | L450 | `config_file = Path("state/data/field_mappings.json")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L101 | `self.test_table.set_table_name("test_field_mapping_table")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L154 | `self.table_name_edit = QLineEdit("test_field_mapping_table")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L281 | `self.reset_mapping_btn.clicked.connect(self.reset_field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L310 | `self.test_table.field_mapping_updated.connect(self.on_field_mapping_updated)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L381 | `self.test_table.apply_field_mapping(auto_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L396 | `self.test_table.apply_field_mapping(mapping.get('field_mappings', {}))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L412 | `def reset_field_mapping(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L483 | `if retrieved and 'field_mappings' in retrieved:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L536 | `stats = self.test_table.get_field_mapping_stats()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L549 | `mapping_signal_connected = len(self.test_table.field_mapping_updated.receivers()) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L557 | `self.log_message("  ✓ field_mapping_updated 信号连接正常")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L559 | `self.log_message("  ✗ field_mapping_updated 信号未连接")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L599 | `if loaded_mapping and 'field_mappings' in loaded_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L600 | `loaded_fields = loaded_mapping['field_mappings']` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L696 | `stats = self.test_table.get_field_mapping_stats()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L732 | `def on_field_mapping_updated(self, table_name: str, mapping: dict):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_phase1_field_mapping_integration.py` | L762 | `required_keys = ['metadata', 'field_mappings', 'edit_history']` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L66 | `field_mapping = config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L67 | `if field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L68 | `print(f"      有字段映射: {len(field_mapping)}个映射")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L70 | `df_mapped = apply_field_mapping_simulation(df_page1, field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L97 | `df_page2_mapped = apply_field_mapping_simulation(df_page2, field_mapping) if field_mapping else df_page2` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L146 | `def apply_field_mapping_simulation(df: pd.DataFrame, field_mapping: dict) -> pd.DataFrame:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L148 | `if not field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L157 | `for excel_col, chinese_name in field_mapping.items():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L66 | `field_mapping = config_sync.get_mapping(test_table)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L67 | `has_mapping = field_mapping is not None and len(field_mapping) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L71 | `print(f"  映射字段数: {len(field_mapping)}")` |

</details>

---

<details>
            <summary>Grep search for "pagination" • **15** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L26 | `def test_real_world_pagination_scenarios():` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L104 | `# 模拟PaginationWorker的数据加载` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L239 | `test_real_world_pagination_scenarios()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L27 | `class TestPaginationFixComprehensive(unittest.TestCase):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L107 | `def test_04_pagination_field_consistency(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L233 | `def test_07_end_to_end_pagination_workflow(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L245 | `# 步骤1：数据加载（模拟PaginationWorker）` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_mapping_complete.py` | L304 | `'pagination_success': True` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_pagination_field_issue.py` | L29 | `def diagnose_pagination_field_issue():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_pagination_field_issue.py` | L161 | `diagnose_pagination_field_issue()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L70 | `self.test_5_pagination_mapping_consistency()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L218 | `def test_5_pagination_mapping_consistency(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L15 | `def test_database_pagination():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L101 | `def test_pagination_state():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L106 | `# 直接导入和使用PaginationState类` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L109 | `# 手动定义PaginationState类来避免文件编码问题` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L113 | `class PaginationState:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L138 | `state = PaginationState(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L172 | `from src.gui.widgets.pagination_cache_manager import PaginationCacheManager` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L174 | `cache_manager = PaginationCacheManager(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L338 | `test_results.append(("数据库分页查询", test_database_pagination()))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L339 | `test_results.append(("分页状态计算", test_pagination_state()))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L379 | `print("  🎨 分页组件: src/gui/widgets/pagination_widget.py")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L380 | `print("     - PaginationWidget 分页控件")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L381 | `print("     - PaginationState 状态管理")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L384 | `print("  💾 缓存管理: src/gui/widgets/pagination_cache_manager.py")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L17 | `def test_database_pagination():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L75 | `def test_pagination_widget():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L88 | `from src.gui.widgets.pagination_widget import PaginationWidget, PaginationState` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L91 | `pagination_widget = PaginationWidget()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L94 | `pagination_widget.set_total_records(1396)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L95 | `pagination_widget.set_current_page(1)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L96 | `pagination_widget.set_page_size(100)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L99 | `state = pagination_widget.get_current_state()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L127 | `from src.gui.widgets.pagination_cache_manager import PaginationCacheManager` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L129 | `cache_manager = PaginationCacheManager(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L257 | `test_results.append(("数据库分页查询", test_database_pagination()))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L258 | `test_results.append(("分页UI组件", test_pagination_widget()))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L25 | `def test_pagination_fix_integration():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L55 | `# 1. 模拟PaginationWorker的数据加载` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L56 | `print("\n   1️⃣ 模拟PaginationWorker加载数据:")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L171 | `test_pagination_fix_integration()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L5 | `1. 确认PaginationWorker现在使用统一字段加载策略` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L23 | `def test_pagination_fix():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L157 | `test_pagination_fix()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L24 | `class PaginationFieldConsistencyTester:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L79 | `def test_pagination_worker_consistency(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L80 | `"""测试PaginationWorker的字段一致性"""` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L82 | `self.logger.info("=== 测试PaginationWorker字段一致性 ===")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L84 | `# 模拟PaginationWorker的加载逻辑` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L121 | `self.logger.info("✅ PaginationWorker字段一致性测试通过")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L124 | `self.logger.error("❌ PaginationWorker字段一致性测试失败")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L129 | `self.logger.error("❌ PaginationWorker数据加载失败")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L133 | `self.logger.error(f"PaginationWorker字段一致性测试失败: {e}", exc_info=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L267 | `report_file = Path("temp/pagination_field_consistency_test_report.json")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L299 | `test_results["pagination_worker_consistency"] = self.test_pagination_worker_consistency()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L329 | `tester = PaginationFieldConsistencyTester()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L12 | `from src.gui.widgets.pagination_widget import PaginationWidget, PaginationState` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L16 | `def test_pagination_settings():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L37 | `pagination_widget = PaginationWidget()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L38 | `layout.addWidget(pagination_widget)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L46 | `state = pagination_widget.get_current_state()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L47 | `default_page_size = pagination_widget.default_page_size` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L48 | `page_size_options = pagination_widget.page_size_options` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L89 | `PAGINATION_THRESHOLD = 30` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L92 | `will_show_pagination = total_records > PAGINATION_THRESHOLD` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L93 | `status = "显示分页" if will_show_pagination else "隐藏分页"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L95 | `if (will_show_pagination and "显示" in expected) or (not will_show_pagination and "隐藏" in expected):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L127 | `pagination_widget.set_total_records(1396)  # 模拟全部在职人员数据` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L130 | `updated_state = pagination_widget.get_current_state()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_optimized_pagination.py` | L140 | `test_pagination_settings()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_new_pagination_threshold.py` | L15 | `def test_new_pagination_threshold():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_new_pagination_threshold.py` | L91 | `pagination_status = "✓ 显示分页" if will_paginate else "✗ 隐藏分页"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_new_pagination_threshold.py` | L93 | `print(f"  {display_name}: {count:,}条 → {pagination_status}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_new_pagination_threshold.py` | L141 | `test_new_pagination_threshold()` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/pagination_widget.py` | L20 | `class PaginationState:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/pagination_widget.py` | L57 | `class PaginationWidget(QWidget):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/pagination_widget.py` | L69 | `self.state = PaginationState()` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/pagination_widget.py` | L265 | `self.state = PaginationState()` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/widgets/pagination_cache_manager.py` | L65 | `class PaginationCacheManager:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L46 | `from src.gui.widgets.pagination_widget import PaginationWidget` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L63 | `from src.gui.widgets.pagination_cache_manager import PaginationCacheManager` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L106 | `class PaginationWorker(QRunnable):` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L109 | `super(PaginationWorker, self).__init__()` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L116 | `self.logger = setup_logger(f"PaginationWorker.{table_name}")` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L359 | `self.pagination_widget = None  # 分页组件` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L361 | `self.use_pagination = True     # 是否启用分页模式` |

</details>

---

现在让我查看关键的代码文件，了解当前的实现逻辑：

---

Read file: src/gui/prototype/prototype_main_window.py

---

Read file: src/gui/prototype/widgets/virtualized_expandable_table.py

---

Read file: src/modules/data_import/config_sync_manager.py

---

让我继续查看分页数据加载的完整逻辑：

---

Read file: src/gui/prototype/prototype_main_window.py

---

Read file: src/gui/prototype/prototype_main_window.py

---

<details>
            <summary>Searched codebase "get_dataframe_paginated" • **0** results</summary>
        
No results found
</details>

---

让我查看分页数据获取函数：

---

<details>
            <summary>Grep search for "get_dataframe_paginated" • **13** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L69 | `df_sample, total_records = table_manager.get_dataframe_paginated(table_name, 1, 1)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L105 | `df_page, _ = table_manager.get_dataframe_paginated(table_name, page_num, page_size)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L120 | `df_page1, total_count = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L123 | `df_page2, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L126 | `df_page3, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L164 | `df_raw, _ = self.table_manager.get_dataframe_paginated(table_name, 1, 10)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L204 | `df_raw, _ = self.table_manager.get_dataframe_paginated(table_name, 1, 10)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L246 | `df_page1, total_count = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L249 | `df_page2, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_pagination_field_issue.py` | L122 | `df, total_count = table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L89 | `df, total_count = table_manager.get_dataframe_paginated(test_table, 1, 50)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L95 | `df2, total_count2 = table_manager.get_dataframe_paginated(test_table, 2, 50)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L82 | `df, count = table_manager.get_dataframe_paginated(test_table, 1, page_size)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L302 | `page_df, count = table_manager.get_dataframe_paginated(test_table, 1, page_size)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_simple.py` | L375 | `print("     - get_dataframe_paginated() 分页查询")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L59 | `df, count = table_manager.get_dataframe_paginated(test_table, page, page_size)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_integration.py` | L223 | `page_df, count = table_manager.get_dataframe_paginated(test_table, 1, page_size)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L57 | `df_page1, total_records = table_manager.get_dataframe_paginated(table_name, 1, 50)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L58 | `df_page2, _ = table_manager.get_dataframe_paginated(table_name, 2, 50)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L125 | `print("   1. 数据加载层统一使用get_dataframe_paginated")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L98 | `page1_data, total_records = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L108 | `page2_data, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L155 | `page1_data, total_count = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L165 | `page2_data, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L214 | `page1_data, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L217 | `page2_data, _ = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/src/modules/data_storage/dynamic_table_manager.py` | L1003 | `def get_dataframe_paginated(self, table_name: str, page: int = 1, page_size: int = 100) -> Tuple[Optional[pd.DataFrame], int]:` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L126 | `df, total_records = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L551 | `df, total_count = self.table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L2821 | `self.dynamic_table_manager.get_dataframe_paginated` |
| `file:///c%3A/test/salary_changes/salary_changes/src/gui/prototype/prototype_main_window.py` | L2872 | `self.dynamic_table_manager.get_dataframe_paginated` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_solution.py` | L53 | `df_sample, _ = table_manager.get_dataframe_paginated(table_name, 1, 5)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_solution.py` | L76 | `df_page1, total_records = table_manager.get_dataframe_paginated(table_name, 1, 10)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_solution.py` | L77 | `df_page2, _ = table_manager.get_dataframe_paginated(table_name, 2, 10)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L5 | `1. PaginationWorker是否统一使用get_dataframe_paginated` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L106 | `print(f"    → 【修复后】统一使用get_dataframe_paginated加载所有字段")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L107 | `df_page1, total_count = table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L110 | `df_page2, _ = table_manager.get_dataframe_paginated(` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L142 | `print("1. 数据加载层：统一使用get_dataframe_paginated加载所有字段")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L53 | `df_sample, _ = table_manager.get_dataframe_paginated(test_table, 1, 1)` |

</details>

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---



---

现在让我查看`get_dataframe_paginated`的具体实现：

---

Read file: src/modules/data_storage/dynamic_table_manager.py

---

Read file: src/gui/prototype/prototype_main_window.py

---

基于我对代码的深入分析，我现在可以给出问题的根本分析和解决方案。

## 问题根源分析

通过仔细阅读项目代码，我发现了**字段映射在分页场景下失效**的根本原因：

### 🔍 核心问题分析

**1. 数据流路径差异**
- **第一页加载**：从Excel导入时，系统会生成字段映射，并正确应用到显示层
- **分页切换**：后续页面通过`PaginationWorker`从数据库加载，但字段映射的应用链路出现断裂

**2. 字段映射应用时机错误**
```python
# PaginationWorker.run() 中的逻辑
def run(self):
    # 1. 从数据库加载原始数据（英文字段名）
    df, total_records = self.table_manager.get_dataframe_paginated(
        self.table_name, self.page, self.page_size
    )
    
    # 2. 应用字段映射（关键问题在这里）
    if self.apply_mapping_func:
        df = self.apply_mapping_func(df, self.table_name)
```

**3. 字段映射配置格式不一致**
- 从`state/data/field_mappings.json`看到，配置文件存储的是`Excel列名 → 中文显示名`的映射
- 但数据库实际存储的是`英文字段名`（如`employee_id`、`employee_name`等）
- 分页加载时，无法找到`英文字段名`对应的中文显示名

### 🔧 具体问题链路

```
Excel导入 → 生成映射(Excel列名→中文名) → 数据库存储(英文字段名) → 分页查询(英文字段名) → 映射查找失败 → 显示英文字段
```

**第一页正常**的原因：
- 可能使用了缓存的Excel数据或特殊的加载路径

**分页失效**的原因：
- `get_dataframe_paginated`返回的DataFrame列名是数据库的英文字段名
- 字段映射配置中的键是Excel的列名（可能包含中文或特殊格式）
- 映射查找时无法匹配，导致字段名保持英文

## 💡 解决方案

### 方案一：修复字段映射键值对应关系（推荐）

**核心思路**：统一字段映射的键为数据库字段名，确保分页查询时能正确匹配。

**实现步骤**：

1. **修改数据导入时的字段映射生成逻辑**
```python
# 在 ExcelImporter 或相关导入模块中
def _generate_field_mapping(self, excel_headers, table_name):
    """生成 数据库字段名 → 中文显示名 的映射"""
    # 获取数据库实际字段名
    db_fields = self._get_database_fields(table_name)
    
    # 建立映射：数据库字段名 → Excel列名（中文显示名）
    field_mapping = {}
    
    # 智能匹配逻辑
    for i, excel_col in enumerate(excel_headers):
        if i < len(db_fields):
            db_field = db_fields[i]
            field_mapping[db_field] = excel_col  # 数据库字段 → 中文名
    
    return field_mapping
```

2. **修改字段映射应用逻辑**
```python
def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
    """修复：使用数据库字段名作为映射键"""
    field_mapping = self.config_sync_manager.load_mapping(table_name)
    
    if not field_mapping:
        return df
    
    # 创建重命名映射：当前列名(数据库字段名) → 中文显示名
    column_rename_map = {}
    for db_field, chinese_name in field_mapping.items():
        if db_field in df.columns:
            column_rename_map[db_field] = chinese_name
    
    if column_rename_map:
        df_renamed = df.rename(columns=column_rename_map)
        return df_renamed
    
    return df
```

### 方案二：建立双向映射机制

**核心思路**：同时维护`Excel列名→数据库字段名`和`数据库字段名→中文显示名`两套映射。

```python
def save_dual_mapping(self, table_name: str, excel_headers: List[str], db_fields: List[str]):
    """保存双向字段映射"""
    mapping_config = {
        "excel_to_db": {},      # Excel列名 → 数据库字段名
        "db_to_display": {},    # 数据库字段名 → 中文显示名
        "metadata": {
            "created_at": datetime.now().isoformat(),
            "source": "excel_import"
        }
    }
    
    for i, (excel_col, db_field) in enumerate(zip(excel_headers, db_fields)):
        mapping_config["excel_to_db"][excel_col] = db_field
        mapping_config["db_to_display"][db_field] = excel_col  # 使用Excel列名作为显示名
    
    # 保存到配置文件
    self.config_sync_manager.save_mapping(table_name, mapping_config)
```

### 方案三：分页查询时动态字段转换

**核心思路**：在`PaginationWorker`中，增加字段名标准化处理。

```python
class PaginationWorker(QRunnable):
    def run(self):
        # 1. 获取分页数据
        df, total_records = self.table_manager.get_dataframe_paginated(
            self.table_name, self.page, self.page_size
        )
        
        # 2. 标准化字段名处理
        if df is not None and self.apply_mapping_func:
            # 确保列名格式统一
            df = self._standardize_column_names(df)
            df = self.apply_mapping_func(df, self.table_name)
        
        # ... 其余逻辑
    
    def _standardize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名，确保与映射配置一致"""
        # 可以在这里处理列名格式化，使其与映射配置的键匹配
        return df
```

## 🎯 最优解决方案

**推荐采用方案一**，因为它：

1. **根本性解决**：统一了字段映射的键值体系
2. **维护简单**：不需要复杂的双向映射维护
3. **性能优良**：查找效率高，无需额外转换
4. **向后兼容**：可以通过迁移脚本处理现有配置

## 📋 实施建议

1. **立即修复**：修改字段映射生成和应用逻辑
2. **数据迁移**：转换现有的字段映射配置格式
3. **测试验证**：确保第一页和分页显示的字段名一致
4. **文档更新**：更新字段映射的设计文档

这个问题的核心在于**数据导入时的字段映射格式与数据库查询时的字段名不匹配**，通过统一字段映射的键为数据库字段名，可以从根本上解决分页时中文表头丢失的问题。

---

_**User**_

当前项目是允许在主界面右侧列表区域某个表头双击修改该表头名称的，这个情况你应该考虑到方案中。

---

_**Assistant**_



---

您提醒得非常重要！表头双击编辑功能确实是一个关键因素，这会显著影响字段映射方案的设计。让我重新分析并提供更完善的解决方案。

## 🔄 重新分析问题

考虑到**表头双击编辑功能**，字段映射的复杂度增加了：

### 字段名称的三层关系
1. **数据库字段名**：`employee_id`, `employee_name` （英文，固定）
2. **Excel导入列名**：`工号`, `姓名` （中文，导入时确定）
3. **用户自定义显示名**：用户双击修改后的名称（动态变化）

### 当前的问题链路
```
导入时：Excel列名 → 字段映射 → 显示正常
用户编辑：双击修改显示名 → 更新映射 → 第一页显示正常
分页切换：数据库查询 → 英文字段名 → 映射查找失败 → 显示英文
```

## 💡 完善的解决方案

### 方案：建立完整的字段映射链路

**核心思路**：建立`数据库字段名 → 用户显示名`的直接映射关系，支持用户自定义编辑。

#### 1. 字段映射数据结构重设计

```json
{
  "table_mappings": {
    "salary_data_2025_05_active_employees": {
      "metadata": {
        "created_at": "2025-06-26T10:00:00",
        "last_modified": "2025-06-26T15:30:00",
        "auto_generated": true,
        "user_modified": true
      },
      "field_mappings": {
        "employee_id": "工号",           // 数据库字段名 → 显示名
        "employee_name": "员工姓名",      // 用户可以双击修改显示名
        "department": "部门",
        "basic_salary": "基本工资",
        "total_salary": "应发工资"
      },
      "original_excel_headers": {       // 保留原始Excel列名（用于审计）
        "employee_id": "工号",
        "employee_name": "姓名",
        "department": "部门"
      },
      "edit_history": [
        {
          "timestamp": "2025-06-26T15:30:00",
          "field": "employee_name",
          "old_value": "姓名",
          "new_value": "员工姓名",
          "action": "user_edit"
        }
      ]
    }
  }
}
```

#### 2. 数据导入时的映射生成逻辑

```python
def _process_sheet_data(self, df, sheet_name, year=None, month=None):
    """处理工作表数据并生成标准化字段映射"""
    
    # 获取Excel原始表头
    excel_headers = df.columns.tolist()
    
    # 生成表名
    table_name = self._generate_table_name(sheet_name, year, month)
    
    # 获取数据库实际字段名（标准化后的）
    db_fields = self._get_database_field_names(table_name)
    
    # 生成初始字段映射：数据库字段名 → Excel列名
    initial_mapping = self._create_initial_field_mapping(excel_headers, db_fields)
    
    # 检查是否已有用户自定义映射
    existing_mapping = self.config_sync_manager.load_mapping(table_name)
    
    if existing_mapping:
        # 合并映射：保留用户自定义，补充新字段
        final_mapping = self._merge_field_mappings(existing_mapping, initial_mapping)
    else:
        final_mapping = initial_mapping
    
    # 保存映射配置
    mapping_data = {
        "field_mappings": final_mapping,
        "original_excel_headers": dict(zip(db_fields, excel_headers)),
        "metadata": {
            "source": "excel_import",
            "auto_generated": True,
            "user_modified": False,
            "created_at": datetime.now().isoformat()
        }
    }
    
    self.config_sync_manager.save_complete_mapping(table_name, mapping_data)
    
    # 继续处理数据...
```

#### 3. 智能字段匹配算法

```python
def _create_initial_field_mapping(self, excel_headers: List[str], db_fields: List[str]) -> Dict[str, str]:
    """创建智能字段映射：数据库字段名 → Excel列名"""
    
    mapping = {}
    
    # 预定义的智能匹配规则
    field_patterns = {
        'employee_id': ['工号', '职工编号', '员工号', '编号'],
        'employee_name': ['姓名', '职工姓名', '员工姓名', '名字'],
        'department': ['部门', '部门名称', '所属部门'],
        'position': ['职位', '岗位', '职务'],
        'basic_salary': ['基本工资', '基础工资', '岗位工资'],
        'performance_bonus': ['绩效工资', '绩效奖金'],
        'total_salary': ['应发工资', '工资合计', '应发合计'],
        # ... 更多规则
    }
    
    # 1. 智能匹配
    for db_field in db_fields:
        if db_field in field_patterns:
            patterns = field_patterns[db_field]
            for excel_header in excel_headers:
                if excel_header in patterns:
                    mapping[db_field] = excel_header
                    break
    
    # 2. 位置匹配（备用策略）
    for i, db_field in enumerate(db_fields):
        if db_field not in mapping and i < len(excel_headers):
            mapping[db_field] = excel_headers[i]
    
    # 3. 默认映射（最后兜底）
    for db_field in db_fields:
        if db_field not in mapping:
            # 将下划线字段名转为友好显示名
            display_name = db_field.replace('_', ' ').title()
            mapping[db_field] = display_name
    
    return mapping
```

#### 4. 表头双击编辑处理逻辑

```python
def _on_header_double_clicked(self, logical_index: int):
    """处理表头双击事件 - 支持用户自定义显示名"""
    try:
        # 获取当前显示的表头名称
        current_display_name = self.horizontalHeaderItem(logical_index).text()
        
        # 获取对应的数据库字段名
        db_field_name = self._get_db_field_name_by_column_index(logical_index)
        
        if not db_field_name:
            self.logger.error(f"无法获取列{logical_index}对应的数据库字段名")
            return
        
        # 显示编辑对话框
        new_display_name, ok = QInputDialog.getText(
            self, 
            "编辑表头", 
            f"请输入新的显示名称:\n(数据库字段: {db_field_name})",
            text=current_display_name
        )
        
        if ok and new_display_name.strip() and new_display_name != current_display_name:
            # 更新字段映射
            success = self._update_field_mapping(db_field_name, new_display_name.strip())
            
            if success:
                # 更新表头显示
                self.horizontalHeaderItem(logical_index).setText(new_display_name.strip())
                
                # 发出信号
                self.header_edited.emit(logical_index, current_display_name, new_display_name.strip())
                
                # 记录编辑历史
                self._record_header_edit_history(db_field_name, current_display_name, new_display_name.strip())
                
                self.logger.info(f"表头编辑成功: {db_field_name} -> {new_display_name.strip()}")
                
    except Exception as e:
        self.logger.error(f"处理表头双击编辑失败: {e}")

def _update_field_mapping(self, db_field_name: str, new_display_name: str) -> bool:
    """更新字段映射中的显示名称"""
    try:
        # 加载当前映射
        current_mapping = self.config_sync_manager.load_mapping(self.current_table_name)
        
        if current_mapping:
            # 更新映射
            current_mapping[db_field_name] = new_display_name
            
            # 保存映射
            return self.config_sync_manager.save_mapping(self.current_table_name, current_mapping, {
                "user_modified": True,
                "last_modified": datetime.now().isoformat()
            })
        
        return False
        
    except Exception as e:
        self.logger.error(f"更新字段映射失败: {e}")
        return False
```

#### 5. 分页数据加载时的映射应用

```python
def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
    """应用字段映射：数据库字段名 → 用户显示名"""
    try:
        # 获取字段映射配置
        field_mapping = self.config_sync_manager.load_mapping(table_name)
        
        if not field_mapping:
            self.logger.info(f"表 {table_name} 没有字段映射配置")
            return df
        
        # 创建列名重命名映射
        column_rename_map = {}
        
        for db_field, display_name in field_mapping.items():
            if db_field in df.columns and display_name:
                column_rename_map[db_field] = display_name
        
        # 应用重命名
        if column_rename_map:
            df_renamed = df.rename(columns=column_rename_map)
            self.logger.info(f"字段映射应用成功: {len(column_rename_map)} 个字段重命名")
            self.logger.debug(f"映射详情: {column_rename_map}")
            return df_renamed
        else:
            self.logger.info(f"表 {table_name} 无需字段重命名")
            return df
            
    except Exception as e:
        self.logger.error(f"应用字段映射失败: {e}")
        return df
```

#### 6. 字段映射配置管理器增强

```python
class ConfigSyncManager:
    
    def save_complete_mapping(self, table_name: str, mapping_data: Dict[str, Any]) -> bool:
        """保存完整的字段映射数据"""
        try:
            config = self._load_config_file()
            
            # 确保结构存在
            if "table_mappings" not in config:
                config["table_mappings"] = {}
            
            # 保存完整映射数据
            config["table_mappings"][table_name] = mapping_data
            config["last_updated"] = datetime.now().isoformat()
            
            # 写入文件
            success = self._save_config_file(config)
            
            if success:
                # 更新内存缓存
                self.memory_cache[table_name] = mapping_data.get("field_mappings", {})
                self.cache_stats["updates"] += 1
                
                # 记录变更日志
                self._log_mapping_change(table_name, "save_complete", mapping_data)
                
            return success
            
        except Exception as e:
            self.logger.error(f"保存完整字段映射失败: {e}")
            return False
    
    def update_single_field_mapping(self, table_name: str, db_field: str, display_name: str) -> bool:
        """更新单个字段的显示名称"""
        try:
            config = self._load_config_file()
            
            if table_name not in config.get("table_mappings", {}):
                self.logger.error(f"表 {table_name} 的映射配置不存在")
                return False
            
            # 更新字段映射
            config["table_mappings"][table_name]["field_mappings"][db_field] = display_name
            
            # 更新元数据
            config["table_mappings"][table_name]["metadata"]["user_modified"] = True
            config["table_mappings"][table_name]["metadata"]["last_modified"] = datetime.now().isoformat()
            
            # 添加编辑历史
            if "edit_history" not in config["table_mappings"][table_name]:
                config["table_mappings"][table_name]["edit_history"] = []
            
            config["table_mappings"][table_name]["edit_history"].append({
                "timestamp": datetime.now().isoformat(),
                "field": db_field,
                "new_value": display_name,
                "action": "user_edit"
            })
            
            # 保存配置
            return self._save_config_file(config)
            
        except Exception as e:
            self.logger.error(f"更新单个字段映射失败: {e}")
            return False
```

## 🎯 方案优势

1. **完整支持用户编辑**：用户双击修改的表头名称会正确保存和应用
2. **分页一致性**：确保分页前后显示的表头名称完全一致
3. **历史追踪**：保留用户编辑历史，支持审计和回滚
4. **智能映射**：导入时自动建立合理的字段映射关系
5. **向后兼容**：可以通过迁移脚本处理现有配置

## 📋 实施步骤

1. **重构字段映射数据结构**：将键统一为数据库字段名
2. **增强导入时映射生成**：建立智能匹配算法
3. **完善表头编辑功能**：确保编辑结果正确保存
4. **修复分页映射应用**：使用数据库字段名查找显示名
5. **数据迁移**：转换现有的字段映射配置格式

这个方案完全兼容表头双击编辑功能，确保用户自定义的表头名称在分页时保持一致。

---

_**User**_

我需要你对上面部分内容澄清一下，请看图片
图片中所提到的“当前的问题链路”是你发现的项目目前的实际问题链路反映？还是你希望经过改进达到那种问题链路？

---

_**Assistant**_



---

您问得很好！我需要澄清一下这个表述。

图片中的"**当前的问题链路**"是我分析发现的**项目目前存在的实际问题**，不是期望达到的状态。

让我重新明确表述：

## 🚨 当前项目的实际问题链路（现状）

```
导入时：Excel列名 → 字段映射 → 显示正常 ✅
用户编辑：双击修改显示名 → 更新映射 → 第一页显示正常 ✅  
分页切换：数据库查询 → 英文字段名 → 映射查找失败 → 显示英文 ❌
```

**问题所在**：
- 前两个步骤工作正常
- **第三个步骤出现断裂**：分页时映射查找失败，导致显示英文字段名

## 🎯 期望的正确链路（目标）

```
导入时：Excel列名 → 字段映射 → 显示正常 ✅
用户编辑：双击修改显示名 → 更新映射 → 第一页显示正常 ✅  
分页切换：数据库查询 → 英文字段名 → 映射查找成功 → 显示中文名 ✅
```

**修复目标**：
- 保持前两个步骤不变
- **修复第三个步骤**：确保分页时也能正确应用字段映射，显示中文表头

所以我提出的解决方案就是要把现在的问题链路修复成正确的目标链路，核心是解决"分页切换时映射查找失败"的问题。

感谢您的提醒，这个澄清很重要！

---

_**User**_

请将本次对话内容梳理一下，以 markdown 格式独立保存到目录 docs/fields_map 下，以中文命名文件名。

---

_**Assistant**_



---



---



---

