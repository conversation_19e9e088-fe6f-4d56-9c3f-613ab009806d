# 月度工资异动处理系统 - 部署指南

**系统版本**: 1.0.0  
**文档版本**: v1.0  
**发布日期**: 2025-01-22  
**ARCHIVE阶段**: 部署文档

## 📋 目录
1. [部署概述](#部署概述)
2. [环境要求](#环境要求)
3. [部署准备](#部署准备)
4. [部署步骤](#部署步骤)
5. [配置说明](#配置说明)
6. [安全配置](#安全配置)
7. [性能优化](#性能优化)
8. [监控和维护](#监控和维护)
9. [故障排除](#故障排除)
10. [升级指南](#升级指南)

## 🎯 部署概述

### 部署架构
月度工资异动处理系统采用单机桌面应用架构：

```
用户计算机
├── salary_changes_system.exe    # 主程序文件
├── 数据文件夹/                   # 本地数据存储
├── 配置文件/                     # 系统配置
├── 模板文件/                     # 报表模板
└── 日志文件/                     # 运行日志
```

### 部署特点
- **单文件部署**: exe文件包含所有依赖
- **免安装运行**: 无需复杂安装过程
- **本地存储**: 数据完全本地化，安全可控
- **即开即用**: 下载后直接运行使用

### 部署模式

#### 标准部署（推荐）
- 适用场景：单用户独立使用
- 部署复杂度：简单
- 维护成本：低
- 数据安全：高

#### 网络部署（可选）
- 适用场景：多用户共享数据
- 部署复杂度：中等
- 维护成本：中等
- 数据安全：需要额外配置

## 💻 环境要求

### 硬件要求

#### 最低配置
- **CPU**: Intel Core i3 或 AMD 等效处理器
- **内存**: 4GB RAM
- **存储**: 2GB 可用磁盘空间
- **显示**: 1024x768 分辨率
- **网络**: 无网络要求（离线运行）

#### 推荐配置
- **CPU**: Intel Core i5 或 AMD 等效处理器及以上
- **内存**: 8GB RAM 或更多
- **存储**: 5GB 可用磁盘空间（SSD推荐）
- **显示**: 1920x1080 分辨率或更高
- **网络**: 可选（用于软件更新）

### 软件要求

#### 操作系统
- **Windows 10** (版本1903或更高)
- **Windows 11** (所有版本)
- **Windows Server 2016/2019/2022** (可选)

#### 必要组件（通常已内置）
- **Microsoft Visual C++ 2015-2022 可再发行程序包**
- **.NET Framework 4.7.2** 或更高版本
- **Windows Update** 保持最新

#### 可选软件
- **Microsoft Office** (Excel 2016或更高版本，用于高级Excel功能)
- **Adobe Acrobat Reader** (查看PDF报表)
- **压缩软件** (如需要解压部署包)

### 权限要求
- **读写权限**: 程序安装目录及子目录
- **临时文件权限**: Windows临时文件夹访问权限
- **注册表权限**: 读取系统配置信息权限

## 📦 部署准备

### 文件清单
确保以下文件已准备完整：

#### 核心文件
- [x] `salary_changes_system.exe` (77.1 MB) - 主程序文件
- [x] `readme.txt` - 快速说明文件
- [x] `用户操作手册.pdf` - 详细操作手册

#### 配置文件（可选）
- [ ] `config.json` - 系统配置文件
- [ ] `user_preferences.json` - 用户偏好设置
- [ ] `template_config.json` - 模板配置文件

#### 模板文件（可选）
- [ ] `template/` 文件夹 - 报表模板文件
- [ ] `关于发放教职工薪酬的请示/` - 审批文档模板
- [ ] `工资审批表/` - 工资审批表模板

### 部署环境检查

#### 系统兼容性检查
```powershell
# PowerShell脚本 - 检查系统环境
Write-Host "=== 系统环境检查 ==="

# 检查操作系统版本
$os = Get-WmiObject -Class Win32_OperatingSystem
Write-Host "操作系统: $($os.Caption) $($os.Version)"

# 检查内存大小
$memory = Get-WmiObject -Class Win32_ComputerSystem
$memoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
Write-Host "物理内存: $memoryGB GB"

# 检查磁盘空间
$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DriveType=3"
foreach ($d in $disk) {
    $freeSpaceGB = [math]::Round($d.FreeSpace / 1GB, 2)
    Write-Host "磁盘 $($d.DeviceID): 可用空间 $freeSpaceGB GB"
}

# 检查必要组件
Write-Host "=== 组件检查 ==="
try {
    $netVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release
    Write-Host ".NET Framework: 已安装 (版本 $($netVersion.Release))"
} catch {
    Write-Host ".NET Framework: 未检测到"
}
```

#### 权限检查
```powershell
# 检查当前用户权限
$currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object System.Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)

if ($isAdmin) {
    Write-Host "当前用户: 管理员权限"
} else {
    Write-Host "当前用户: 标准用户权限"
}
```

## 🚀 部署步骤

### 标准部署流程

#### Step 1: 准备部署包
1. **下载程序**: 获取 `salary_changes_system.exe` 文件
2. **选择位置**: 选择合适的安装目录（建议：`C:\Programs\SalarySystem\`）
3. **创建目录**: 如果目录不存在，创建目标目录
4. **文件复制**: 将exe文件复制到目标目录

```batch
:: 批处理脚本示例
@echo off
echo 开始部署月度工资异动处理系统...

:: 创建程序目录
set INSTALL_DIR=C:\Programs\SalarySystem
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

:: 复制程序文件
copy "salary_changes_system.exe" "%INSTALL_DIR%\"

:: 创建桌面快捷方式
set DESKTOP=%USERPROFILE%\Desktop
echo [InternetShortcut] > "%DESKTOP%\月度工资异动处理系统.url"
echo URL=file:///%INSTALL_DIR%/salary_changes_system.exe >> "%DESKTOP%\月度工资异动处理系统.url"
echo IconFile=%INSTALL_DIR%\salary_changes_system.exe >> "%DESKTOP%\月度工资异动处理系统.url"
echo IconIndex=0 >> "%DESKTOP%\月度工资异动处理系统.url"

echo 部署完成！
pause
```

#### Step 2: 首次启动
1. **双击运行**: 运行 `salary_changes_system.exe`
2. **初始化等待**: 首次启动需要3-5秒初始化时间
3. **文件夹创建**: 系统自动创建必要的工作文件夹
4. **配置初始化**: 自动生成默认配置文件

#### Step 3: 验证部署
1. **启动测试**: 验证程序能正常启动
2. **功能测试**: 测试基本功能模块
3. **文件检查**: 确认所有必要文件夹已创建
4. **权限验证**: 确认程序有必要的读写权限

### 网络部署（可选）

#### 共享文件夹部署
```
网络结构:
文件服务器
├── Programs/
│   └── SalarySystem/
│       ├── salary_changes_system.exe
│       └── shared_templates/
└── UserData/
    ├── User1/
    ├── User2/
    └── ...
```

#### 配置步骤
1. **服务器准备**: 在文件服务器创建共享文件夹
2. **权限设置**: 设置适当的访问权限
3. **客户端配置**: 配置客户端访问网络路径
4. **测试连接**: 验证网络访问正常

## ⚙️ 配置说明

### 系统配置文件

#### config.json 配置说明
```json
{
  "system": {
    "version": "1.0.0",
    "debug_mode": false,
    "auto_backup": true,
    "backup_interval": 24,
    "max_backup_files": 10
  },
  "paths": {
    "data_dir": "./data",
    "output_dir": "./output",
    "template_dir": "./template",
    "log_dir": "./logs",
    "backup_dir": "./backup"
  },
  "processing": {
    "max_workers": 4,
    "chunk_size": 1000,
    "timeout_seconds": 300,
    "memory_limit_mb": 1024
  },
  "ui": {
    "theme": "default",
    "font_size": 9,
    "auto_save": true,
    "confirm_actions": true
  }
}
```

#### 配置项说明

**系统配置 (system)**
- `version`: 系统版本号
- `debug_mode`: 调试模式开关
- `auto_backup`: 自动备份功能
- `backup_interval`: 备份间隔（小时）
- `max_backup_files`: 最大备份文件数

**路径配置 (paths)**
- `data_dir`: 数据文件目录
- `output_dir`: 输出文件目录
- `template_dir`: 模板文件目录
- `log_dir`: 日志文件目录
- `backup_dir`: 备份文件目录

**处理配置 (processing)**
- `max_workers`: 最大工作线程数
- `chunk_size`: 数据块大小
- `timeout_seconds`: 超时时间
- `memory_limit_mb`: 内存限制

**界面配置 (ui)**
- `theme`: 界面主题
- `font_size`: 字体大小
- `auto_save`: 自动保存
- `confirm_actions`: 操作确认

### 用户配置文件

#### user_preferences.json 配置说明
```json
{
  "recent_files": [
    "C:\\Data\\工资表2024-01.xlsx",
    "C:\\Data\\工资表2024-02.xlsx"
  ],
  "default_paths": {
    "import_path": "C:\\Data",
    "export_path": "C:\\Output"
  },
  "display": {
    "window_maximized": false,
    "window_width": 1200,
    "window_height": 800,
    "show_tips": true
  },
  "processing_options": {
    "auto_match": true,
    "match_threshold": 0.85,
    "validate_data": true,
    "generate_preview": true
  }
}
```

## 🔒 安全配置

### 数据安全
- **本地存储**: 所有数据保存在本地，不上传云端
- **文件加密**: 敏感数据文件可选择加密存储
- **访问控制**: 通过Windows文件权限控制访问
- **备份保护**: 自动备份重要数据

### 权限控制
```powershell
# 设置文件夹权限（管理员权限执行）
$programDir = "C:\Programs\SalarySystem"
$dataDir = "$programDir\data"

# 设置程序目录权限
icacls $programDir /grant "Users:(OI)(CI)RX" /T
icacls $dataDir /grant "Users:(OI)(CI)F" /T

Write-Host "权限设置完成"
```

### 网络安全（如适用）
- **防火墙配置**: 配置Windows防火墙规则
- **网络隔离**: 将系统部署在内网环境
- **访问审计**: 启用文件访问审计功能

## 🔧 性能优化

### 系统优化

#### 内存优化
```json
// config.json 中的内存配置
{
  "processing": {
    "memory_limit_mb": 2048,        // 增加内存限制
    "chunk_size": 500,              // 减少块大小
    "enable_gc": true,              // 启用垃圾回收
    "gc_threshold": 0.8             // GC阈值
  }
}
```

#### 磁盘优化
- **SSD推荐**: 使用SSD硬盘提高I/O性能
- **磁盘清理**: 定期清理临时文件和日志
- **分区优化**: 将数据和程序分别存储在不同分区

#### CPU优化
```json
// 处理器优化配置
{
  "processing": {
    "max_workers": 8,               // 根据CPU核心数调整
    "thread_priority": "normal",    // 线程优先级
    "enable_parallel": true         // 启用并行处理
  }
}
```

### 应用优化

#### 启动优化
- **预加载**: 启用常用功能预加载
- **缓存配置**: 配置适当的缓存大小
- **延迟加载**: 非关键模块延迟加载

#### 运行时优化
- **数据分片**: 大数据文件分批处理
- **内存监控**: 监控内存使用情况
- **自动清理**: 定期清理临时数据

## 📊 监控和维护

### 日志监控

#### 日志文件说明
- **salary_system.log**: 系统运行日志
- **error.log**: 错误和异常日志
- **performance.log**: 性能监控日志
- **access.log**: 文件访问日志

#### 日志配置
```json
{
  "logging": {
    "level": "INFO",
    "max_file_size": "10MB",
    "max_files": 5,
    "compress_old": true,
    "console_output": false
  }
}
```

### 性能监控

#### 监控脚本示例
```powershell
# 性能监控脚本
$processName = "salary_changes_system"
$process = Get-Process -Name $processName -ErrorAction SilentlyContinue

if ($process) {
    $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
    $cpuPercent = [math]::Round($process.CPU, 2)
    
    Write-Host "[$((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))] 内存使用: $memoryMB MB, CPU时间: $cpuPercent 秒"
    
    # 内存使用过高警告
    if ($memoryMB -gt 1024) {
        Write-Warning "内存使用过高: $memoryMB MB"
    }
} else {
    Write-Host "程序未运行"
}
```

### 维护任务

#### 定期维护清单
- [ ] **日志清理**: 每月清理过期日志文件
- [ ] **数据备份**: 每周备份重要数据
- [ ] **临时文件清理**: 每周清理临时文件
- [ ] **配置检查**: 每月检查配置文件完整性
- [ ] **性能检查**: 每月检查系统性能指标
- [ ] **安全检查**: 每季度检查文件权限和安全设置

#### 自动维护脚本
```batch
@echo off
echo 开始系统维护...

:: 清理临时文件
del /q /s "%TEMP%\salary_system_*"

:: 清理过期日志（保留30天）
forfiles /p "logs" /m "*.log" /d -30 /c "cmd /c del @path"

:: 压缩旧备份文件
for %%f in (backup\*.bak) do (
    if not exist "backup\%%~nf.zip" (
        powershell "Compress-Archive -Path 'backup\%%f' -DestinationPath 'backup\%%~nf.zip'"
        del "backup\%%f"
    )
)

echo 维护完成！
pause
```

## 🛠️ 故障排除

### 常见部署问题

#### 问题1：程序无法启动
**症状**: 双击exe文件无反应或立即关闭
**原因分析**:
- 缺少必要的运行时库
- 权限不足
- 杀毒软件误报

**解决方案**:
```batch
:: 解决方案脚本
@echo off
echo 诊断程序启动问题...

:: 检查文件是否存在
if not exist "salary_changes_system.exe" (
    echo 错误: 程序文件不存在
    goto :end
)

:: 检查文件是否被占用
tasklist | find /i "salary_changes_system.exe" > nul
if %errorlevel% == 0 (
    echo 警告: 程序已在运行
    goto :end
)

:: 尝试以管理员权限运行
echo 尝试以管理员权限运行...
powershell -Command "Start-Process 'salary_changes_system.exe' -Verb RunAs"

:end
pause
```

#### 问题2：数据文件无法访问
**症状**: 提示"文件访问被拒绝"或"文件不存在"
**解决方案**:
1. 检查文件路径是否正确
2. 确认文件未被其他程序占用
3. 检查文件权限设置
4. 尝试以管理员权限运行

#### 问题3：性能问题
**症状**: 程序运行缓慢或卡顿
**解决方案**:
1. 检查系统资源使用情况
2. 调整程序配置参数
3. 关闭不必要的后台程序
4. 增加虚拟内存设置

### 高级故障排除

#### 诊断工具
```powershell
# 系统诊断脚本
function Get-SystemDiagnostics {
    Write-Host "=== 系统诊断报告 ===" -ForegroundColor Green
    
    # 系统信息
    $os = Get-WmiObject Win32_OperatingSystem
    Write-Host "操作系统: $($os.Caption) ($($os.Version))"
    
    # 内存信息
    $memory = Get-WmiObject Win32_ComputerSystem
    $totalMemory = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
    Write-Host "总内存: $totalMemory GB"
    
    # 磁盘信息
    Get-WmiObject Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
        $freeSpace = [math]::Round($_.FreeSpace / 1GB, 2)
        $totalSpace = [math]::Round($_.Size / 1GB, 2)
        Write-Host "磁盘 $($_.DeviceID): $freeSpace GB / $totalSpace GB"
    }
    
    # 进程信息
    $salaryProcess = Get-Process -Name "salary_changes_system" -ErrorAction SilentlyContinue
    if ($salaryProcess) {
        $memUsage = [math]::Round($salaryProcess.WorkingSet64 / 1MB, 2)
        Write-Host "程序内存使用: $memUsage MB"
    } else {
        Write-Host "程序未运行"
    }
}

Get-SystemDiagnostics
```

## 🔄 升级指南

### 升级准备

#### 备份清单
1. **配置文件备份**
   - `config.json`
   - `user_preferences.json`
   - 自定义模板文件

2. **数据文件备份**
   - `data/` 文件夹
   - `output/` 文件夹
   - 重要的工作文件

3. **日志文件备份**（可选）
   - `logs/` 文件夹内容

#### 升级脚本
```batch
@echo off
echo 开始系统升级...

:: 备份当前版本
set BACKUP_DIR=backup_%date:~0,4%%date:~5,2%%date:~8,2%
mkdir "%BACKUP_DIR%"

:: 备份配置文件
copy "config.json" "%BACKUP_DIR%\" 2>nul
copy "user_preferences.json" "%BACKUP_DIR%\" 2>nul

:: 备份数据文件夹
xcopy "data" "%BACKUP_DIR%\data" /E /I /Q 2>nul

:: 停止运行中的程序
taskkill /f /im "salary_changes_system.exe" 2>nul

:: 更新程序文件
copy "salary_changes_system_new.exe" "salary_changes_system.exe"

:: 启动新版本
start "" "salary_changes_system.exe"

echo 升级完成！
pause
```

### 版本兼容性

#### 配置文件兼容性
- **向前兼容**: 新版本支持旧版本配置文件
- **自动升级**: 自动转换配置文件格式
- **备份保护**: 升级前自动备份原配置

#### 数据文件兼容性
- **数据格式**: 保持数据格式向前兼容
- **模板文件**: 支持旧版本模板文件
- **增量升级**: 支持增量数据迁移

### 升级验证

#### 验证清单
1. [ ] 程序能正常启动
2. [ ] 配置文件加载正确
3. [ ] 数据文件访问正常
4. [ ] 核心功能工作正常
5. [ ] 报表生成功能正常
6. [ ] 用户偏好设置保持

#### 回滚流程
如果升级失败，可以按以下步骤回滚：

1. **停止新版本程序**
2. **恢复备份的程序文件**
3. **恢复备份的配置文件**
4. **验证回滚成功**
5. **联系技术支持**

---

**文档更新日期**: 2025-01-22  
**技术支持**: 月度工资异动处理系统开发团队  
**版权所有**: © 2025 月度工资异动处理系统开发团队

**🚀 成功部署后，您的系统即可投入生产使用！** 