<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度工资异动处理系统 v1.0</title>
    <style>
        /* General Styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; background: #f5f5f5; color: #333; overflow: hidden; }

        /* Main Layout - 改为响应式布局 */
        .main-container { 
            width: 100vw; 
            height: 100vh; 
            display: flex; 
            flex-direction: column; 
            background: #fff; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-width: 1024px; /* 设置最小宽度 */
            min-height: 768px; /* 设置最小高度 */
        }
        .header { height: 60px; background: linear-gradient(135deg, #4285F4, #34A853); color: white; display: flex; align-items: center; padding: 0 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { font-size: 20px; font-weight: 400; }
        .workspace { flex: 1; display: flex; min-height: 0; /* 重要：确保flex子元素能正确收缩 */ }
        .footer { height: 60px; background: #f8f9fa; border-top: 1px solid #e0e0e0; display: flex; align-items: center; padding: 0 30px; justify-content: space-between; }
        
        /* Header Menu */
        .header .menu-bar { margin-left: auto; display: flex; gap: 20px; align-items: center; }
        .header .menu-item { padding: 8px 16px; border-radius: 4px; cursor: pointer; transition: background 0.2s; position: relative; }
        .header .menu-item:hover { background: rgba(255,255,255,0.1); }
        .exit-icon { padding: 8px; border-radius: 4px; cursor: pointer; transition: background 0.2s; font-size: 18px; font-weight: bold; }
        .exit-icon:hover { background: rgba(255,255,255,0.1); }
        .settings-dropdown { position: absolute; top: 100%; left: 0; background: white; color: #333; min-width: 150px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border-radius: 6px; padding: 8px 0; display: none; z-index: 1000; }
        .settings-dropdown.show { display: block; }
        .dropdown-item { padding: 10px 16px; cursor: pointer; transition: background 0.2s; font-size: 14px; }
        .dropdown-item:hover { background: #f5f5f5; }

        /* Left Panel - 改为响应式宽度 */
        .left-panel { 
            width: 320px; 
            min-width: 280px; 
            max-width: 400px; 
            background: #fafafa; 
            border-right: 1px solid #e0e0e0; 
            display: flex; 
            flex-direction: column; 
            padding: 20px; 
            flex-shrink: 0; /* 防止左侧面板被压缩 */
        }
        .section-title { font-size: 16px; font-weight: 500; color: #333; margin-bottom: 15px; display: flex; align-items: center; }
        .section-title::before { content: ''; width: 4px; height: 16px; background: #4285F4; margin-right: 8px; border-radius: 2px; }
        .tree-view { flex: 1; overflow-y: auto; }
        .tree-item { 
            padding: 8px 12px; 
            cursor: pointer; 
            border-radius: 6px; 
            font-size: 14px; 
            margin-left: 15px; 
            display: flex; 
            align-items: center; 
            gap: 8px; 
            transition: background 0.2s;
        }
        .tree-item:hover { background: #e9e9e9; }
        .tree-item.active { background: #4285F4; color: white; }
        
        /* 根据层级调整样式 */
        .tree-node { position: relative; }
        .tree-node > .tree-item { 
            font-weight: 500; 
            margin-left: 15px;
        }
        .tree-node .tree-node > .tree-item { 
            font-weight: 400; 
            margin-left: 35px; 
            font-size: 13px;
        }
        .tree-node .tree-node .tree-node > .tree-item { 
            font-weight: 400; 
            margin-left: 55px; 
            font-size: 13px;
            color: #666;
        }
        .tree-node .tree-node .tree-node .tree-node > .tree-item { 
            font-weight: 400; 
            margin-left: 75px; 
            font-size: 12px;
            color: #666;
        }
        
        /* 活动状态下保持白色文字 */
        .tree-item.active { 
            color: white !important; 
        }
        
        .tree-node > .tree-children { 
            display: block; 
            padding-left: 0px; /* 移除额外的左边距，通过item的margin-left控制 */
        }
        .tree-node.collapsed > .tree-children { display: none; }
        .tree-toggle { 
            transition: transform 0.2s; 
            font-size: 12px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .tree-node.collapsed .tree-toggle { transform: rotate(-90deg); }

        /* Main Workspace - 改为响应式布局 */
        .main-workspace { flex: 1; display: flex; flex-direction: column; min-width: 0; /* 重要：允许收缩 */ }
        .control-panel { 
            min-height: 80px; 
            background: #fff; 
            border-bottom: 1px solid #e0e0e0; 
            display: flex; 
            align-items: center; 
            padding: 15px 30px; 
            gap: 15px; 
            flex-wrap: wrap;
            /* 为换行内容提供合适的垂直空间 */
            align-content: center; /* 确保换行内容居中对齐 */
            position: relative; /* 为可能的装饰元素做准备 */
        }
        
        /* 查询区域样式优化 */
        .query-section { 
            display: flex; 
            align-items: center; 
            gap: 8px; 
            flex-wrap: wrap;
            /* 在小屏幕上优先保持在同一行 */
            flex-shrink: 0;
        }
        
        .primary-button, .secondary-button, .export-button { 
            padding: 10px 20px; 
            border-radius: 6px; 
            font-size: 14px; 
            cursor: pointer; 
            transition: all 0.2s; 
            border: 1px solid transparent; 
            white-space: nowrap; 
            flex-shrink: 0; /* 防止按钮被压缩 */
        }
        .primary-button { background: #4285F4; color: white; border-color: #4285F4; }
        .primary-button:hover { background: #3367D6; }
        .secondary-button { background: #fff; color: #4285F4; border-color: #4285F4; }
        .secondary-button:hover { background: #f8f9ff; }
        .export-button { background: #34A853; color: white; border-color: #34A853; }
        .export-button:hover { background: #2E7D32; }
        
        .control-actions-right { 
            margin-left: auto; 
            display: flex; 
            gap: 15px; 
            flex-wrap: wrap;
            /* 确保导出按钮组在空间不足时能正确换行 */
        }
        
        .preview-area { flex: 1; background: #fff; overflow: hidden; display: flex; flex-direction: column; min-height: 0; }
        .tab-navigation { padding: 0 20px; border-bottom: 1px solid #e0e0e0; display: flex; align-items: center; height: 50px; background: #fff; }
        .tabs { display: flex; flex-wrap: wrap; gap: 5px; }
        .tab-item { padding: 12px 20px; cursor: pointer; border-bottom: 2px solid transparent; font-size: 14px; color: #666; transition: all 0.2s; white-space: nowrap; }
        .tab-item.active, .tab-item:hover { color: #4285F4; border-bottom-color: #4285F4; }
        
        /* 查询输入框样式优化 */
        .query-input { 
            width: 300px; 
            min-width: 200px; 
            padding: 8px 35px 8px 12px; /* 右侧留出空间给清除图标 */
            border: 1px solid #ddd; 
            border-radius: 6px; 
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        
        /* 输入框获取焦点时的蓝色边框 */
        .query-input:focus {
            outline: none;
            border-color: #4285F4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        /* 查询按钮样式 - 改为蓝色背景 */
        .query-button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #4285F4;
            background: #4285F4;
            color: white;
            white-space: nowrap;
        }
        
        .query-button:hover {
            background: #3367D6;
            border-color: #3367D6;
        }

        /* NEW: Data Interaction Area (Split View) - 改为响应式布局 */
        .data-interaction-area { 
            flex: 1; 
            display: flex; 
            overflow: hidden; 
            padding: 20px; 
            gap: 20px; 
            min-height: 0;
        }
        .data-list-panel { 
            flex: 3; 
            min-width: 400px; /* 设置最小宽度 */
            display: grid; 
            grid-template-rows: 1fr auto; 
            overflow: hidden; 
            gap: 15px; 
        }
        .details-panel { 
            flex: 2; 
            min-width: 300px; /* 设置最小宽度 */
            max-width: 500px; /* 设置最大宽度 */
            background: #fafafa; 
            border: 1px solid #e0e0e0; 
            border-radius: 8px; 
            display: flex; 
            flex-direction: column; 
            overflow: hidden; 
            position: relative;
        }
        .table-container { overflow-y: auto; border-radius: 8px; border: 1px solid #e0e0e0; min-height: 0; }
        
        /* Data Table - 改为响应式表格 */
        .data-table { width: 100%; border-collapse: collapse; min-width: 600px; /* 设置表格最小宽度 */ }
        .data-table th, .data-table td { padding: 12px 16px; text-align: left; border-bottom: 1px solid #f0f0f0; }
        .data-table th { background: #f8f9fa; color: #333; font-weight: 500; font-size: 14px; position: sticky; top: 0; z-index: 10; }
        .data-table td { color: #666; font-size: 14px; }
        .data-table tr { cursor: pointer; }
        .data-table tr:hover { background: #f8f9ff; }
        .data-table tr.selected { background: #e3f2fd; }

        /* Pagination - 改为响应式分页 */
        .pagination-container { display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px; }
        .pagination-info { font-size: 14px; color: #666; }
        .pagination-controls { display: flex; align-items: center; gap: 8px; flex-wrap: wrap; }
        .pagination-button { padding: 6px 10px; border: 1px solid #ddd; background: #fff; border-radius: 4px; cursor: pointer; white-space: nowrap; }
        .pagination-button.active { background: #4285F4; color: white; border-color: #4285F4; }
        .pagination-button.disabled { color: #ccc; cursor: not-allowed; }

        /* NEW: Details Panel Styles */
        .details-header { padding: 16px; border-bottom: 1px solid #e0e0e0; background: #f5f5f5; }
        .details-header h3 { font-size: 16px; font-weight: 500; word-wrap: break-word; /* 长文本换行 */ }
        .details-placeholder { flex: 1; display: flex; align-items: center; justify-content: center; color: #999; text-align: center; }
        .details-content { flex: 1; overflow-y: auto; padding: 20px; }
        .details-section { margin-bottom: 20px; }
        .details-section h4 { font-size: 14px; color: #333; margin-bottom: 12px; border-bottom: 1px solid #e0e0e0; padding-bottom: 8px; }
        .details-item { display: grid; grid-template-columns: 100px 1fr; align-items: center; gap: 15px; padding: 8px 0; }
        .details-label { font-weight: 500; color: #666; font-size: 14px; }
        .details-value { font-size: 14px; color: #333; word-wrap: break-word; /* 长文本换行 */ }
        .details-value.editable { cursor: pointer; padding: 4px; border-radius: 4px; transition: background 0.2s; }
        .details-value.editable:hover { background: #e0e0e0; }
        .details-item input { width: 100%; padding: 4px; border: 1px solid #4285F4; border-radius: 4px; font-size: 14px; font-family: inherit; }
        .diff-value { font-size: 12px; color: #E53935; margin-left: 8px; }
        .diff-value.positive { color: #34A853; }
        
        /* Footer */
        .status-info { display: flex; gap: 20px; align-items: center; font-size: 14px; color: #666; flex-wrap: wrap; }
        .status-item { display: flex; align-items: center; gap: 8px; white-space: nowrap; }
        .status-dot { width: 8px; height: 8px; border-radius: 50%; background: #34A853; }
        .status-dot.processing { background: #FBBC05; }

        .page-size-select {
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #666;
            background: #fff;
            cursor: pointer;
        }

        /* 响应式媒体查询 */
        @media screen and (max-width: 1600px) {
            .query-input { width: 280px; min-width: 200px; }
            .control-panel { gap: 12px; }
        }

        @media screen and (max-width: 1400px) {
            .left-panel { width: 280px; min-width: 250px; }
            .data-list-panel { min-width: 350px; }
            .details-panel { min-width: 250px; max-width: 400px; }
            .query-input { width: 250px; min-width: 180px; }
            .control-panel { gap: 10px; padding: 15px 25px; }
            
            /* 在中等屏幕上，调整按钮间距 */
            .control-actions-right { gap: 12px; }
        }

        @media screen and (max-width: 1200px) {
            .left-panel { width: 250px; min-width: 220px; padding: 15px; }
            .control-panel { padding: 15px 20px; }
            .tab-navigation { padding: 0 15px; }
            .data-interaction-area { padding: 15px; gap: 15px; }
            .data-list-panel { min-width: 300px; }
            .details-panel { min-width: 200px; max-width: 350px; }
            .query-input { width: 220px; min-width: 160px; }
            
            /* 在较小屏幕上，减少按钮内边距 */
            .primary-button, .secondary-button, .export-button, .query-button {
                padding: 8px 16px;
                font-size: 13px;
            }
            .control-actions-right { gap: 10px; }
        }

        @media screen and (max-width: 1024px) {
            .main-container { min-width: 1024px; }
            .left-panel { width: 220px; }
            .control-panel { 
                min-height: auto; 
                padding: 12px 15px; 
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }
            
            /* 在小屏幕上，垂直排列主要控制区域 */
            .query-section {
                width: 100%;
                justify-content: flex-start;
            }
            .query-input { width: 200px; min-width: 150px; }
            .control-actions-right { 
                margin-left: 0; 
                width: 100%;
                justify-content: flex-start;
            }
            
            .tab-navigation { height: auto; min-height: 50px; padding: 10px 15px; }
            .data-interaction-area { flex-direction: column; }
            .data-list-panel, .details-panel { flex: none; min-width: auto; max-width: none; }
            .details-panel { height: 300px; }
        }

        @media screen and (max-width: 900px) {
            .control-panel {
                padding: 10px 12px;
            }
            
            /* 极小屏幕上，按钮使用更紧凑的样式 */
            .primary-button, .secondary-button, .export-button, .query-button {
                padding: 6px 12px;
                font-size: 12px;
            }
            
            .query-input { 
                width: 180px; 
                min-width: 120px;
                font-size: 13px;
                padding: 6px 10px;
            }
            
            /* 导出按钮组在极小屏幕上也可能需要换行 */
            .control-actions-right {
                flex-wrap: wrap;
            }
        }

        /* 设置窗口样式保持不变，但添加响应式支持 */
        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .settings-window {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .settings-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .settings-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }

        .close-button {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .close-button:hover {
            background: #e0e0e0;
            color: #333;
        }

        .settings-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .config-description {
            font-size: 14px;
            color: #666;
        }

        .config-switch {
            width: 50px;
            height: 26px;
            background: #ddd;
            border-radius: 13px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }

        .config-switch.active {
            background: #4285F4;
        }

        .config-switch::after {
            content: '';
            width: 22px;
            height: 22px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .config-switch.active::after {
            transform: translateX(24px);
        }

        .settings-footer {
            padding: 20px 30px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            background: #f8f9fa;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 3000;
        }

        .modal {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 30px;
            width: 90%;
            max-width: 500px;
        }

        .modal h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }

        .modal p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.5;
        }

        /* 新增：查询输入框容器样式 */
        .query-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        /* 新增：清除图标样式 */
        .clear-icon {
            position: absolute;
            right: 10px;
            width: 16px;
            height: 16px;
            cursor: pointer;
            color: #999;
            font-size: 14px;
            display: none; /* 默认隐藏 */
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
            z-index: 1;
        }

        .clear-icon:hover {
            color: #333;
            background: #f0f0f0;
        }

        /* 当输入框有内容时显示清除图标 */
        .query-input:not(:placeholder-shown) + .clear-icon {
            display: flex;
        }

        /* 兼容性：对于不支持:placeholder-shown的浏览器 */
        .query-input-container.has-content .clear-icon {
            display: flex;
        }

        /* 新增：左右两列布局样式 */
        .details-two-column { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px; 
            height: 100%; 
        }
        .details-column { 
            display: flex; 
            flex-direction: column; 
        }
        .details-column-header { 
            background: #f8f9fa; 
            padding: 12px 16px; 
            border-bottom: 2px solid #e0e0e0; 
            font-weight: 500; 
            font-size: 14px; 
            color: #333; 
            text-align: center;
        }
        .details-column-content { 
            flex: 1; 
            padding: 16px; 
            overflow-y: auto; 
        }
        .details-field-row { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 12px 0; 
            border-bottom: 1px solid #f0f0f0; 
        }
        .details-field-row:last-child { 
            border-bottom: none; 
        }
        .details-field-label { 
            font-weight: 500; 
            color: #666; 
            font-size: 14px; 
            min-width: 80px; 
        }
        .details-field-value { 
            font-size: 14px; 
            color: #333; 
            text-align: right; 
            flex: 1; 
        }
        .details-field-value.editable { 
            cursor: pointer; 
            padding: 4px 8px; 
            border-radius: 4px; 
            transition: background 0.2s; 
            border: 1px solid transparent; 
        }
        .details-field-value.editable:hover { 
            background: #f0f8ff; 
            border-color: #4285F4; 
        }
        
        /* 新增：单列表格布局样式 */
        .details-single-table { 
            flex: 1; 
            overflow-y: auto; 
        }
        .details-table { 
            width: 100%; 
            border-collapse: collapse; 
        }
        .details-table th, .details-table td { 
            padding: 12px 16px; 
            text-align: left; 
            border-bottom: 1px solid #f0f0f0; 
            vertical-align: middle;
        }
        .details-table th { 
            background: #f8f9fa; 
            color: #333; 
            font-weight: 500; 
            font-size: 14px; 
            width: 120px;
        }
        .details-table td { 
            color: #333; 
            font-size: 14px; 
        }
        .details-table .current-value { 
            padding: 4px 8px; 
            border-radius: 4px; 
            transition: background 0.2s; 
            border: 1px solid transparent; 
            cursor: pointer;
        }
        .details-table .current-value:hover { 
            background: #f0f8ff; 
            border-color: #4285F4; 
        }
        .details-table .original-value { 
            color: #666; 
            margin-left: 8px; 
            font-size: 12px;
        }
        
        /* 拖拽调整大小控制 */
        .details-resizer { 
            position: absolute; 
            left: 0; 
            top: 0; 
            bottom: 0; 
            width: 4px; 
            background: #e0e0e0; 
            cursor: col-resize; 
            transition: background 0.2s; 
            z-index: 10;
            opacity: 0.7;
        }
        .details-resizer:hover, .details-resizer.dragging { 
            background: #4285F4; 
            opacity: 1;
        }
        
        /* 显示/隐藏控制按钮 */
        .details-toggle { 
            position: absolute; 
            left: -12px; 
            top: 50%; 
            transform: translateY(-50%); 
            width: 24px; 
            height: 48px; 
            background: #4285F4; 
            border: none; 
            border-radius: 4px 0 0 4px; 
            color: white; 
            cursor: pointer; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            font-size: 14px; 
            z-index: 11;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .details-toggle:hover { 
            background: #3367D6; 
            left: -14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        /* 当详细信息面板隐藏时，切换按钮的特殊样式 */
        .details-toggle.panel-hidden {
            left: auto !important;
            right: 12px !important;
            background: #4285F4 !important;
            border-radius: 0 4px 4px 0 !important;
            position: fixed !important;
            z-index: 1000 !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            width: 24px !important;
            height: 48px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 14px !important;
            color: white !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
            transition: all 0.2s !important;
        }
        .details-toggle.panel-hidden:hover {
            background: #3367D6 !important;
            right: 14px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
        }
        .data-interaction-area.details-hidden { 
            gap: 0; 
        }
        .data-interaction-area.details-hidden .data-list-panel { 
            flex: 1; 
            max-width: none;
            width: 100%;
        }
        
        /* 当详细信息面板隐藏时，切换按钮的特殊样式 */
        .details-toggle.panel-hidden {
            left: auto !important;
            right: 20px !important;
            background: #4285F4 !important;
            border-radius: 4px 0 0 4px !important;
            position: fixed !important;
            z-index: 1000 !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            width: 24px !important;
            height: 48px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            box-shadow: -2px 0 8px rgba(0,0,0,0.3) !important;
        }
        .details-toggle.panel-hidden:hover {
            background: #3367D6 !important;
            right: 18px !important;
        }
        
        /* 隐藏状态样式 */
        .details-panel.hidden { 
            width: 0 !important; 
            min-width: 0 !important; 
            max-width: 0 !important; 
            overflow: hidden; 
            border: none; 
            padding: 0;
            margin: 0;
            flex: none !important;
        }
        .details-panel.hidden .details-resizer {
            display: none;
        }

        /* 新增：详细信息表格的双列布局样式 */
        .details-table .salary-row td {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;
        }
        .details-table .original-value {
            color: #666;
            font-size: 14px;
            font-weight: normal;
            flex-shrink: 0;
            min-width: 80px;
        }
        .details-table .current-value-container {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: flex-end;
        }
        .details-table .current-value { 
            padding: 4px 8px; 
            border-radius: 4px; 
            transition: background 0.2s; 
            border: 1px solid transparent; 
            cursor: pointer;
            font-weight: 500;
        }
        .details-table .current-value:hover { 
            background: #f0f8ff; 
            border-color: #4285F4; 
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <header class="header">
            <h1>月度工资异动处理系统 v1.0</h1>
            <nav class="menu-bar">
                <div class="menu-item">文件</div>
                <div class="menu-item">报表</div>
                <div class="menu-item">分析</div>
                <div class="menu-item">工具</div>
                <div class="menu-item" onclick="toggleSettingsDropdown(event)">
                    设置
                    <div class="settings-dropdown" id="settings-dropdown">
                        <div class="dropdown-item" onclick="openSettingsWindow('basic')">基础规则</div>
                        <div class="dropdown-item" onclick="openSettingsWindow('types')">异动类型</div>
                        <div class="dropdown-item" onclick="openSettingsWindow('output')">输出选项</div>
                    </div>
                </div>
                <div class="menu-item">帮助</div>
                <div class="exit-icon" onclick="exitSystem()" title="退出系统">×</div>
            </nav>
        </header>

        <!-- Main Workspace -->
        <div class="workspace">
            <!-- Left Panel -->
            <aside class="left-panel">
                <h2 class="section-title">数据导航</h2>
                <div id="tree-view" class="tree-view"></div>
            </aside>

            <!-- Main Workspace -->
            <main class="main-workspace">
                <!-- Control Panel -->
                <div class="control-panel">
                    <div class="query-section">
                        <div class="query-input-container">
                            <input type="text" class="query-input" id="query-input" placeholder="输入工号或姓名查询..." oninput="handleQueryInput()" onkeydown="handleQueryKeydown(event)">
                            <div class="clear-icon" onclick="clearQuery()">×</div>
                        </div>
                        <button class="query-button" onclick="performSearch()">查询</button>
                    </div>
                    <div class="control-actions-right">
                        <button class="export-button">异动处理</button>
                        <button class="export-button">导出审批表</button>
                        <button class="export-button">导出请示 (OA)</button>
                        <button class="export-button">导出请示 (部务会)</button>
                    </div>
                </div>
                <!-- Preview Area -->
                <div class="preview-area">
                    <div class="tab-navigation">
                        <div class="tabs">
                            <div class="tab-item active" onclick="switchTab('summary')">异动汇总</div>
                            <div class="tab-item" onclick="switchTab('matching')">人员匹配</div>
                            <div class="tab-item" onclick="switchTab('calculation')">计算结果</div>
                            <div class="tab-item" onclick="switchTab('log')">处理日志</div>
                        </div>
                    </div>
                    <!-- NEW: Data Interaction Area (Split View) -->
                    <div class="data-interaction-area">
                        <div class="data-list-panel">
                            <div class="table-container" id="table-container">
                                <!-- Table will be rendered here by JS -->
                            </div>
                            <div class="pagination-container" id="pagination-container">
                                <!-- Pagination will be rendered here by JS -->
                            </div>
                        </div>
                        <div class="details-panel" id="details-panel">
                            <div class="details-resizer" id="details-resizer" title="拖拽调整宽度，双击重置"></div>
                            <button class="details-toggle" id="details-toggle" onclick="toggleDetailsPanel()" title="隐藏详细信息">▶</button>
                            <div class="details-placeholder">
                                <p style="padding: 20px; color: #999; text-align: center;">从左侧列表中选择一项以查看详细信息。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        
        <!-- Footer -->
        <footer class="footer">
            <div class="status-info">
                <div class="status-item"><div class="status-dot"></div><span id="status-text">系统就绪</span></div>
                <div class="status-item" id="status-files">未加载文件</div>
                <div class="status-item" id="status-stats"></div>
            </div>
        </footer>
    </div>
    
    <!-- All settings windows and modals are kept as they are -->
    <!-- ... existing settings windows ... -->
    <div class="settings-overlay" id="basic-settings-overlay">
        <div class="settings-window">
            <div class="settings-header">
                <h3>基础规则设置</h3>
                <button class="close-button" onclick="closeSettingsWindow('basic')">×</button>
            </div>
            
            <div class="settings-content">
                <div class="config-item">
                    <div>
                        <div class="config-label">启用工号优先匹配</div>
                        <div class="config-description">优先使用工号进行人员匹配，推荐开启</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">启用身份证辅助匹配</div>
                        <div class="config-description">当工号匹配失败时，使用身份证号进行匹配</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">启用姓名模糊匹配</div>
                        <div class="config-description">当前两种方式都失败时，使用姓名进行模糊匹配</div>
                    </div>
                    <div class="config-switch" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">严格匹配模式</div>
                        <div class="config-description">开启后将进行更严格的数据验证</div>
                    </div>
                    <div class="config-switch" onclick="toggleSwitch(this)"></div>
                </div>
            </div>
            
            <div class="settings-footer">
                <button class="secondary-button" onclick="resetBasicSettings()">恢复默认</button>
                <button class="secondary-button" onclick="closeSettingsWindow('basic')">取消</button>
                <button class="primary-button" onclick="saveBasicSettings()">保存设置</button>
            </div>
        </div>
    </div>

    <div class="settings-overlay" id="types-settings-overlay">
        <div class="settings-window">
            <div class="settings-header">
                <h3>异动类型设置</h3>
                <button class="close-button" onclick="closeSettingsWindow('types')">×</button>
            </div>
            
            <div class="settings-content">
                <div class="config-item">
                    <div>
                        <div class="config-label">处理起薪（恢复待遇）</div>
                        <div class="config-description">处理人员起薪和恢复待遇异动</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">处理停薪-退休</div>
                        <div class="config-description">处理退休人员停薪异动</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">处理考核扣款</div>
                        <div class="config-description">处理因考核产生的扣款异动</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">处理请假扣款</div>
                        <div class="config-description">处理因请假产生的扣款异动</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">处理工资调整</div>
                        <div class="config-description">处理职级调整等引起的工资变动</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
            </div>
            
            <div class="settings-footer">
                <button class="secondary-button" onclick="resetTypesSettings()">恢复默认</button>
                <button class="secondary-button" onclick="closeSettingsWindow('types')">取消</button>
                <button class="primary-button" onclick="saveTypesSettings()">保存设置</button>
            </div>
        </div>
    </div>

    <div class="settings-overlay" id="output-settings-overlay">
        <div class="settings-window">
            <div class="settings-header">
                <h3>输出选项设置</h3>
                <button class="close-button" onclick="closeSettingsWindow('output')">×</button>
            </div>
            
            <div class="settings-content">
                <div class="config-item">
                    <div>
                        <div class="config-label">生成异动汇总表</div>
                        <div class="config-description">自动生成Excel格式的异动汇总表</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">生成工资审批表</div>
                        <div class="config-description">自动生成Excel格式的工资审批表</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">生成OA流程请示文档</div>
                        <div class="config-description">自动生成Word格式的OA流程请示文档</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">生成部务会请示文档</div>
                        <div class="config-description">自动生成Word格式的部务会请示文档</div>
                    </div>
                    <div class="config-switch active" onclick="toggleSwitch(this)"></div>
                </div>
                <div class="config-item">
                    <div>
                        <div class="config-label">生成处理详情日志</div>
                        <div class="config-description">保存详细的处理过程日志文件</div>
                    </div>
                    <div class="config-switch" onclick="toggleSwitch(this)"></div>
                </div>
            </div>
            
            <div class="settings-footer">
                <button class="secondary-button" onclick="resetOutputSettings()">恢复默认</button>
                <button class="secondary-button" onclick="closeSettingsWindow('output')">取消</button>
                <button class="primary-button" onclick="saveOutputSettings()">保存设置</button>
            </div>
        </div>
    </div>

    <div class="modal-overlay" id="modal-overlay">
        <div class="modal">
            <h3>处理完成</h3>
            <p>工资异动处理已完成，共处理 159 人，生成以下文件：</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li>2025年5月份工资表（异动处理后）.xlsx</li>
                <li>5月份异动汇总表.xlsx</li>
                <li>工资审批表.xlsx</li>
                <li>关于发放教职工薪酬的请示.docx</li>
            </ul>
            <div style="text-align: right; margin-top: 20px;">
                <button class="secondary-button" onclick="closeModal()">关闭</button>
                <button class="primary-button" onclick="openOutputFolder()" style="margin-left: 10px;">打开文件夹</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {

        // --- STATE MANAGEMENT ---
        const appState = {
            selectedPersonId: null,
            editingField: { element: null, personId: null, field: null },
            currentPage: 1,
            pageSize: 10,
            allData: [],
            displayedData: [],
            treeData: [
                { 
                    name: '月度工资表', 
                    children: [
                        { 
                            name: '2025年', 
                            children: [
                                { 
                                    name: '5月', 
                                    children: [ 
                                        { name: '全部在职人员', isDefault: true }, // 标记为默认选中
                                        { name: '离休人员' }, 
                                        { name: '退休人员' } 
                                    ] 
                                },
                                { 
                                    name: '4月', 
                                    children: [ 
                                        { name: '全部在职人员' }, 
                                        { name: '离休人员' },
                                        { name: '退休人员' }
                                    ] 
                                },
                                { 
                                    name: '3月', 
                                    children: [ 
                                        { name: '全部在职人员' }, 
                                        { name: '离休人员' },
                                        { name: '退休人员' }
                                    ] 
                                }
                            ]
                        },
                        { 
                            name: '2024年', 
                            children: [
                                { 
                                    name: '12月', 
                                    children: [ 
                                        { name: '全部在职人员' }, 
                                        { name: '离休人员' },
                                        { name: '退休人员' }
                                    ] 
                                },
                                { 
                                    name: '11月', 
                                    children: [ 
                                        { name: '全部在职人员' }, 
                                        { name: '离休人员' },
                                        { name: '退休人员' }
                                    ] 
                                }
                            ]
                        }
                    ]
                },
                { 
                    name: '异动人员表', 
                    children: [
                        { 
                            name: '2025年', 
                            children: [
                                { 
                                    name: '5月', 
                                    children: [ 
                                        { name: '起薪' }, 
                                        { name: '转正定级' }, 
                                        { name: '停薪-退休' } 
                                    ] 
                                },
                                { 
                                    name: '4月', 
                                    children: [ 
                                        { name: '考核扣款' }, 
                                        { name: '请假扣款' },
                                        { name: '工资调整' }
                                    ] 
                                },
                                { 
                                    name: '3月', 
                                    children: [ 
                                        { name: '起薪' }, 
                                        { name: '转正定级' }
                                    ] 
                                }
                            ]
                        },
                        { 
                            name: '2024年', 
                            children: [
                                { 
                                    name: '12月', 
                                    children: [ 
                                        { name: '起薪' }, 
                                        { name: '考核扣款' }
                                    ] 
                                },
                                { 
                                    name: '11月', 
                                    children: [ 
                                        { name: '转正定级' }, 
                                        { name: '请假扣款' }
                                    ] 
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        // --- MOCK DATA GENERATION ---
        function generateMockData(count = 159) {
            const data = [];
            const names = ['张三', '李四', '王五', '赵六', '钱七', '陈八', '刘九', '杨十', '黄一', '周二'];
            const departments = ['财务处', '人事处', '行政处', '技术部', '市场部'];
            
            // 前几条添加固定的易于测试的数据
            const testData = [
                { name: '张三', employeeId: '2024001', department: '财务处' },
                { name: '李四', employeeId: '2024002', department: '人事处' },
                { name: '王五', employeeId: '2024003', department: '技术部' },
                { name: '测试员工', employeeId: '2024999', department: '测试部门' }
            ];
            
            for (let i = 1; i <= count; i++) {
                const originalBasic = Math.round((Math.random() * 5000 + 5000) / 100) * 100;
                const originalAllowance = Math.round((Math.random() * 2000 + 1000) / 50) * 50;
                
                let personData;
                if (i <= testData.length) {
                    // 使用预定义的测试数据
                    personData = {
                        id: i,
                        name: testData[i-1].name,
                        employeeId: testData[i-1].employeeId,
                        department: testData[i-1].department,
                        position: '专员',
                        basicSalary: originalBasic,
                        allowance: originalAllowance,
                        original_basicSalary: originalBasic,
                        original_allowance: originalAllowance,
                    };
                } else {
                    // 生成随机数据
                    personData = {
                        id: i,
                        name: names[Math.floor(Math.random() * names.length)] + (i-testData.length),
                        employeeId: `202400${String(i).padStart(3, '0')}`,
                        department: departments[Math.floor(Math.random() * departments.length)],
                        position: '专员',
                        basicSalary: originalBasic,
                        allowance: originalAllowance,
                        original_basicSalary: originalBasic,
                        original_allowance: originalAllowance,
                    };
                }
                
                data.push(personData);
            }
            return data;
        }

        // --- UI RENDERERS ---

        function renderTreeView() {
            const treeContainer = document.getElementById('tree-view');
            let html = '';
            let defaultNodePath = null; // 记录默认选中节点的路径
            
            function buildTree(nodes, depth = 0, parentPath = '') {
                html += `<div class="tree-children">`;
                nodes.forEach((node, index) => {
                    const hasChildren = node.children && node.children.length > 0;
                    const currentPath = parentPath ? `${parentPath} > ${node.name}` : node.name;
                    const nodeId = `node-${depth}-${index}`;
                    
                    // 检查是否是默认选中节点
                    if (node.isDefault) {
                        defaultNodePath = { path: currentPath, nodeId: nodeId };
                    }
                    
                    // 根据层级决定默认展开状态
                    let expandedClass = '';
                    if (hasChildren) {
                        if (depth === 0) {
                            // 第一级：月度工资表默认展开，异动人员表默认收起
                            expandedClass = node.name === '月度工资表' ? '' : 'collapsed';
                        } else if (depth === 1 && node.name === '2025年') {
                            // 第二级：2025年默认展开
                            expandedClass = '';
                        } else if (depth === 2 && node.name === '5月') {
                            // 第三级：5月默认展开
                            expandedClass = '';
                        } else {
                            // 其他情况默认收起
                            expandedClass = 'collapsed';
                        }
                    }
                    
                    html += `<div class="tree-node ${expandedClass}" id="${nodeId}">`;
                    html += `<div class="tree-item" onclick="selectTreeNode(this, '${currentPath}', '${nodeId}')">
                                ${hasChildren ? '<span class="tree-toggle">▶</span>' : ''}
                                <span>${node.name}</span>
                             </div>`;
                    if (hasChildren) {
                        buildTree(node.children, depth + 1, currentPath);
                    }
                    html += `</div>`;
                });
                html += `</div>`;
            }
            
            buildTree(appState.treeData, 0, '');
            treeContainer.innerHTML = html.replace('<div class="tree-children">', '').slice(0, -6); // Remove outer container
            
            // 设置默认选中
            if (defaultNodePath) {
                setTimeout(() => {
                    const defaultElement = document.querySelector(`#${defaultNodePath.nodeId} .tree-item`);
                    if (defaultElement) {
                        defaultElement.classList.add('active');
                        document.getElementById('status-files').textContent = `已加载: ${defaultNodePath.path}`;
                        console.log('默认选中:', defaultNodePath.path);
                    }
                }, 10); // 稍微延迟确保DOM完全渲染
            }
        }

        function renderTable() {
            const tableContainer = document.getElementById('table-container');
            const { currentPage, pageSize, displayedData } = appState;
            
            if (displayedData.length === 0) {
                // 检查是否是因为查询导致的无结果
                const queryInput = document.getElementById('query-input');
                const hasQuery = queryInput && queryInput.value.trim() !== '';
                
                // 使用DOM方法创建元素，而不是innerHTML，确保文本内容正确显示
                const placeholderDiv = document.createElement('div');
                placeholderDiv.className = 'details-placeholder';
                placeholderDiv.style.cssText = 'padding: 40px; text-align: center;';
                
                const mainMessage = document.createElement('p');
                mainMessage.style.cssText = 'color: #666; font-size: 16px; margin-bottom: 10px;';
                mainMessage.textContent = hasQuery ? 
                    `没有找到匹配"${queryInput.value.trim()}"的记录。` : 
                    '没有可显示的数据。';
                placeholderDiv.appendChild(mainMessage);
                
                tableContainer.innerHTML = '';
                tableContainer.appendChild(placeholderDiv);
                renderPagination();
                return;
            }

            const pageData = displayedData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
            let tableHtml = `<table class="data-table">
                <thead><tr><th>姓名</th><th>工号</th><th>部门</th><th>基本工资</th><th>岗位津贴</th></tr></thead>
                <tbody>`;
            
            pageData.forEach(p => {
                const isSelected = p.id === appState.selectedPersonId;
                tableHtml += `<tr data-id="${p.id}" class="${isSelected ? 'selected' : ''}" onclick="selectRow(this)">
                    <td>${p.name}</td><td>${p.employeeId}</td><td>${p.department}</td><td>${p.basicSalary.toFixed(2)}</td><td>${p.allowance.toFixed(2)}</td>
                </tr>`;
            });

            tableHtml += `</tbody></table>`;
            tableContainer.innerHTML = tableHtml;
            renderPagination();
        }

        function renderPagination() {
            const container = document.getElementById('pagination-container');
            const { currentPage, pageSize, displayedData } = appState;
            const totalRecords = displayedData.length;
            const totalPages = Math.ceil(totalRecords / pageSize);

            if (totalRecords === 0) {
                container.style.display = 'none';
                return;
            }
            container.style.display = 'flex';
            
            const startRecord = (currentPage - 1) * pageSize + 1;
            const endRecord = Math.min(currentPage * pageSize, totalRecords);

            let html = `<div class="pagination-info">显示 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条</div>`;
            
            if (totalPages > 1) {
                html += `<div class="pagination-controls">
                            <button class="pagination-button ${currentPage === 1 ? 'disabled' : ''}" onclick="changePage(${currentPage - 1})">上一页</button>`;
                
                const pageSiblings = 1;
                const pagesToShow = [];
                
                for(let i = 1; i <= totalPages; i++) {
                    if (i === 1 || i === totalPages || (i >= currentPage - pageSiblings && i <= currentPage + pageSiblings)) {
                        pagesToShow.push(i);
                    }
                }

                const finalPages = [];
                let lastPage = 0;
                for (const page of pagesToShow) {
                    if (lastPage) {
                        if (page > lastPage + 1) {
                            finalPages.push('...');
                        }
                    }
                    finalPages.push(page);
                    lastPage = page;
                }

                finalPages.forEach(page => {
                    if (page === '...') {
                        html += `<button class="pagination-button disabled">...</button>`;
                    } else {
                        html += `<button class="pagination-button ${page === currentPage ? 'active' : ''}" onclick="changePage(${page})">${page}</button>`;
                    }
                });

                html += `<button class="pagination-button ${currentPage === totalPages ? 'disabled' : ''}" onclick="changePage(${currentPage + 1})">下一页</button>`;
            } else {
                html += `<div class="pagination-controls">`;
            }
            
            // 每页显示记录数选择器始终显示
            html += `<span style="margin-right: 10px;">每页显示：</span>
                     <select class="page-size-select" onchange="changePageSize(this.value)">
                         <option value="10" ${pageSize === 10 ? 'selected' : ''}>10条</option>
                         <option value="20" ${pageSize === 20 ? 'selected' : ''}>20条</option>
                         <option value="50" ${pageSize === 50 ? 'selected' : ''}>50条</option>
                     </select>
                     </div>`;
            
            container.innerHTML = html;
        }

        function renderDetailsPanel() {
            const detailsPanel = document.getElementById('details-panel');
            const person = appState.allData.find(p => p.id === appState.selectedPersonId);

            // 保持拖拽条和切换按钮
            const resizer = document.getElementById('details-resizer');
            const toggleButton = document.getElementById('details-toggle');
            
            if (!person) {
                detailsPanel.innerHTML = `
                    <div class="details-resizer" id="details-resizer" title="拖拽调整宽度，双击重置"></div>
                    <button class="details-toggle" id="details-toggle" onclick="toggleDetailsPanel()" title="隐藏详细信息">▶</button>
                    <div class="details-placeholder">
                        <p style="padding: 20px; color: #999; text-align: center;">从左侧列表中选择一项以查看详细信息。</p>
                    </div>`;
                return;
            }
            
            function createDiffHtml(current, original) {
                const diff = current - original;
                if (Math.abs(diff) < 0.01) return '';
                const sign = diff > 0 ? '+' : '';
                const className = diff > 0 ? 'positive' : '';
                return `<span class="diff-value ${className}">(${sign}${diff.toFixed(2)})</span>`;
            }

            const basicSalaryDiff = createDiffHtml(person.basicSalary, person.original_basicSalary);
            const allowanceDiff = createDiffHtml(person.allowance, person.original_allowance);

            detailsPanel.innerHTML = `
                <div class="details-resizer" id="details-resizer" title="拖拽调整宽度，双击重置"></div>
                <button class="details-toggle" id="details-toggle" onclick="toggleDetailsPanel()" title="隐藏详细信息">▶</button>
                <div class="details-header"><h3>${person.name} (${person.employeeId}) - 详细信息</h3></div>
                <div class="details-content">
                    <div class="details-single-table">
                        <table class="details-table">
                            <tr class="salary-row">
                                <th>基本工资</th>
                                <td>
                                    <span class="original-value">${person.original_basicSalary.toFixed(2)}</span>
                                    <div class="current-value-container">
                                        <span class="current-value editable" data-field="basicSalary" data-id="${person.id}" ondblclick="enableEditMode(this)">
                                            ${person.basicSalary.toFixed(2)}
                                        </span>
                                        ${basicSalaryDiff}
                                    </div>
                                </td>
                            </tr>
                            <tr class="salary-row">
                                <th>岗位津贴</th>
                                <td>
                                    <span class="original-value">${person.original_allowance.toFixed(2)}</span>
                                    <div class="current-value-container">
                                        <span class="current-value editable" data-field="allowance" data-id="${person.id}" ondblclick="enableEditMode(this)">
                                            ${person.allowance.toFixed(2)}
                                        </span>
                                        ${allowanceDiff}
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>部门</th>
                                <td>${person.department}</td>
                            </tr>
                            <tr>
                                <th>职位</th>
                                <td>${person.position}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            `;
            
            // 重新初始化拖拽功能（因为DOM被重新创建了）
            setTimeout(() => {
                initDetailsResizer();
            }, 10);
        }

        // --- EVENT HANDLERS & ACTIONS ---
        
        window.selectRow = (rowElement) => {
            const personId = parseInt(rowElement.dataset.id);
            if (appState.editingField.element || appState.selectedPersonId === personId) return;
            
            const prevSelected = document.querySelector('.data-table tr.selected');
            if (prevSelected) prevSelected.classList.remove('selected');
            rowElement.classList.add('selected');

            appState.selectedPersonId = personId;
            renderDetailsPanel();
        };

        window.changePage = (page) => {
            const totalPages = Math.ceil(appState.displayedData.length / appState.pageSize);
            if (page < 1 || page > totalPages) return;
            appState.currentPage = page;
            renderTable();
        };

        window.changePageSize = (newSize) => {
            appState.pageSize = parseInt(newSize);
            appState.currentPage = 1; // 重置到第一页
            renderTable();
        };

        window.performSearch = () => {
            const query = document.getElementById('query-input').value.toLowerCase().trim();
            
            if (query === '') {
                // 如果查询为空，显示所有数据
                appState.displayedData = [...appState.allData];
            } else {
                // 根据姓名或工号进行筛选
                appState.displayedData = appState.allData.filter(p => 
                    p.name.toLowerCase().includes(query) || 
                    p.employeeId.toLowerCase().includes(query)
                );
            }
            
            appState.currentPage = 1;
            appState.selectedPersonId = null;
            renderTable();
            renderDetailsPanel();
            
            // 更新状态栏信息
            const totalRecords = appState.displayedData.length;
            const statusText = query === '' ? 
                `共 ${totalRecords} 条记录` : 
                `查询到 ${totalRecords} 条记录`;
            document.getElementById('status-stats').textContent = statusText;
        };

        // 新增：清除查询功能
        window.clearQuery = () => {
            const queryInput = document.getElementById('query-input');
            queryInput.value = '';
            
            // 恢复到初始状态
            appState.displayedData = [...appState.allData];
            appState.currentPage = 1;
            appState.selectedPersonId = null;
            renderTable();
            renderDetailsPanel();
            
            // 更新状态栏信息
            document.getElementById('status-stats').textContent = `共 ${appState.allData.length} 条记录`;
            
            // 更新清除图标显示状态
            updateClearIconVisibility();
            
            // 让输入框重新获得焦点
            queryInput.focus();
        };

        // 新增：更新清除图标显示状态
        function updateClearIconVisibility() {
            const queryInput = document.getElementById('query-input');
            const container = queryInput.parentElement;
            
            if (queryInput.value.trim() !== '') {
                container.classList.add('has-content');
            } else {
                container.classList.remove('has-content');
            }
        }

        // 新增：处理输入框输入事件
        window.handleQueryInput = function() {
            updateClearIconVisibility();
            
            // 可选：实时搜索（在用户停止输入后延迟执行）
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                performSearch();
            }, 300); // 300ms延迟
        };

        // 新增：处理回车键搜索
        window.handleQueryKeydown = function(event) {
            if (event.key === 'Enter') {
                clearTimeout(window.searchTimeout); // 取消延迟搜索
                performSearch();
            }
        };

        window.selectTreeNode = (element, fullPath, nodeId) => {
            const prevSelected = document.querySelector('.tree-item.active');
            if (prevSelected) prevSelected.classList.remove('active');
            element.classList.add('active');

            const parent = element.parentElement;
            if (parent.classList.contains('tree-node')) {
                const isCollapsed = parent.classList.contains('collapsed');
                
                // 智能展开/收起逻辑
                if (element.nextElementSibling && element.nextElementSibling.classList.contains('tree-children')) {
                    // 如果当前节点有子节点，切换其展开/收起状态
                    parent.classList.toggle('collapsed', !isCollapsed);
                } else {
                    // 如果是叶子节点，只展开当前路径
                    parent.classList.remove('collapsed');
                }
            }
            
            // 更新状态栏显示完整路径
            document.getElementById('status-files').textContent = `已加载: ${fullPath}`;
            
            // 根据选中的节点类型模拟不同的数据加载
            console.log('选中节点:', fullPath);
            
            // 可以在这里根据不同的路径加载不同的模拟数据
            // 暂时使用现有的搜索逻辑
            performSearch();
        };

        window.enableEditMode = (element) => {
            if (appState.editingField.element) return; // Already editing

            const personId = parseInt(element.dataset.id);
            const field = element.dataset.field;
            const person = appState.allData.find(p => p.id === personId);
            const currentValue = person[field];
            
            // 保存原始内容用于取消编辑
            const originalContent = element.innerHTML;
            
            // 创建输入框
            element.innerHTML = `<input type="number" step="0.01" value="${currentValue}" onblur="saveFieldEdit(this)" onkeydown="handleEditKeydown(event, this)" style="width: 100%; border: 1px solid #4285F4; border-radius: 4px; padding: 4px 8px; font-size: 14px; font-family: inherit;">`;
            const input = element.querySelector('input');
            input.focus();
            input.select();
            
            appState.editingField = { element, personId, field, originalContent };
        };

        window.handleEditKeydown = (event, inputElement) => {
            if (event.key === 'Enter') inputElement.blur(); // Trigger blur to save
            else if (event.key === 'Escape') cancelFieldEdit();
        };
        
        window.saveFieldEdit = (inputElement) => {
            const { personId, field } = appState.editingField;
            const newValue = parseFloat(inputElement.value);

            if (!isNaN(newValue)) {
                const person = appState.allData.find(p => p.id === personId);
                if(person) {
                    person[field] = newValue;
                }
            }
            
            // 清除编辑状态
            appState.editingField = { element: null, personId: null, field: null, originalContent: null };
            
            // 重新渲染详细信息面板和表格以显示更新后的值
            renderTable();
            renderDetailsPanel();
        };

        window.cancelFieldEdit = () => {
            const { element, originalContent } = appState.editingField;
            
            if (element && originalContent) {
                element.innerHTML = originalContent;
            }
            
            appState.editingField = { element: null, personId: null, field: null, originalContent: null };
        };

        // --- GLOBAL LISTENERS ---
        
        document.addEventListener('click', (e) => {
            // Deselect row if clicking outside the main interaction areas
            const isOutside = !e.target.closest('.data-list-panel') && !e.target.closest('.details-panel');
            if (isOutside && appState.selectedPersonId != null && !appState.editingField.element) {
                const prevSelected = document.querySelector('.data-table tr.selected');
                if (prevSelected) prevSelected.classList.remove('selected');
                appState.selectedPersonId = null;
                renderDetailsPanel();
            }

            // Close settings dropdown
            if (!e.target.closest('.menu-item') && document.getElementById('settings-dropdown').classList.contains('show')) {
                document.getElementById('settings-dropdown').classList.remove('show');
            }
        });

        window.toggleSettingsDropdown = (event) => {
            event.stopPropagation();
            document.getElementById('settings-dropdown').classList.toggle('show');
        };

        // --- 设置窗体相关函数 ---
        
        // 打开设置窗体
        window.openSettingsWindow = (type) => {
            // 关闭下拉菜单
            document.getElementById('settings-dropdown').classList.remove('show');
            
            // 显示对应的设置窗体
            const overlayId = type + '-settings-overlay';
            const overlay = document.getElementById(overlayId);
            if (overlay) {
                overlay.style.display = 'flex';
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        };

        // 关闭设置窗体
        window.closeSettingsWindow = (type) => {
            const overlayId = type + '-settings-overlay';
            const overlay = document.getElementById(overlayId);
            if (overlay) {
                overlay.style.display = 'none';
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        };

        // 切换开关状态
        window.toggleSwitch = (switchElement) => {
            switchElement.classList.toggle('active');
        };

        // 基础规则设置相关函数
        window.resetBasicSettings = () => {
            const switches = document.querySelectorAll('#basic-settings-overlay .config-switch');
            switches.forEach((sw, index) => {
                // 默认前两个开关开启，后两个关闭
                if (index < 2) {
                    sw.classList.add('active');
                } else {
                    sw.classList.remove('active');
                }
            });
        };

        window.saveBasicSettings = () => {
            // 这里可以添加保存设置的逻辑
            console.log('保存基础规则设置');
            closeSettingsWindow('basic');
            // 可以在这里添加保存成功的提示
        };

        // 异动类型设置相关函数
        window.resetTypesSettings = () => {
            const switches = document.querySelectorAll('#types-settings-overlay .config-switch');
            switches.forEach((sw) => {
                sw.classList.add('active'); // 默认所有异动类型都开启
            });
        };

        window.saveTypesSettings = () => {
            // 这里可以添加保存设置的逻辑
            console.log('保存异动类型设置');
            closeSettingsWindow('types');
            // 可以在这里添加保存成功的提示
        };

        // 输出选项设置相关函数
        window.resetOutputSettings = () => {
            const switches = document.querySelectorAll('#output-settings-overlay .config-switch');
            switches.forEach((sw, index) => {
                // 默认前四个开关开启，最后一个关闭
                if (index < 4) {
                    sw.classList.add('active');
                } else {
                    sw.classList.remove('active');
                }
            });
        };

        window.saveOutputSettings = () => {
            // 这里可以添加保存设置的逻辑
            console.log('保存输出选项设置');
            closeSettingsWindow('output');
            // 可以在这里添加保存成功的提示
        };

        // 处理模态框和设置窗体的ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // 关闭所有设置窗体
                const overlays = document.querySelectorAll('.settings-overlay');
                overlays.forEach(overlay => {
                    if (overlay.style.display === 'flex') {
                        overlay.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }
                });
                
                // 关闭模态框
                const modal = document.getElementById('modal-overlay');
                if (modal && modal.style.display === 'flex') {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            }
        });

        // 点击设置窗体背景关闭窗体
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('settings-overlay')) {
                e.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });

        // 其他必要的功能函数（如果还没有定义的话）
        window.exitSystem = () => {
            if (confirm('确定要退出系统吗？')) {
                // 这里可以添加退出逻辑
                console.log('退出系统');
            }
        };

        window.switchTab = (tabName) => {
            // 移除所有标签的active类
            document.querySelectorAll('.tab-item').forEach(tab => tab.classList.remove('active'));
            // 添加active类到当前点击的标签
            event.target.classList.add('active');
            
            // 这里可以添加切换标签内容的逻辑
            console.log('切换到标签:', tabName);
        };

        window.closeModal = () => {
            document.getElementById('modal-overlay').style.display = 'none';
            document.body.style.overflow = 'auto';
        };

        window.openOutputFolder = () => {
            // 这里可以添加打开输出文件夹的逻辑
            console.log('打开输出文件夹');
            closeModal();
        };

        // --- 详细信息面板控制功能 ---
        
        // 切换详细信息面板显示/隐藏
        window.toggleDetailsPanel = () => {
            const detailsPanel = document.getElementById('details-panel');
            const dataInteractionArea = document.querySelector('.data-interaction-area');
            const toggleButton = document.getElementById('details-toggle');
            
            if (!detailsPanel || !dataInteractionArea || !toggleButton) {
                console.warn('Required elements not found for toggle function');
                return;
            }
            
            if (detailsPanel.classList.contains('hidden')) {
                // 显示详细信息面板
                detailsPanel.classList.remove('hidden');
                dataInteractionArea.classList.remove('details-hidden');
                toggleButton.classList.remove('panel-hidden');
                toggleButton.innerHTML = '▶';
                toggleButton.title = '隐藏详细信息';
                
                // 恢复面板大小
                detailsPanel.style.display = 'flex';
                detailsPanel.style.flex = '2';
                detailsPanel.style.width = 'auto';
                detailsPanel.style.minWidth = '300px';
                detailsPanel.style.maxWidth = '500px';
                
                console.log('详细信息面板已显示');
            } else {
                // 隐藏详细信息面板
                detailsPanel.classList.add('hidden');
                dataInteractionArea.classList.add('details-hidden');
                toggleButton.classList.add('panel-hidden');
                toggleButton.innerHTML = '◀';
                toggleButton.title = '显示详细信息';
                
                console.log('详细信息面板已隐藏');
            }
        };
        
        // 初始化拖拽调整大小功能
        function initDetailsResizer() {
            const resizer = document.getElementById('details-resizer');
            const detailsPanel = document.getElementById('details-panel');
            const dataListPanel = document.querySelector('.data-list-panel');
            const dataInteractionArea = document.querySelector('.data-interaction-area');
            
            if (!resizer || !detailsPanel || !dataListPanel) {
                console.warn('Resizer elements not found');
                return;
            }
            
            let isResizing = false;
            let startX = 0;
            let containerWidth = 0;
            let startDetailsWidth = 0;
            
            resizer.addEventListener('mousedown', (e) => {
                isResizing = true;
                startX = e.clientX;
                containerWidth = dataInteractionArea.offsetWidth;
                startDetailsWidth = detailsPanel.offsetWidth;
                
                resizer.classList.add('dragging');
                document.body.style.cursor = 'col-resize';
                document.body.style.userSelect = 'none'; // 防止文本选择
                
                e.preventDefault();
                e.stopPropagation();
            });
            
            document.addEventListener('mousemove', (e) => {
                if (!isResizing) return;
                
                const deltaX = startX - e.clientX; // 往左拖动为正值
                const minDetailsWidth = 250;
                const maxDetailsWidth = Math.min(600, containerWidth * 0.6); // 最大不超过容器宽度的60%
                const minListWidth = 400;
                
                let newDetailsWidth = startDetailsWidth + deltaX;
                newDetailsWidth = Math.max(minDetailsWidth, Math.min(maxDetailsWidth, newDetailsWidth));
                
                const newListWidth = containerWidth - newDetailsWidth - 20; // 减去gap
                
                if (newListWidth >= minListWidth) {
                    // 直接设置像素宽度，不使用flex
                    detailsPanel.style.flex = 'none';
                    detailsPanel.style.width = newDetailsWidth + 'px';
                    detailsPanel.style.minWidth = newDetailsWidth + 'px';
                    detailsPanel.style.maxWidth = newDetailsWidth + 'px';
                    
                    dataListPanel.style.flex = '1';
                    dataListPanel.style.width = 'auto';
                    dataListPanel.style.minWidth = '400px';
                    dataListPanel.style.maxWidth = 'none';
                }
                
                e.preventDefault();
            });
            
            document.addEventListener('mouseup', (e) => {
                if (!isResizing) return;
                
                isResizing = false;
                resizer.classList.remove('dragging');
                document.body.style.cursor = 'default';
                document.body.style.userSelect = 'auto';
                
                // 不要重新设置flex，保持当前的像素宽度
                console.log('拖拽完成，详细信息面板宽度:', detailsPanel.offsetWidth + 'px');
                
                e.preventDefault();
            });
            
            // 防止在调整大小时选择文本
            resizer.addEventListener('selectstart', (e) => {
                e.preventDefault();
            });
            
            // 双击重置为默认比例
            resizer.addEventListener('dblclick', () => {
                // 重置为flex布局
                detailsPanel.style.flex = '2';
                detailsPanel.style.width = 'auto';
                detailsPanel.style.minWidth = '300px';
                detailsPanel.style.maxWidth = '500px';
                
                dataListPanel.style.flex = '3';
                dataListPanel.style.width = 'auto';
                dataListPanel.style.minWidth = '400px';
                dataListPanel.style.maxWidth = 'none';
                
                console.log('已重置为默认比例');
            });
        }

        // --- INITIALIZATION ---
        function init() {
            appState.allData = generateMockData();
            appState.displayedData = [...appState.allData];
            renderTreeView();
            renderTable();
            renderDetailsPanel();
            document.getElementById('status-stats').textContent = `共 ${appState.allData.length} 条记录`;
            
            // 初始化查询输入框状态
            updateClearIconVisibility();
            
            // 初始化查询输入框的事件监听（备用方案，防止内联事件不工作）
            const queryInput = document.getElementById('query-input');
            if (queryInput) {
                queryInput.addEventListener('input', handleQueryInput);
                queryInput.addEventListener('keydown', handleQueryKeydown);
            }

            // 初始化拖拽调整大小功能
            setTimeout(() => {
                initDetailsResizer();
                console.log('详细信息面板控制功能已初始化');
                
                // 验证功能
                const resizer = document.getElementById('details-resizer');
                const toggleButton = document.getElementById('details-toggle');
                
                if (resizer) {
                    console.log('✓ 拖拽条已找到，位置:', resizer.getBoundingClientRect());
                    resizer.style.background = '#e0e0e0'; // 确保拖拽条可见
                } else {
                    console.warn('✗ 拖拽条未找到');
                }
                
                if (toggleButton) {
                    console.log('✓ 切换按钮已找到，当前内容:', toggleButton.textContent);
                    console.log('✓ 切换按钮位置:', toggleButton.getBoundingClientRect());
                } else {
                    console.warn('✗ 切换按钮未找到');
                }
            }, 100); // 延迟一小段时间确保DOM完全渲染
        }

        init();
    });
    </script>
</body>
</html> 