# 业务流程设计

## 1. 核心业务流程概览

### 1.1 主流程图

```mermaid
flowchart TD
    A[系统启动] --> B[用户界面初始化]
    B --> C[加载系统配置]
    C --> D[文件选择和验证]
    D --> E[数据预处理]
    E --> F[人员信息匹配]
    F --> G[异动规则应用]
    G --> H[工资计算处理]
    H --> I[结果验证和确认]
    I --> J[报表文档生成]
    J --> K[数据保存和备份]
    K --> L[处理完成]
    
    %% 异常处理分支
    D --> D1[文件格式异常]
    E --> E1[数据验证异常]
    F --> F1[匹配失败异常]
    G --> G1[规则配置异常]
    H --> H1[计算错误异常]
    I --> I1[结果异常]
    
    D1 --> M[错误处理和提示]
    E1 --> M
    F1 --> M
    G1 --> M
    H1 --> M
    I1 --> M
    
    M --> N{用户选择}
    N -->|重试| D
    N -->|修正| O[手动修正]
    N -->|跳过| P[记录异常继续]
    N -->|退出| Q[终止处理]
    
    O --> F
    P --> G
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style M fill:#ffcdd2
    style Q fill:#ffecb3
```

### 1.2 业务流程分阶段说明

#### 1.2.1 数据准备阶段
- **输入数据收集**: 工资表、异动人员表
- **数据格式验证**: 文件格式、数据结构检查
- **数据完整性检查**: 必填字段、数据类型验证
- **数据备份**: 原始数据安全备份

#### 1.2.2 人员匹配阶段
- **人员索引建立**: 工号、身份证、姓名索引
- **自动匹配执行**: 多字段匹配算法
- **匹配结果验证**: 置信度评估
- **手动确认处理**: 低置信度匹配人工确认

#### 1.2.3 异动计算阶段
- **异动规则解析**: 规则配置加载
- **计算逻辑执行**: 异动类型对应计算
- **结果验证检查**: 计算结果合理性验证
- **异常处理机制**: 计算错误处理和回滚

#### 1.2.4 结果生成阶段
- **报表数据整理**: 异动汇总统计
- **模板文档填充**: 审批表、请示文档生成
- **输出文件生成**: Excel、Word文件导出
- **处理记录保存**: 操作历史和审计日志

## 2. 详细业务流程设计

### 2.1 文件处理流程

```mermaid
flowchart TD
    A[用户选择文件] --> B[文件存在性检查]
    B --> C{文件存在?}
    C -->|否| D[提示文件不存在]
    D --> A
    
    C -->|是| E[文件格式检查]
    E --> F{格式正确?}
    F -->|否| G[提示格式错误]
    G --> A
    
    F -->|是| H[文件读取权限检查]
    H --> I{有读取权限?}
    I -->|否| J[提示权限不足]
    J --> A
    
    I -->|是| K[读取文件内容]
    K --> L[解析Excel结构]
    L --> M{结构完整?}
    M -->|否| N[提示结构错误]
    N --> A
    
    M -->|是| O[提取工作表数据]
    O --> P[数据类型验证]
    P --> Q{数据有效?}
    Q -->|否| R[显示数据错误详情]
    R --> S[用户选择处理方式]
    S --> T{继续处理?}
    T -->|否| A
    T -->|是| U[标记问题数据]
    
    Q -->|是| V[数据预览显示]
    U --> V
    V --> W[用户确认数据]
    W --> X[数据处理就绪]
    
    style A fill:#e3f2fd
    style X fill:#e8f5e8
    style D,G,J,N,R fill:#ffecb3
```

#### 2.1.1 文件验证规则

**工资表文件验证:**
```python
SALARY_FILE_VALIDATION = {
    "file_extensions": [".xls", ".xlsx"],
    "required_sheets": ["全部在职人员工资表"],
    "required_columns": [
        "工号", "姓名", "部门名称", "2025年岗位工资", 
        "2025年薪级工资", "应发工资"
    ],
    "data_types": {
        "工号": "string",
        "姓名": "string", 
        "2025年岗位工资": "float",
        "应发工资": "float"
    },
    "validation_rules": {
        "工号": "^[0-9]{8}$",  # 8位数字
        "姓名": "^[\u4e00-\u9fa5]{2,10}$",  # 2-10个中文字符
        "应发工资": ">=0"  # 非负数
    }
}
```

**异动人员文件验证:**
```python
CHANGE_FILE_VALIDATION = {
    "file_extensions": [".xlsx"],
    "expected_sheets": [
        "起薪（恢复薪资待遇）", "转正定级", "停薪-退休", 
        "停薪-调离", "其他-考核扣款", "请假扣款", "脱产读博"
    ],
    "common_columns": ["工作证号", "姓名", "证件号码"],
    "validation_rules": {
        "工作证号": "^[0-9]{8}$",
        "证件号码": "^[0-9]{17}[0-9Xx]$|^[0-9]{15}$"
    }
}
```

### 2.2 人员匹配流程

```mermaid
flowchart TD
    A[开始人员匹配] --> B[加载工资表人员数据]
    B --> C[建立人员索引]
    C --> D[加载异动人员数据]
    D --> E[初始化匹配结果列表]
    
    E --> F[遍历异动人员]
    F --> G[获取当前异动人员信息]
    G --> H[工号精确匹配]
    H --> I{匹配成功?}
    
    I -->|是| J[记录精确匹配结果]
    J --> K[置信度=100%]
    K --> L[下一个人员]
    
    I -->|否| M[身份证号匹配]
    M --> N{匹配成功?}
    
    N -->|是| O[记录身份证匹配结果]
    O --> P[置信度=95%]
    P --> Q{置信度>=阈值?}
    Q -->|是| L
    Q -->|否| R[加入待确认列表]
    
    N -->|否| S[姓名模糊匹配]
    S --> T[计算姓名相似度]
    T --> U{相似度>=阈值?}
    
    U -->|是| V[记录模糊匹配结果]
    V --> W[置信度=相似度分数]
    W --> X{置信度>=阈值?}
    X -->|是| L
    X -->|否| R
    
    U -->|否| Y[标记为未匹配]
    Y --> R
    
    R --> Z[下一个人员]
    Z --> AA{还有人员?}
    AA -->|是| G
    AA -->|否| BB[处理待确认列表]
    
    L --> AA
    
    BB --> CC{有待确认项?}
    CC -->|是| DD[显示匹配确认界面]
    CC -->|否| EE[匹配完成]
    
    DD --> FF[用户手动确认]
    FF --> GG[更新匹配结果]
    GG --> EE
    
    style A fill:#e3f2fd
    style EE fill:#e8f5e8
    style DD,FF fill:#fff3e0
    style Y fill:#ffcdd2
```

#### 2.2.1 匹配算法详细设计

**多字段匹配策略:**
```python
class PersonMatchingAlgorithm:
    def __init__(self):
        self.confidence_threshold = 0.85
        self.fuzzy_threshold = 0.80
        
    def match_person(self, change_person, salary_persons):
        """执行人员匹配的核心算法"""
        
        # 1. 工号精确匹配 (优先级最高)
        if change_person.employee_id:
            exact_match = self.exact_match_by_id(
                change_person.employee_id, salary_persons)
            if exact_match:
                return MatchResult(
                    matched_person=exact_match,
                    confidence=1.0,
                    method="employee_id_exact"
                )
        
        # 2. 身份证号匹配
        if change_person.id_card:
            id_match = self.exact_match_by_id_card(
                change_person.id_card, salary_persons)
            if id_match:
                return MatchResult(
                    matched_person=id_match,
                    confidence=0.95,
                    method="id_card_exact"
                )
        
        # 3. 姓名模糊匹配
        if change_person.name:
            fuzzy_matches = self.fuzzy_match_by_name(
                change_person.name, salary_persons)
            if fuzzy_matches:
                best_match = max(fuzzy_matches, key=lambda x: x.confidence)
                if best_match.confidence >= self.fuzzy_threshold:
                    return best_match
        
        # 4. 未匹配
        return MatchResult(
            matched_person=None,
            confidence=0.0,
            method="no_match"
        )
    
    def fuzzy_match_by_name(self, name, salary_persons):
        """姓名模糊匹配实现"""
        from difflib import SequenceMatcher
        
        matches = []
        for person in salary_persons:
            similarity = SequenceMatcher(None, name, person.name).ratio()
            if similarity >= self.fuzzy_threshold:
                matches.append(MatchResult(
                    matched_person=person,
                    confidence=similarity,
                    method="name_fuzzy"
                ))
        
        return sorted(matches, key=lambda x: x.confidence, reverse=True)
```

### 2.3 异动计算流程

```mermaid
flowchart TD
    A[开始异动计算] --> B[加载异动规则配置]
    B --> C[初始化计算引擎]
    C --> D[遍历匹配成功的人员]
    
    D --> E[获取人员工资数据]
    E --> F[获取人员异动信息]
    F --> G[确定异动类型]
    G --> H[查找对应规则]
    
    H --> I{规则存在?}
    I -->|否| J[使用默认规则]
    I -->|是| K[加载规则配置]
    
    J --> K
    K --> L[解析规则参数]
    L --> M[执行计算前验证]
    M --> N{验证通过?}
    
    N -->|否| O[记录验证错误]
    O --> P[跳过当前人员]
    
    N -->|是| Q[创建工资副本]
    Q --> R[应用异动规则]
    R --> S[执行具体计算]
    S --> T[计算后验证]
    
    T --> U{结果合理?}
    U -->|否| V[回滚计算结果]
    V --> W[记录计算错误]
    W --> P
    
    U -->|是| X[记录变动详情]
    X --> Y[保存计算结果]
    Y --> Z[更新处理状态]
    
    Z --> AA[下一个人员]
    P --> AA
    AA --> BB{还有人员?}
    BB -->|是| E
    BB -->|否| CC[生成计算摘要]
    
    CC --> DD[计算完成]
    
    style A fill:#e3f2fd
    style DD fill:#e8f5e8
    style O,W fill:#ffcdd2
    style V fill:#fff3e0
```

#### 2.3.1 异动计算规则引擎

**规则执行器设计:**
```python
class SalaryChangeRuleEngine:
    def __init__(self, rule_config):
        self.rules = self.load_rules(rule_config)
        self.calculators = {
            "clear": ClearCalculator(),
            "replace": ReplaceCalculator(), 
            "subtract": SubtractCalculator(),
            "add": AddCalculator(),
            "calculate": FormulaCalculator()
        }
    
    def apply_change(self, employee_salary, change_data):
        """应用异动规则"""
        change_type = change_data['change_type']
        rule = self.get_rule(change_type)
        
        if not rule:
            raise RuleNotFoundError(f"未找到异动类型 {change_type} 的规则")
        
        # 创建计算上下文
        context = CalculationContext(
            original_salary=employee_salary.copy(),
            change_data=change_data,
            rule=rule
        )
        
        # 执行计算前验证
        self.validate_before_calculation(context)
        
        # 选择计算器并执行
        calculator = self.calculators[rule.operation_type]
        result = calculator.calculate(context)
        
        # 执行计算后验证
        self.validate_after_calculation(context, result)
        
        return result

# 具体计算器实现
class ClearCalculator:
    """清零计算器 - 用于离职、调离等"""
    def calculate(self, context):
        result = context.original_salary.copy()
        affected_fields = context.rule.affected_fields
        
        for field in affected_fields:
            if field in result:
                result[field] = 0.0
        
        return result

class SubtractCalculator:
    """扣减计算器 - 用于考核扣款、请假扣款等"""
    def calculate(self, context):
        result = context.original_salary.copy()
        change_amount = context.change_data.get('change_amount', 0)
        
        if context.rule.operation_type == 'fixed_amount':
            # 固定金额扣减
            result['gross_salary'] -= change_amount
        elif context.rule.operation_type == 'proportional':
            # 按比例扣减
            ratio = context.change_data.get('deduction_ratio', 0)
            for field in context.rule.affected_fields:
                if field in result:
                    result[field] *= (1 - ratio)
        
        return result
```

### 2.4 报表生成流程

```mermaid
flowchart TD
    A[开始报表生成] --> B[收集处理结果数据]
    B --> C[生成异动汇总统计]
    C --> D[创建异动明细列表]
    D --> E[准备报表数据结构]
    
    E --> F[生成异动汇总表]
    F --> G[加载审批表模板]
    G --> H[填充审批表数据]
    H --> I[生成工资审批表]
    
    I --> J[加载请示文档模板]
    J --> K[填充请示文档数据]
    K --> L[生成请示文档]
    
    L --> M[生成处理日志报告]
    M --> N[创建输出目录]
    N --> O[保存所有文件]
    
    O --> P{保存成功?}
    P -->|否| Q[显示保存错误]
    Q --> R[用户选择重试或修改路径]
    R --> N
    
    P -->|是| S[更新文件清单]
    S --> T[生成完成通知]
    T --> U[报表生成完成]
    
    style A fill:#e3f2fd
    style U fill:#e8f5e8
    style Q fill:#ffcdd2
```

#### 2.4.1 报表数据结构设计

**异动汇总表数据结构:**
```python
@dataclass
class ChangeSummaryData:
    """异动汇总数据结构"""
    change_month: str           # 异动月份
    total_employees: int        # 总处理人数
    success_count: int          # 成功处理数
    error_count: int            # 异常记录数
    total_amount_change: float  # 总金额变动
    increase_amount: float      # 增加总额
    decrease_amount: float      # 减少总额
    
    change_type_summary: List[ChangeTypeSummary]  # 按类型汇总
    department_summary: List[DepartmentSummary]   # 按部门汇总
    detail_records: List[ChangeDetailRecord]      # 详细记录

@dataclass
class ChangeTypeSummary:
    """异动类型汇总"""
    change_type: str           # 异动类型
    employee_count: int        # 人员数量
    total_amount: float        # 变动总额
    average_amount: float      # 平均变动
    percentage: float          # 占比

@dataclass 
class ChangeDetailRecord:
    """异动详细记录"""
    employee_id: str           # 工号
    employee_name: str         # 姓名
    department: str            # 部门
    change_type: str           # 异动类型
    original_salary: float     # 原工资
    new_salary: float          # 新工资
    change_amount: float       # 变动金额
    effective_date: str        # 生效日期
    remarks: str              # 备注
```

### 2.5 异常处理流程

```mermaid
flowchart TD
    A[检测到异常] --> B[异常类型识别]
    B --> C{异常级别}
    
    C -->|严重| D[立即停止处理]
    C -->|警告| E[记录警告继续]
    C -->|一般| F[用户选择处理方式]
    
    D --> G[保存当前状态]
    G --> H[显示错误详情]
    H --> I[用户选择恢复方式]
    
    I --> J{选择恢复?}
    J -->|是| K[尝试错误恢复]
    J -->|否| L[终止处理]
    
    K --> M{恢复成功?}
    M -->|是| N[从断点继续]
    M -->|否| L
    
    E --> O[记录到日志]
    O --> P[继续后续处理]
    
    F --> Q{用户选择}
    Q -->|跳过| R[跳过当前项目]
    Q -->|重试| S[重新执行]
    Q -->|修正| T[手动修正数据]
    Q -->|停止| L
    
    R --> P
    S --> U[重新执行操作]
    T --> V[显示修正界面]
    V --> W[用户修正数据]
    W --> U
    
    U --> X{执行成功?}
    X -->|是| P
    X -->|否| Y[增加重试计数]
    Y --> Z{重试次数超限?}
    Z -->|是| L
    Z -->|否| F
    
    P --> AA[处理完成]
    L --> BB[生成异常报告]
    N --> AA
    
    style A fill:#ffcdd2
    style AA fill:#e8f5e8
    style L,BB fill:#f8d7da
    style V,W fill:#fff3e0
```

#### 2.5.1 异常处理策略

**异常分类和处理策略:**
```python
class ExceptionHandler:
    """异常处理器"""
    
    EXCEPTION_LEVELS = {
        "CRITICAL": {
            "description": "严重错误，必须停止处理",
            "action": "stop_immediately",
            "examples": ["数据库连接失败", "磁盘空间不足", "内存溢出"]
        },
        "ERROR": {
            "description": "处理错误，需要用户干预",
            "action": "user_decision",
            "examples": ["计算结果异常", "文件读写失败", "数据格式错误"]
        },
        "WARNING": {
            "description": "警告信息，可以继续处理",
            "action": "log_and_continue", 
            "examples": ["匹配置信度低", "数据缺失", "格式不规范"]
        },
        "INFO": {
            "description": "信息提示，正常处理",
            "action": "log_only",
            "examples": ["处理进度更新", "状态变更", "操作完成"]
        }
    }
    
    def handle_exception(self, exception, context=None):
        """处理异常的统一入口"""
        
        # 1. 异常分类
        level = self.classify_exception(exception)
        
        # 2. 记录异常日志
        self.log_exception(exception, level, context)
        
        # 3. 根据级别执行对应策略
        if level == "CRITICAL":
            return self.handle_critical_exception(exception, context)
        elif level == "ERROR":
            return self.handle_error_exception(exception, context)
        elif level == "WARNING":
            return self.handle_warning_exception(exception, context)
        else:
            return self.handle_info_exception(exception, context)
    
    def handle_critical_exception(self, exception, context):
        """处理严重异常"""
        # 1. 立即保存当前状态
        self.save_current_state(context)
        
        # 2. 停止所有处理
        self.stop_all_processing()
        
        # 3. 显示错误信息给用户
        error_dialog = CriticalErrorDialog(exception)
        user_choice = error_dialog.show()
        
        if user_choice == "retry_with_fix":
            return self.attempt_error_recovery(exception, context)
        else:
            return ProcessingResult(status="terminated", reason=str(exception))
```

## 3. 数据流设计

### 3.1 系统数据流图

```mermaid
flowchart LR
    A[工资表Excel] --> B[数据读取器]
    C[异动人员Excel] --> B
    
    B --> D[数据验证器]
    D --> E[数据清洗器]
    E --> F[人员匹配器]
    
    F --> G[异动计算引擎]
    G --> H[结果验证器]
    H --> I[数据存储器]
    
    I --> J[报表生成器]
    I --> K[模板填充器]
    
    J --> L[异动汇总表]
    K --> M[工资审批表]
    K --> N[请示文档]
    
    I --> O[SQLite数据库]
    I --> P[处理日志]
    
    subgraph "数据处理流水线"
        B --> D --> E --> F --> G --> H --> I
    end
    
    subgraph "输出生成器"
        J
        K
    end
    
    subgraph "数据存储"
        O
        P
    end
```

### 3.2 数据转换流程

#### 3.2.1 Excel到内部数据结构

```python
class DataTransformer:
    """数据转换器"""
    
    def transform_salary_data(self, excel_data):
        """转换工资表数据"""
        salary_records = []
        
        for row in excel_data.iterrows():
            record = SalaryRecord(
                employee_id=str(row['工号']).zfill(8),
                name=str(row['姓名']).strip(),
                department=str(row['部门名称']).strip(),
                basic_salary=float(row['2025年岗位工资'] or 0),
                grade_salary=float(row['2025年薪级工资'] or 0),
                allowance=float(row['津贴'] or 0),
                performance_basic=float(row['2025年基础性绩效'] or 0),
                performance_reward=float(row['2025年奖励性绩效预发'] or 0),
                gross_salary=float(row['应发工资'] or 0),
                # ... 其他字段
            )
            salary_records.append(record)
        
        return salary_records
    
    def transform_change_data(self, excel_sheets):
        """转换异动人员数据"""
        change_records = []
        
        for sheet_name, sheet_data in excel_sheets.items():
            change_type = self.parse_change_type(sheet_name)
            
            for row in sheet_data.iterrows():
                record = ChangeRecord(
                    employee_id=str(row.get('工作证号', '')).zfill(8),
                    name=str(row['姓名']).strip(),
                    id_card=str(row.get('证件号码', '')).strip(),
                    change_type=change_type,
                    effective_date=self.parse_date(row.get('生效日期')),
                    change_amount=float(row.get('变动金额', 0)),
                    # ... 其他字段
                )
                change_records.append(record)
        
        return change_records
```

## 4. 性能和可靠性设计

### 4.1 性能优化策略

#### 4.1.1 分批处理机制
```python
class BatchProcessor:
    """分批处理器"""
    
    def __init__(self, batch_size=100):
        self.batch_size = batch_size
    
    def process_in_batches(self, data_list, processor_func):
        """分批处理数据"""
        total_count = len(data_list)
        results = []
        
        for i in range(0, total_count, self.batch_size):
            batch = data_list[i:i + self.batch_size]
            batch_results = processor_func(batch)
            results.extend(batch_results)
            
            # 更新进度
            progress = min((i + self.batch_size) / total_count * 100, 100)
            self.notify_progress(progress, f"已处理 {min(i + self.batch_size, total_count)}/{total_count} 条记录")
        
        return results
```

#### 4.1.2 内存管理优化
```python
class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_mb=500):
        self.max_memory_mb = max_memory_mb
        
    def monitor_memory_usage(self):
        """监控内存使用"""
        import psutil
        process = psutil.Process()
        memory_usage = process.memory_info().rss / 1024 / 1024  # MB
        
        if memory_usage > self.max_memory_mb:
            self.trigger_memory_cleanup()
            
    def trigger_memory_cleanup(self):
        """触发内存清理"""
        import gc
        gc.collect()
        
        # 清理大数据对象缓存
        self.clear_data_cache()
```

### 4.2 可靠性保障

#### 4.2.1 数据一致性检查
```python
class DataConsistencyChecker:
    """数据一致性检查器"""
    
    def check_salary_data_consistency(self, salary_data):
        """检查工资数据一致性"""
        errors = []
        
        for record in salary_data:
            # 检查工资计算是否正确
            calculated_gross = (record.basic_salary + record.grade_salary + 
                              record.allowance + record.performance_basic + 
                              record.performance_reward)
            
            if abs(calculated_gross - record.gross_salary) > 0.01:
                errors.append(f"工号 {record.employee_id} 工资计算不一致")
        
        return errors
    
    def check_change_calculation_consistency(self, original, modified, change_amount):
        """检查异动计算一致性"""
        expected_change = modified.gross_salary - original.gross_salary
        
        if abs(expected_change - change_amount) > 0.01:
            return False, f"预期变动 {expected_change} 与记录变动 {change_amount} 不一致"
        
        return True, "计算一致"
```

#### 4.2.2 断点续传机制
```python
class CheckpointManager:
    """断点管理器"""
    
    def save_checkpoint(self, stage, processed_data, current_index):
        """保存处理检查点"""
        checkpoint = {
            "timestamp": datetime.now().isoformat(),
            "stage": stage,
            "current_index": current_index,
            "processed_count": len(processed_data),
            "data_hash": self.calculate_data_hash(processed_data)
        }
        
        with open("checkpoint.json", "w", encoding="utf-8") as f:
            json.dump(checkpoint, f, ensure_ascii=False, indent=2)
    
    def load_checkpoint(self):
        """加载处理检查点"""
        try:
            with open("checkpoint.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return None
    
    def resume_from_checkpoint(self, checkpoint):
        """从检查点恢复处理"""
        stage = checkpoint["stage"]
        current_index = checkpoint["current_index"]
        
        # 根据阶段恢复相应的处理状态
        if stage == "matching":
            return self.resume_matching_process(current_index)
        elif stage == "calculation":
            return self.resume_calculation_process(current_index)
        # ... 其他阶段
```

这个业务流程设计提供了完整的工作流程规范，确保系统能够稳定、高效地完成工资异动处理任务。 