# 数据库设计

## 1. 数据库概览

### 1.1 设计原则
- **轻量级部署**: 使用SQLite，单文件存储，易于部署和备份
- **数据完整性**: 支持外键约束，确保数据一致性
- **性能优化**: 合理设计索引，提升查询效率
- **扩展友好**: 预留扩展字段，支持业务发展

### 1.2 数据库选型对比

| 特性 | SQLite | MySQL | PostgreSQL | SQL Server |
|------|--------|-------|------------|------------|
| 部署复杂度 | ★★★★★ | ★★ | ★★ | ★ |
| 单机性能 | ★★★★ | ★★★ | ★★★ | ★★★★ |
| 维护成本 | ★★★★★ | ★★ | ★★ | ★ |
| 功能完整性 | ★★★ | ★★★★ | ★★★★★ | ★★★★★ |
| 适用场景 | 单机桌面应用 | 中小型Web应用 | 大型应用 | 企业级应用 |

## 2. 数据模型设计

### 2.1 核心实体关系图 (ERD)

```mermaid
erDiagram
    EMPLOYEES ||--o{ SALARY_DATA : has
    EMPLOYEES ||--o{ SALARY_CHANGES : has
    SALARY_CHANGES }o--|| CHANGE_RULES : follows
    PROCESSING_HISTORY ||--o{ SALARY_CHANGES : includes
    
    EMPLOYEES {
        int id PK
        varchar employee_id UK "工号"
        varchar id_card UK "身份证号"
        varchar name "姓名"
        varchar department "部门"
        varchar position "职位"
        varchar employee_type "人员类别"
        varchar status "状态"
        datetime created_time
        datetime updated_time
    }
    
    SALARY_DATA {
        int id PK
        varchar employee_id FK "工号"
        varchar salary_month "工资月份"
        decimal basic_salary "岗位工资"
        decimal grade_salary "薪级工资"
        decimal allowance "津贴"
        decimal performance_basic "基础性绩效"
        decimal performance_reward "奖励性绩效"
        decimal gross_salary "应发工资"
        decimal net_salary "实发工资"
        datetime created_time
    }
    
    SALARY_CHANGES {
        int id PK
        varchar employee_id FK "工号"
        varchar change_month "异动月份"
        varchar change_type "异动类型"
        text change_reason "异动原因"
        date effective_date "生效日期"
        text affected_fields "影响字段JSON"
        text original_values "原始值JSON"
        text new_values "新值JSON"
        decimal change_amount "变动金额"
        varchar operator "操作人"
        varchar approval_status "审批状态"
        datetime created_time
    }
    
    CHANGE_RULES {
        int id PK
        varchar rule_name UK "规则名称"
        varchar change_type "异动类型"
        varchar operation_type "操作类型"
        text affected_fields "影响字段JSON"
        text calculation_formula "计算公式"
        text rule_config "规则配置JSON"
        boolean is_active "是否启用"
        int priority "优先级"
        datetime created_time
    }
    
    PROCESSING_HISTORY {
        int id PK
        varchar batch_id UK "批次ID"
        varchar process_month "处理月份"
        varchar process_type "处理类型"
        text input_files "输入文件JSON"
        text output_files "输出文件JSON"
        int total_employees "处理员工数"
        int success_count "成功数量"
        int error_count "错误数量"
        decimal total_amount "总金额变动"
        varchar process_status "处理状态"
        datetime start_time
        datetime end_time
    }
    
    SYSTEM_CONFIG {
        int id PK
        varchar config_key UK "配置键"
        text config_value "配置值"
        varchar config_type "配置类型"
        text description "描述"
        datetime created_time
        datetime updated_time
    }
```

## 3. 详细表结构设计

### 3.1 员工基础信息表 (employees)

```sql
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(20) UNIQUE NOT NULL COMMENT '工号',
    id_card VARCHAR(18) UNIQUE COMMENT '身份证号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    employee_type VARCHAR(50) COMMENT '人员类别',
    hire_date DATE COMMENT '入职日期',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active/inactive/resigned',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    education VARCHAR(50) COMMENT '学历',
    graduation_school VARCHAR(200) COMMENT '毕业学校',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束定义
    CONSTRAINT chk_status CHECK (status IN ('active', 'inactive', 'resigned')),
    CONSTRAINT chk_employee_id CHECK (LENGTH(employee_id) = 8 AND employee_id GLOB '[0-9]*')
);

-- 索引创建
CREATE INDEX idx_employees_id ON employees(employee_id);
CREATE INDEX idx_employees_idcard ON employees(id_card);
CREATE INDEX idx_employees_name ON employees(name);
CREATE INDEX idx_employees_dept ON employees(department);
CREATE INDEX idx_employees_status ON employees(status);
```

**字段说明：**
- `employee_id`: 8位数字工号，系统主要匹配字段
- `id_card`: 18位身份证号，重要匹配字段
- `status`: 员工状态，用于过滤有效员工
- `employee_type`: 人员类别（在编在岗、编外A岗、编外B岗等）

### 3.2 工资数据表 (salary_data)

```sql
CREATE TABLE salary_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(20) NOT NULL COMMENT '工号',
    salary_month VARCHAR(7) NOT NULL COMMENT '工资月份（YYYY-MM）',
    
    -- 基本工资项目
    basic_salary DECIMAL(10,2) DEFAULT 0 COMMENT '岗位工资',
    grade_salary DECIMAL(10,2) DEFAULT 0 COMMENT '薪级工资',
    allowance DECIMAL(10,2) DEFAULT 0 COMMENT '津贴',
    remaining_allowance DECIMAL(10,2) DEFAULT 0 COMMENT '结余津贴',
    
    -- 绩效工资项目
    performance_basic DECIMAL(10,2) DEFAULT 0 COMMENT '基础性绩效',
    performance_reward DECIMAL(10,2) DEFAULT 0 COMMENT '奖励性绩效预发',
    
    -- 补贴项目
    subsidy_transport DECIMAL(10,2) DEFAULT 0 COMMENT '交通补贴',
    subsidy_housing DECIMAL(10,2) DEFAULT 0 COMMENT '住房补贴',
    subsidy_property DECIMAL(10,2) DEFAULT 0 COMMENT '物业补贴',
    subsidy_communication DECIMAL(10,2) DEFAULT 0 COMMENT '通讯补贴',
    subsidy_car DECIMAL(10,2) DEFAULT 0 COMMENT '车补',
    subsidy_health DECIMAL(10,2) DEFAULT 0 COMMENT '卫生费',
    
    -- 其他项目
    bonus DECIMAL(10,2) DEFAULT 0 COMMENT '补发',
    advance DECIMAL(10,2) DEFAULT 0 COMMENT '借支',
    
    -- 汇总项目
    gross_salary DECIMAL(10,2) DEFAULT 0 COMMENT '应发工资',
    
    -- 扣除项目
    fund_housing DECIMAL(10,2) DEFAULT 0 COMMENT '公积金',
    insurance_pension DECIMAL(10,2) DEFAULT 0 COMMENT '养老保险',
    insurance_medical DECIMAL(10,2) DEFAULT 0 COMMENT '医疗保险',
    insurance_unemployment DECIMAL(10,2) DEFAULT 0 COMMENT '失业保险',
    
    -- 实发工资
    net_salary DECIMAL(10,2) DEFAULT 0 COMMENT '实发工资',
    
    -- 元数据
    data_source VARCHAR(100) COMMENT '数据来源',
    import_batch_id VARCHAR(50) COMMENT '导入批次ID',
    is_calculated BOOLEAN DEFAULT FALSE COMMENT '是否已计算',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束定义
    FOREIGN KEY (employee_id) REFERENCES employees(employee_id),
    UNIQUE(employee_id, salary_month),
    CONSTRAINT chk_salary_month CHECK (salary_month GLOB '[0-9][0-9][0-9][0-9]-[0-1][0-9]'),
    CONSTRAINT chk_gross_positive CHECK (gross_salary >= 0)
);

-- 索引创建
CREATE INDEX idx_salary_employee_month ON salary_data(employee_id, salary_month);
CREATE INDEX idx_salary_month ON salary_data(salary_month);
CREATE INDEX idx_salary_batch ON salary_data(import_batch_id);
CREATE INDEX idx_salary_calculated ON salary_data(is_calculated);
```

### 3.3 异动记录表 (salary_changes)

```sql
CREATE TABLE salary_changes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(20) NOT NULL COMMENT '工号',
    change_month VARCHAR(7) NOT NULL COMMENT '异动月份',
    change_type VARCHAR(50) NOT NULL COMMENT '异动类型',
    change_subtype VARCHAR(50) COMMENT '异动子类型',
    change_reason TEXT COMMENT '异动原因',
    effective_date DATE COMMENT '生效日期',
    end_date DATE COMMENT '结束日期（如适用）',
    
    -- 变动详情（JSON格式存储）
    affected_fields TEXT COMMENT '影响字段（JSON格式）',
    original_values TEXT COMMENT '原始值（JSON格式）',
    new_values TEXT COMMENT '新值（JSON格式）',
    calculation_details TEXT COMMENT '计算详情（JSON格式）',
    
    -- 变动金额
    change_amount DECIMAL(10,2) COMMENT '变动金额',
    percentage_change DECIMAL(5,2) COMMENT '变动百分比',
    
    -- 处理信息
    operator VARCHAR(50) COMMENT '操作人',
    approval_status VARCHAR(20) DEFAULT 'pending' COMMENT '审批状态',
    approval_time DATETIME COMMENT '审批时间',
    approver VARCHAR(50) COMMENT '审批人',
    
    -- 状态管理
    processing_status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态',
    error_message TEXT COMMENT '错误信息',
    
    -- 元数据
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_time DATETIME COMMENT '处理完成时间',
    remarks TEXT COMMENT '备注',
    
    -- 约束定义
    FOREIGN KEY (employee_id) REFERENCES employees(employee_id),
    CONSTRAINT chk_approval_status CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    CONSTRAINT chk_processing_status CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed'))
);

-- 索引创建
CREATE INDEX idx_changes_employee_month ON salary_changes(employee_id, change_month);
CREATE INDEX idx_changes_type ON salary_changes(change_type);
CREATE INDEX idx_changes_status ON salary_changes(processing_status);
CREATE INDEX idx_changes_approval ON salary_changes(approval_status);
CREATE INDEX idx_changes_effective_date ON salary_changes(effective_date);
```

### 3.4 异动规则配置表 (change_rules)

```sql
CREATE TABLE change_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name VARCHAR(100) UNIQUE NOT NULL COMMENT '规则名称',
    rule_code VARCHAR(50) UNIQUE NOT NULL COMMENT '规则代码',
    change_type VARCHAR(50) NOT NULL COMMENT '异动类型',
    change_subtype VARCHAR(50) COMMENT '异动子类型',
    
    -- 操作定义
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型：add/subtract/replace/clear/calculate',
    affected_fields TEXT NOT NULL COMMENT '影响字段（JSON格式）',
    calculation_formula TEXT COMMENT '计算公式',
    
    -- 规则配置
    rule_config TEXT COMMENT '规则配置（JSON格式）',
    condition_expression TEXT COMMENT '适用条件表达式',
    
    -- 规则元数据
    description TEXT COMMENT '规则描述',
    examples TEXT COMMENT '使用示例',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority INTEGER DEFAULT 0 COMMENT '优先级',
    
    -- 版本控制
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束定义
    CONSTRAINT chk_operation_type CHECK (operation_type IN ('add', 'subtract', 'replace', 'clear', 'calculate')),
    CONSTRAINT chk_priority CHECK (priority >= 0 AND priority <= 100)
);

-- 索引创建
CREATE INDEX idx_rules_type ON change_rules(change_type);
CREATE INDEX idx_rules_active ON change_rules(is_active);
CREATE INDEX idx_rules_priority ON change_rules(priority);
```

### 3.5 处理历史表 (processing_history)

```sql
CREATE TABLE processing_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id VARCHAR(50) UNIQUE NOT NULL COMMENT '批次ID',
    process_month VARCHAR(7) NOT NULL COMMENT '处理月份',
    process_type VARCHAR(50) NOT NULL COMMENT '处理类型',
    
    -- 输入输出文件
    input_files TEXT COMMENT '输入文件（JSON格式）',
    output_files TEXT COMMENT '输出文件（JSON格式）',
    
    -- 处理统计
    total_employees INTEGER DEFAULT 0 COMMENT '处理员工数',
    success_count INTEGER DEFAULT 0 COMMENT '成功数量',
    error_count INTEGER DEFAULT 0 COMMENT '错误数量',
    warning_count INTEGER DEFAULT 0 COMMENT '警告数量',
    
    -- 金额统计
    total_amount DECIMAL(15,2) DEFAULT 0 COMMENT '总金额变动',
    increase_amount DECIMAL(15,2) DEFAULT 0 COMMENT '增加金额',
    decrease_amount DECIMAL(15,2) DEFAULT 0 COMMENT '减少金额',
    
    -- 处理状态
    process_status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态',
    progress_percentage INTEGER DEFAULT 0 COMMENT '进度百分比',
    current_step VARCHAR(100) COMMENT '当前步骤',
    
    -- 时间记录
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_seconds INTEGER COMMENT '处理耗时（秒）',
    
    -- 操作信息
    operator VARCHAR(50) COMMENT '操作人',
    operator_ip VARCHAR(45) COMMENT '操作IP',
    
    -- 详细信息
    error_details TEXT COMMENT '错误详情（JSON格式）',
    summary_report TEXT COMMENT '处理摘要报告',
    remarks TEXT COMMENT '备注',
    
    -- 约束定义
    CONSTRAINT chk_process_status CHECK (process_status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT chk_progress CHECK (progress_percentage >= 0 AND progress_percentage <= 100)
);

-- 索引创建
CREATE INDEX idx_processing_month ON processing_history(process_month);
CREATE INDEX idx_processing_status ON processing_history(process_status);
CREATE INDEX idx_processing_time ON processing_history(start_time);
CREATE INDEX idx_processing_operator ON processing_history(operator);
```

### 3.6 系统配置表 (system_config)

```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型',
    category VARCHAR(50) DEFAULT 'general' COMMENT '配置分类',
    
    -- 配置元数据
    description TEXT COMMENT '配置描述',
    default_value TEXT COMMENT '默认值',
    valid_values TEXT COMMENT '有效值列表（JSON格式）',
    
    -- 访问控制
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    is_readonly BOOLEAN DEFAULT FALSE COMMENT '是否只读',
    requires_restart BOOLEAN DEFAULT FALSE COMMENT '是否需要重启',
    
    -- 版本控制
    version INTEGER DEFAULT 1 COMMENT '版本号',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束定义
    CONSTRAINT chk_config_type CHECK (config_type IN ('string', 'number', 'boolean', 'json', 'date', 'file_path'))
);

-- 索引创建
CREATE INDEX idx_config_key ON system_config(config_key);
CREATE INDEX idx_config_category ON system_config(category);
CREATE INDEX idx_config_system ON system_config(is_system);
```

## 4. 数据访问层设计

### 4.1 DAO接口定义

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
import sqlite3
from datetime import datetime

class BaseDAO(ABC):
    """数据访问对象基类"""
    
    def __init__(self, db_connection: sqlite3.Connection):
        self.conn = db_connection
        self.conn.row_factory = sqlite3.Row  # 返回字典形式的结果
    
    def execute_query(self, sql: str, params: tuple = ()) -> List[sqlite3.Row]:
        """执行查询操作"""
        cursor = self.conn.cursor()
        cursor.execute(sql, params)
        return cursor.fetchall()
    
    def execute_update(self, sql: str, params: tuple = ()) -> int:
        """执行更新操作"""
        cursor = self.conn.cursor()
        cursor.execute(sql, params)
        self.conn.commit()
        return cursor.rowcount
    
    def execute_insert(self, sql: str, params: tuple = ()) -> int:
        """执行插入操作，返回新记录ID"""
        cursor = self.conn.cursor()
        cursor.execute(sql, params)
        self.conn.commit()
        return cursor.lastrowid

class EmployeeDAO(BaseDAO):
    """员工数据访问对象"""
    
    def find_by_employee_id(self, employee_id: str) -> Optional[Dict]:
        """根据工号查找员工"""
        sql = "SELECT * FROM employees WHERE employee_id = ?"
        result = self.execute_query(sql, (employee_id,))
        return dict(result[0]) if result else None
    
    def find_by_id_card(self, id_card: str) -> Optional[Dict]:
        """根据身份证号查找员工"""
        sql = "SELECT * FROM employees WHERE id_card = ?"
        result = self.execute_query(sql, (id_card,))
        return dict(result[0]) if result else None
    
    def find_by_name(self, name: str, fuzzy: bool = False) -> List[Dict]:
        """根据姓名查找员工"""
        if fuzzy:
            sql = "SELECT * FROM employees WHERE name LIKE ?"
            params = (f"%{name}%",)
        else:
            sql = "SELECT * FROM employees WHERE name = ?"
            params = (name,)
        
        results = self.execute_query(sql, params)
        return [dict(row) for row in results]
    
    def batch_insert(self, employees: List[Dict]) -> int:
        """批量插入员工数据"""
        sql = """INSERT OR REPLACE INTO employees 
                 (employee_id, id_card, name, department, position, employee_type, status)
                 VALUES (?, ?, ?, ?, ?, ?, ?)"""
        
        cursor = self.conn.cursor()
        count = 0
        for emp in employees:
            params = (
                emp['employee_id'], emp.get('id_card'), emp['name'],
                emp.get('department'), emp.get('position'), 
                emp.get('employee_type'), emp.get('status', 'active')
            )
            cursor.execute(sql, params)
            count += 1
        
        self.conn.commit()
        return count
    
    def get_active_employees(self) -> List[Dict]:
        """获取所有在职员工"""
        sql = "SELECT * FROM employees WHERE status = 'active' ORDER BY employee_id"
        results = self.execute_query(sql)
        return [dict(row) for row in results]

class SalaryDAO(BaseDAO):
    """工资数据访问对象"""
    
    def get_monthly_salary(self, month: str) -> List[Dict]:
        """获取指定月份所有员工工资"""
        sql = """SELECT sd.*, e.name, e.department, e.employee_type 
                 FROM salary_data sd
                 JOIN employees e ON sd.employee_id = e.employee_id
                 WHERE sd.salary_month = ?
                 ORDER BY sd.employee_id"""
        results = self.execute_query(sql, (month,))
        return [dict(row) for row in results]
    
    def get_employee_salary(self, employee_id: str, month: str) -> Optional[Dict]:
        """获取指定员工指定月份工资"""
        sql = "SELECT * FROM salary_data WHERE employee_id = ? AND salary_month = ?"
        result = self.execute_query(sql, (employee_id, month))
        return dict(result[0]) if result else None
    
    def save_salary_data(self, salary_data: List[Dict], month: str) -> int:
        """保存工资数据"""
        sql = """INSERT OR REPLACE INTO salary_data 
                 (employee_id, salary_month, basic_salary, grade_salary, allowance,
                  performance_basic, performance_reward, subsidy_transport, 
                  subsidy_housing, gross_salary, net_salary, data_source)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
        
        cursor = self.conn.cursor()
        count = 0
        for salary in salary_data:
            params = (
                salary['employee_id'], month,
                salary.get('basic_salary', 0), salary.get('grade_salary', 0),
                salary.get('allowance', 0), salary.get('performance_basic', 0),
                salary.get('performance_reward', 0), salary.get('subsidy_transport', 0),
                salary.get('subsidy_housing', 0), salary.get('gross_salary', 0),
                salary.get('net_salary', 0), salary.get('data_source', 'import')
            )
            cursor.execute(sql, params)
            count += 1
        
        self.conn.commit()
        return count
    
    def update_salary(self, employee_id: str, month: str, updates: Dict) -> bool:
        """更新员工工资数据"""
        if not updates:
            return False
        
        fields = ', '.join([f"{key} = ?" for key in updates.keys()])
        sql = f"UPDATE salary_data SET {fields}, updated_time = CURRENT_TIMESTAMP WHERE employee_id = ? AND salary_month = ?"
        
        params = list(updates.values()) + [employee_id, month]
        rows_affected = self.execute_update(sql, params)
        return rows_affected > 0

class ChangeDAO(BaseDAO):
    """异动记录数据访问对象"""
    
    def record_change(self, change_data: Dict) -> int:
        """记录工资异动"""
        sql = """INSERT INTO salary_changes 
                 (employee_id, change_month, change_type, change_reason,
                  effective_date, affected_fields, original_values, 
                  new_values, change_amount, operator, processing_status)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
        
        params = (
            change_data['employee_id'], change_data['change_month'],
            change_data['change_type'], change_data.get('change_reason'),
            change_data.get('effective_date'), 
            change_data.get('affected_fields', '{}'),
            change_data.get('original_values', '{}'),
            change_data.get('new_values', '{}'),
            change_data.get('change_amount', 0),
            change_data.get('operator', 'system'),
            change_data.get('processing_status', 'pending')
        )
        
        return self.execute_insert(sql, params)
    
    def get_monthly_changes(self, month: str) -> List[Dict]:
        """获取月度异动汇总"""
        sql = """SELECT sc.*, e.name, e.department 
                 FROM salary_changes sc
                 JOIN employees e ON sc.employee_id = e.employee_id
                 WHERE sc.change_month = ?
                 ORDER BY sc.created_time"""
        results = self.execute_query(sql, (month,))
        return [dict(row) for row in results]
    
    def get_change_statistics(self, start_month: str, end_month: str) -> List[Dict]:
        """获取异动统计数据"""
        sql = """SELECT 
                    change_type,
                    COUNT(*) as change_count,
                    SUM(change_amount) as total_amount,
                    AVG(change_amount) as avg_amount
                 FROM salary_changes
                 WHERE change_month BETWEEN ? AND ?
                 GROUP BY change_type
                 ORDER BY total_amount DESC"""
        results = self.execute_query(sql, (start_month, end_month))
        return [dict(row) for row in results]
    
    def update_change_status(self, change_id: int, status: str, error_message: str = None) -> bool:
        """更新异动处理状态"""
        if error_message:
            sql = """UPDATE salary_changes 
                     SET processing_status = ?, error_message = ?, processed_time = CURRENT_TIMESTAMP 
                     WHERE id = ?"""
            params = (status, error_message, change_id)
        else:
            sql = """UPDATE salary_changes 
                     SET processing_status = ?, processed_time = CURRENT_TIMESTAMP 
                     WHERE id = ?"""
            params = (status, change_id)
        
        rows_affected = self.execute_update(sql, params)
        return rows_affected > 0
```

## 5. 数据初始化和配置

### 5.1 初始系统配置

```sql
-- 插入系统默认配置
INSERT INTO system_config (config_key, config_value, config_type, category, description) VALUES
('app.version', '1.0.0', 'string', 'system', '应用程序版本号'),
('app.name', '月度工资异动处理系统', 'string', 'system', '应用程序名称'),

-- 数据处理配置
('data.batch_size', '1000', 'number', 'data', '批处理大小'),
('data.backup_enabled', 'true', 'boolean', 'data', '是否启用数据备份'),
('data.backup_retention_days', '30', 'number', 'data', '备份保留天数'),

-- 文件路径配置
('path.data_dir', 'data', 'string', 'path', '数据目录'),
('path.template_dir', 'template', 'string', 'path', '模板目录'),
('path.output_dir', 'output', 'string', 'path', '输出目录'),
('path.log_dir', 'logs', 'string', 'path', '日志目录'),

-- 匹配算法配置
('match.fuzzy_threshold', '0.85', 'number', 'algorithm', '模糊匹配阈值'),
('match.enable_fuzzy_name', 'true', 'boolean', 'algorithm', '是否启用姓名模糊匹配'),

-- 计算规则配置
('calc.precision', '2', 'number', 'calculation', '金额计算精度'),
('calc.enable_validation', 'true', 'boolean', 'calculation', '是否启用计算验证'),

-- 界面配置
('ui.theme', 'default', 'string', 'ui', '界面主题'),
('ui.font_size', '12', 'number', 'ui', '字体大小'),
('ui.auto_save_interval', '300', 'number', 'ui', '自动保存间隔（秒）');
```

### 5.2 默认异动规则

```sql
-- 插入默认异动规则
INSERT INTO change_rules (rule_name, rule_code, change_type, operation_type, affected_fields, rule_config, description, priority) VALUES
('离职人员工资清零', 'RESIGN_CLEAR', '停薪-退休', 'clear', 
 '["basic_salary", "grade_salary", "allowance", "performance_basic", "performance_reward"]',
 '{"clear_all": true, "effective_immediately": true}',
 '离职人员停发所有工资项目', 90),

('调离人员工资清零', 'TRANSFER_CLEAR', '停薪-调离', 'clear',
 '["basic_salary", "grade_salary", "allowance", "performance_basic", "performance_reward"]',
 '{"clear_all": true, "effective_immediately": true}',
 '调离人员停发所有工资项目', 90),

('恢复薪资待遇', 'RESTORE_SALARY', '起薪', 'replace',
 '["basic_salary", "grade_salary", "performance_basic", "performance_reward"]',
 '{"restore_from_standard": true, "calculate_proportional": true}',
 '恢复正常薪资待遇，按实际工作天数计算', 80),

('考核扣款处理', 'ASSESSMENT_DEDUCTION', '其他-考核扣款', 'subtract',
 '["gross_salary"]',
 '{"fixed_amount": true, "monthly_deduction": true, "min_remaining": 0}',
 '高层次人才考核扣款，按月扣减固定金额', 70),

('请假扣款计算', 'LEAVE_DEDUCTION', '请假扣款', 'calculate',
 '["basic_salary", "performance_basic"]',
 '{"calculation_method": "proportional", "base_days": 22}',
 '请假按天扣减基本工资和绩效工资', 60),

('脱产读博调整', 'STUDY_ADJUSTMENT', '脱产读博', 'clear',
 '["performance_reward"]',
 '{"keep_basic": true, "clear_reward": true}',
 '脱产读博停发奖励性绩效，保留基本工资', 50);
```

## 6. 性能优化策略

### 6.1 索引优化

```sql
-- 复合索引优化查询性能
CREATE INDEX idx_salary_employee_month_status ON salary_data(employee_id, salary_month, is_calculated);
CREATE INDEX idx_changes_type_month_status ON salary_changes(change_type, change_month, processing_status);
CREATE INDEX idx_employees_status_type ON employees(status, employee_type);

-- 覆盖索引减少回表查询
CREATE INDEX idx_salary_summary ON salary_data(salary_month, employee_id, gross_salary, net_salary);
```

### 6.2 查询优化

```sql
-- 使用视图简化复杂查询
CREATE VIEW v_employee_current_salary AS
SELECT 
    e.employee_id,
    e.name,
    e.department,
    e.employee_type,
    s.salary_month,
    s.gross_salary,
    s.net_salary,
    s.basic_salary + s.grade_salary as base_total,
    s.performance_basic + s.performance_reward as performance_total
FROM employees e
JOIN salary_data s ON e.employee_id = s.employee_id
WHERE e.status = 'active'
  AND s.salary_month = (
      SELECT MAX(salary_month) 
      FROM salary_data s2 
      WHERE s2.employee_id = e.employee_id
  );

-- 异动汇总视图
CREATE VIEW v_monthly_change_summary AS
SELECT 
    change_month,
    change_type,
    COUNT(*) as change_count,
    SUM(change_amount) as total_amount,
    AVG(change_amount) as avg_amount,
    MIN(change_amount) as min_amount,
    MAX(change_amount) as max_amount
FROM salary_changes
WHERE processing_status = 'completed'
GROUP BY change_month, change_type;
```

### 6.3 数据库维护

```sql
-- 定期维护SQL
-- 1. 分析表统计信息
ANALYZE employees;
ANALYZE salary_data;
ANALYZE salary_changes;

-- 2. 重建索引
REINDEX idx_salary_employee_month;
REINDEX idx_changes_type_month_status;

-- 3. 清理过期数据（可选）
-- DELETE FROM processing_history WHERE start_time < date('now', '-1 year');
-- DELETE FROM salary_changes WHERE created_time < date('now', '-2 years') AND processing_status = 'completed';
```

这个数据库设计提供了完整的数据存储方案，支持工资异动处理的各种业务需求，同时具备良好的性能和扩展性。 