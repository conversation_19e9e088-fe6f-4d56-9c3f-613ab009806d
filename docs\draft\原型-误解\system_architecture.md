# 系统架构设计

## 1. 整体系统架构

### 1.1 架构概览

```mermaid
graph TB
    A[用户界面层 - GUI Layer] --> B[业务逻辑层 - Business Layer]
    B --> C[数据访问层 - Data Access Layer]
    C --> D[数据存储层 - Storage Layer]
    
    A --> E[Tkinter主界面]
    A --> F[文件选择对话框]
    A --> G[进度显示界面]
    A --> H[结果预览界面]
    
    B --> I[主控制器]
    B --> J[人员匹配器]
    B --> K[工资计算引擎]
    B --> L[报表生成器]
    B --> M[模板管理器]
    
    C --> N[Excel处理器]
    C --> O[数据库管理器]
    C --> P[数据同步器]
    C --> Q[配置管理器]
    
    D --> R[SQLite数据库]
    D --> S[Excel文件]
    D --> T[Word模板]
    D --> U[配置文件]
```

### 1.2 架构分层说明

#### 1.2.1 用户界面层 (GUI Layer)
- **职责**: 用户交互、界面展示、事件处理
- **技术**: Tkinter + ttk
- **组件**:
  - 主窗口管理器
  - 文件选择对话框
  - 进度条和状态显示
  - 结果预览和错误提示

#### 1.2.2 业务逻辑层 (Business Layer)
- **职责**: 核心业务逻辑、流程控制、规则引擎
- **组件**:
  - 主控制器 (`MainController`)
  - 人员匹配器 (`PersonMatcher`)
  - 工资计算引擎 (`SalaryCalculator`)
  - 报表生成器 (`ReportGenerator`)
  - 模板管理器 (`TemplateManager`)

#### 1.2.3 数据访问层 (Data Access Layer)
- **职责**: 数据读写、格式转换、数据验证
- **组件**:
  - Excel处理器 (`ExcelProcessor`)
  - 数据库管理器 (`DatabaseManager`)
  - 数据同步器 (`DataSynchronizer`)
  - 配置管理器 (`ConfigManager`)

#### 1.2.4 数据存储层 (Storage Layer)
- **职责**: 数据持久化存储
- **组件**:
  - SQLite数据库文件
  - Excel工资表文件
  - Word模板文件
  - JSON配置文件

## 2. 技术栈详细选型

### 2.1 核心技术栈

| 技术组件 | 选择方案 | 版本要求 | 选择理由 |
|---------|---------|---------|---------|
| **开发语言** | Python | 3.8+ | 生态丰富、易于维护 |
| **GUI框架** | Tkinter + ttk | 内置 | 无需额外安装、跨平台 |
| **数据处理** | pandas | 2.0+ | 强大的数据处理能力 |
| **Excel处理** | openpyxl + xlrd | 最新版 | 完整Excel格式支持 |
| **数据库** | SQLite | 内置 | 轻量级、单文件部署 |
| **文档处理** | python-docx | 最新版 | Word文档生成和编辑 |
| **打包工具** | PyInstaller | 最新版 | 生成独立可执行文件 |
| **日志系统** | logging | 内置 | 分级日志管理 |

### 2.2 依赖包清单

```txt
# 核心数据处理
pandas>=2.0.0
openpyxl>=3.1.0
xlrd>=2.0.0
xlwt>=1.3.0
xlsxwriter>=3.0.0

# 文档处理
python-docx>=0.8.11

# 数据库ORM (可选)
sqlalchemy>=2.0.0

# 界面增强 (可选)
ttkbootstrap>=1.10.0

# 开发和测试
pytest>=7.0.0
pytest-cov>=4.0.0

# 打包部署
pyinstaller>=5.0.0
auto-py-to-exe>=2.3.0
```

## 3. 模块架构设计

### 3.1 核心模块关系图

```mermaid
graph TD
    A[main.py 主程序入口] --> B[MainController 主控制器]
    
    B --> C[GUI模块组]
    B --> D[业务逻辑模块组]
    B --> E[数据访问模块组]
    
    C --> F[MainWindow 主窗口]
    C --> G[FileDialog 文件对话框]
    C --> H[ProgressDialog 进度对话框]
    C --> I[ResultViewer 结果查看器]
    
    D --> J[PersonMatcher 人员匹配器]
    D --> K[SalaryCalculator 工资计算器]
    D --> L[ReportGenerator 报表生成器]
    D --> M[TemplateManager 模板管理器]
    D --> N[RuleEngine 规则引擎]
    
    E --> O[ExcelProcessor Excel处理器]
    E --> P[DatabaseManager 数据库管理器]
    E --> Q[DataSynchronizer 数据同步器]
    E --> R[ConfigManager 配置管理器]
    
    P --> S[EmployeeDAO 员工数据访问]
    P --> T[SalaryDAO 工资数据访问]
    P --> U[ChangeDAO 异动数据访问]
    P --> V[ConfigDAO 配置数据访问]
```

### 3.2 模块依赖层次

```mermaid
graph LR
    subgraph "Level 1 - 基础工具层"
        A1[日志管理]
        A2[配置管理] 
        A3[数据验证]
        A4[异常处理]
    end
    
    subgraph "Level 2 - 数据访问层"
        B1[Excel处理器]
        B2[数据库管理器]
        B3[文件系统操作]
        B4[数据同步器]
    end
    
    subgraph "Level 3 - 业务逻辑层"
        C1[人员匹配器]
        C2[工资计算器]
        C3[规则引擎]
        C4[报表生成器]
        C5[模板管理器]
    end
    
    subgraph "Level 4 - 应用控制层"
        D1[主控制器]
        D2[工作流管理器]
    end
    
    subgraph "Level 5 - 用户界面层"
        E1[主界面]
        E2[对话框]
        E3[组件]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    
    D1 --> E1
    D2 --> E2
```

## 4. 设计模式应用

### 4.1 主要设计模式

#### 4.1.1 MVC模式 (Model-View-Controller)
- **Model**: 数据模型和业务逻辑
- **View**: Tkinter用户界面
- **Controller**: 主控制器协调模型和视图

#### 4.1.2 策略模式 (Strategy Pattern)
- **应用场景**: 异动规则处理
- **实现**: 不同异动类型采用不同处理策略

```python
class SalaryChangeStrategy:
    def apply_change(self, employee_data, change_data):
        pass

class ResignationStrategy(SalaryChangeStrategy):
    def apply_change(self, employee_data, change_data):
        # 离职处理逻辑
        pass

class PromotionStrategy(SalaryChangeStrategy):
    def apply_change(self, employee_data, change_data):
        # 晋升处理逻辑
        pass
```

#### 4.1.3 工厂模式 (Factory Pattern)
- **应用场景**: 报表生成器创建
- **实现**: 根据报表类型创建对应生成器

```python
class ReportGeneratorFactory:
    @staticmethod
    def create_generator(report_type):
        if report_type == "summary":
            return SummaryReportGenerator()
        elif report_type == "approval":
            return ApprovalReportGenerator()
        # ... 其他类型
```

#### 4.1.4 观察者模式 (Observer Pattern)
- **应用场景**: 进度通知和状态更新
- **实现**: 业务处理进度通知GUI更新

```python
class ProgressNotifier:
    def __init__(self):
        self.observers = []
    
    def add_observer(self, observer):
        self.observers.append(observer)
    
    def notify_progress(self, progress, message):
        for observer in self.observers:
            observer.update_progress(progress, message)
```

#### 4.1.5 单例模式 (Singleton Pattern)
- **应用场景**: 配置管理器、日志管理器
- **实现**: 确保全局唯一实例

```python
class ConfigManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
```

## 5. 性能与扩展性设计

### 5.1 性能优化策略

#### 5.1.1 内存管理
- **分批处理**: 大数据集分块处理，避免内存溢出
- **延迟加载**: 按需加载数据，减少内存占用
- **缓存机制**: 常用数据缓存，提高访问速度

#### 5.1.2 数据库优化
- **索引策略**: 关键字段建立索引
- **批量操作**: 批量插入和更新，减少数据库访问次数
- **连接池**: 数据库连接复用

#### 5.1.3 文件处理优化
- **流式读取**: 大文件流式处理
- **并行处理**: 多线程处理独立任务
- **压缩存储**: 历史数据压缩存储

### 5.2 扩展性设计

#### 5.2.1 模块化扩展
- **插件架构**: 支持功能模块插件化扩展
- **配置驱动**: 通过配置文件扩展功能
- **接口标准化**: 统一的接口规范

#### 5.2.2 规则引擎扩展
- **规则配置化**: 异动规则外部配置
- **动态加载**: 运行时规则更新
- **版本管理**: 规则版本控制

#### 5.2.3 模板系统扩展
- **模板热更新**: 无需重启即可更新模板
- **多格式支持**: 支持多种文档格式
- **自定义模板**: 用户自定义模板功能

## 6. 安全性设计

### 6.1 数据安全
- **数据加密**: 敏感数据加密存储
- **访问控制**: 文件和数据访问权限控制
- **数据备份**: 自动数据备份和恢复

### 6.2 操作安全
- **操作审计**: 详细操作日志记录
- **回滚机制**: 错误操作回滚功能
- **权限验证**: 关键操作权限验证

### 6.3 系统安全
- **异常处理**: 完善的异常捕获和处理
- **资源限制**: 系统资源使用限制
- **版本控制**: 代码和配置版本管理

## 7. 部署架构

### 7.1 单机部署架构

```mermaid
graph TB
    A[Windows桌面环境] --> B[Python运行时环境]
    B --> C[应用程序主体]
    C --> D[SQLite数据库]
    C --> E[Excel文件处理]
    C --> F[Word文档生成]
    C --> G[配置文件]
    C --> H[日志文件]
    C --> I[模板文件]
    
    subgraph "文件系统结构"
        J[应用根目录]
        J --> K[src/ 源码目录]
        J --> L[data/ 数据目录]
        J --> M[template/ 模板目录]
        J --> N[output/ 输出目录]
        J --> O[logs/ 日志目录]
        J --> P[config/ 配置目录]
    end
```

### 7.2 打包部署策略

#### 7.2.1 PyInstaller配置
```python
# build.spec 配置文件
a = Analysis(['src/main.py'],
             pathex=['.'],
             binaries=[],
             datas=[('template', 'template'),
                    ('config', 'config')],
             hiddenimports=['openpyxl', 'xlrd'],
             hookspath=[],
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=None,
             noarchive=False)
```

#### 7.2.2 安装包结构
```
SalaryChangeSystem/
├── SalaryChangeSystem.exe    # 主程序
├── template/                 # 模板文件
├── config/                   # 配置文件
├── docs/                     # 用户文档
├── examples/                 # 示例数据
└── README.txt               # 使用说明
```

这个系统架构设计提供了完整的技术架构蓝图，确保系统具备良好的可维护性、扩展性和稳定性。 