2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 16:47:23.987 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 16:47:25.291 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 16:47:25.291 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 16:47:25.291 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 16:47:25.318 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 16:47:25.323 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 16:47:25.327 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.328 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 16:47:25.329 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 16:47:25.329 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 16:47:25.329 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 16:47:25.337 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 16:47:25.612 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 16:47:25.626 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 16:47:25.627 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 16:47:25.628 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 16:47:25.639 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 16:47:25.651 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 16:47:25.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 16:47:25.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 16:47:25.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 16:47:25.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 16:47:25.709 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 16:47:25.728 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 95个展开项
2025-06-26 16:47:25.749 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-26 16:47:25.750 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 16:47:25.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 16:47:25.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 16:47:25.828 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:25.829 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 16:47:25.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 16:47:25.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:25.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:25.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.83ms
2025-06-26 16:47:25.856 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:25.882 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 16:47:25.949 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 16:47:25.996 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.997 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 16:47:26.000 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:26.095 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共56个表的映射
2025-06-26 16:47:26.095 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:26.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:26.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:26.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.02ms
2025-06-26 16:47:26.099 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:26.099 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:26.099 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:26.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.53ms
2025-06-26 16:47:26.102 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:26.102 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:26.103 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 16:47:26.432 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 16:47:26.437 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 16:47:26.438 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 16:47:26.507 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 16:47:26.520 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 16:47:26.523 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 16:47:26.997 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 16:47:26.998 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 8 个月份
2025-06-26 16:47:26.999 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 16:47:27.000 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 16:47:27.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 16:47:27.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 16:47:27.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 16:47:27.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 16:47:27.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 16:47:27.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 20 个月份
2025-06-26 16:47:27.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:47:27.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 16:47:32.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:32.139 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:32.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:32.139 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年']
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 16:47:34.078 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:34.078 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:34.078 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:34.078 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:47:35.753 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_a_grade_employees 第1页数据，每页50条
2025-06-26 16:47:35.753 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:47:35.753 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_a_grade_employees: 7 个字段重命名
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:47:35.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:47:35.769 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:47:35.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:47:35.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:47:35.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 61.54ms
2025-06-26 16:47:35.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:47:35.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:47:38.366 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:47:38.366 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:01.389 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 id_card 在表 salary_data_2026_07_a_grade_employees 中不存在，创建新字段映射
2025-06-26 16:48:01.398 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_07_a_grade_employees.id_card -> 身份证
2025-06-26 16:48:01.398 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: id_card -> 身份证
2025-06-26 16:48:01.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:01.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:48:01.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列3, id_card -> 身份证
2025-06-26 16:48:17.326 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 姓名 在表 salary_data_2026_07_a_grade_employees 中不存在，创建新字段映射
2025-06-26 16:48:17.341 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_07_a_grade_employees.姓名 -> 姓名1
2025-06-26 16:48:17.341 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: 姓名 -> 姓名1
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, 姓名 -> 姓名1
2025-06-26 16:48:28.405 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:48:28.405 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:28.405 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:28.448 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 16:48:28.452 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_pension_employees: 7 个字段重命名
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:28.452 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:48:28.485 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 16:48:28.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:48:28.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:28.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:48:28.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 105.47ms
2025-06-26 16:48:28.558 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 16:48:43.126 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:43.142 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 16:48:43.142 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_retired_employees: 5 个字段重命名
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:43.142 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 16:48:43.142 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 16:48:43.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 16:48:43.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 16:48:43.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:43.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 16:48:43.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 86.06ms
2025-06-26 16:48:43.231 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 16:48:45.004 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:48:45.004 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:51.420 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表']
2025-06-26 16:48:51.421 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:51.422 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:51.422 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:48:51.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:51.423 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:51.424 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 16:48:51.426 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 16:48:51.426 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:48:51.427 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_pension_employees: 7 个字段重命名
2025-06-26 16:48:51.429 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:51.429 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:51.431 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:51.431 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:48:51.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 16:48:51.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:48:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:48:51.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 6.57ms
2025-06-26 16:48:51.439 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:48:53.335 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:48:53.336 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表']
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 16:48:59.828 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_active_employees，第1页，每页50条
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_07_active_employees 第1页
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_active_employees 第1页数据，每页50条
2025-06-26 16:48:59.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:48:59.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_active_employees: 7 个字段重命名
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 16:48:59.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:48:59.845 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_active_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:59.860 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:48:59.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 57.20ms
2025-06-26 16:48:59.918 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:48:59.919 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年']
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:49:03.220 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2794 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_07_a_grade_employees 无需字段重命名
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 16:49:03.241 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:49:03.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 40.99ms
2025-06-26 16:49:03.304 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:03.305 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:03.311 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年']
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月
2025-06-26 16:49:25.626 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:25.642 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:25.642 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.67ms
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:25.658 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 6月', '工资表 > 2026年 > 7月']
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月 > A岗职工
2025-06-26 16:49:27.286 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_06_a_grade_employees，第1页，每页50条
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_06_a_grade_employees 第1页
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月 > A岗职工
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_06_a_grade_employees 第1页数据，每页50条
2025-06-26 16:49:27.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:49:27.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_06_a_grade_employees: 7 个字段重命名
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:49:27.303 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:49:27.303 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_06_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:27.317 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:49:27.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:49:27.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:49:27.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 29.90ms
2025-06-26 16:49:27.333 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:27.333 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:29.259 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:49:29.259 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:49:43.410 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 6月', '工资表 > 2026年 > 7月']
2025-06-26 16:49:43.411 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月
2025-06-26 16:49:43.412 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:43.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:43.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:43.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:43.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 18.65ms
2025-06-26 16:49:43.432 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:43.434 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:43.435 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:43.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:43.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:43.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:43.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 4.71ms
2025-06-26 16:49:43.445 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:43.448 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:43.450 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月
2025-06-26 16:49:44.474 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:44.474 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 16:49:44.475 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:44.476 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:44.477 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_a_grade_employees，第1页，每页50条
2025-06-26 16:49:44.477 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_a_grade_employees 第1页
2025-06-26 16:49:44.478 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 16:49:44.478 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_a_grade_employees 第1页数据，每页50条
2025-06-26 16:49:44.479 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:49:44.481 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:49:44.482 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:49:44.483 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_a_grade_employees 没有保存的字段映射信息
2025-06-26 16:49:44.484 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:49:44.486 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:49:44.497 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:49:44.498 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:44.499 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_01_a_grade_employees 的字段偏好: 7个字段 ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary']
2025-06-26 16:49:44.501 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:44.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:44.506 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:49:44.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:44.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 7 列
2025-06-26 16:49:44.545 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 41.81ms
2025-06-26 16:49:44.546 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:44.547 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:46.230 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:49:46.230 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 16:49:53.999 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:49:53.999 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_retired_employees 获取数据...
2025-06-26 16:49:54.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_retired_employees 获取 2 行数据。
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_retired_employees 没有保存的字段映射信息
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:49:54.015 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 16:49:54.015 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 16:49:54.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 79.73ms
2025-06-26 16:49:54.095 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 16:49:56.001 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:49:56.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_pension_employees 获取数据...
2025-06-26 16:49:56.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_pension_employees 获取 13 行数据。
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_pension_employees 没有保存的字段映射信息
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:49:56.016 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 16:49:56.016 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:50:04.286 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:203 | 表 salary_data_2026_01_pension_employees 配置不存在，创建新的字段映射配置
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 employee_name 在表 salary_data_2026_01_pension_employees 中不存在，创建新字段映射
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_01_pension_employees.employee_name -> 姓名
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: employee_name -> 姓名
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, employee_name -> 姓名
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:50:12.209 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_active_employees 第1页
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_active_employees 第1页数据，每页50条
2025-06-26 16:50:12.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:50:12.225 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_active_employees 没有保存的字段映射信息
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 16:50:12.244 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:50:12.251 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:12.264 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:50:12.268 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 16:50:12.295 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:50:12.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 16:50:12.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:12.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:12.338 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.37ms
2025-06-26 16:50:12.338 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:50:12.339 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 16:50:22.072 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:203 | 表 salary_data_2026_01_active_employees 配置不存在，创建新的字段映射配置
2025-06-26 16:50:22.072 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 employee_name 在表 salary_data_2026_01_active_employees 中不存在，创建新字段映射
2025-06-26 16:50:22.088 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_01_active_employees.employee_name -> 姓名
2025-06-26 16:50:22.088 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: employee_name -> 姓名
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, employee_name -> 姓名
2025-06-26 16:50:33.380 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:33.381 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:33.381 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:33.382 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:50:33.384 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:50:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:33.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:33.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:33.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 21.86ms
2025-06-26 16:50:33.409 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 16:50:33.410 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 16:50:43.457 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:43.457 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:43.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第3页, 每页50条
2025-06-26 16:50:43.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:43.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.34ms
2025-06-26 16:50:43.489 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 16
2025-06-26 16:50:43.489 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-26 16:50:51.838 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:51.854 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:51.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第4页, 每页50条
2025-06-26 16:50:51.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第4页数据: 50 行，总计1396行
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:51.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 16:50:51.870 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第4页, 显示50条记录，字段数: 16
2025-06-26 16:50:51.870 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 4
2025-06-26 16:51:00.046 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:51:00.046 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:51:00.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:51:00.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:51:00.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.32ms
2025-06-26 16:51:00.077 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 16:51:00.077 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 16:51:14.096 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:51:14.096 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:51:14.096 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:51:14.096 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:51:14.111 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:51:14.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 22.18ms
2025-06-26 16:51:14.148 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 16:51:14.150 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_refresh:627 | 本地分页数据刷新完成
2025-06-26 16:51:28.684 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 17:21:28.879 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:21:28.880 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:21:28.880 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:21:28.880 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:21:28.881 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:21:28.881 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:21:29.743 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.746 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.751 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.752 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2025_03_test' 生成字段映射，共 9 个字段
2025-06-26 17:21:29.759 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 17:21:29.769 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.773 | INFO     | src.modules.data_import.auto_field_mapping_generator:validate_mapping_keys:166 | 字段映射验证完成:
2025-06-26 17:21:29.774 | INFO     | src.modules.data_import.auto_field_mapping_generator:validate_mapping_keys:170 | 验证结果: 原始 4 个字段，验证后 6 个字段
2025-06-26 17:21:29.775 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.776 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'test_table' 生成字段映射，共 4 个字段
2025-06-26 17:21:29.776 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 17:24:00.400 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:24:00.400 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:24:00.401 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:24:00.401 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:24:00.402 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:24:00.402 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:24:00.704 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:24:00.705 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 7 个字段
2025-06-26 17:24:00.708 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:24:00.709 | ERROR    | src.modules.data_import.config_sync_manager:_load_config_file:501 | 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-06-26 17:24:00.710 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_01_demo
2025-06-26 17:24:00.735 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:24:00.735 | ERROR    | src.modules.data_import.config_sync_manager:_load_config_file:501 | 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-06-26 17:24:00.735 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: demo_table
2025-06-26 17:24:00.735 | INFO     | src.modules.data_import.config_sync_manager:update_single_field_mapping:309 | 字段映射更新成功: demo_table.employee_name -> 员工姓名
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:32:23.049 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:32:24.419 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 17:32:24.419 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 17:32:24.419 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 17:32:24.450 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 17:32:24.450 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 17:32:24.450 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:24.450 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 17:32:24.450 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 17:32:24.450 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 17:32:24.450 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 17:32:24.450 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 17:32:24.713 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 17:32:24.713 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:32:24.713 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:32:24.713 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 17:32:24.744 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 17:32:24.787 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 17:32:24.789 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 17:32:24.799 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 17:32:24.800 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 17:32:24.802 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 17:32:24.837 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 97个展开项
2025-06-26 17:32:24.939 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '异动人员表 > 2024年', '工资表', '异动人员表']
2025-06-26 17:32:24.941 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 17:32:25.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 17:32:25.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 17:32:25.036 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:32:25.036 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 17:32:25.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 17:32:25.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:25.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:25.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.62ms
2025-06-26 17:32:25.045 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:25.053 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 17:32:25.086 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 17:32:25.122 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:25.123 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:32:25.123 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 17:32:25.124 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 17:32:25.124 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 17:32:25.124 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:32:25.220 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共58个表的映射
2025-06-26 17:32:25.220 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:25.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:25.222 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:25.222 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.58ms
2025-06-26 17:32:25.223 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:25.224 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:25.224 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:25.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:25.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:25.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.15ms
2025-06-26 17:32:25.229 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:25.231 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:25.231 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 17:32:25.606 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 17:32:25.613 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 17:32:25.614 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 17:32:25.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 17:32:25.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 17:32:25.618 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 17:32:26.075 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 8 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 20 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 17:32:37.592 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 17:32:37.592 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年
2025-06-26 17:32:37.592 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:37.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.67ms
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:37.608 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:37.608 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:37.608 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 17:32:41.724 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:41.724 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:41.724 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:41.724 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:41.739 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:32:43.456 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_a_grade_employees 第1页数据，每页50条
2025-06-26 17:32:43.456 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:32:43.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 8 个字段重命名
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 17:32:43.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:32:43.472 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:32:43.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:32:43.524 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.84ms
2025-06-26 17:32:43.534 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:32:43.535 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:32:49.203 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:32:49.203 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:33:03.870 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:03.871 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:33:03.872 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:03.872 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:33:03.872 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:33:03.873 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:33:03.873 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 17:33:03.875 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 17:33:03.876 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:33:03.877 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 7 个字段重命名
2025-06-26 17:33:03.878 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:33:03.880 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:03.882 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:33:03.884 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:33:04.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 17:33:04.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:33:04.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:04.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:33:04.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 215.96ms
2025-06-26 17:33:04.102 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:33:14.482 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:33:14.482 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 17:33:14.482 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:33:14.482 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:33:14.482 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:33:14.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 32.28ms
2025-06-26 17:33:14.514 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:33:17.559 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:33:17.559 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 17:33:22.987 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_active_employees，第1页，每页50条
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_07_active_employees 第1页
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_active_employees 第1页数据，每页50条
2025-06-26 17:33:22.987 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:33:22.987 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 7 个字段重命名
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 17:33:23.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:33:23.003 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_active_employees 无字段偏好设置，显示所有字段
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 50
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:33:23.021 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:33:23.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:23.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:33:23.058 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 54.57ms
2025-06-26 17:33:23.058 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:33:23.058 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 17:33:27.139 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:33:27.139 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:27.139 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:33:27.139 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:33:27.139 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:33:27.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:33:27.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:33:27.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:27.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:33:27.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 93.82ms
2025-06-26 17:33:27.263 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 17:33:27.263 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 17:33:48.766 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.68ms
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:33:48.782 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:48.782 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:33:48.782 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 17:33:57.239 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: reset_headers
2025-06-26 17:33:59.146 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:59.146 | INFO     | src.gui.prototype.prototype_main_window:_reload_current_table_data:3615 | 重新加载表格数据: salary_data_2026_07_active_employees
2025-06-26 17:33:59.146 | ERROR    | src.gui.prototype.prototype_main_window:_reload_current_table_data:3633 | 重新加载当前表格数据失败: 'PaginationWidget' object has no attribute 'get_current_page'
2025-06-26 17:33:59.146 | INFO     | src.gui.prototype.prototype_main_window:_reset_headers_to_default:3515 | 表头已重置为默认显示: salary_data_2026_07_active_employees
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2025年', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年', '工资表']
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:13.393 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 17:34:13.409 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:34:13.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 16.17ms
2025-06-26 17:34:13.425 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:34:13.425 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:34:13.425 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2026年']
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:34:17.100 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:34:17.100 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 17:34:17.120 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:34:17.120 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:34:17.120 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:34:17.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 42.60ms
2025-06-26 17:34:17.162 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:34:20.025 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:34:20.025 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月 > 退休人员']
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:34:23.700 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:34:23.700 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 17:34:23.700 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 7 个字段重命名
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:34:23.716 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 17:34:23.716 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:34:25.289 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:34:25.289 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:34:42.763 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:34:42.764 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:42.764 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:42.765 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:34:42.766 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:34:42.767 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:34:42.767 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 17:34:42.768 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 17:34:42.769 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:42.770 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:42.770 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:34:42.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 17:34:42.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:34:42.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:42.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:34:42.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 18.71ms
2025-06-26 17:34:42.791 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:34:42.793 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:34:42.801 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:44.647 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:34:44.647 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:35:02.455 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:35:02.455 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:35:02.455 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:35:02.455 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:35:02.455 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:35:02.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 32.59ms
2025-06-26 17:35:02.488 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:35:02.488 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:35:17.364 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 17:35:17.364 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2026年 > 7月 > A岗职工。打开导入对话框。
2025-06-26 17:35:17.364 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:35:17.364 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:35:17.364 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 17:35:17.364 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 17:35:17.364 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:35:17.400 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 17:35:17.444 | INFO     | src.gui.dialogs:_init_field_mapping:1925 | 初始化字段映射表格: 17 个默认字段
2025-06-26 17:35:17.578 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 17:35:17.579 | INFO     | src.gui.dialogs:_apply_default_settings:2141 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-06-26 17:35:17.579 | INFO     | src.gui.dialogs:_setup_tooltips:2396 | 工具提示设置完成
2025-06-26 17:35:17.580 | INFO     | src.gui.dialogs:_setup_shortcuts:2435 | 快捷键设置完成
2025-06-26 17:35:17.580 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-26 17:35:21.023 | INFO     | src.gui.dialogs:_on_target_changed:2080 | 目标位置已更新: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:35:33.366 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:35:35.046 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:35:35.047 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: A岗职工 -> A岗职工
2025-06-26 17:35:35.048 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2176 | 根据人员类别 'A岗职工' 自动选择工作表: A岗职工
2025-06-26 17:35:47.904 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 21列, 20行
2025-06-26 17:35:59.994 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.214 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.217 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.217 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.217 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.421 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.421 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.436 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.436 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.436 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:00.577 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-26 17:36:00.577 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:00.580 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-26 17:36:00.581 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-26 17:36:00.581 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-26 17:36:00.581 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 17:36:00.582 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 12 个字段
2025-06-26 17:36:00.591 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_retired_employees
2025-06-26 17:36:00.592 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_retired_employees 生成标准化字段映射: 12 个字段
2025-06-26 17:36:00.592 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-26 17:36:00.597 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-26 17:36:00.599 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-26 17:36:00.599 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_retired_employees 不存在，将根据模板创建...
2025-06-26 17:36:00.615 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。
2025-06-26 17:36:00.628 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.644 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.647 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:00.794 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-26 17:36:00.854 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:00.854 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-26 17:36:00.870 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-26 17:36:00.870 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-26 17:36:00.870 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 17:36:00.870 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 21 个字段
2025-06-26 17:36:00.893 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_pension_employees
2025-06-26 17:36:00.917 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_pension_employees 生成标准化字段映射: 21 个字段
2025-06-26 17:36:00.919 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-26 17:36:00.926 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-26 17:36:00.928 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:36:00.929 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_pension_employees 不存在，将根据模板创建...
2025-06-26 17:36:00.949 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。
2025-06-26 17:36:00.956 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.966 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.967 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-26 17:36:01.105 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-26 17:36:01.105 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 23 个字段
2025-06-26 17:36:01.121 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_active_employees
2025-06-26 17:36:01.121 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_active_employees 生成标准化字段映射: 23 个字段
2025-06-26 17:36:01.121 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-26 17:36:01.121 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-26 17:36:01.136 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:36:01.138 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_active_employees 不存在，将根据模板创建...
2025-06-26 17:36:01.178 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。
2025-06-26 17:36:01.180 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:01.180 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:01.181 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:01.356 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-26 17:36:01.358 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:01.359 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-26 17:36:01.360 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-26 17:36:01.361 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-26 17:36:01.361 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-26 17:36:01.363 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 17 个字段
2025-06-26 17:36:01.372 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_a_grade_employees
2025-06-26 17:36:01.372 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_a_grade_employees 生成标准化字段映射: 17 个字段
2025-06-26 17:36:01.373 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet A岗职工 存在 2 个验证错误
2025-06-26 17:36:01.376 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet A岗职工 数据处理完成: 62 行
2025-06-26 17:36:01.377 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:36:01.379 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_a_grade_employees 不存在，将根据模板创建...
2025-06-26 17:36:01.382 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。
2025-06-26 17:36:01.382 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-26 17:36:01.398 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-08', 'data_description': '2026年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 8月 > A岗职工'}
2025-06-26 17:36:01.403 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-08', 'data_description': '2026年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 8月 > A岗职工'}
2025-06-26 17:36:01.429 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '工资表 > 2026年 > 8月 > A岗职工'
2025-06-26 17:36:01.430 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:01.433 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: ['工资表', '2026年', '8月', 'A岗职工']
2025-06-26 17:36:01.433 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷新导航面板
2025-06-26 17:36:01.433 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-26 17:36:01.438 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 17:36:01.438 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 17:36:01.446 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:36:01.446 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:36:01.447 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:36:01.448 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:36:01.448 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:36:01.449 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:36:01.450 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 17:36:01.451 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 17:36:01.453 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:01.455 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:36:01.455 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:36:01.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 17:36:01.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:36:01.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:01.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:36:01.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 17.64ms
2025-06-26 17:36:01.491 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:36:01.492 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:36:01.498 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:36:01.550 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 84 个匹配类型 'salary_data' 的表
2025-06-26 17:36:01.553 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 17:36:01.553 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 17:36:01.555 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 17:36:01.556 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 17:36:01.559 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 17:36:01.561 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 17:36:01.565 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 17:36:01.569 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 17:36:01.571 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 21 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 17:36:01.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 17:36:01.577 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.379 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.394 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:36:02.411 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.412 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:36:02.421 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:36:02.421 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_08_a_grade_employees，第1页，每页50条
2025-06-26 17:36:02.422 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_08_a_grade_employees 第1页
2025-06-26 17:36:02.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.424 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_08_a_grade_employees 第1页数据，每页50条
2025-06-26 17:36:02.432 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.432 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:36:02.442 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:36:02.443 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:02.445 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:36:02.446 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 17:36:02.448 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 17:36:02.460 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:36:02.461 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:02.478 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:36:02.485 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:36:02.499 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:36:02.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:36:02.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:36:02.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:02.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:36:02.573 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 69.24ms
2025-06-26 17:36:02.588 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:36:02.589 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:36:39.598 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:36:39.598 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:39.598 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:39.598 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:36:39.598 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:36:39.613 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 30.86ms
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:36:39.628 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:36:45.912 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:36:45.912 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:45.912 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:36:45.912 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:36:45.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.66ms
2025-06-26 17:36:45.928 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 17:36:45.928 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 17:36:53.918 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:36:53.918 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:53.918 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:53.918 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:36:53.918 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:36:53.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:36:53.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:36:53.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:53.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:36:54.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 84.29ms
2025-06-26 17:36:54.003 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:36:54.003 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:37:02.997 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:37:02.997 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > 离休人员
2025-06-26 17:37:03.012 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > 离休人员
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:37:03.012 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_08_retired_employees 获取数据...
2025-06-26 17:37:03.012 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_08_retired_employees 获取 2 行数据。
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:37:03.030 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 2 个字段重命名
2025-06-26 17:37:03.035 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:37:03.041 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:03.055 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:37:03.057 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:37:03.061 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:37:03.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 2
2025-06-26 17:37:03.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:37:03.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:03.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:37:03.092 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 31.29ms
2025-06-26 17:37:03.093 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:37:05.309 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:37:05.309 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:37:15.346 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:37:15.347 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > 退休人员
2025-06-26 17:37:15.348 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:37:15.348 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:37:15.349 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > 退休人员
2025-06-26 17:37:15.349 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:37:15.351 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_08_pension_employees 获取数据...
2025-06-26 17:37:15.353 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_08_pension_employees 获取 13 行数据。
2025-06-26 17:37:15.355 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:37:15.357 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 4 个字段重命名
2025-06-26 17:37:15.359 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '津贴', 'deduction', '应发工资', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:37:15.360 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:15.362 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:37:15.363 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:37:15.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 17:37:15.379 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:37:15.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:15.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:37:15.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 22.34ms
2025-06-26 17:37:15.396 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > 全部在职人员
2025-06-26 17:37:27.012 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_08_active_employees，第1页，每页50条
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_08_active_employees 第1页
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > 全部在职人员
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_08_active_employees 第1页数据，每页50条
2025-06-26 17:37:27.012 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:37:27.027 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 17:37:27.052 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:37:27.052 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:27.065 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:37:27.069 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_active_employees 无字段偏好设置，显示所有字段
2025-06-26 17:37:27.091 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:37:27.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 17:37:27.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:37:27.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:27.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:37:27.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.37ms
2025-06-26 17:37:27.131 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:37:27.132 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 17:37:43.302 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:37:43.302 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:43.302 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:37:43.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_active_employees 分页获取数据: 第28页, 每页50条
2025-06-26 17:37:43.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_active_employees 获取第28页数据: 46 行，总计1396行
2025-06-26 17:37:43.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 46
2025-06-26 17:37:43.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(46)自动调整最大可见行数为: 46
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 46 行, 16 列
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 46 行, 最大可见行数: 46, 耗时: 31.38ms
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第28页, 显示46条记录，字段数: 16
2025-06-26 17:37:43.334 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 28
2025-06-26 17:38:13.745 | INFO     | __main__:main:302 | 应用程序正常退出
