2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 16:47:23.987 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 16:47:25.291 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 16:47:25.291 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 16:47:25.291 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 16:47:25.318 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 16:47:25.323 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 16:47:25.327 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.328 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 16:47:25.329 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 16:47:25.329 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 16:47:25.329 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 16:47:25.337 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 16:47:25.612 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 16:47:25.626 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 16:47:25.627 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 16:47:25.628 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 16:47:25.639 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 16:47:25.651 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 16:47:25.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 16:47:25.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 16:47:25.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 16:47:25.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 16:47:25.709 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 16:47:25.728 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 95个展开项
2025-06-26 16:47:25.749 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-26 16:47:25.750 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 16:47:25.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 16:47:25.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 16:47:25.828 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:25.829 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 16:47:25.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 16:47:25.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:25.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:25.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.83ms
2025-06-26 16:47:25.856 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:25.882 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 16:47:25.949 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 16:47:25.996 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.997 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 16:47:26.000 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:26.095 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共56个表的映射
2025-06-26 16:47:26.095 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:26.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:26.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:26.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.02ms
2025-06-26 16:47:26.099 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:26.099 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:26.099 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:26.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.53ms
2025-06-26 16:47:26.102 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:26.102 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:26.103 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 16:47:26.432 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 16:47:26.437 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 16:47:26.438 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 16:47:26.507 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 16:47:26.520 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 16:47:26.523 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 16:47:26.997 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 16:47:26.998 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 8 个月份
2025-06-26 16:47:26.999 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 16:47:27.000 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 16:47:27.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 16:47:27.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 16:47:27.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 16:47:27.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 16:47:27.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 16:47:27.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 20 个月份
2025-06-26 16:47:27.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:47:27.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 16:47:32.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:32.139 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:32.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:32.139 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年']
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 16:47:34.078 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:34.078 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:34.078 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:34.078 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:47:35.753 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_a_grade_employees 第1页数据，每页50条
2025-06-26 16:47:35.753 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:47:35.753 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_a_grade_employees: 7 个字段重命名
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:47:35.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:47:35.769 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:47:35.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:47:35.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:47:35.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 61.54ms
2025-06-26 16:47:35.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:47:35.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:47:38.366 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:47:38.366 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:01.389 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 id_card 在表 salary_data_2026_07_a_grade_employees 中不存在，创建新字段映射
2025-06-26 16:48:01.398 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_07_a_grade_employees.id_card -> 身份证
2025-06-26 16:48:01.398 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: id_card -> 身份证
2025-06-26 16:48:01.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:01.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:48:01.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列3, id_card -> 身份证
2025-06-26 16:48:17.326 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 姓名 在表 salary_data_2026_07_a_grade_employees 中不存在，创建新字段映射
2025-06-26 16:48:17.341 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_07_a_grade_employees.姓名 -> 姓名1
2025-06-26 16:48:17.341 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: 姓名 -> 姓名1
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, 姓名 -> 姓名1
2025-06-26 16:48:28.405 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:48:28.405 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:28.405 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:28.448 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 16:48:28.452 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_pension_employees: 7 个字段重命名
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:28.452 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:48:28.485 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 16:48:28.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:48:28.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:28.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:48:28.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 105.47ms
2025-06-26 16:48:28.558 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 16:48:43.126 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:43.142 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 16:48:43.142 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_retired_employees: 5 个字段重命名
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:43.142 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 16:48:43.142 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 16:48:43.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 16:48:43.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 16:48:43.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:43.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 16:48:43.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 86.06ms
2025-06-26 16:48:43.231 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 16:48:45.004 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:48:45.004 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:51.420 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表']
2025-06-26 16:48:51.421 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:51.422 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:51.422 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:48:51.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:51.423 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:51.424 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 16:48:51.426 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 16:48:51.426 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:48:51.427 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_pension_employees: 7 个字段重命名
2025-06-26 16:48:51.429 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:51.429 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:51.431 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:51.431 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:48:51.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 16:48:51.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:48:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:48:51.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 6.57ms
2025-06-26 16:48:51.439 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:48:53.335 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:48:53.336 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表']
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 16:48:59.828 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_active_employees，第1页，每页50条
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_07_active_employees 第1页
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_active_employees 第1页数据，每页50条
2025-06-26 16:48:59.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:48:59.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_active_employees: 7 个字段重命名
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 16:48:59.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:48:59.845 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_active_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:59.860 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:48:59.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 57.20ms
2025-06-26 16:48:59.918 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:48:59.919 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年']
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:49:03.220 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2794 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_07_a_grade_employees 无需字段重命名
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 16:49:03.241 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:49:03.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 40.99ms
2025-06-26 16:49:03.304 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:03.305 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:03.311 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年']
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月
2025-06-26 16:49:25.626 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:25.642 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:25.642 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.67ms
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:25.658 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 6月', '工资表 > 2026年 > 7月']
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月 > A岗职工
2025-06-26 16:49:27.286 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_06_a_grade_employees，第1页，每页50条
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_06_a_grade_employees 第1页
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月 > A岗职工
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_06_a_grade_employees 第1页数据，每页50条
2025-06-26 16:49:27.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:49:27.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_06_a_grade_employees: 7 个字段重命名
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:49:27.303 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:49:27.303 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_06_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:27.317 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:49:27.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:49:27.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:49:27.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 29.90ms
2025-06-26 16:49:27.333 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:27.333 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:29.259 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:49:29.259 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:49:43.410 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 6月', '工资表 > 2026年 > 7月']
2025-06-26 16:49:43.411 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月
2025-06-26 16:49:43.412 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:43.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:43.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:43.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:43.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 18.65ms
2025-06-26 16:49:43.432 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:43.434 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:43.435 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:43.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:43.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:43.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:43.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 4.71ms
2025-06-26 16:49:43.445 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:43.448 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:43.450 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月
2025-06-26 16:49:44.474 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:44.474 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 16:49:44.475 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:44.476 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:44.477 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_a_grade_employees，第1页，每页50条
2025-06-26 16:49:44.477 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_a_grade_employees 第1页
2025-06-26 16:49:44.478 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 16:49:44.478 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_a_grade_employees 第1页数据，每页50条
2025-06-26 16:49:44.479 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:49:44.481 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:49:44.482 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:49:44.483 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_a_grade_employees 没有保存的字段映射信息
2025-06-26 16:49:44.484 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:49:44.486 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:49:44.497 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:49:44.498 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:44.499 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_01_a_grade_employees 的字段偏好: 7个字段 ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary']
2025-06-26 16:49:44.501 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:44.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:44.506 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:49:44.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:44.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 7 列
2025-06-26 16:49:44.545 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 41.81ms
2025-06-26 16:49:44.546 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:44.547 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:46.230 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:49:46.230 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 16:49:53.999 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:49:53.999 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_retired_employees 获取数据...
2025-06-26 16:49:54.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_retired_employees 获取 2 行数据。
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_retired_employees 没有保存的字段映射信息
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:49:54.015 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 16:49:54.015 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 16:49:54.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 79.73ms
2025-06-26 16:49:54.095 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 16:49:56.001 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:49:56.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_pension_employees 获取数据...
2025-06-26 16:49:56.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_pension_employees 获取 13 行数据。
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_pension_employees 没有保存的字段映射信息
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:49:56.016 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 16:49:56.016 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:50:04.286 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:203 | 表 salary_data_2026_01_pension_employees 配置不存在，创建新的字段映射配置
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 employee_name 在表 salary_data_2026_01_pension_employees 中不存在，创建新字段映射
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_01_pension_employees.employee_name -> 姓名
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: employee_name -> 姓名
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, employee_name -> 姓名
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:50:12.209 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_active_employees 第1页
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_active_employees 第1页数据，每页50条
2025-06-26 16:50:12.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:50:12.225 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_active_employees 没有保存的字段映射信息
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 16:50:12.244 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:50:12.251 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:12.264 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:50:12.268 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 16:50:12.295 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:50:12.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 16:50:12.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:12.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:12.338 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.37ms
2025-06-26 16:50:12.338 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:50:12.339 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 16:50:22.072 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:203 | 表 salary_data_2026_01_active_employees 配置不存在，创建新的字段映射配置
2025-06-26 16:50:22.072 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 employee_name 在表 salary_data_2026_01_active_employees 中不存在，创建新字段映射
2025-06-26 16:50:22.088 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_01_active_employees.employee_name -> 姓名
2025-06-26 16:50:22.088 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: employee_name -> 姓名
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, employee_name -> 姓名
2025-06-26 16:50:33.380 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:33.381 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:33.381 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:33.382 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:50:33.384 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:50:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:33.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:33.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:33.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 21.86ms
2025-06-26 16:50:33.409 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 16:50:33.410 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 16:50:43.457 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:43.457 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:43.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第3页, 每页50条
2025-06-26 16:50:43.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:43.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.34ms
2025-06-26 16:50:43.489 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 16
2025-06-26 16:50:43.489 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-26 16:50:51.838 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:51.854 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:51.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第4页, 每页50条
2025-06-26 16:50:51.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第4页数据: 50 行，总计1396行
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:51.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 16:50:51.870 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第4页, 显示50条记录，字段数: 16
2025-06-26 16:50:51.870 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 4
2025-06-26 16:51:00.046 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:51:00.046 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:51:00.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:51:00.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:51:00.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.32ms
2025-06-26 16:51:00.077 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 16:51:00.077 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 16:51:14.096 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:51:14.096 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:51:14.096 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:51:14.096 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:51:14.111 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:51:14.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 22.18ms
2025-06-26 16:51:14.148 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 16:51:14.150 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_refresh:627 | 本地分页数据刷新完成
2025-06-26 16:51:28.684 | INFO     | __main__:main:302 | 应用程序正常退出
