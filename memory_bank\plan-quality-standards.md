# PLAN阶段代码质量标准 (Quality Standards)

**文档版本**: v1.0  
**创建时间**: 2025-06-13 19:30  
**所属阶段**: PLAN阶段 Day 2  
**负责内容**: 代码质量标准、审查流程、检查清单

## 🎯 代码质量目标

### 质量核心原则
1. **可读性优先**: 代码即文档，半年后的自己能看懂
2. **简洁性**: 宁可多写两行，不要复杂逻辑
3. **可维护性**: 遵循SOLID原则，易于扩展和修改
4. **可测试性**: 函数单一职责，便于单元测试
5. **一致性**: 统一的编码风格和命名规范

### 质量量化指标
| 指标类别 | 具体指标 | 目标值 | 检查工具 |
|----------|----------|--------|----------|
| 代码风格 | PEP 8遵循度 | 100% | flake8 |
| 代码复杂度 | 函数最大行数 | ≤ 300行 | pylint |
| 代码复杂度 | 圈复杂度 | ≤ 10 | radon |
| 代码重复 | 重复代码率 | < 5% | vulture |
| 文档覆盖 | 函数文档字符串 | 100% | pydocstyle |
| 类型注解 | 类型覆盖率 | ≥ 90% | mypy |
| 导入规范 | 导入顺序 | 标准化 | isort |

## 📋 代码审查检查清单

### 1. 代码结构审查

#### A. 文件和模块组织 ✅
- [ ] **文件位置正确**: 按功能模块放置在对应的src子目录
- [ ] **模块命名规范**: 小写字母 + 下划线，见名知意
- [ ] **导入顺序规范**: 标准库 → 第三方库 → 本地模块
- [ ] **导入路径**: 使用绝对路径导入自定义模块
- [ ] **循环导入**: 检查是否存在循环导入问题

```python
# ✅ 正确的导入顺序示例
import os
import sys
from datetime import datetime

import pandas as pd
import PyQt5.QtWidgets as widgets

from src.utils.log_config import get_logger
from src.data_import.excel_processor import ExcelProcessor
```

#### B. 函数和类设计 ✅
- [ ] **单一职责**: 每个函数只干一件事
- [ ] **函数长度**: 不超过300行代码
- [ ] **参数数量**: 不超过6个参数，多了考虑使用数据类
- [ ] **返回类型**: 明确的返回类型，避免返回None和实际值混合
- [ ] **副作用控制**: 纯函数优先，有副作用的函数要明确标注

```python
# ✅ 好的函数设计示例
def calculate_salary_difference(
    baseline_salary: Decimal, 
    current_salary: Decimal,
    calculation_date: datetime
) -> Tuple[Decimal, str]:
    """
    计算工资差额
    
    Args:
        baseline_salary: 基准工资
        current_salary: 当前工资  
        calculation_date: 计算日期
        
    Returns:
        (差额, 变化类型描述)
        
    Raises:
        ValueError: 工资金额为负数时
    """
    if baseline_salary < 0 or current_salary < 0:
        raise ValueError("工资金额不能为负数")
    
    difference = current_salary - baseline_salary
    change_type = "增加" if difference > 0 else "减少" if difference < 0 else "无变化"
    
    return difference, change_type
```

### 2. 编码风格审查

#### A. 命名规范 ✅
- [ ] **变量命名**: 小写字母 + 下划线，描述性命名
- [ ] **函数命名**: 动词开头，说明功能
- [ ] **类命名**: 驼峰命名，名词
- [ ] **常量命名**: 大写字母 + 下划线
- [ ] **私有成员**: 下划线开头

```python
# ✅ 正确的命名示例
class SalaryChangeDetector:
    """工资异动检测器"""
    
    MAX_CONFIDENCE_SCORE = 1.0  # 常量
    _baseline_data = None        # 私有成员
    
    def detect_salary_changes(self, employee_data: List[Dict]) -> List[ChangeRecord]:
        """检测工资变化"""
        change_records = []  # 描述性变量名
        
        for employee_info in employee_data:
            current_salary = employee_info.get('salary', 0)
            # ...
```

#### B. 代码格式 ✅
- [ ] **缩进**: 4个空格，不使用Tab
- [ ] **行长度**: 最大88字符（black标准）
- [ ] **空行**: 类定义前后2行，函数定义前后1行
- [ ] **空格**: 运算符前后有空格，逗号后有空格
- [ ] **引号**: 统一使用双引号

```python
# ✅ 正确的格式示例
class DataProcessor:
    """数据处理器"""

    def __init__(self, config_path: str = "config.json"):
        """初始化处理器"""
        self.config = self._load_config(config_path)
        self.logger = get_logger(__name__)

    def process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理数据"""
        if data.empty:
            self.logger.warning("数据为空")
            return data

        # 数据清洗
        cleaned_data = self._clean_data(data)
        
        # 数据验证
        validated_data = self._validate_data(cleaned_data)
        
        return validated_data
```

### 3. 注释和文档审查

#### A. 函数文档字符串 ✅
- [ ] **完整性**: 所有公共函数都有文档字符串
- [ ] **格式规范**: 使用Google或NumPy文档格式
- [ ] **参数说明**: 每个参数的类型和含义
- [ ] **返回值说明**: 返回值的类型和含义
- [ ] **异常说明**: 可能抛出的异常类型和条件

```python
# ✅ 完整的文档字符串示例
def import_excel_data(
    file_path: str,
    sheet_name: Optional[str] = None,
    field_mapping: Optional[Dict[str, str]] = None,
    validate_data: bool = True
) -> ImportResult:
    """
    导入Excel工资数据
    
    从指定的Excel文件中导入工资数据，支持字段映射和数据验证。
    
    Args:
        file_path: Excel文件路径，必须是有效的文件路径
        sheet_name: 工作表名称，为None时使用第一个工作表
        field_mapping: 字段映射字典，{Excel列名: 标准字段名}
        validate_data: 是否执行数据验证，建议保持True
        
    Returns:
        ImportResult: 导入结果对象，包含以下属性：
            - status: 导入状态枚举
            - total_rows: 总行数
            - success_rows: 成功导入行数
            - error_rows: 错误行数
            - warnings: 警告信息列表
            - errors: 错误信息列表
            
    Raises:
        FileNotFoundError: 文件不存在时
        PermissionError: 文件权限不足时
        ValueError: 文件格式不支持时
        
    Example:
        >>> result = import_excel_data("salary_2023_12.xlsx", "工资表")
        >>> if result.status == ImportStatus.SUCCESS:
        ...     print(f"成功导入{result.success_rows}行数据")
    """
    pass
```

#### B. 行内注释 ✅
- [ ] **关键逻辑**: 复杂算法要有注释说明
- [ ] **业务逻辑**: 业务规则要有注释解释
- [ ] **魔术数字**: 数字常量要有注释说明含义
- [ ] **临时方案**: TODO注释标明待改进的地方

```python
# ✅ 合理的行内注释示例
def detect_employee_changes(baseline: pd.DataFrame, current: pd.DataFrame) -> List[ChangeRecord]:
    """检测员工变化"""
    changes = []
    
    # 使用工号作为唯一标识进行匹配
    baseline_ids = set(baseline['工号'].values)
    current_ids = set(current['工号'].values)
    
    # 检测新增员工（在当前期出现但基准期没有）
    new_employees = current_ids - baseline_ids
    for emp_id in new_employees:
        emp_data = current[current['工号'] == emp_id].iloc[0]
        changes.append(ChangeRecord(
            employee_id=emp_id,
            change_type=ChangeType.NEW_EMPLOYEE,
            confidence_score=0.95  # 新员工识别置信度较高
        ))
    
    # TODO: 实现离职员工检测算法
    # TODO: 优化大数据量时的性能
    
    return changes
```

### 4. 错误处理审查

#### A. 异常处理 ✅
- [ ] **异常类型**: 使用具体的异常类型，避免裸except
- [ ] **异常信息**: 提供有用的错误信息
- [ ] **异常传播**: 适当的异常捕获和重新抛出
- [ ] **资源清理**: 使用with语句或finally确保资源释放
- [ ] **日志记录**: 记录异常信息到日志

```python
# ✅ 正确的异常处理示例
def load_excel_file(file_path: str) -> pd.DataFrame:
    """加载Excel文件"""
    logger = get_logger(__name__)
    
    try:
        # 使用with语句确保文件正确关闭
        with pd.ExcelFile(file_path) as excel_file:
            if not excel_file.sheet_names:
                raise ValueError(f"Excel文件 {file_path} 没有工作表")
            
            # 读取第一个工作表
            data = pd.read_excel(excel_file, sheet_name=0)
            logger.info(f"成功读取Excel文件: {file_path}, 共{len(data)}行数据")
            return data
            
    except FileNotFoundError:
        error_msg = f"文件不存在: {file_path}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)
        
    except PermissionError:
        error_msg = f"文件权限不足: {file_path}"
        logger.error(error_msg)
        raise PermissionError(error_msg)
        
    except pd.errors.ExcelFileError as e:
        error_msg = f"Excel文件格式错误: {file_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"读取Excel文件时发生未知错误: {file_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise RuntimeError(error_msg) from e
```

#### B. 边界条件处理 ✅
- [ ] **空值检查**: 检查None、空字符串、空列表
- [ ] **范围检查**: 数值范围、日期范围验证
- [ ] **类型检查**: 运行时类型验证
- [ ] **默认值**: 合理的默认值设置

```python
# ✅ 完善的边界条件处理
def calculate_change_percentage(old_value: Decimal, new_value: Decimal) -> Optional[Decimal]:
    """计算变化百分比"""
    # 空值检查
    if old_value is None or new_value is None:
        return None
    
    # 类型检查
    if not isinstance(old_value, (int, float, Decimal)) or not isinstance(new_value, (int, float, Decimal)):
        raise TypeError("参数必须是数值类型")
    
    # 边界条件：基准值为0
    if old_value == 0:
        return Decimal('100') if new_value > 0 else Decimal('0')
    
    # 范围检查：避免异常大的百分比
    percentage = (new_value - old_value) / old_value * 100
    if abs(percentage) > 10000:  # 超过10000%认为是异常数据
        logger.warning(f"变化百分比异常: {percentage}%, 原值: {old_value}, 新值: {new_value}")
    
    return Decimal(str(percentage)).quantize(Decimal('0.01'))  # 保留2位小数
```

### 5. 性能和安全审查

#### A. 性能考虑 ✅
- [ ] **算法复杂度**: 避免不必要的嵌套循环
- [ ] **内存使用**: 大数据集分批处理
- [ ] **数据库查询**: 避免N+1查询问题
- [ ] **缓存策略**: 合理使用缓存减少重复计算
- [ ] **资源管理**: 及时释放不需要的资源

```python
# ✅ 性能优化示例
def match_employees_optimized(baseline_df: pd.DataFrame, current_df: pd.DataFrame) -> List[EmployeeMatch]:
    """优化的员工匹配算法"""
    # 使用字典提高查找效率，避免O(n²)复杂度
    baseline_dict = baseline_df.set_index('工号').to_dict('index')
    matches = []
    
    # 分批处理大数据集，避免内存溢出
    batch_size = 1000
    for i in range(0, len(current_df), batch_size):
        batch = current_df.iloc[i:i+batch_size]
        
        for _, employee in batch.iterrows():
            emp_id = employee['工号']
            baseline_info = baseline_dict.get(emp_id)
            
            if baseline_info:
                matches.append(EmployeeMatch(
                    employee_id=emp_id,
                    baseline_data=baseline_info,
                    current_data=employee.to_dict()
                ))
    
    return matches
```

#### B. 安全考虑 ✅
- [ ] **输入验证**: 验证所有外部输入
- [ ] **SQL注入**: 使用参数化查询
- [ ] **路径遍历**: 验证文件路径安全性
- [ ] **敏感信息**: 避免在日志中记录敏感数据
- [ ] **权限检查**: 验证文件和目录权限

```python
# ✅ 安全的文件处理示例
def safe_file_path(user_path: str, allowed_dir: str) -> str:
    """安全的文件路径处理"""
    # 规范化路径，防止路径遍历攻击
    normalized_path = os.path.normpath(user_path)
    allowed_path = os.path.normpath(allowed_dir)
    
    # 检查是否在允许的目录内
    if not normalized_path.startswith(allowed_path):
        raise SecurityError(f"文件路径不在允许的目录内: {user_path}")
    
    # 检查文件是否存在且可读
    if not os.path.exists(normalized_path):
        raise FileNotFoundError(f"文件不存在: {normalized_path}")
    
    if not os.access(normalized_path, os.R_OK):
        raise PermissionError(f"文件不可读: {normalized_path}")
    
    return normalized_path
```

## 🔍 代码审查流程

### 1. 审查准备阶段

#### A. 审查前检查 ✅
- [ ] **代码完整性**: 代码功能完整，可以运行
- [ ] **自测通过**: 开发者已进行基本测试
- [ ] **静态检查**: flake8、pylint、mypy检查通过
- [ ] **单元测试**: 相关单元测试编写并通过
- [ ] **文档更新**: 相关文档已更新

#### B. 审查材料准备 ✅
- [ ] **变更说明**: 清晰的变更描述和原因
- [ ] **影响分析**: 变更对其他模块的影响
- [ ] **测试计划**: 对应的测试用例
- [ ] **风险评估**: 潜在风险和应对措施

### 2. 审查执行阶段

#### A. 在线审查清单 ✅
```markdown
## 代码审查清单

### 1. 功能正确性 ✅
- [ ] 代码实现符合需求
- [ ] 逻辑清晰正确
- [ ] 边界条件处理完善
- [ ] 错误处理适当

### 2. 代码质量 ✅
- [ ] 遵循编码规范
- [ ] 命名清晰合理
- [ ] 注释充分准确
- [ ] 结构清晰易懂

### 3. 性能和安全 ✅
- [ ] 算法效率合理
- [ ] 内存使用适当
- [ ] 安全考虑充分
- [ ] 无明显性能瓶颈

### 4. 可维护性 ✅
- [ ] 代码可读性好
- [ ] 模块耦合度低
- [ ] 易于测试
- [ ] 符合项目架构

### 5. 测试完备性 ✅
- [ ] 单元测试覆盖充分
- [ ] 测试用例设计合理
- [ ] 测试数据准备完整
- [ ] 测试结果验证正确
```

#### B. 审查重点 ✅
1. **核心算法**: 异动识别算法的正确性和效率
2. **数据处理**: Excel导入和数据转换的准确性
3. **错误处理**: 异常情况的处理是否完善
4. **性能关键点**: 大数据量处理的性能
5. **安全检查**: 文件操作和数据库访问的安全性

### 3. 审查结果处理

#### A. 审查意见分类 ✅
| 级别 | 说明 | 处理要求 | 示例 |
|------|------|----------|------|
| 🔴 必须修改 | 功能错误、安全问题 | 必须修改才能合并 | 空指针异常、SQL注入风险 |
| 🟡 建议修改 | 代码质量、性能优化 | 建议修改，可协商 | 命名不规范、算法可优化 |
| 🔵 讨论问题 | 设计方案、技术选择 | 需要讨论确认 | 架构设计、技术选型 |
| 🟢 表扬认可 | 好的实现、优秀设计 | 学习借鉴 | 优雅的算法、清晰的逻辑 |

#### B. 审查报告模板 ✅
```markdown
# 代码审查报告

**审查对象**: [模块名称/功能描述]
**审查时间**: [审查日期]
**审查人员**: [审查员姓名]
**代码版本**: [Git提交hash]

## 总体评价
- **功能完整性**: ⭐⭐⭐⭐⭐ (1-5星)
- **代码质量**: ⭐⭐⭐⭐⭐
- **性能表现**: ⭐⭐⭐⭐⭐
- **安全考虑**: ⭐⭐⭐⭐⭐

## 主要发现

### 🔴 必须修改问题
1. [问题描述] - 文件: [文件名:行号]
   - 问题: [具体问题]
   - 影响: [影响范围]
   - 建议: [修改建议]

### 🟡 建议修改问题
1. [问题描述] - 文件: [文件名:行号]
   - 建议: [改进建议]
   - 原因: [改进原因]

### 🟢 优秀实现
1. [好的实现] - 文件: [文件名:行号]
   - 优点: [具体优点]

## 审查结论
- [ ] ✅ 通过，可以合并
- [ ] 🔄 有条件通过，修改后可合并
- [ ] ❌ 不通过，需要重大修改

## 后续行动
- [ ] 开发者修改代码
- [ ] 重新审查
- [ ] 补充测试用例
- [ ] 更新文档
```

## 🛠️ 质量保证工具配置

### 1. 静态代码分析工具

#### A. flake8配置 (.flake8)
```ini
[flake8]
max-line-length = 88
select = E,W,F,C
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
exclude = 
    .git,
    __pycache__,
    venv,
    .venv,
    build,
    dist
per-file-ignores =
    __init__.py:F401  # unused imports in __init__.py
```

#### B. pylint配置 (.pylintrc)
```ini
[MASTER]
init-hook='import sys; sys.path.append("src")'

[MESSAGES CONTROL]
disable=
    C0111,  # missing-docstring
    R0903,  # too-few-public-methods
    R0913,  # too-many-arguments

[FORMAT]
max-line-length=88
max-module-lines=1000

[DESIGN]
max-args=6
max-locals=15
max-returns=6
max-branches=12
max-statements=50
```

#### C. mypy配置 (mypy.ini)
```ini
[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

[mypy-pandas.*]
ignore_missing_imports = True

[mypy-PyQt5.*]
ignore_missing_imports = True
```

### 2. 代码格式化工具

#### A. black配置 (pyproject.toml)
```toml
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.mypy_cache
  | \.pytest_cache
  | \.venv
  | build
  | dist
)/
'''
```

#### B. isort配置 (pyproject.toml)
```toml
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["pandas", "PyQt5", "pytest"]
```

### 3. 质量门禁脚本

#### A. 预提交检查脚本 (pre-commit.sh)
```bash
#!/bin/bash
# 预提交质量检查脚本

echo "🔍 执行代码质量检查..."

# 1. 代码格式化
echo "📝 代码格式化..."
black src/ test/
isort src/ test/

# 2. 静态分析
echo "🔍 静态代码分析..."
flake8 src/ test/
if [ $? -ne 0 ]; then
    echo "❌ flake8检查失败"
    exit 1
fi

pylint src/
if [ $? -ne 0 ]; then
    echo "❌ pylint检查失败" 
    exit 1
fi

# 3. 类型检查
echo "🔍 类型检查..."
mypy src/
if [ $? -ne 0 ]; then
    echo "❌ mypy检查失败"
    exit 1
fi

# 4. 单元测试
echo "🧪 单元测试..."
pytest test/unit/ -v --cov=src --cov-report=term-missing
if [ $? -ne 0 ]; then
    echo "❌ 单元测试失败"
    exit 1
fi

echo "✅ 所有质量检查通过！"
```

#### B. 质量报告生成脚本 (quality-report.sh)
```bash
#!/bin/bash
# 生成代码质量报告

mkdir -p reports

echo "📊 生成代码质量报告..."

# 代码覆盖率报告
pytest test/ --cov=src --cov-report=html:reports/coverage

# 代码复杂度报告
radon cc src/ -a -o reports/complexity.txt

# 代码重复检查
vulture src/ > reports/dead-code.txt

# 安全检查
bandit -r src/ -f html -o reports/security.html

echo "✅ 质量报告生成完成，查看 reports/ 目录"
```

## 📈 质量监控和改进

### 1. 质量指标监控

#### A. 周期性质量检查
| 检查项目 | 检查频率 | 责任人 | 指标要求 |
|----------|----------|--------|----------|
| 代码覆盖率 | 每日 | 开发者 | ≥ 80% |
| 静态分析 | 每次提交 | 开发者 | 0个严重问题 |
| 代码重复率 | 每周 | Tech Lead | < 5% |
| 文档完整性 | 每周 | 开发者 | 100% |
| 性能基准 | 每月 | 团队 | 性能不退化 |

#### B. 质量趋势分析
- **历史趋势图**: 跟踪质量指标的变化趋势
- **问题分析**: 分析常见问题类型和根因
- **改进建议**: 基于数据提出改进措施
- **最佳实践**: 总结和推广好的实践

### 2. 持续改进机制

#### A. 定期回顾
- **周度代码回顾**: 回顾本周代码质量情况
- **月度质量报告**: 生成月度质量分析报告
- **季度标准更新**: 根据实际情况更新质量标准
- **年度总结**: 质量改进成果总结和计划

#### B. 知识分享
- **代码分享会**: 分享优秀代码实现
- **问题案例**: 分析典型问题和解决方案
- **工具培训**: 新工具和技术的培训
- **标准宣讲**: 编码标准的培训和答疑

---

**代码质量标准完成状态**: ✅ 完成  
**下一步行动**: 制定PLAN阶段总结报告  
**质量标准生效时间**: CREATIVE阶段开始  
**质量负责人**: 开发团队全员，Tech Lead监督 