# PLAN阶段技术方案细化 (Technical Design)

**文档版本**: v1.0  
**创建时间**: 2025-06-13 17:50  
**所属阶段**: PLAN阶段  
**负责内容**: API接口设计、数据库schema、技术实现路径

## 🏗️ 系统架构设计

### 整体架构模式
**采用分层架构 + 模块化设计**:
- **展示层 (Presentation)**: PyQt5 用户界面
- **业务逻辑层 (Business)**: 6大核心功能模块
- **数据访问层 (Data Access)**: 数据库操作抽象
- **基础设施层 (Infrastructure)**: 配置、日志、工具类

### 模块间通信机制
**采用事件驱动 + 直接调用混合模式**:
- **同步调用**: 简单数据查询和配置读取
- **异步事件**: 长时间操作(导入、识别、报告生成)
- **消息总线**: 模块间状态通知和数据更新

## 🔌 API接口设计规范

### 接口设计原则
1. **一致性**: 统一的方法命名和参数规范
2. **可扩展性**: 预留扩展参数和版本兼容
3. **错误处理**: 统一的异常类型和错误码
4. **文档化**: 完整的类型注解和文档字符串

### 核心接口定义

#### 1. 数据导入模块接口 (DataImport)

```python
from typing import List, Dict, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass

class ImportStatus(Enum):
    """导入状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ImportResult:
    """导入结果数据类"""
    status: ImportStatus
    total_rows: int
    success_rows: int
    error_rows: int
    warnings: List[str]
    errors: List[str]
    data_summary: Dict[str, Any]

class IDataImporter:
    """数据导入模块接口"""
    
    def detect_file_format(self, file_path: str) -> str:
        """
        检测文件格式
        Args:
            file_path: 文件路径
        Returns:
            文件格式类型 ('xlsx', 'xls', 'csv')
        """
        pass
    
    def validate_file_structure(self, file_path: str) -> Tuple[bool, List[str]]:
        """
        验证文件结构
        Args:
            file_path: 文件路径
        Returns:
            (是否有效, 错误信息列表)
        """
        pass
    
    def import_salary_data(self, 
                          file_path: str, 
                          sheet_name: Optional[str] = None,
                          field_mapping: Optional[Dict[str, str]] = None,
                          progress_callback: Optional[callable] = None) -> ImportResult:
        """
        导入工资数据
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称 (Excel文件用)
            field_mapping: 字段映射配置
            progress_callback: 进度回调函数
        Returns:
            导入结果
        """
        pass
    
    def get_field_suggestions(self, file_path: str) -> Dict[str, List[str]]:
        """
        获取字段映射建议
        Args:
            file_path: 文件路径
        Returns:
            字段映射建议 {标准字段: [可能的列名]}
        """
        pass
```

#### 2. 异动识别模块接口 (ChangeDetection)

```python
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

class ChangeType(Enum):
    """异动类型枚举"""
    NEW_EMPLOYEE = "new_employee"           # 新员工入职
    EMPLOYEE_LEAVE = "employee_leave"       # 员工离职
    SALARY_ADJUSTMENT = "salary_adjustment" # 基本工资调整
    POSITION_CHANGE = "position_change"     # 岗位变动
    ALLOWANCE_CHANGE = "allowance_change"   # 津贴补贴变化
    BONUS_PAYMENT = "bonus_payment"         # 奖金发放
    DEDUCTION = "deduction"                 # 扣款项目
    SPECIAL_PAYMENT = "special_payment"     # 特殊发放
    CORRECTION = "correction"               # 数据更正

@dataclass
class ChangeRecord:
    """异动记录数据类"""
    employee_id: str
    employee_name: str
    change_type: ChangeType
    change_date: datetime
    old_value: Optional[Decimal]
    new_value: Optional[Decimal]
    amount_difference: Decimal
    reason: str
    confidence_score: float  # 识别置信度 0-1
    requires_approval: bool
    additional_info: Dict[str, Any]

class IChangeDetector:
    """异动识别模块接口"""
    
    def establish_baseline(self, 
                          baseline_data: List[Dict], 
                          reference_date: datetime) -> bool:
        """
        建立基准工资数据
        Args:
            baseline_data: 基准数据列表
            reference_date: 基准日期
        Returns:
            是否成功建立基准
        """
        pass
    
    def detect_changes(self, 
                      current_data: List[Dict], 
                      detection_date: datetime) -> List[ChangeRecord]:
        """
        检测工资异动
        Args:
            current_data: 当前期工资数据
            detection_date: 检测日期
        Returns:
            异动记录列表
        """
        pass
    
    def validate_change(self, change_record: ChangeRecord) -> Tuple[bool, List[str]]:
        """
        验证异动记录
        Args:
            change_record: 异动记录
        Returns:
            (是否有效, 警告信息)
        """
        pass
    
    def get_change_statistics(self, 
                             start_date: datetime, 
                             end_date: datetime) -> Dict[str, Any]:
        """
        获取异动统计信息
        Args:
            start_date: 开始日期
            end_date: 结束日期
        Returns:
            统计信息字典
        """
        pass
```

#### 3. 数据存储模块接口 (DataStorage)

```python
from typing import Generator, Union
from abc import ABC, abstractmethod

class IDataRepository(ABC):
    """数据仓库基础接口"""
    
    @abstractmethod
    def create_table(self, table_name: str, schema: Dict[str, str]) -> bool:
        """创建数据表"""
        pass
    
    @abstractmethod
    def insert_data(self, table_name: str, data: List[Dict]) -> int:
        """插入数据"""
        pass
    
    @abstractmethod
    def update_data(self, table_name: str, data: Dict, condition: Dict) -> int:
        """更新数据"""
        pass
    
    @abstractmethod
    def query_data(self, 
                   table_name: str, 
                   condition: Optional[Dict] = None,
                   limit: Optional[int] = None) -> List[Dict]:
        """查询数据"""
        pass

class ISalaryDataStorage:
    """工资数据存储接口"""
    
    def save_salary_data(self, 
                        period: str, 
                        data: List[Dict], 
                        data_type: str = "current") -> bool:
        """
        保存工资数据
        Args:
            period: 期间 (YYYY-MM格式)
            data: 工资数据列表
            data_type: 数据类型 ("current", "baseline", "imported")
        Returns:
            是否保存成功
        """
        pass
    
    def get_salary_data(self, 
                       period: str, 
                       data_type: str = "current") -> List[Dict]:
        """
        获取工资数据
        Args:
            period: 期间
            data_type: 数据类型
        Returns:
            工资数据列表
        """
        pass
    
    def save_change_records(self, changes: List[ChangeRecord]) -> bool:
        """保存异动记录"""
        pass
    
    def get_change_records(self, 
                          period: str, 
                          change_type: Optional[ChangeType] = None) -> List[ChangeRecord]:
        """获取异动记录"""
        pass
    
    def backup_data(self, backup_path: str) -> bool:
        """备份数据"""
        pass
    
    def restore_data(self, backup_path: str) -> bool:
        """恢复数据"""
        pass
```

#### 4. 报告生成模块接口 (ReportGeneration)

```python
from enum import Enum

class ReportType(Enum):
    """报告类型枚举"""
    CHANGE_SUMMARY = "change_summary"       # 异动汇总报告
    INDIVIDUAL_DETAIL = "individual_detail" # 个人异动明细
    DEPARTMENT_STAT = "department_stat"     # 部门统计报告
    APPROVAL_DOC = "approval_doc"           # 审批文档
    CUSTOM = "custom"                       # 自定义报告

@dataclass
class ReportRequest:
    """报告生成请求"""
    report_type: ReportType
    period: str
    template_path: Optional[str]
    output_path: str
    parameters: Dict[str, Any]
    include_charts: bool = False

class IReportGenerator:
    """报告生成模块接口"""
    
    def generate_report(self, 
                       request: ReportRequest,
                       progress_callback: Optional[callable] = None) -> Tuple[bool, str]:
        """
        生成报告
        Args:
            request: 报告生成请求
            progress_callback: 进度回调
        Returns:
            (是否成功, 输出文件路径或错误信息)
        """
        pass
    
    def preview_report(self, request: ReportRequest) -> bytes:
        """
        预览报告
        Args:
            request: 报告请求
        Returns:
            报告预览数据 (PDF bytes)
        """
        pass
    
    def get_available_templates(self, report_type: ReportType) -> List[str]:
        """获取可用模板列表"""
        pass
    
    def validate_template(self, template_path: str) -> Tuple[bool, List[str]]:
        """验证模板有效性"""
        pass
```

#### 5. 系统配置模块接口 (SystemConfig)

```python
class IConfigManager:
    """配置管理接口"""
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        pass
    
    def set_config(self, key: str, value: Any) -> bool:
        """设置配置值"""
        pass
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        pass
    
    def load_config(self) -> bool:
        """从文件加载配置"""
        pass
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        pass
    
    def get_template_paths(self) -> Dict[str, str]:
        """获取模板文件路径配置"""
        pass
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """验证配置有效性"""
        pass
```

## 🗄️ 数据库Schema设计

### 数据库设计原则
1. **动态性**: 支持基于Excel结构的动态表创建
2. **版本控制**: 表结构变更历史追踪
3. **性能优化**: 合理的索引和查询优化
4. **数据完整性**: 外键约束和数据验证

### 核心表结构设计

#### 1. 系统元数据表

```sql
-- 表结构元信息
CREATE TABLE table_schemas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL UNIQUE,
    schema_version INTEGER NOT NULL DEFAULT 1,
    field_definitions TEXT NOT NULL, -- JSON格式字段定义
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 数据版本管理
CREATE TABLE data_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL,
    version_tag TEXT NOT NULL,
    period TEXT NOT NULL, -- YYYY-MM格式
    data_type TEXT NOT NULL, -- 'current', 'baseline', 'imported'
    record_count INTEGER DEFAULT 0,
    checksum TEXT, -- 数据校验和
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(table_name, version_tag, period, data_type)
);

-- 系统配置表
CREATE TABLE system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    value_type TEXT NOT NULL, -- 'string', 'number', 'boolean', 'json'
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 工资数据表(动态创建)

```sql
-- 工资数据表模板 (根据Excel结构动态创建)
-- 表名格式: salary_data_YYYYMM
CREATE TABLE salary_data_template (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL,
    employee_name TEXT NOT NULL,
    department TEXT,
    position TEXT,
    base_salary DECIMAL(10,2),
    -- 其他字段根据Excel动态添加
    import_batch_id TEXT,
    data_source TEXT, -- 'excel_import', 'manual_input', 'system_calc'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引创建示例
CREATE INDEX idx_salary_employee ON salary_data_template(employee_id);
CREATE INDEX idx_salary_department ON salary_data_template(department);
CREATE INDEX idx_salary_period ON salary_data_template(import_batch_id);
```

#### 3. 异动记录表

```sql
-- 异动记录主表
CREATE TABLE change_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL,
    employee_name TEXT NOT NULL,
    change_type TEXT NOT NULL, -- ChangeType枚举值
    change_date DATE NOT NULL,
    period TEXT NOT NULL, -- YYYY-MM
    old_value DECIMAL(10,2),
    new_value DECIMAL(10,2),
    amount_difference DECIMAL(10,2) NOT NULL,
    reason TEXT,
    confidence_score REAL, -- 0-1之间
    requires_approval BOOLEAN DEFAULT FALSE,
    approval_status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    approved_by TEXT,
    approved_at DATETIME,
    additional_info TEXT, -- JSON格式额外信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 异动详细信息表 (一对多关系)
CREATE TABLE change_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    change_record_id INTEGER NOT NULL,
    field_name TEXT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    field_type TEXT, -- 'amount', 'text', 'date', 'number'
    FOREIGN KEY (change_record_id) REFERENCES change_records(id)
);

-- 异动记录索引
CREATE INDEX idx_change_employee ON change_records(employee_id);
CREATE INDEX idx_change_type ON change_records(change_type);
CREATE INDEX idx_change_period ON change_records(period);
CREATE INDEX idx_change_date ON change_records(change_date);
```

#### 4. 基准数据管理表

```sql
-- 基准工资数据表
CREATE TABLE baseline_salary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL,
    employee_name TEXT NOT NULL,
    department TEXT,
    position TEXT,
    base_salary DECIMAL(10,2),
    effective_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT TRUE,
    salary_structure TEXT, -- JSON格式完整工资结构
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 基准数据版本控制
CREATE TABLE baseline_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version_name TEXT NOT NULL UNIQUE,
    effective_date DATE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    employee_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 基准数据索引
CREATE INDEX idx_baseline_employee ON baseline_salary(employee_id);
CREATE INDEX idx_baseline_effective ON baseline_salary(effective_date);
CREATE INDEX idx_baseline_current ON baseline_salary(is_current);
```

#### 5. 报告和模板管理表

```sql
-- 报告生成记录
CREATE TABLE report_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    report_type TEXT NOT NULL,
    period TEXT NOT NULL,
    template_path TEXT,
    output_path TEXT,
    parameters TEXT, -- JSON格式参数
    generation_status TEXT DEFAULT 'pending', -- 'pending', 'success', 'failed'
    error_message TEXT,
    file_size INTEGER,
    generated_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 模板管理表
CREATE TABLE template_registry (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_name TEXT NOT NULL UNIQUE,
    template_type TEXT NOT NULL, -- ReportType枚举值
    file_path TEXT NOT NULL,
    version TEXT DEFAULT '1.0',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_modified DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 报告索引
CREATE INDEX idx_report_type ON report_history(report_type);
CREATE INDEX idx_report_period ON report_history(period);
CREATE INDEX idx_template_type ON template_registry(template_type);
```

### 动态表创建策略

#### 表名规范
```python
def generate_table_name(period: str, data_type: str) -> str:
    """
    生成动态表名
    Args:
        period: 期间 (YYYY-MM)
        data_type: 数据类型
    Returns:
        表名
    """
    period_clean = period.replace('-', '')
    return f"salary_data_{period_clean}_{data_type}"

# 示例:
# salary_data_202506_current    # 2025年6月当前数据
# salary_data_202506_baseline   # 2025年6月基准数据
# salary_data_202506_imported   # 2025年6月导入数据
```

#### 字段映射机制
```python
STANDARD_FIELD_MAPPING = {
    # 标准字段名: (数据库列名, 数据类型, 是否必需)
    'employee_id': ('employee_id', 'TEXT', True),
    'employee_name': ('employee_name', 'TEXT', True),
    'department': ('department', 'TEXT', False),
    'position': ('position', 'TEXT', False),
    'base_salary': ('base_salary', 'DECIMAL(10,2)', False),
    'total_salary': ('total_salary', 'DECIMAL(10,2)', False),
    # 动态字段将根据Excel结构自动添加
}

def create_dynamic_table(table_name: str, excel_columns: List[str]) -> str:
    """
    根据Excel列结构创建动态表
    """
    sql_parts = ["CREATE TABLE {} (".format(table_name)]
    sql_parts.append("id INTEGER PRIMARY KEY AUTOINCREMENT,")
    
    # 添加标准字段
    for field, (col_name, data_type, required) in STANDARD_FIELD_MAPPING.items():
        null_constraint = "NOT NULL" if required else ""
        sql_parts.append(f"{col_name} {data_type} {null_constraint},")
    
    # 添加动态字段 (基于Excel列)
    for col in excel_columns:
        if col not in [mapping[0] for mapping in STANDARD_FIELD_MAPPING.values()]:
            safe_col_name = sanitize_column_name(col)
            sql_parts.append(f"{safe_col_name} TEXT,")
    
    # 添加元数据字段
    sql_parts.extend([
        "import_batch_id TEXT,",
        "data_source TEXT DEFAULT 'excel_import',",
        "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,",
        "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP"
    ])
    
    sql_parts[-1] = sql_parts[-1].rstrip(',')  # 移除最后的逗号
    sql_parts.append(");")
    
    return '\n'.join(sql_parts)
```

## 🛠️ 技术实现路径

### 关键技术实现方案

#### 1. 异动识别核心算法

```python
class ChangeDetectionEngine:
    """异动识别引擎实现"""
    
    def __init__(self, storage: ISalaryDataStorage):
        self.storage = storage
        self.detection_rules = self._load_detection_rules()
    
    def detect_changes(self, current_data: List[Dict], period: str) -> List[ChangeRecord]:
        """
        核心异动识别算法
        """
        changes = []
        baseline_data = self.storage.get_salary_data(period, "baseline")
        baseline_dict = {row['employee_id']: row for row in baseline_data}
        
        for current_row in current_data:
            emp_id = current_row['employee_id']
            
            if emp_id not in baseline_dict:
                # 新员工识别
                change = self._detect_new_employee(current_row, period)
                changes.append(change)
            else:
                baseline_row = baseline_dict[emp_id]
                # 现有员工异动识别
                employee_changes = self._detect_employee_changes(
                    baseline_row, current_row, period
                )
                changes.extend(employee_changes)
        
        # 离职员工识别
        current_emp_ids = {row['employee_id'] for row in current_data}
        for emp_id, baseline_row in baseline_dict.items():
            if emp_id not in current_emp_ids:
                change = self._detect_employee_leave(baseline_row, period)
                changes.append(change)
        
        return changes
    
    def _detect_employee_changes(self, baseline: Dict, current: Dict, period: str) -> List[ChangeRecord]:
        """检测单个员工的异动"""
        changes = []
        
        # 基本工资变化
        if baseline.get('base_salary') != current.get('base_salary'):
            change = ChangeRecord(
                employee_id=current['employee_id'],
                employee_name=current['employee_name'],
                change_type=ChangeType.SALARY_ADJUSTMENT,
                change_date=datetime.now().date(),
                old_value=Decimal(str(baseline.get('base_salary', 0))),
                new_value=Decimal(str(current.get('base_salary', 0))),
                amount_difference=Decimal(str(current.get('base_salary', 0))) - 
                                Decimal(str(baseline.get('base_salary', 0))),
                reason="基本工资调整",
                confidence_score=0.95,
                requires_approval=True,
                additional_info={}
            )
            changes.append(change)
        
        # 岗位变化检测
        if baseline.get('position') != current.get('position'):
            # 岗位变动逻辑...
            pass
        
        # 其他字段变化检测...
        
        return changes
```

#### 2. 动态表管理机制

```python
class DynamicTableManager:
    """动态表管理器"""
    
    def __init__(self, db_connection):
        self.db = db_connection
    
    def create_table_from_excel(self, period: str, excel_path: str, data_type: str = "current") -> bool:
        """根据Excel结构创建动态表"""
        try:
            # 读取Excel结构
            df = pd.read_excel(excel_path, nrows=0)  # 只读取列名
            excel_columns = df.columns.tolist()
            
            # 生成表名
            table_name = generate_table_name(period, data_type)
            
            # 检查表是否已存在
            if self._table_exists(table_name):
                return self._update_table_structure(table_name, excel_columns)
            
            # 创建新表
            create_sql = create_dynamic_table(table_name, excel_columns)
            self.db.execute(create_sql)
            
            # 记录表结构元信息
            self._save_table_schema(table_name, excel_columns)
            
            return True
            
        except Exception as e:
            logger.error(f"创建动态表失败: {e}")
            return False
    
    def _update_table_structure(self, table_name: str, new_columns: List[str]) -> bool:
        """更新表结构以支持新列"""
        try:
            existing_columns = self._get_table_columns(table_name)
            new_cols = set(new_columns) - set(existing_columns)
            
            for col in new_cols:
                safe_col_name = sanitize_column_name(col)
                alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {safe_col_name} TEXT"
                self.db.execute(alter_sql)
            
            return True
        except Exception as e:
            logger.error(f"更新表结构失败: {e}")
            return False
```

#### 3. 模板引擎集成

```python
from docx import Document
from jinja2 import Environment, FileSystemLoader

class WordTemplateEngine:
    """Word模板处理引擎"""
    
    def __init__(self, template_dir: str):
        self.template_dir = template_dir
        self.jinja_env = Environment(loader=FileSystemLoader(template_dir))
    
    def generate_report(self, template_path: str, data: Dict, output_path: str) -> bool:
        """生成Word报告"""
        try:
            # 读取Word模板
            doc = Document(template_path)
            
            # 处理段落中的占位符
            for paragraph in doc.paragraphs:
                self._replace_placeholders(paragraph, data)
            
            # 处理表格中的占位符
            for table in doc.tables:
                self._process_table(table, data)
            
            # 保存结果
            doc.save(output_path)
            return True
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return False
    
    def _replace_placeholders(self, paragraph, data: Dict):
        """替换段落中的占位符"""
        if '{{' in paragraph.text and '}}' in paragraph.text:
            template = self.jinja_env.from_string(paragraph.text)
            paragraph.text = template.render(data)
    
    def _process_table(self, table, data: Dict):
        """处理表格数据"""
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    self._replace_placeholders(paragraph, data)
```

#### 4. 异步操作和进度反馈

```python
from PyQt5.QtCore import QThread, pyqtSignal
import threading

class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self):
        self.active_tasks = {}
    
    def run_async_task(self, task_id: str, task_func: callable, 
                      progress_callback: callable = None) -> QThread:
        """运行异步任务"""
        
        class TaskThread(QThread):
            progress = pyqtSignal(int)
            finished = pyqtSignal(object)
            error = pyqtSignal(str)
            
            def __init__(self, func, callback=None):
                super().__init__()
                self.func = func
                self.callback = callback
                if callback:
                    self.progress.connect(callback)
            
            def run(self):
                try:
                    result = self.func(self.progress.emit)
                    self.finished.emit(result)
                except Exception as e:
                    self.error.emit(str(e))
        
        thread = TaskThread(task_func, progress_callback)
        self.active_tasks[task_id] = thread
        thread.start()
        
        return thread
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.active_tasks:
            thread = self.active_tasks[task_id]
            thread.terminate()
            thread.wait()
            del self.active_tasks[task_id]
            return True
        return False
```

## 🔐 安全和性能考虑

### 数据安全
1. **SQL注入防护**: 使用参数化查询
2. **文件路径验证**: 防止路径遍历攻击
3. **敏感数据加密**: 工资数据本地加密存储
4. **访问控制**: 操作权限和审计日志

### 性能优化
1. **数据库索引**: 关键字段建立索引
2. **批量操作**: 大数据量导入使用批量插入
3. **内存管理**: 大文件分块处理
4. **缓存策略**: 频繁查询数据缓存

### 错误处理
1. **统一异常类**: 自定义异常类型系统
2. **错误恢复**: 关键操作支持回滚
3. **用户友好提示**: 技术错误转换为用户易懂信息
4. **日志记录**: 完整的操作和错误日志

---

**文档状态**: ✅ 技术方案细化完成  
**下一步**: 测试计划制定和PLAN阶段总结  
**预计更新**: PLAN阶段Day 2 完成后 