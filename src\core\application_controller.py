"""
应用控制器 (Application Controller)

作为应用的总指挥，负责协调视图(View)和模型(Model)的交互。
它接收来自视图的用户操作请求，调用相应的服务处理业务逻辑，并更新应用状态。
"""
import re
from PyQt5.QtCore import QObject, QThreadPool, pyqtSlot

from src.core.application_state_service import get_application_state_service
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.worker import Worker


class ApplicationController(QObject):
    """
    处理应用的核心业务逻辑和流程控制。
    """
    def __init__(self, dynamic_table_manager: DynamicTableManager, parent=None):
        super().__init__(parent)
        self.state_service = get_application_state_service()
        self.table_manager = dynamic_table_manager
        self.thread_pool = QThreadPool()

    @pyqtSlot(str)
    def navigate_to_path(self, path: str):
        """
        处理导航变更请求。
        这是一个核心业务流程：
        1.  更新状态为加载中。
        2.  根据路径生成表名。
        3.  在新线程中从数据库加载数据。
        4.  加载成功后，更新数据状态。
        5.  加载失败后，更新错误状态。
        6.  更新状态为加载完成。
        """
        self.state_service.set_loading(True)
        self.state_service.set_navigation_path(path)
        self.state_service.set_status_message(f"正在加载 {path}...", "info")

        table_name = self._generate_table_name_from_path(path.split(' > '))
        self.state_service.set_table_name(table_name)
        
        if not table_name:
            self.state_service.set_data(None)
            self.state_service.set_status_message("导航路径无效，无法加载数据。", "error")
            self.state_service.set_loading(False)
            return

        # 使用工作线程加载数据
        worker = Worker(self._load_data_task, table_name)
        worker.signals.result.connect(self._on_data_loaded)
        worker.signals.error.connect(self._on_data_load_error)
        self.thread_pool.start(worker)

    def _load_data_task(self, table_name: str):
        """在工作线程中执行的数据加载任务。"""
        if not self.table_manager.table_exists(table_name):
            raise FileNotFoundError(f"数据表 '{table_name}' 不存在，请先导入数据。")
        
        df = self.table_manager.get_dataframe_from_table(table_name)
        if df is None or df.empty:
            raise ValueError(f"数据表 '{table_name}' 为空。")
            
        return df

    @pyqtSlot(object)
    def _on_data_loaded(self, df):
        """数据加载成功后的回调。"""
        self.state_service.set_data(df)
        self.state_service.set_status_message("数据加载成功。", "success")
        self.state_service.set_loading(False)

    @pyqtSlot(tuple)
    def _on_data_load_error(self, error_info):
        """数据加载失败后的回调。"""
        exctype, value, traceback = error_info
        error_message = str(value)
        self.state_service.set_data(None)
        self.state_service.set_status_message(f"加载失败: {error_message}", "error")
        self.state_service.set_loading(False)

    def _generate_table_name_from_path(self, path_list: list[str]) -> str:
        """
        根据导航路径列表生成数据库表名。
        这是一个业务逻辑，应当放在Controller中。
        """
        if len(path_list) < 4:
            return ""

        base_name = "salary_data"
        year = re.sub(r'\D', '', path_list[1])
        month = re.sub(r'\D', '', path_list[2]).zfill(2)

        category = path_list[3]
        category_map = {
            "全部在职人员": "active_employees",
            "A岗职工": "a_grade_employees",
            "B岗职工": "b_grade_employees",
            "退休人员": "pension_employees",
            "离休人员": "retired_employees",
            "其他人员": "other_employees"
        }
        category_en = category_map.get(category, "unknown_category")
        
        return f"{base_name}_{year}_{month}_{category_en}"

# --- Singleton Instance ---
_controller_instance = None

def get_application_controller(dynamic_table_manager: DynamicTableManager = None) -> ApplicationController:
    """
    获取应用控制器的全局单例。
    在首次调用时必须提供dynamic_table_manager。
    """
    global _controller_instance
    if _controller_instance is None:
        if dynamic_table_manager is None:
            raise ValueError("首次初始化ApplicationController时必须提供DynamicTableManager实例。")
        _controller_instance = ApplicationController(dynamic_table_manager)
    return _controller_instance 