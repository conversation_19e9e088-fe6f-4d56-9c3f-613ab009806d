"""
月度工资异动处理系统 - 配置管理模块

本模块负责系统配置的统一管理，包括：
- 配置文件的读取和写入
- 配置项的验证和默认值处理
- 模板文件路径管理
- 用户偏好设置

支持的配置格式：
- JSON格式配置文件
- 环境变量覆盖
- 运行时配置更新

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict, field
from src.utils.log_config import get_module_logger, log_function_call, log_file_operation, log_error_with_context

# 模块logger
logger = get_module_logger(__name__)

@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_name: str = "salary_system.db"
    db_path: Optional[str] = None
    backup_enabled: bool = True
    backup_count: int = 5
    connection_timeout: int = 30

@dataclass
class ImportConfig:
    """数据导入配置"""
    supported_formats: list = field(default_factory=lambda: [".xlsx", ".xls", ".csv"])
    max_file_size_mb: int = 100
    chunk_size: int = 1000
    encoding_priority: list = field(default_factory=lambda: ["utf-8", "gbk", "gb2312"])
    
@dataclass
class ProcessingConfig:
    """数据处理配置"""
    match_threshold: float = 0.95
    calculation_precision: int = 2
    async_processing: bool = True
    max_workers: int = 4

@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "default"
    window_width: int = 1200
    window_height: int = 800
    auto_save_interval: int = 300  # 秒
    show_progress: bool = True

@dataclass
class ReportConfig:
    """报告生成配置"""
    default_template_dir: str = "template"
    output_dir: str = "output"
    auto_open_report: bool = True
    export_formats: list = field(default_factory=lambda: ["docx", "pdf", "xlsx"])

@dataclass
class SystemConfig:
    """系统总配置"""
    version: str = "0.1.0"
    debug_mode: bool = False
    log_level: str = "INFO"
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    import_settings: ImportConfig = field(default_factory=ImportConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    reports: ReportConfig = field(default_factory=ReportConfig)

class ConfigManager:
    """
    配置管理器
    
    负责系统配置的加载、保存、验证和管理
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为config.json
        """
        log_function_call("ConfigManager.__init__", config_file=config_file)
        
        # 确定配置文件路径
        if config_file is None:
            self.config_file = self._get_default_config_path()
        else:
            self.config_file = Path(config_file)
        
        # 初始化配置
        self.config = SystemConfig()
        
        # 加载配置
        self.load_config()
        
        logger.info(f"配置管理器初始化完成，配置文件: {self.config_file}")
    
    def _get_default_config_path(self) -> Path:
        """
        获取默认配置文件路径
        
        Returns:
            配置文件的绝对路径
        """
        # 获取项目根目录
        current_file = Path(__file__)
        project_root = current_file.parent.parent.parent  # 回到项目根目录
        
        return project_root / "config.json"
    
    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        try:
            # 项目根目录
            project_root = self.config_file.parent
            
            # 创建必要目录
            directories = [
                project_root / "logs",
                project_root / self.config.reports.default_template_dir,
                project_root / self.config.reports.output_dir,
                project_root / "data"
            ]
            
            for directory in directories:
                directory.mkdir(exist_ok=True)
                logger.debug(f"确保目录存在: {directory}")
                
        except Exception as e:
            log_error_with_context(e, {"操作": "创建必要目录"})
    
    def load_config(self) -> None:
        """
        从文件加载配置
        """
        log_function_call("load_config")
        
        try:
            if self.config_file.exists():
                log_file_operation("读取", self.config_file)
                
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 解析配置数据
                self.config = self._parse_config_dict(config_data)
                logger.info("配置文件加载成功")
            else:
                logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 保存默认配置
            
            # 从环境变量覆盖配置
            self._load_from_environment()
            
            # 确保目录存在
            self._ensure_directories()
            
        except Exception as e:
            log_error_with_context(e, {
                "操作": "加载配置文件",
                "文件路径": str(self.config_file)
            })
            logger.warning("使用默认配置")
    
    def _parse_config_dict(self, config_data: Dict[str, Any]) -> SystemConfig:
        """
        解析配置字典为SystemConfig对象
        
        Args:
            config_data: 配置字典
            
        Returns:
            SystemConfig对象
        """
        try:
            # 处理嵌套配置
            database_data = config_data.get('database', {})
            import_data = config_data.get('import_settings', {})
            processing_data = config_data.get('processing', {})
            ui_data = config_data.get('ui', {})
            reports_data = config_data.get('reports', {})
            
            return SystemConfig(
                version=config_data.get('version', '0.1.0'),
                debug_mode=config_data.get('debug_mode', False),
                log_level=config_data.get('log_level', 'INFO'),
                database=DatabaseConfig(**database_data),
                import_settings=ImportConfig(**import_data),
                processing=ProcessingConfig(**processing_data),
                ui=UIConfig(**ui_data),
                reports=ReportConfig(**reports_data)
            )
        except Exception as e:
            log_error_with_context(e, {"操作": "解析配置数据"})
            logger.warning("配置解析失败，使用默认配置")
            return SystemConfig()
    
    def _load_from_environment(self) -> None:
        """从环境变量加载配置覆盖"""
        env_mappings = {
            'SALARY_SYSTEM_DEBUG': ('debug_mode', bool),
            'SALARY_SYSTEM_LOG_LEVEL': ('log_level', str),
            'SALARY_SYSTEM_DB_PATH': ('database.db_path', str),
            'SALARY_SYSTEM_MAX_WORKERS': ('processing.max_workers', int),
        }
        
        for env_var, (config_path, value_type) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    # 类型转换
                    if value_type == bool:
                        typed_value = env_value.lower() in ('true', '1', 'yes', 'on')
                    elif value_type == int:
                        typed_value = int(env_value)
                    else:
                        typed_value = env_value
                    
                    # 设置配置值
                    self._set_nested_value(config_path, typed_value)
                    logger.debug(f"从环境变量覆盖配置: {config_path} = {typed_value}")
                    
                except Exception as e:
                    logger.warning(f"环境变量 {env_var} 值无效: {env_value}")
    
    def _set_nested_value(self, path: str, value: Any) -> None:
        """设置嵌套配置值"""
        parts = path.split('.')
        obj = self.config
        
        for part in parts[:-1]:
            obj = getattr(obj, part)
        
        setattr(obj, parts[-1], value)
    
    def save_config(self) -> None:
        """
        保存配置到文件
        """
        log_function_call("save_config")
        
        try:
            # 转换为字典
            config_dict = asdict(self.config)
            
            # 确保配置文件目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置
            log_file_operation("写入", self.config_file)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logger.info("配置文件保存成功")
            
        except Exception as e:
            log_error_with_context(e, {
                "操作": "保存配置文件",
                "文件路径": str(self.config_file)
            })
    
    def get_config(self) -> SystemConfig:
        """
        获取当前配置
        
        Returns:
            当前系统配置
        """
        return self.config
    
    def get_section(self, section_name: str) -> Any:
        """
        获取配置的特定部分
        
        Args:
            section_name: 配置部分名称
            
        Returns:
            配置部分的内容
            
        Raises:
            AttributeError: 配置部分不存在
        """
        if hasattr(self.config, section_name):
            return getattr(self.config, section_name)
        else:
            raise AttributeError(f"配置部分 '{section_name}' 不存在")
    
    def update_config(self, **kwargs) -> None:
        """
        更新配置项
        
        Args:
            **kwargs: 要更新的配置项
        """
        log_function_call("update_config", **kwargs)
        
        try:
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    logger.debug(f"更新配置: {key} = {value}")
                else:
                    logger.warning(f"未知配置项: {key}")
            
            # 自动保存
            self.save_config()
            
        except Exception as e:
            log_error_with_context(e, {"操作": "更新配置"})
    
    def get_database_path(self) -> Path:
        """
        获取数据库文件路径
        
        Returns:
            数据库文件的绝对路径
        """
        if self.config.database.db_path:
            return Path(self.config.database.db_path)
        else:
            # 使用默认路径（项目根目录）
            project_root = self.config_file.parent
            return project_root / "data" / self.config.database.db_name
    
    def get_template_path(self, template_name: Optional[str] = None) -> Path:
        """
        获取模板文件路径
        
        Args:
            template_name: 模板文件名，如果为None则返回模板目录
            
        Returns:
            模板文件或目录的绝对路径
        """
        project_root = self.config_file.parent
        template_dir = project_root / self.config.reports.default_template_dir
        
        if template_name:
            return template_dir / template_name
        else:
            return template_dir
    
    def get_output_path(self, output_name: Optional[str] = None) -> Path:
        """
        获取输出文件路径
        
        Args:
            output_name: 输出文件名，如果为None则返回输出目录
            
        Returns:
            输出文件或目录的绝对路径
        """
        project_root = self.config_file.parent
        output_dir = project_root / self.config.reports.output_dir
        
        if output_name:
            return output_dir / output_name
        else:
            return output_dir
    
    def validate_config(self) -> Dict[str, list]:
        """
        验证配置的有效性
        
        Returns:
            验证结果字典，包含错误和警告信息
        """
        log_function_call("validate_config")
        
        errors = []
        warnings = []
        
        try:
            # 验证数据库配置
            if self.config.database.connection_timeout <= 0:
                errors.append("数据库连接超时时间必须大于0")
            
            # 验证导入配置
            if self.config.import_settings.max_file_size_mb <= 0:
                errors.append("最大文件大小必须大于0")
            
            if self.config.import_settings.chunk_size <= 0:
                errors.append("数据块大小必须大于0")
            
            # 验证处理配置
            if not 0 < self.config.processing.match_threshold <= 1:
                errors.append("匹配阈值必须在0和1之间")
            
            if self.config.processing.max_workers <= 0:
                errors.append("最大工作线程数必须大于0")
            
            # 验证UI配置
            if self.config.ui.window_width <= 0 or self.config.ui.window_height <= 0:
                errors.append("窗口尺寸必须大于0")
            
            # 验证路径是否存在（警告）
            template_path = self.get_template_path()
            if not template_path.exists():
                warnings.append(f"模板目录不存在: {template_path}")
            
            logger.info(f"配置验证完成: {len(errors)} 个错误, {len(warnings)} 个警告")
            
        except Exception as e:
            log_error_with_context(e, {"操作": "验证配置"})
            errors.append(f"配置验证过程中发生错误: {str(e)}")
        
        return {
            'errors': errors,
            'warnings': warnings,
            'valid': len(errors) == 0
        }

# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        ConfigManager实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_config() -> SystemConfig:
    """
    获取当前系统配置
    
    Returns:
        SystemConfig实例
    """
    return get_config_manager().get_config()

# 模块导出
__all__ = [
    "SystemConfig",
    "DatabaseConfig", 
    "ImportConfig",
    "ProcessingConfig",
    "UIConfig",
    "ReportConfig",
    "ConfigManager",
    "get_config_manager",
    "get_config"
]

if __name__ == "__main__":
    # 测试代码
    print("测试配置管理模块...")
    
    # 创建配置管理器
    config_mgr = ConfigManager("test_config.json")
    
    # 获取配置
    config = config_mgr.get_config()
    print(f"当前版本: {config.version}")
    print(f"调试模式: {config.debug_mode}")
    print(f"数据库名称: {config.database.db_name}")
    
    # 更新配置
    config_mgr.update_config(debug_mode=True, log_level="DEBUG")
    
    # 验证配置
    validation = config_mgr.validate_config()
    print(f"配置验证结果: {validation}")
    
    # 获取路径
    db_path = config_mgr.get_database_path()
    template_path = config_mgr.get_template_path()
    
    print(f"数据库路径: {db_path}")
    print(f"模板路径: {template_path}")
    
    print("配置管理模块测试完成！") 