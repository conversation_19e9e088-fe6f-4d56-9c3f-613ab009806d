"""
月度工资异动处理系统 - 主窗口

本模块实现系统的主窗口界面，提供系统的主要入口和功能导航。

主要功能:
1. 主界面布局和导航
2. 菜单栏和工具栏管理
3. 各功能模块的调度
4. 状态信息显示
5. 主题和响应式布局
"""

import sys
import os
from typing import Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QMenuBar, QToolBar, QStatusBar, QAction, QTabWidget,
    QLabel, QPushButton, QTextEdit, QSplitter, QFrame,
    QMessageBox, QFileDialog, QProgressBar, QGroupBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize
from PyQt5.QtGui import QIcon, QFont, QPixmap, QPalette, QColor

# 导入项目核心模块
from src.utils.log_config import setup_logger
from src.core.config_manager import ConfigManager
from src.modules.system_config import UserPreferences
from src.modules.data_import import ExcelImporter, DataValidator
from src.modules.change_detection import ChangeDetector, BaselineManager
from src.modules.report_generation import ReportManager

# 导入GUI子模块
from .dialogs import DataImportDialog, SettingsDialog, AboutDialog, ProgressDialog
from .change_detection_dialog import ChangeDetectionDialog
from .windows import ChangeDetectionWindow, ReportGenerationWindow
from src.gui.widgets import DataTableWidget

class MainWindow(QMainWindow):
    """
    系统主窗口类
    
    负责系统的主界面显示和功能导航，协调各个功能模块的工作。
    """
    
    # 信号定义
    status_message = pyqtSignal(str)  # 状态消息信号
    progress_update = pyqtSignal(int)  # 进度更新信号
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化日志
        self.logger = setup_logger(__name__)
        
        # 初始化配置管理器
        try:
            self.config_manager = ConfigManager()
            self.user_preferences = UserPreferences()
            self.logger.info("配置管理器初始化成功")
        except Exception as e:
            self.logger.error(f"配置管理器初始化失败: {e}")
            QMessageBox.critical(self, "错误", f"配置管理器初始化失败: {e}")
            sys.exit(1)
        
        # 初始化功能模块
        self._init_modules()
        
        # 初始化界面
        self._init_ui()
        
        # 连接信号和槽
        self._connect_signals()
        
        # 应用用户偏好设置
        self._apply_user_preferences()
        
        # 显示欢迎信息
        self._show_welcome_message()
        
        self.logger.info("主窗口初始化完成")
    
    def _init_modules(self):
        """初始化功能模块"""
        try:
            # 数据导入模块
            self.excel_importer = ExcelImporter()
            self.data_validator = DataValidator()
            
            # 异动检测模块 - 需要创建依赖的管理器
            self.baseline_manager = BaselineManager(self.config_manager)
            self.change_detector = ChangeDetector(self.config_manager, self.baseline_manager)
            
            # 报告生成模块
            self.report_manager = ReportManager()
            
            # 子窗口
            self.change_detection_window = None
            self.report_generation_window = None
            
            self.logger.info("功能模块初始化成功")
            
        except Exception as e:
            self.logger.error(f"功能模块初始化失败: {e}")
            raise
    
    def _init_ui(self):
        """初始化用户界面"""
        # 设置窗口基本属性
        self.setWindowTitle("月度工资异动处理系统 v1.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口图标
        self._set_window_icon()
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 设置样式
        self._set_window_style()
    
    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            # 尝试设置自定义图标
            icon_path = os.path.join("resources", "icons", "app_icon.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            else:
                # 使用默认图标
                self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")
    
    def _create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建欢迎区域
        self._create_welcome_area(main_layout)
        
        # 创建功能选项卡
        self._create_function_tabs(main_layout)
        
        # 创建数据显示区域
        self._create_data_display_area(main_layout)
    
    def _create_welcome_area(self, parent_layout):
        """创建欢迎区域"""
        welcome_frame = QFrame()
        welcome_frame.setFrameStyle(QFrame.StyledPanel)
        welcome_frame.setMaximumHeight(100)
        
        welcome_layout = QHBoxLayout(welcome_frame)
        
        # 系统标题
        title_label = QLabel("月度工资异动处理系统")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        # 系统描述
        desc_label = QLabel("自动化处理工资异动数据，生成标准化报告")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: gray;")
        
        # 布局
        title_layout = QVBoxLayout()
        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)
        
        welcome_layout.addStretch()
        welcome_layout.addLayout(title_layout)
        welcome_layout.addStretch()
        
        parent_layout.addWidget(welcome_frame)
    
    def _create_function_tabs(self, parent_layout):
        """创建功能选项卡"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # 数据导入选项卡
        self._create_import_tab()
        
        # 异动检测选项卡
        self._create_detection_tab()
        
        # 报告生成选项卡
        self._create_report_tab()
        
        # 系统设置选项卡
        self._create_settings_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_import_tab(self):
        """创建数据导入选项卡"""
        import_widget = QWidget()
        import_layout = QVBoxLayout(import_widget)
        
        # 导入功能组
        import_group = QGroupBox("数据导入")
        import_group_layout = QGridLayout(import_group)
        
        # 导入按钮
        import_excel_btn = QPushButton("导入Excel工资表")
        import_excel_btn.setMinimumHeight(40)
        import_excel_btn.clicked.connect(self.show_import_dialog)
        
        import_baseline_btn = QPushButton("导入基准数据")
        import_baseline_btn.setMinimumHeight(40)
        import_baseline_btn.clicked.connect(self.import_baseline_data)
        
        # 数据验证按钮
        validate_btn = QPushButton("验证数据完整性")
        validate_btn.setMinimumHeight(40)
        validate_btn.clicked.connect(self.validate_imported_data)
        
        # 布局
        import_group_layout.addWidget(import_excel_btn, 0, 0)
        import_group_layout.addWidget(import_baseline_btn, 0, 1)
        import_group_layout.addWidget(validate_btn, 1, 0, 1, 2)
        
        import_layout.addWidget(import_group)
        import_layout.addStretch()
        
        self.tab_widget.addTab(import_widget, "数据导入")
    
    def _create_detection_tab(self):
        """创建异动检测选项卡"""
        detection_widget = QWidget()
        detection_layout = QVBoxLayout(detection_widget)
        
        # 检测功能组
        detection_group = QGroupBox("异动检测")
        detection_group_layout = QGridLayout(detection_group)
        
        # 检测按钮
        detect_changes_btn = QPushButton("开始异动检测")
        detect_changes_btn.setMinimumHeight(40)
        detect_changes_btn.clicked.connect(self.start_change_detection)
        
        view_results_btn = QPushButton("查看检测结果")
        view_results_btn.setMinimumHeight(40)
        view_results_btn.clicked.connect(self.show_detection_window)
        
        export_changes_btn = QPushButton("导出异动数据")
        export_changes_btn.setMinimumHeight(40)
        export_changes_btn.clicked.connect(self.export_change_data)
        
        # 布局
        detection_group_layout.addWidget(detect_changes_btn, 0, 0)
        detection_group_layout.addWidget(view_results_btn, 0, 1)
        detection_group_layout.addWidget(export_changes_btn, 1, 0, 1, 2)
        
        detection_layout.addWidget(detection_group)
        detection_layout.addStretch()
        
        self.tab_widget.addTab(detection_widget, "异动检测")
    
    def _create_report_tab(self):
        """创建报告生成选项卡"""
        report_widget = QWidget()
        report_layout = QVBoxLayout(report_widget)
        
        # 报告功能组
        report_group = QGroupBox("报告生成")
        report_group_layout = QGridLayout(report_group)
        
        # 报告生成按钮
        generate_word_btn = QPushButton("生成Word报告")
        generate_word_btn.setMinimumHeight(40)
        generate_word_btn.clicked.connect(self.generate_word_report)
        
        generate_excel_btn = QPushButton("生成Excel报告")
        generate_excel_btn.setMinimumHeight(40)
        generate_excel_btn.clicked.connect(self.generate_excel_report)
        
        preview_report_btn = QPushButton("预览报告")
        preview_report_btn.setMinimumHeight(40)
        preview_report_btn.clicked.connect(self.show_report_window)
        
        # 布局
        report_group_layout.addWidget(generate_word_btn, 0, 0)
        report_group_layout.addWidget(generate_excel_btn, 0, 1)
        report_group_layout.addWidget(preview_report_btn, 1, 0, 1, 2)
        
        report_layout.addWidget(report_group)
        report_layout.addStretch()
        
        self.tab_widget.addTab(report_widget, "报告生成")
    
    def _create_settings_tab(self):
        """创建系统设置选项卡"""
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        
        # 设置功能组
        settings_group = QGroupBox("系统设置")
        settings_group_layout = QGridLayout(settings_group)
        
        # 设置按钮
        config_btn = QPushButton("系统配置")
        config_btn.setMinimumHeight(40)
        config_btn.clicked.connect(self.show_settings_dialog)
        
        template_btn = QPushButton("模板管理")
        template_btn.setMinimumHeight(40)
        template_btn.clicked.connect(self.manage_templates)
        
        backup_btn = QPushButton("数据备份")
        backup_btn.setMinimumHeight(40)
        backup_btn.clicked.connect(self.backup_data)
        
        about_btn = QPushButton("关于系统")
        about_btn.setMinimumHeight(40)
        about_btn.clicked.connect(self.show_about_dialog)
        
        # 布局
        settings_group_layout.addWidget(config_btn, 0, 0)
        settings_group_layout.addWidget(template_btn, 0, 1)
        settings_group_layout.addWidget(backup_btn, 1, 0)
        settings_group_layout.addWidget(about_btn, 1, 1)
        
        settings_layout.addWidget(settings_group)
        settings_layout.addStretch()
        
        self.tab_widget.addTab(settings_widget, "系统设置")
    
    def _create_data_display_area(self, parent_layout):
        """创建数据显示区域"""
        # 创建数据表格控件
        self.data_table = DataTableWidget()
        self.data_table.setMinimumHeight(200)
        
        parent_layout.addWidget(self.data_table, 1)  # 拉伸比例为1
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 导入动作
        import_action = QAction('导入数据(&I)', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.show_import_dialog)
        file_menu.addAction(import_action)
        
        # 导出动作
        export_action = QAction('导出数据(&E)', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_change_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出动作
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 异动检测动作
        detect_action = QAction('异动检测(&D)', self)
        detect_action.setShortcut('F5')
        detect_action.triggered.connect(self.start_change_detection)
        tools_menu.addAction(detect_action)
        
        # 报告生成动作
        report_action = QAction('生成报告(&R)', self)
        report_action.setShortcut('F6')
        report_action.triggered.connect(self.show_report_window)
        tools_menu.addAction(report_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu('设置(&S)')
        
        # 系统设置动作
        config_action = QAction('系统设置(&S)', self)
        config_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(config_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于动作
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setIconSize(QSize(32, 32))
        
        # 导入工具
        import_action = toolbar.addAction('导入')
        import_action.triggered.connect(self.show_import_dialog)
        
        toolbar.addSeparator()
        
        # 检测工具
        detect_action = toolbar.addAction('检测')
        detect_action.triggered.connect(self.start_change_detection)
        
        # 报告工具
        report_action = toolbar.addAction('报告')
        report_action.triggered.connect(self.show_report_window)
        
        toolbar.addSeparator()
        
        # 设置工具
        settings_action = toolbar.addAction('设置')
        settings_action.triggered.connect(self.show_settings_dialog)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
        self.timer.start(1000)  # 每秒更新
        self._update_time()
    
    def _update_time(self):
        """更新状态栏时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def _set_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.status_message.connect(self.show_status_message)
        self.progress_update.connect(self.update_progress)
    
    def _apply_user_preferences(self):
        """应用用户偏好设置"""
        try:
            preferences = self.user_preferences.get_all_preferences()
            
            # 应用窗口设置
            if 'window_geometry' in preferences:
                self.restoreGeometry(preferences['window_geometry'])
            
            # 应用主题设置
            if 'theme' in preferences:
                self._apply_theme(preferences['theme'])
                
        except Exception as e:
            self.logger.warning(f"应用用户偏好设置失败: {e}")
    
    def _apply_theme(self, theme_name: str):
        """应用主题"""
        # 这里可以实现不同主题的切换
        pass
    
    def _show_welcome_message(self):
        """显示欢迎信息"""
        self.show_status_message("欢迎使用月度工资异动处理系统")
    
    # === 槽函数 ===
    
    def show_status_message(self, message: str, timeout: int = 5000):
        """显示状态消息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))
        self.logger.info(f"状态消息: {message}")
    
    def update_progress(self, value: int):
        """更新进度条"""
        self.progress_bar.setValue(value)
        if value == 0:
            self.progress_bar.setVisible(False)
        else:
            self.progress_bar.setVisible(True)
    
    def show_import_dialog(self):
        """显示数据导入对话框"""
        try:
            dialog = DataImportDialog(self)
            if dialog.exec_() == dialog.Accepted:
                # 获取导入的数据
                imported_data = dialog.get_imported_data()
                if imported_data and imported_data.get('data'):
                    # 显示导入的数据到主界面
                    self.data_table.set_data(
                        imported_data['data'], 
                        imported_data['headers']
                    )
                    self.show_status_message(f"数据导入成功，共导入 {len(imported_data['data'])} 行数据")
                else:
                    self.show_status_message("数据导入成功")
                # 刷新数据显示
                self.refresh_data_display()
        except Exception as e:
            self.logger.error(f"显示导入对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"显示导入对话框失败: {e}")
    
    def import_baseline_data(self):
        """导入基准数据 - 已整合到数据导入流程中"""
        try:
            # 引导用户使用数据导入功能，并勾选"设为基准数据"选项
            QMessageBox.information(
                self, "提示", 
                "基准数据导入已整合到数据导入功能中。\n\n"
                "请使用菜单：文件 → 数据导入\n"
                "在导入时勾选'设为基准数据'选项。"
            )
            
            # 直接打开数据导入对话框
            self.show_import_dialog()
            
        except Exception as e:
            self.logger.error(f"导入基准数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入基准数据失败: {e}")
    
    def validate_imported_data(self):
        """验证导入的数据"""
        try:
            # 创建进度对话框
            progress_dialog = ProgressDialog("数据验证中...", self)
            progress_dialog.show()
            
            # 这里实现数据验证逻辑
            # 模拟验证过程
            for i in range(101):
                progress_dialog.update_progress(i)
                if progress_dialog.wasCanceled():
                    break
                QTimer.singleShot(10, lambda: None)  # 模拟延迟
            
            progress_dialog.close()
            self.show_status_message("数据验证完成")
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            QMessageBox.critical(self, "错误", f"数据验证失败: {e}")
    
    def start_change_detection(self):
        """开始异动检测"""
        try:
            dialog = ChangeDetectionDialog(self)
            
            # 连接检测完成信号
            dialog.detection_completed.connect(self._on_detection_completed)
            
            # 显示对话框
            if dialog.exec_() == dialog.Accepted:
                result = dialog.get_detection_result()
                if result:
                    self.show_status_message(f"异动检测完成，发现 {result['detection_summary']['total_changes']} 项异动")
                else:
                    self.show_status_message("异动检测已取消")
            
        except Exception as e:
            self.logger.error(f"异动检测失败: {e}")
            QMessageBox.critical(self, "错误", f"异动检测失败: {e}")
    
    def show_detection_window(self):
        """显示异动检测窗口"""
        try:
            # 使用新的异动检测对话框
            self.start_change_detection()
            
        except Exception as e:
            self.logger.error(f"显示检测窗口失败: {e}")
            QMessageBox.critical(self, "错误", f"显示检测窗口失败: {e}")
    
    def _on_detection_completed(self, result):
        """处理异动检测完成"""
        try:
            # 可以在这里处理检测结果，比如更新界面显示
            total_changes = result['detection_summary']['total_changes']
            net_change = result['detection_summary']['net_change']
            
            summary_text = f"""异动检测完成！
发现异动: {total_changes} 项
净变动: {net_change:,.2f} 元
检测期间: {result['baseline_period']} → {result['current_period']}"""
            
            QMessageBox.information(self, "检测完成", summary_text)
            
        except Exception as e:
            self.logger.error(f"处理检测结果失败: {e}")
    
    def export_change_data(self):
        """导出异动数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出异动数据", "", "Excel文件 (*.xlsx)"
            )
            if file_path:
                # 这里实现数据导出逻辑
                self.show_status_message(f"异动数据导出: {os.path.basename(file_path)}")
        except Exception as e:
            self.logger.error(f"导出异动数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出异动数据失败: {e}")
    
    def generate_word_report(self):
        """生成Word报告"""
        try:
            self.show_status_message("生成Word报告中...")
            # 这里实现Word报告生成逻辑
            self.show_status_message("Word报告生成完成")
        except Exception as e:
            self.logger.error(f"生成Word报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成Word报告失败: {e}")
    
    def generate_excel_report(self):
        """生成Excel报告"""
        try:
            self.show_status_message("生成Excel报告中...")
            # 这里实现Excel报告生成逻辑
            self.show_status_message("Excel报告生成完成")
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成Excel报告失败: {e}")
    
    def show_report_window(self):
        """显示报告生成窗口"""
        try:
            if self.report_generation_window is None:
                self.report_generation_window = ReportGenerationWindow(self)
            self.report_generation_window.show()
            self.report_generation_window.raise_()
        except Exception as e:
            self.logger.error(f"显示报告窗口失败: {e}")
            QMessageBox.critical(self, "错误", f"显示报告窗口失败: {e}")
    
    def show_settings_dialog(self):
        """显示系统设置对话框"""
        try:
            dialog = SettingsDialog(self)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"显示设置对话框失败: {e}")
    
    def manage_templates(self):
        """管理模板"""
        try:
            self.show_status_message("模板管理功能开发中...")
        except Exception as e:
            self.logger.error(f"模板管理失败: {e}")
            QMessageBox.critical(self, "错误", f"模板管理失败: {e}")
    
    def backup_data(self):
        """数据备份"""
        try:
            backup_path, _ = QFileDialog.getSaveFileName(
                self, "选择备份位置", "", "备份文件 (*.backup)"
            )
            if backup_path:
                # 这里实现数据备份逻辑
                self.show_status_message(f"数据备份完成: {os.path.basename(backup_path)}")
        except Exception as e:
            self.logger.error(f"数据备份失败: {e}")
            QMessageBox.critical(self, "错误", f"数据备份失败: {e}")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            dialog = AboutDialog(self)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示关于对话框失败: {e}")
            QMessageBox.information(self, "关于", "月度工资异动处理系统 v1.0")
    
    def refresh_data_display(self):
        """刷新数据显示"""
        try:
            # 这里实现数据刷新逻辑
            self.data_table.refresh_data()
        except Exception as e:
            self.logger.error(f"刷新数据显示失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 保存用户偏好设置
            window_geometry = self.saveGeometry()
            # 将QByteArray转换为可序列化的格式
            if hasattr(window_geometry, 'data'):
                # 转换为base64字符串
                import base64
                geometry_data = base64.b64encode(window_geometry.data()).decode('utf-8')
                self.user_preferences.set_preference('window_geometry', geometry_data)
            
            # 关闭子窗口
            if self.change_detection_window:
                self.change_detection_window.close()
            if self.report_generation_window:
                self.report_generation_window.close()
            
            self.logger.info("主窗口正常关闭")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭事件处理失败: {e}")
            event.accept()  # 即使有错误也允许关闭 