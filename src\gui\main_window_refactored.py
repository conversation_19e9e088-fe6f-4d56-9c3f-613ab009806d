"""
月度工资异动处理系统 - 主窗口 (重构版)

本模块实现系统的主窗口界面，专注于GUI展示和用户交互，
通过应用服务层统一管理业务逻辑，实现高内聚低耦合。

重构改进:
1. 移除直接业务模块依赖，仅依赖应用服务层
2. 分离UI逻辑和业务逻辑
3. 大幅简化代码复杂度
4. 提高可测试性和可维护性
"""

import sys
import os
from typing import Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QMenuBar, QToolBar, QStatusBar, QAction, QTabWidget,
    QLabel, QPushButton, QTextEdit, QSplitter, QFrame,
    QMessageBox, QFileDialog, QProgressBar, QGroupBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize
from PyQt5.QtGui import QIcon, QFont, QPixmap, QPalette, QColor

# 仅依赖应用服务层和GUI子模块 - 大幅减少耦合
from src.core.application_service import ApplicationService, ServiceResult
from src.utils.log_config import setup_logger

# 导入GUI子模块
from .dialogs import DataImportDialog, SettingsDialog, AboutDialog, ProgressDialog
from .windows import ChangeDetectionWindow, ReportGenerationWindow
from src.gui.widgets import DataTableWidget


class MainWindow(QMainWindow):
    """
    系统主窗口类 (重构版)
    
    专注于GUI界面管理和用户交互，通过应用服务层访问业务逻辑。
    
    重构原则:
    - 单一职责: 仅负责UI层逻辑
    - 依赖倒置: 依赖抽象的应用服务层
    - 高内聚低耦合: 减少外部依赖
    """
    
    # 信号定义
    status_message = pyqtSignal(str)  # 状态消息信号
    progress_update = pyqtSignal(int)  # 进度更新信号
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化日志
        self.logger = setup_logger(__name__)
        
        # 初始化应用服务层 - 唯一的业务逻辑入口
        self.app_service = ApplicationService()
        
        # 初始化应用服务
        self._init_application_service()
        
        # 初始化界面
        self._init_ui()
        
        # 连接信号和槽
        self._connect_signals()
        
        # 应用用户偏好设置
        self._apply_user_preferences()
        
        # 最后强制确保UI元素可见（防止被用户偏好覆盖）
        self._force_show_ui_elements()
        
        # 显示欢迎信息
        self._show_welcome_message()
        
        self.logger.info("主窗口初始化完成")
    
    def _init_application_service(self):
        """初始化应用服务层"""
        try:
            result = self.app_service.initialize()
            if not result.success:
                self.logger.error(f"应用服务初始化失败: {result.message}")
                QMessageBox.critical(self, "错误", f"应用服务初始化失败: {result.message}")
                sys.exit(1)
            
            self.logger.info("应用服务初始化成功")
            
        except Exception as e:
            self.logger.error(f"应用服务初始化异常: {e}")
            QMessageBox.critical(self, "错误", f"应用服务初始化异常: {e}")
            sys.exit(1)
    
    def _init_ui(self):
        """初始化用户界面"""
        # 设置窗口基本属性
        self.setWindowTitle("月度工资异动处理系统 v1.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口图标
        self._set_window_icon()
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 强制显示UI元素
        self._ensure_ui_visibility()
        
        # 设置样式
        self._set_window_style()
    
    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            # 尝试设置自定义图标
            icon_path = os.path.join("resources", "icons", "app_icon.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            else:
                # 使用默认图标
                self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")
    
    def _create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建欢迎区域
        self._create_welcome_area(main_layout)
        
        # 创建功能选项卡
        self._create_function_tabs(main_layout)
        
        # 创建数据显示区域
        self._create_data_display_area(main_layout)
    
    def _create_welcome_area(self, parent_layout):
        """创建欢迎区域"""
        welcome_frame = QFrame()
        welcome_frame.setFrameStyle(QFrame.StyledPanel)
        welcome_frame.setMaximumHeight(100)
        
        welcome_layout = QHBoxLayout(welcome_frame)
        
        # 系统标题
        title_label = QLabel("月度工资异动处理系统")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        # 系统描述
        desc_label = QLabel("自动化处理工资异动数据，生成标准化报告")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: gray;")
        
        # 布局
        title_layout = QVBoxLayout()
        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)
        
        welcome_layout.addStretch()
        welcome_layout.addLayout(title_layout)
        welcome_layout.addStretch()
        
        parent_layout.addWidget(welcome_frame)
    
    def _create_function_tabs(self, parent_layout):
        """创建功能选项卡"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # 创建各个选项卡
        self._create_import_tab()
        self._create_detection_tab()
        self._create_report_tab()
        self._create_settings_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_import_tab(self):
        """创建数据导入选项卡"""
        import_widget = QWidget()
        import_layout = QVBoxLayout(import_widget)
        
        # 导入功能组
        import_group = QGroupBox("数据导入")
        import_group_layout = QGridLayout(import_group)
        
        # 导入按钮
        import_excel_btn = QPushButton("导入Excel工资表")
        import_excel_btn.setMinimumHeight(40)
        import_excel_btn.clicked.connect(self.show_import_dialog)
        
        import_baseline_btn = QPushButton("导入基准数据")
        import_baseline_btn.setMinimumHeight(40)
        import_baseline_btn.clicked.connect(self.import_baseline_data)
        
        # 数据验证按钮
        validate_btn = QPushButton("验证数据完整性")
        validate_btn.setMinimumHeight(40)
        validate_btn.clicked.connect(self.validate_imported_data)
        
        # 布局
        import_group_layout.addWidget(import_excel_btn, 0, 0)
        import_group_layout.addWidget(import_baseline_btn, 0, 1)
        import_group_layout.addWidget(validate_btn, 1, 0, 1, 2)
        
        import_layout.addWidget(import_group)
        import_layout.addStretch()
        
        self.tab_widget.addTab(import_widget, "数据导入")
    
    def _create_detection_tab(self):
        """创建异动检测选项卡"""
        detection_widget = QWidget()
        detection_layout = QVBoxLayout(detection_widget)
        
        # 检测功能组
        detection_group = QGroupBox("异动检测")
        detection_group_layout = QGridLayout(detection_group)
        
        # 检测按钮
        detect_changes_btn = QPushButton("开始异动检测")
        detect_changes_btn.setMinimumHeight(40)
        detect_changes_btn.clicked.connect(self.start_change_detection)
        
        view_results_btn = QPushButton("查看检测结果")
        view_results_btn.setMinimumHeight(40)
        view_results_btn.clicked.connect(self.show_detection_window)
        
        export_changes_btn = QPushButton("导出异动数据")
        export_changes_btn.setMinimumHeight(40)
        export_changes_btn.clicked.connect(self.export_change_data)
        
        # 布局
        detection_group_layout.addWidget(detect_changes_btn, 0, 0)
        detection_group_layout.addWidget(view_results_btn, 0, 1)
        detection_group_layout.addWidget(export_changes_btn, 1, 0, 1, 2)
        
        detection_layout.addWidget(detection_group)
        detection_layout.addStretch()
        
        self.tab_widget.addTab(detection_widget, "异动检测")
    
    def _create_report_tab(self):
        """创建报告生成选项卡"""
        report_widget = QWidget()
        report_layout = QVBoxLayout(report_widget)
        
        # 报告功能组
        report_group = QGroupBox("报告生成")
        report_group_layout = QGridLayout(report_group)
        
        # 报告生成按钮
        generate_word_btn = QPushButton("生成Word报告")
        generate_word_btn.setMinimumHeight(40)
        generate_word_btn.clicked.connect(self.generate_word_report)
        
        generate_excel_btn = QPushButton("生成Excel报告")
        generate_excel_btn.setMinimumHeight(40)
        generate_excel_btn.clicked.connect(self.generate_excel_report)
        
        preview_report_btn = QPushButton("预览报告")
        preview_report_btn.setMinimumHeight(40)
        preview_report_btn.clicked.connect(self.show_report_window)
        
        # 布局
        report_group_layout.addWidget(generate_word_btn, 0, 0)
        report_group_layout.addWidget(generate_excel_btn, 0, 1)
        report_group_layout.addWidget(preview_report_btn, 1, 0, 1, 2)
        
        report_layout.addWidget(report_group)
        report_layout.addStretch()
        
        self.tab_widget.addTab(report_widget, "报告生成")
    
    def _create_settings_tab(self):
        """创建系统设置选项卡"""
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        
        # 设置功能组
        settings_group = QGroupBox("系统设置")
        settings_group_layout = QGridLayout(settings_group)
        
        # 设置按钮
        config_btn = QPushButton("系统配置")
        config_btn.setMinimumHeight(40)
        config_btn.clicked.connect(self.show_settings_dialog)
        
        template_btn = QPushButton("模板管理")
        template_btn.setMinimumHeight(40)
        template_btn.clicked.connect(self.manage_templates)
        
        backup_btn = QPushButton("数据备份")
        backup_btn.setMinimumHeight(40)
        backup_btn.clicked.connect(self.backup_data)
        
        about_btn = QPushButton("关于系统")
        about_btn.setMinimumHeight(40)
        about_btn.clicked.connect(self.show_about_dialog)
        
        # 布局
        settings_group_layout.addWidget(config_btn, 0, 0)
        settings_group_layout.addWidget(template_btn, 0, 1)
        settings_group_layout.addWidget(backup_btn, 1, 0)
        settings_group_layout.addWidget(about_btn, 1, 1)
        
        settings_layout.addWidget(settings_group)
        settings_layout.addStretch()
        
        self.tab_widget.addTab(settings_widget, "系统设置")
    
    def _create_data_display_area(self, parent_layout):
        """创建数据显示区域"""
        # 创建数据表格控件
        self.data_table = DataTableWidget()
        self.data_table.setMinimumHeight(200)
        
        parent_layout.addWidget(self.data_table, 1)  # 拉伸比例为1
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 导入数据动作
        import_action = QAction('导入数据(&I)', self)
        import_action.setShortcut('Ctrl+I')
        import_action.setStatusTip('导入Excel工资数据文件')
        import_action.triggered.connect(self.show_import_dialog)
        file_menu.addAction(import_action)
        
        # 导出数据动作
        export_action = QAction('导出数据(&E)', self)
        export_action.setShortcut('Ctrl+E')
        export_action.setStatusTip('导出当前数据到Excel文件')
        export_action.triggered.connect(self.export_change_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 导入历史动作
        import_history_action = QAction('导入历史(&H)', self)
        import_history_action.setStatusTip('查看数据导入历史记录')
        import_history_action.triggered.connect(self.show_import_history)
        file_menu.addAction(import_history_action)
        
        file_menu.addSeparator()
        
        # 退出动作
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出系统')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 数据菜单
        data_menu = menubar.addMenu('数据(&D)')
        
        # 基准数据管理动作
        baseline_action = QAction('基准数据管理(&B)', self)
        baseline_action.setStatusTip('管理异动检测的基准数据')
        baseline_action.triggered.connect(self.import_baseline_data)
        data_menu.addAction(baseline_action)
        
        # 数据验证动作
        validate_action = QAction('数据验证(&V)', self)
        validate_action.setShortcut('Ctrl+Shift+V')
        validate_action.setStatusTip('验证导入数据的完整性和准确性')
        validate_action.triggered.connect(self.validate_imported_data)
        data_menu.addAction(validate_action)
        
        data_menu.addSeparator()
        
        # 数据备份动作
        backup_action = QAction('数据备份(&K)', self)
        backup_action.setStatusTip('备份系统数据到指定位置')
        backup_action.triggered.connect(self.backup_data)
        data_menu.addAction(backup_action)
        
        # 系统日志动作
        log_action = QAction('系统日志(&L)', self)
        log_action.setStatusTip('查看系统运行日志')
        log_action.triggered.connect(self.show_system_log)
        data_menu.addAction(log_action)
        
        # 异动菜单
        change_menu = menubar.addMenu('异动(&C)')
        
        # 异动检测动作
        detect_action = QAction('异动检测(&D)', self)
        detect_action.setShortcut('F5')
        detect_action.setStatusTip('启动工资异动检测分析')
        detect_action.triggered.connect(self.start_change_detection)
        change_menu.addAction(detect_action)
        
        change_menu.addSeparator()
        
        # 新增人员动作
        new_staff_action = QAction('新增人员(&N)', self)
        new_staff_action.setStatusTip('查看和处理新增人员异动')
        new_staff_action.triggered.connect(self.show_new_staff_changes)
        change_menu.addAction(new_staff_action)
        
        # 离职人员动作
        leaving_staff_action = QAction('离职人员(&L)', self)
        leaving_staff_action.setStatusTip('查看和处理离职人员异动')
        leaving_staff_action.triggered.connect(self.show_leaving_staff_changes)
        change_menu.addAction(leaving_staff_action)
        
        # 工资调整动作
        salary_adjust_action = QAction('工资调整(&S)', self)
        salary_adjust_action.setStatusTip('查看和处理工资调整异动')
        salary_adjust_action.triggered.connect(self.show_salary_adjustments)
        change_menu.addAction(salary_adjust_action)
        
        change_menu.addSeparator()
        
        # 检测结果动作
        detection_result_action = QAction('检测结果(&R)', self)
        detection_result_action.setShortcut('Ctrl+R')
        detection_result_action.setStatusTip('查看异动检测结果')
        detection_result_action.triggered.connect(self.show_detection_window)
        change_menu.addAction(detection_result_action)
        
        # 报告菜单
        report_menu = menubar.addMenu('报告(&R)')
        
        # 生成Word报告动作
        word_report_action = QAction('生成Word报告(&W)', self)
        word_report_action.setShortcut('F6')
        word_report_action.setStatusTip('生成标准Word格式异动报告')
        word_report_action.triggered.connect(self.generate_word_report)
        report_menu.addAction(word_report_action)
        
        # 生成Excel分析表动作
        excel_report_action = QAction('生成Excel分析表(&E)', self)
        excel_report_action.setShortcut('F7')
        excel_report_action.setStatusTip('生成Excel格式数据分析表')
        excel_report_action.triggered.connect(self.generate_excel_report)
        report_menu.addAction(excel_report_action)
        
        # 自定义报告动作
        custom_report_action = QAction('自定义报告(&C)', self)
        custom_report_action.setStatusTip('创建自定义格式的报告')
        custom_report_action.triggered.connect(self.generate_custom_report)
        report_menu.addAction(custom_report_action)
        
        report_menu.addSeparator()
        
        # 报告模板管理动作
        template_action = QAction('报告模板管理(&T)', self)
        template_action.setStatusTip('管理报告生成模板')
        template_action.triggered.connect(self.manage_templates)
        report_menu.addAction(template_action)
        
        # 综合报告窗口动作
        report_window_action = QAction('报告管理窗口(&M)', self)
        report_window_action.setShortcut('F8')
        report_window_action.setStatusTip('打开报告管理窗口')
        report_window_action.triggered.connect(self.show_report_window)
        report_menu.addAction(report_window_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu('设置(&S)')
        
        # 用户设置动作
        user_settings_action = QAction('用户设置(&U)', self)
        user_settings_action.setStatusTip('配置用户个人偏好设置')
        user_settings_action.triggered.connect(self.show_user_settings)
        settings_menu.addAction(user_settings_action)
        
        # 系统设置动作
        system_settings_action = QAction('系统设置(&S)', self)
        system_settings_action.setStatusTip('配置系统全局设置')
        system_settings_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(system_settings_action)
        
        settings_menu.addSeparator()
        
        # 偏好设置动作
        preferences_action = QAction('偏好设置(&P)', self)
        preferences_action.setShortcut('Ctrl+,')
        preferences_action.setStatusTip('配置应用程序偏好设置')
        preferences_action.triggered.connect(self.show_preferences_dialog)
        settings_menu.addAction(preferences_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 用户手册动作
        manual_action = QAction('用户手册(&M)', self)
        manual_action.setShortcut('F1')
        manual_action.setStatusTip('打开用户操作手册')
        manual_action.triggered.connect(self.show_user_manual)
        help_menu.addAction(manual_action)
        
        help_menu.addSeparator()
        
        # 关于动作
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('查看系统版本和开发信息')
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        toolbar.setMovable(False)  # 固定工具栏位置
        
        # 导入数据工具按钮 - 最常用功能
        import_action = QAction('导入数据', self)
        import_action.setShortcut('Ctrl+I')
        import_action.setStatusTip('导入Excel工资数据文件 (Ctrl+I)')
        import_action.triggered.connect(self.show_import_dialog)
        toolbar.addAction(import_action)
        
        # 异动检测工具按钮 - 核心功能
        detect_action = QAction('异动检测', self)
        detect_action.setShortcut('F5')
        detect_action.setStatusTip('启动工资异动检测分析 (F5)')
        detect_action.triggered.connect(self.start_change_detection)
        toolbar.addAction(detect_action)
        
        # 生成报告工具按钮 - 重要输出功能
        report_action = QAction('生成报告', self)
        report_action.setShortcut('F6')
        report_action.setStatusTip('生成Word格式异动报告 (F6)')
        report_action.triggered.connect(self.generate_word_report)
        toolbar.addAction(report_action)
        
        toolbar.addSeparator()
        
        # 数据验证工具按钮 - 数据质量保证
        validate_action = QAction('数据验证', self)
        validate_action.setShortcut('Ctrl+Shift+V')
        validate_action.setStatusTip('验证导入数据的完整性 (Ctrl+Shift+V)')
        validate_action.triggered.connect(self.validate_imported_data)
        toolbar.addAction(validate_action)
        
        # 基准管理工具按钮 - 基础配置
        baseline_action = QAction('基准管理', self)
        baseline_action.setStatusTip('管理异动检测的基准数据')
        baseline_action.triggered.connect(self.import_baseline_data)
        toolbar.addAction(baseline_action)
        
        toolbar.addSeparator()
        
        # 检测结果工具按钮 - 结果查看
        result_action = QAction('查看结果', self)
        result_action.setShortcut('Ctrl+R')
        result_action.setStatusTip('查看异动检测结果 (Ctrl+R)')
        result_action.triggered.connect(self.show_detection_window)
        toolbar.addAction(result_action)
        
        # Excel报告工具按钮 - 数据分析
        excel_action = QAction('Excel报告', self)
        excel_action.setShortcut('F7')
        excel_action.setStatusTip('生成Excel格式数据分析表 (F7)')
        excel_action.triggered.connect(self.generate_excel_report)
        toolbar.addAction(excel_action)
        
        toolbar.addSeparator()
        
        # 系统设置工具按钮 - 配置管理
        settings_action = QAction('系统设置', self)
        settings_action.setStatusTip('配置系统全局设置')
        settings_action.triggered.connect(self.show_settings_dialog)
        toolbar.addAction(settings_action)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 定时器更新时间
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self._update_time)
        self.time_timer.start(1000)  # 每秒更新
        self._update_time()
    
    def _ensure_ui_visibility(self):
        """确保UI元素可见性"""
        try:
            # 强制显示菜单栏
            menu_bar = self.menuBar()
            menu_bar.setVisible(True)
            menu_bar.show()
            
            # 强制显示工具栏
            from PyQt5.QtWidgets import QToolBar
            toolbars = self.findChildren(QToolBar)
            for toolbar in toolbars:
                toolbar.setVisible(True)
                toolbar.show()
            
            # 强制显示状态栏
            status_bar = self.statusBar()
            status_bar.setVisible(True)
            status_bar.show()
            
            self.logger.info(f"UI元素可见性确保完成: 菜单栏={menu_bar.isVisible()}, 工具栏数量={len(toolbars)}, 状态栏={status_bar.isVisible()}")
            
        except Exception as e:
            self.logger.error(f"确保UI元素可见性失败: {e}")
    
    def _update_time(self):
        """更新状态栏时间"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def _set_window_style(self):
        """设置窗口样式"""
        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin: 10px 0px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.status_message.connect(self.show_status_message)
        self.progress_update.connect(self.update_progress)
    
    def _apply_user_preferences(self):
        """应用用户偏好设置"""
        try:
            # 通过应用服务获取用户偏好设置
            result = self.app_service.get_user_preferences()
            if result.success and result.data:
                preferences = result.data
                
                # 应用窗口几何设置 (暂时禁用以防止隐藏菜单栏/工具栏)
                # TODO: 修复几何恢复与UI元素显示的冲突
                # if 'window_geometry' in preferences:
                #     import base64
                #     geometry_data = base64.b64decode(preferences['window_geometry'])
                #     self.restoreGeometry(geometry_data)
                self.logger.info("跳过窗口几何恢复以确保UI元素正常显示")
                
                # 应用主题设置
                if 'theme' in preferences:
                    self._apply_theme(preferences['theme'])
                
                # 应用UI显示设置
                if 'ui' in preferences:
                    ui_prefs = preferences['ui']
                    
                    # 菜单栏显示设置 (默认显示)
                    show_menubar = ui_prefs.get('show_menubar', True)
                    self.menuBar().setVisible(show_menubar)
                    
                    # 工具栏显示设置
                    show_toolbar = ui_prefs.get('show_toolbar', True)
                    from PyQt5.QtWidgets import QToolBar
                    toolbars = self.findChildren(QToolBar)
                    for toolbar in toolbars:
                        toolbar.setVisible(show_toolbar)
                    
                    # 状态栏显示设置
                    show_statusbar = ui_prefs.get('show_statusbar', True)
                    self.statusBar().setVisible(show_statusbar)
                    
                    self.logger.info(f"UI偏好设置已应用: 菜单栏={show_menubar}, 工具栏={show_toolbar}, 状态栏={show_statusbar}")
            else:
                # 没有偏好设置时，确保基本UI元素可见
                self.menuBar().setVisible(True)
                self.statusBar().setVisible(True)
                from PyQt5.QtWidgets import QToolBar
                toolbars = self.findChildren(QToolBar)
                for toolbar in toolbars:
                    toolbar.setVisible(True)
                self.logger.info("应用默认UI设置: 菜单栏、工具栏、状态栏均显示")
                    
        except Exception as e:
            self.logger.warning(f"应用用户偏好设置失败: {e}")
            # 出错时确保基本UI元素可见
            self.menuBar().setVisible(True)
            self.statusBar().setVisible(True)
            from PyQt5.QtWidgets import QToolBar
            toolbars = self.findChildren(QToolBar)
            for toolbar in toolbars:
                toolbar.setVisible(True)
    
    def _apply_theme(self, theme_name: str):
        """应用主题"""
        # 这里可以根据主题名称设置不同的样式
        pass
    
    def _force_show_ui_elements(self):
        """强制显示UI元素（最终保险措施）"""
        try:
            # 使用延迟执行，确保在所有初始化完成后执行
            from PyQt5.QtCore import QTimer
            
            def force_show():
                # 强制显示菜单栏
                menu_bar = self.menuBar()
                menu_bar.setVisible(True)
                menu_bar.show()
                menu_bar.setNativeMenuBar(False)  # 禁用原生菜单栏以确保显示
                
                # 强制显示工具栏
                from PyQt5.QtWidgets import QToolBar
                toolbars = self.findChildren(QToolBar)
                for toolbar in toolbars:
                    toolbar.setVisible(True)
                    toolbar.show()
                    toolbar.setFloatable(False)  # 防止意外隐藏
                
                # 强制显示状态栏
                status_bar = self.statusBar()
                status_bar.setVisible(True)
                status_bar.show()
                
                self.logger.info(f"强制显示UI元素完成: 菜单栏={menu_bar.isVisible()}, 工具栏数量={len(toolbars)}, 状态栏={status_bar.isVisible()}")
            
            # 延迟100ms执行，确保在窗口几何恢复之后
            QTimer.singleShot(100, force_show)
            
        except Exception as e:
            self.logger.error(f"强制显示UI元素失败: {e}")
    
    def _show_welcome_message(self):
        """显示欢迎信息"""
        self.show_status_message("欢迎使用月度工资异动处理系统")
    
    # ==================== 状态管理方法 ====================
    
    def show_status_message(self, message: str, timeout: int = 5000):
        """显示状态消息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))
        self.logger.info(f"状态消息: {message}")
    
    def update_progress(self, value: int):
        """更新进度条"""
        self.progress_bar.setValue(value)
        if value == 0:
            self.progress_bar.setVisible(False)
        else:
            self.progress_bar.setVisible(True)
    
    def _handle_service_result(self, result: ServiceResult, success_message: Optional[str] = None):
        """
        统一处理服务结果
        
        Args:
            result: 服务操作结果
            success_message: 成功时的自定义消息
        """
        if result.success:
            message = success_message or result.message
            self.show_status_message(message)
            self.refresh_data_display()
        else:
            self.logger.error(f"操作失败: {result.message}")
            QMessageBox.critical(self, "错误", result.message)
    
    # ==================== 数据导入相关方法 ====================
    
    def show_import_dialog(self):
        """显示数据导入对话框"""
        try:
            dialog = DataImportDialog(self)
            if dialog.exec_() == dialog.Accepted:
                # 获取导入的数据
                imported_data = dialog.get_imported_data()
                if imported_data and imported_data.get('data'):
                    # 显示导入的数据到主界面
                    self.data_table.set_data(
                        imported_data['data'], 
                        imported_data['headers']
                    )
                    self.show_status_message(f"数据导入成功，共导入 {len(imported_data['data'])} 行数据")
                    self.refresh_data_display()
                else:
                    file_path = dialog.get_selected_file()
                    if file_path:
                        result = self.app_service.import_excel_data(file_path)
                        self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"显示导入对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"显示导入对话框失败: {e}")
    
    def show_import_history(self):
        """显示导入历史"""
        try:
            result = self.app_service.get_import_history()
            if result.success and result.data:
                # 创建简单的历史显示对话框
                from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
                
                dialog = QDialog(self)
                dialog.setWindowTitle("数据导入历史")
                dialog.resize(600, 400)
                
                layout = QVBoxLayout(dialog)
                
                # 历史记录文本框
                history_text = QTextEdit()
                history_text.setReadOnly(True)
                
                history_content = []
                for record in result.data:
                    history_content.append(f"时间: {record.get('timestamp', 'N/A')}")
                    history_content.append(f"文件: {record.get('filename', 'N/A')}")
                    history_content.append(f"状态: {record.get('status', 'N/A')}")
                    history_content.append(f"记录数: {record.get('record_count', 'N/A')}")
                    history_content.append("-" * 50)
                
                history_text.setPlainText("\n".join(history_content))
                layout.addWidget(history_text)
                
                # 按钮区域
                button_layout = QHBoxLayout()
                close_button = QPushButton("关闭")
                close_button.clicked.connect(dialog.close)
                button_layout.addStretch()
                button_layout.addWidget(close_button)
                layout.addLayout(button_layout)
                
                dialog.exec_()
            else:
                QMessageBox.information(self, "提示", "暂无导入历史记录")
        except Exception as e:
            self.logger.error(f"显示导入历史失败: {e}")
            QMessageBox.critical(self, "错误", f"显示导入历史失败: {e}")

    def import_baseline_data(self):
        """导入基准数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择基准数据文件", "", "Excel文件 (*.xlsx *.xls)"
            )
            if file_path:
                result = self.app_service.import_baseline_data(file_path)
                self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"导入基准数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入基准数据失败: {e}")
    
    def validate_imported_data(self):
        """验证导入的数据"""
        try:
            # 创建进度对话框
            progress_dialog = ProgressDialog("数据验证中...", self)
            progress_dialog.show()
            
            # 通过应用服务验证数据
            result = self.app_service.validate_data()
            
            progress_dialog.close()
            self._handle_service_result(result)
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            QMessageBox.critical(self, "错误", f"数据验证失败: {e}")
    
    def show_system_log(self):
        """显示系统日志"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
            import os
            
            dialog = QDialog(self)
            dialog.setWindowTitle("系统日志")
            dialog.resize(800, 600)
            
            layout = QVBoxLayout(dialog)
            
            # 日志内容文本框
            log_text = QTextEdit()
            log_text.setReadOnly(True)
            log_text.setFont(QFont("Consolas", 9))
            
            # 读取日志文件
            log_file_path = "logs/system.log"
            if os.path.exists(log_file_path):
                try:
                    with open(log_file_path, 'r', encoding='utf-8') as f:
                        log_content = f.read()
                        # 只显示最后1000行
                        lines = log_content.split('\n')
                        if len(lines) > 1000:
                            lines = lines[-1000:]
                            log_content = '\n'.join(lines)
                        log_text.setPlainText(log_content)
                        # 滚动到底部
                        log_text.moveCursor(log_text.textCursor().End)
                except Exception as e:
                    log_text.setPlainText(f"无法读取日志文件: {e}")
            else:
                log_text.setPlainText("日志文件不存在")
            
            layout.addWidget(log_text)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            refresh_button = QPushButton("刷新")
            refresh_button.clicked.connect(lambda: self.show_system_log())
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.close)
            
            button_layout.addWidget(refresh_button)
            button_layout.addStretch()
            button_layout.addWidget(close_button)
            layout.addLayout(button_layout)
            
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示系统日志失败: {e}")
            QMessageBox.critical(self, "错误", f"显示系统日志失败: {e}")

    # ==================== 异动检测相关方法 ====================
    
    def start_change_detection(self):
        """开始异动检测"""
        try:
            self.show_status_message("开始异动检测...")
            result = self.app_service.detect_changes()
            self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"异动检测失败: {e}")
            QMessageBox.critical(self, "错误", f"异动检测失败: {e}")
    
    def show_new_staff_changes(self):
        """显示新增人员异动"""
        try:
            result = self.app_service.get_changes_by_type('new_staff')
            if result.success and result.data:
                self._show_change_details("新增人员异动", result.data)
            else:
                QMessageBox.information(self, "提示", "暂无新增人员异动数据")
        except Exception as e:
            self.logger.error(f"显示新增人员异动失败: {e}")
            QMessageBox.critical(self, "错误", f"显示新增人员异动失败: {e}")
    
    def show_leaving_staff_changes(self):
        """显示离职人员异动"""
        try:
            result = self.app_service.get_changes_by_type('leaving_staff')
            if result.success and result.data:
                self._show_change_details("离职人员异动", result.data)
            else:
                QMessageBox.information(self, "提示", "暂无离职人员异动数据")
        except Exception as e:
            self.logger.error(f"显示离职人员异动失败: {e}")
            QMessageBox.critical(self, "错误", f"显示离职人员异动失败: {e}")
    
    def show_salary_adjustments(self):
        """显示工资调整异动"""
        try:
            result = self.app_service.get_changes_by_type('salary_adjustment')
            if result.success and result.data:
                self._show_change_details("工资调整异动", result.data)
            else:
                QMessageBox.information(self, "提示", "暂无工资调整异动数据")
        except Exception as e:
            self.logger.error(f"显示工资调整异动失败: {e}")
            QMessageBox.critical(self, "错误", f"显示工资调整异动失败: {e}")
    
    def _show_change_details(self, title: str, data: list):
        """显示异动详情的通用方法"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle(title)
        dialog.resize(800, 500)
        
        layout = QVBoxLayout(dialog)
        
        # 创建表格
        table = QTableWidget()
        if data:
            # 设置表格结构
            headers = list(data[0].keys())
            table.setColumnCount(len(headers))
            table.setRowCount(len(data))
            table.setHorizontalHeaderLabels(headers)
            
            # 填充数据
            for row, item in enumerate(data):
                for col, header in enumerate(headers):
                    table.setItem(row, col, QTableWidgetItem(str(item.get(header, ''))))
            
            # 调整列宽
            table.resizeColumnsToContents()
        
        layout.addWidget(table)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        export_button = QPushButton("导出")
        export_button.clicked.connect(lambda: self._export_change_data(title, data))
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.close)
        
        button_layout.addWidget(export_button)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)
        
        dialog.exec_()
    
    def _export_change_data(self, title: str, data: list):
        """导出异动数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, f"导出{title}", f"{title}.xlsx", "Excel文件 (*.xlsx)"
            )
            if file_path and data:
                import pandas as pd
                df = pd.DataFrame(data)
                df.to_excel(file_path, index=False)
                self.show_status_message(f"{title}导出成功")
        except Exception as e:
            self.logger.error(f"导出{title}失败: {e}")
            QMessageBox.critical(self, "错误", f"导出{title}失败: {e}")

    def show_detection_window(self):
        """显示异动检测窗口"""
        try:
            # 获取检测结果
            result = self.app_service.get_detection_results()
            if result.success:
                if not hasattr(self, 'change_detection_window') or self.change_detection_window is None:
                    self.change_detection_window = ChangeDetectionWindow(
                        detection_results=result.data,
                        parent=self
                    )
                self.change_detection_window.show()
                self.change_detection_window.raise_()
            else:
                QMessageBox.information(self, "提示", "没有检测结果数据")
        except Exception as e:
            self.logger.error(f"显示检测窗口失败: {e}")
            QMessageBox.critical(self, "错误", f"显示检测窗口失败: {e}")
    
    def export_change_data(self):
        """导出异动数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出异动数据", "", "Excel文件 (*.xlsx)"
            )
            if file_path:
                result = self.app_service.export_change_data(file_path)
                self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"导出异动数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出异动数据失败: {e}")
    
    # ==================== 报告生成相关方法 ====================
    
    def generate_word_report(self):
        """生成Word报告"""
        try:
            self.show_status_message("生成Word报告中...")
            result = self.app_service.generate_word_report()
            self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"生成Word报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成Word报告失败: {e}")
    
    def generate_excel_report(self):
        """生成Excel报告"""
        try:
            self.show_status_message("生成Excel报告中...")
            result = self.app_service.generate_excel_report()
            self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成Excel报告失败: {e}")
    
    def generate_custom_report(self):
        """生成自定义报告"""
        try:
            # 显示自定义报告配置对话框
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QComboBox, QCheckBox, QPushButton, QHBoxLayout
            
            dialog = QDialog(self)
            dialog.setWindowTitle("自定义报告配置")
            dialog.resize(400, 300)
            
            layout = QVBoxLayout(dialog)
            form_layout = QFormLayout()
            
            # 报告格式选择
            format_combo = QComboBox()
            format_combo.addItems(["Excel", "Word", "PDF"])
            form_layout.addRow("报告格式:", format_combo)
            
            # 包含内容选择
            include_summary = QCheckBox("包含汇总信息")
            include_summary.setChecked(True)
            form_layout.addRow("", include_summary)
            
            include_details = QCheckBox("包含详细数据")
            include_details.setChecked(True)
            form_layout.addRow("", include_details)
            
            include_charts = QCheckBox("包含图表分析")
            include_charts.setChecked(False)
            form_layout.addRow("", include_charts)
            
            layout.addLayout(form_layout)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            generate_button = QPushButton("生成报告")
            cancel_button = QPushButton("取消")
            
            def generate_custom():
                config = {
                    'format': format_combo.currentText(),
                    'include_summary': include_summary.isChecked(),
                    'include_details': include_details.isChecked(),
                    'include_charts': include_charts.isChecked()
                }
                dialog.accept()
                self._execute_custom_report(config)
            
            generate_button.clicked.connect(generate_custom)
            cancel_button.clicked.connect(dialog.reject)
            
            button_layout.addWidget(generate_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)
            
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"配置自定义报告失败: {e}")
            QMessageBox.critical(self, "错误", f"配置自定义报告失败: {e}")
    
    def _execute_custom_report(self, config: dict):
        """执行自定义报告生成"""
        try:
            self.show_status_message("生成自定义报告中...")
            result = self.app_service.generate_custom_report(config)
            self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"生成自定义报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成自定义报告失败: {e}")

    def show_report_window(self):
        """显示报告生成窗口"""
        try:
            if not hasattr(self, 'report_generation_window') or self.report_generation_window is None:
                self.report_generation_window = ReportGenerationWindow(self)
            self.report_generation_window.show()
            self.report_generation_window.raise_()
        except Exception as e:
            self.logger.error(f"显示报告窗口失败: {e}")
            QMessageBox.critical(self, "错误", f"显示报告窗口失败: {e}")
    
    # ==================== 系统设置相关方法 ====================
    
    def show_user_settings(self):
        """显示用户设置对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QLineEdit, QComboBox, QPushButton, QHBoxLayout
            
            dialog = QDialog(self)
            dialog.setWindowTitle("用户设置")
            dialog.resize(400, 300)
            
            layout = QVBoxLayout(dialog)
            form_layout = QFormLayout()
            
            # 用户个人信息设置
            username_edit = QLineEdit()
            username_edit.setText("当前用户")  # 从配置中获取
            form_layout.addRow("用户名:", username_edit)
            
            department_edit = QLineEdit()
            department_edit.setText("人事部")  # 从配置中获取
            form_layout.addRow("部门:", department_edit)
            
            # 界面设置
            theme_combo = QComboBox()
            theme_combo.addItems(["默认主题", "深色主题", "简约主题"])
            form_layout.addRow("界面主题:", theme_combo)
            
            language_combo = QComboBox()
            language_combo.addItems(["简体中文", "English"])
            form_layout.addRow("语言:", language_combo)
            
            layout.addLayout(form_layout)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            save_button = QPushButton("保存")
            cancel_button = QPushButton("取消")
            
            def save_user_settings():
                # 保存用户设置
                settings = {
                    'username': username_edit.text(),
                    'department': department_edit.text(),
                    'theme': theme_combo.currentText(),
                    'language': language_combo.currentText()
                }
                # 通过应用服务保存设置
                result = self.app_service.save_user_settings(settings)
                if result.success:
                    self.show_status_message("用户设置保存成功")
                    dialog.accept()
                else:
                    QMessageBox.critical(dialog, "错误", result.message)
            
            save_button.clicked.connect(save_user_settings)
            cancel_button.clicked.connect(dialog.reject)
            
            button_layout.addWidget(save_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)
            
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"显示用户设置失败: {e}")
            QMessageBox.critical(self, "错误", f"显示用户设置失败: {e}")

    def show_settings_dialog(self):
        """显示系统设置对话框"""
        try:
            dialog = SettingsDialog(self)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"显示设置对话框失败: {e}")
    
    def show_preferences_dialog(self):
        """显示偏好设置对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTabWidget, QWidget, QFormLayout, QCheckBox, QSpinBox, QPushButton, QHBoxLayout
            
            dialog = QDialog(self)
            dialog.setWindowTitle("偏好设置")
            dialog.resize(500, 400)
            
            layout = QVBoxLayout(dialog)
            
            # 创建选项卡
            tab_widget = QTabWidget()
            
            # 常规选项卡
            general_tab = QWidget()
            general_layout = QFormLayout(general_tab)
            
            auto_save = QCheckBox("自动保存数据")
            auto_save.setChecked(True)
            general_layout.addRow("", auto_save)
            
            confirm_delete = QCheckBox("删除前确认")
            confirm_delete.setChecked(True)
            general_layout.addRow("", confirm_delete)
            
            startup_check = QCheckBox("启动时检查更新")
            startup_check.setChecked(False)
            general_layout.addRow("", startup_check)
            
            tab_widget.addTab(general_tab, "常规")
            
            # 数据选项卡
            data_tab = QWidget()
            data_layout = QFormLayout(data_tab)
            
            backup_interval = QSpinBox()
            backup_interval.setRange(1, 24)
            backup_interval.setValue(2)
            backup_interval.setSuffix(" 小时")
            data_layout.addRow("自动备份间隔:", backup_interval)
            
            max_records = QSpinBox()
            max_records.setRange(1000, 100000)
            max_records.setValue(10000)
            data_layout.addRow("最大显示记录数:", max_records)
            
            tab_widget.addTab(data_tab, "数据")
            
            layout.addWidget(tab_widget)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            save_button = QPushButton("保存")
            cancel_button = QPushButton("取消")
            reset_button = QPushButton("重置为默认")
            
            def save_preferences():
                preferences = {
                    'auto_save': auto_save.isChecked(),
                    'confirm_delete': confirm_delete.isChecked(),
                    'startup_check': startup_check.isChecked(),
                    'backup_interval': backup_interval.value(),
                    'max_records': max_records.value()
                }
                result = self.app_service.save_preferences(preferences)
                if result.success:
                    self.show_status_message("偏好设置保存成功")
                    dialog.accept()
                else:
                    QMessageBox.critical(dialog, "错误", result.message)
            
            save_button.clicked.connect(save_preferences)
            cancel_button.clicked.connect(dialog.reject)
            
            button_layout.addWidget(reset_button)
            button_layout.addStretch()
            button_layout.addWidget(save_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)
            
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"显示偏好设置失败: {e}")
            QMessageBox.critical(self, "错误", f"显示偏好设置失败: {e}")

    def manage_templates(self):
        """管理模板"""
        try:
            self.show_status_message("模板管理功能开发中...")
        except Exception as e:
            self.logger.error(f"模板管理失败: {e}")
            QMessageBox.critical(self, "错误", f"模板管理失败: {e}")
    
    def backup_data(self):
        """数据备份"""
        try:
            backup_path, _ = QFileDialog.getSaveFileName(
                self, "选择备份位置", "", "备份文件 (*.backup)"
            )
            if backup_path:
                result = self.app_service.backup_data(backup_path)
                self._handle_service_result(result)
        except Exception as e:
            self.logger.error(f"数据备份失败: {e}")
            QMessageBox.critical(self, "错误", f"数据备份失败: {e}")
    
    def show_user_manual(self):
        """显示用户手册"""
        try:
            import os
            import webbrowser
            
            # 查找用户手册文件
            manual_paths = [
                "docs/user_manual.md",
                "docs/user_manual.html", 
                "docs/user_manual.pdf"
            ]
            
            manual_found = False
            for path in manual_paths:
                if os.path.exists(path):
                    # 使用系统默认程序打开文件
                    if os.name == 'nt':  # Windows
                        os.startfile(path)
                    else:  # macOS and Linux
                        webbrowser.open(f'file://{os.path.abspath(path)}')
                    manual_found = True
                    self.show_status_message("用户手册已打开")
                    break
            
            if not manual_found:
                QMessageBox.information(self, "提示", "用户手册文件不存在，请联系系统管理员")
                
        except Exception as e:
            self.logger.error(f"打开用户手册失败: {e}")
            QMessageBox.critical(self, "错误", f"打开用户手册失败: {e}")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            dialog = AboutDialog(self)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示关于对话框失败: {e}")
            QMessageBox.information(self, "关于", "月度工资异动处理系统 v1.0")
    
    # ==================== 数据显示相关方法 ====================
    
    def refresh_data_display(self):
        """刷新数据显示"""
        try:
            # 这里可以通过应用服务获取最新数据来刷新显示
            self.data_table.refresh_data()
        except Exception as e:
            self.logger.error(f"刷新数据显示失败: {e}")
    
    # ==================== 窗口生命周期方法 ====================
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 保存用户偏好设置
            window_geometry = self.saveGeometry()
            if hasattr(window_geometry, 'data'):
                import base64
                geometry_data = base64.b64encode(window_geometry.data()).decode('utf-8')
                preferences = {'window_geometry': geometry_data}
                self.app_service.save_user_preferences(preferences)
            
            # 关闭子窗口
            if hasattr(self, 'change_detection_window') and self.change_detection_window:
                self.change_detection_window.close()
            if hasattr(self, 'report_generation_window') and self.report_generation_window:
                self.report_generation_window.close()
            
            # 清理应用服务资源
            self.app_service.cleanup()
            
            self.logger.info("主窗口正常关闭")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭事件处理失败: {e}")
            event.accept()  # 即使有错误也允许关闭 