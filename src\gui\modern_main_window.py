"""
现代化主窗口 - Modern Main Window

基于Material Design规范的现代化主窗口实现，保持与原有功能的完全兼容性。

主要功能:
1. 现代化布局 (Header + Sidebar + Main + Footer)
2. 响应式设计
3. Toast消息系统集成
4. 动画效果支持
5. 完整API兼容性
"""

import sys
import os
from typing import Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QMenuBar, QToolBar, QStatusBar, QAction, QTabWidget,
    QLabel, QPushButton, QTextEdit, QSplitter, QFrame,
    QMessageBox, QFileDialog, QProgressBar, QGroupBox,
    QTreeWidget, QTreeWidgetItem, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize, QPropertyAnimation, QEasingCurve, QByteArray
from PyQt5.QtGui import QIcon, QFont, QPixmap, QPalette, QColor, QResizeEvent

# 导入项目核心模块
from src.utils.log_config import setup_logger
from src.core.config_manager import ConfigManager
from src.modules.system_config import UserPreferences
from src.modules.data_import import ExcelImporter, DataValidator
from src.modules.change_detection import ChangeDetector, BaselineManager
from src.modules.report_generation import ReportManager

# 导入GUI子模块
from .dialogs import DataImportDialog, SettingsDialog, AboutDialog, ProgressDialog
from .windows import ChangeDetectionWindow, ReportGenerationWindow
# 使用绝对导入避免包名冲突
from src.gui.widgets import DataTableWidget

# 导入现代化组件
from .modern_style import (
    MaterialDesignPalette, ModernStyleSheet, ModernAnimations, 
    ModernShadowEffects, ResponsiveLayout
)
from .toast_system import toast_manager


class ModernHeaderWidget(QWidget):
    """现代化头部组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("HeaderWidget")
        self.setFixedHeight(80)  # 增加高度使头部更醒目
        
        # 创建布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(30, 0, 30, 0)  # 增加左右边距
        layout.setSpacing(20)
        
        # 系统标题
        self.title_label = QLabel("月度工资异动处理系统")
        title_font = QFont()
        title_font.setPointSize(18)  # 增大字号
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet(f"color: {MaterialDesignPalette.PRIMARY['text']}; margin: 10px 0;")
        layout.addWidget(self.title_label)
        
        # 弹性空间
        layout.addStretch()
        
        # 系统信息
        self.version_label = QLabel("v2.0 现代化版本")
        self.version_label.setStyleSheet(f"""
            color: {MaterialDesignPalette.PRIMARY['text']}; 
            font-size: 12pt; 
            font-weight: 300;
            margin: 10px 0;
        """)
        layout.addWidget(self.version_label)
        
        # 应用头部专用样式
        self._apply_header_style()
    
    def _apply_header_style(self):
        """应用头部专用样式"""
        header_style = f"""
        #HeaderWidget {{
            background: qlineargradient(
                x1: 0, y1: 0, x2: 1, y2: 1,
                stop: 0 {MaterialDesignPalette.PRIMARY['main']},
                stop: 0.5 {MaterialDesignPalette.SECONDARY['main']},
                stop: 1 {MaterialDesignPalette.PRIMARY['dark']}
            );
            border: none;
            border-bottom: 2px solid {MaterialDesignPalette.PRIMARY['dark']};
        }}
        """
        self.setStyleSheet(header_style)


class ModernSidebarWidget(QWidget):
    """现代化侧边栏组件"""
    
    # 信号定义
    item_selected = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("SidebarWidget")
        
        # 应用侧边栏样式
        self.setStyleSheet(f"""
        #SidebarWidget {{
            background-color: {MaterialDesignPalette.BACKGROUND['sidebar']};
            border-right: 1px solid {MaterialDesignPalette.BORDER['light']};
            min-width: 250px;
            max-width: 350px;
        }}
        QTreeWidget {{
            background-color: transparent;
            border: none;
            font-size: 10pt;
        }}
        QTreeWidget::item {{
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            margin: 2px 8px;
        }}
        QTreeWidget::item:hover {{
            background-color: {MaterialDesignPalette.PRIMARY['light']}40;
        }}
        QTreeWidget::item:selected {{
            background-color: {MaterialDesignPalette.PRIMARY['main']};
            color: {MaterialDesignPalette.PRIMARY['text']};
        }}
        """)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(10)
        
        # 导航标题
        nav_title = QLabel("功能导航")
        nav_title.setStyleSheet(f"""
        QLabel {{
            color: {MaterialDesignPalette.TEXT['primary']};
            font-size: 12pt;
            font-weight: bold;
            padding: 10px 20px;
        }}
        """)
        layout.addWidget(nav_title)
        
        # 创建导航树
        self._create_navigation_tree(layout)
        
        # 弹性空间
        layout.addStretch()
    
    def _create_navigation_tree(self, parent_layout):
        """创建导航树"""
        self.nav_tree = QTreeWidget()
        self.nav_tree.setHeaderHidden(True)
        self.nav_tree.setRootIsDecorated(False)
        self.nav_tree.itemClicked.connect(self._on_item_clicked)
        
        # 添加导航项
        self._add_nav_items()
        
        parent_layout.addWidget(self.nav_tree)
    
    def _add_nav_items(self):
        """添加导航项"""
        # 数据管理
        data_item = QTreeWidgetItem(["📊 数据管理"])
        data_item.setData(0, Qt.UserRole, "data_management")
        self.nav_tree.addTopLevelItem(data_item)
        
        import_item = QTreeWidgetItem(["📥 数据导入"])
        import_item.setData(0, Qt.UserRole, "data_import")
        data_item.addChild(import_item)
        
        validate_item = QTreeWidgetItem(["✓ 数据验证"])
        validate_item.setData(0, Qt.UserRole, "data_validation")
        data_item.addChild(validate_item)
        
        # 异动检测
        detection_item = QTreeWidgetItem(["🔍 异动检测"])
        detection_item.setData(0, Qt.UserRole, "change_detection")
        self.nav_tree.addTopLevelItem(detection_item)
        
        # 报告生成
        report_item = QTreeWidgetItem(["📋 报告生成"])
        report_item.setData(0, Qt.UserRole, "report_generation")
        self.nav_tree.addTopLevelItem(report_item)
        
        word_item = QTreeWidgetItem(["📄 Word报告"])
        word_item.setData(0, Qt.UserRole, "word_report")
        report_item.addChild(word_item)
        
        excel_item = QTreeWidgetItem(["📊 Excel报告"])
        excel_item.setData(0, Qt.UserRole, "excel_report")
        report_item.addChild(excel_item)
        
        # 系统设置
        settings_item = QTreeWidgetItem(["⚙️ 系统设置"])
        settings_item.setData(0, Qt.UserRole, "settings")
        self.nav_tree.addTopLevelItem(settings_item)
        
        # 展开所有项
        self.nav_tree.expandAll()
    
    def _on_item_clicked(self, item, column):
        """处理项目点击"""
        action = item.data(0, Qt.UserRole)
        if action:
            self.item_selected.emit(action)


class ModernMainContentWidget(QWidget):
    """现代化主内容区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 创建欢迎卡片
        self._create_welcome_card(layout)
        
        # 创建功能卡片区域
        self._create_function_cards(layout)
        
        # 创建数据显示区域
        self._create_data_display_area(layout)
    
    def _create_welcome_card(self, parent_layout):
        """创建欢迎卡片"""
        welcome_card = QFrame()
        welcome_card.setObjectName("WelcomeCard")
        welcome_card.setProperty("class", "ModernCard")
        welcome_card.setProperty("shadowLevel", "2")
        welcome_card.setFixedHeight(120)
        
        # 应用卡片阴影效果
        shadow = ModernShadowEffects.create_card_shadow(2)
        welcome_card.setGraphicsEffect(shadow)
        
        card_layout = QHBoxLayout(welcome_card)
        card_layout.setContentsMargins(30, 20, 30, 20)
        
        # 欢迎文本
        welcome_text = QVBoxLayout()
        
        title = QLabel("欢迎使用现代化工资异动处理系统")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['primary']};")
        welcome_text.addWidget(title)
        
        subtitle = QLabel("基于Material Design的现代化界面，提供更优秀的用户体验")
        subtitle.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['secondary']}; font-size: 10pt;")
        welcome_text.addWidget(subtitle)
        
        card_layout.addLayout(welcome_text)
        card_layout.addStretch()
        
        parent_layout.addWidget(welcome_card)
    
    def _create_function_cards(self, parent_layout):
        """创建功能卡片"""
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(20)
        
        # 数据导入卡片
        import_card = self._create_function_card(
            "📥", "数据导入", "导入Excel工资数据", MaterialDesignPalette.STATUS['info']
        )
        cards_layout.addWidget(import_card)
        
        # 异动检测卡片
        detection_card = self._create_function_card(
            "🔍", "异动检测", "智能检测工资变化", MaterialDesignPalette.STATUS['warning']
        )
        cards_layout.addWidget(detection_card)
        
        # 报告生成卡片
        report_card = self._create_function_card(
            "📋", "报告生成", "生成标准化报告", MaterialDesignPalette.STATUS['success']
        )
        cards_layout.addWidget(report_card)
        
        parent_layout.addLayout(cards_layout)
    
    def _create_function_card(self, icon: str, title: str, description: str, color: str) -> QFrame:
        """创建功能卡片"""
        card = QFrame()
        card.setProperty("class", "ModernCard")
        card.setProperty("shadowLevel", "1")
        card.setFixedHeight(100)
        
        # 应用卡片阴影效果
        shadow = ModernShadowEffects.create_card_shadow(1)
        card.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)
        
        # 图标和标题行
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 20px; color: {color};")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['primary']};")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['secondary']}; font-size: 9pt;")
        layout.addWidget(desc_label)
        
        return card
    
    def _create_data_display_area(self, parent_layout):
        """创建数据显示区域"""
        data_frame = QFrame()
        data_frame.setProperty("class", "ModernCard")
        data_frame.setProperty("shadowLevel", "1")
        data_frame.setMinimumHeight(300)
        
        # 应用卡片阴影效果
        shadow = ModernShadowEffects.create_card_shadow(1)
        data_frame.setGraphicsEffect(shadow)
        
        data_layout = QVBoxLayout(data_frame)
        data_layout.setContentsMargins(20, 20, 20, 20)
        
        # 数据区域标题
        data_title = QLabel("数据概览")
        data_title_font = QFont()
        data_title_font.setBold(True)
        data_title_font.setPointSize(12)
        data_title.setFont(data_title_font)
        data_title.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['primary']};")
        data_layout.addWidget(data_title)
        
        # 数据表格
        self.data_table = DataTableWidget()
        data_layout.addWidget(self.data_table)
        
        parent_layout.addWidget(data_frame)


class ModernMainWindow(QMainWindow):
    """现代化主窗口"""
    
    # 信号定义 - 保持与原有主窗口的兼容性
    status_message = pyqtSignal(str)
    progress_update = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        
        # 初始化日志
        self.logger = setup_logger(__name__)
        
        # 初始化配置管理器
        try:
            self.config_manager = ConfigManager()
            self.user_preferences = UserPreferences()
            self.logger.info("配置管理器初始化成功")
        except Exception as e:
            self.logger.error(f"配置管理器初始化失败: {e}")
            QMessageBox.critical(self, "错误", f"配置管理器初始化失败: {e}")
            sys.exit(1)
        
        # 初始化功能模块
        self._init_modules()
        
        # 初始化Toast系统
        toast_manager.set_parent(self)
        
        # 初始化界面
        self._init_ui()
        
        # 连接信号和槽
        self._connect_signals()
        
        # 应用现代化样式
        self._apply_modern_style()
        
        # 应用用户偏好设置
        self._apply_user_preferences()
        
        # 显示欢迎消息
        self._show_welcome_message()
        
        self.logger.info("现代化主窗口初始化完成")
    
    def _init_modules(self):
        """初始化功能模块 - 保持与原有实现的兼容性"""
        try:
            # 数据导入模块
            self.excel_importer = ExcelImporter()
            self.data_validator = DataValidator()
            
            # 异动检测模块
            self.baseline_manager = BaselineManager(self.config_manager)
            self.change_detector = ChangeDetector(self.config_manager, self.baseline_manager)
            
            # 报告生成模块
            self.report_manager = ReportManager()
            
            # 子窗口
            self.change_detection_window = None
            self.report_generation_window = None
            
            self.logger.info("功能模块初始化成功")
            
        except Exception as e:
            self.logger.error(f"功能模块初始化失败: {e}")
            raise
    
    def _init_ui(self):
        """初始化用户界面"""
        # 设置窗口基本属性
        self.setWindowTitle("月度工资异动处理系统 v2.0 - 现代化版本")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # 设置窗口图标
        self._set_window_icon()
        
        # 禁用传统的菜单栏和工具栏，完全使用现代化布局
        # self._create_menu_bar()  # 注释掉传统菜单栏
        # self._create_tool_bar()  # 注释掉传统工具栏
        
        # 创建现代化中央组件（包含现代化头部）
        self._create_modern_central_widget()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 应用现代化样式
        self._apply_modern_style()
        
        # 连接信号和槽
        self._connect_signals()
        
        # 应用用户偏好设置
        self._apply_user_preferences()
        
        # 显示欢迎消息
        self._show_welcome_message()
    
    def _create_modern_central_widget(self):
        """创建现代化中央组件"""
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 添加现代化头部组件
        self.header_widget = ModernHeaderWidget(self)
        main_layout.addWidget(self.header_widget)
        
        # 创建内容分割器
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)
        
        # 现代化侧边栏
        self.sidebar_widget = ModernSidebarWidget(self)
        self.sidebar_widget.item_selected.connect(self._handle_navigation)
        content_splitter.addWidget(self.sidebar_widget)
        
        # 主内容区域
        self.main_content_widget = ModernMainContentWidget(self)
        content_splitter.addWidget(self.main_content_widget)
        
        # 设置分割比例
        content_splitter.setSizes([300, 1100])
        
        main_layout.addWidget(content_splitter)
        
        # 保存引用以便响应式调整
        self.content_splitter = content_splitter
        
        # 设置为中央组件
        self.setCentralWidget(central_widget)
    
    def _apply_modern_style(self):
        """应用现代化样式"""
        try:
            # 获取完整样式表
            complete_style = ModernStyleSheet.get_complete_style()
            
            # 添加调试信息
            self.logger.info(f"正在应用现代化样式，样式长度: {len(complete_style)} 字符")
            
            # 应用完整的现代化样式表
            self.setStyleSheet(complete_style)
            
            # 强制刷新界面
            self.update()
            self.repaint()
            
            # 为子组件也应用样式
            for child in self.findChildren(QWidget):
                child.update()
            
            self.logger.info("现代化样式应用成功")
            
            # 显示样式预览（前100字符）
            preview = complete_style[:100].replace('\n', ' ').strip()
            self.logger.debug(f"样式预览: {preview}...")
            
        except Exception as e:
            self.logger.error(f"应用现代化样式失败: {e}")
            # 尝试应用基本样式作为后备方案
            try:
                basic_style = f"""
                QMainWindow {{
                    background-color: {MaterialDesignPalette.BACKGROUND['default']};
                    color: {MaterialDesignPalette.TEXT['primary']};
                    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
                    font-size: 9pt;
                }}
                """
                self.setStyleSheet(basic_style)
                self.logger.info("已应用基本备用样式")
            except Exception as backup_error:
                self.logger.error(f"应用备用样式也失败: {backup_error}")
    
    def _handle_navigation(self, action: str):
        """处理导航操作"""
        if action == "data_import":
            self.show_import_dialog()
            toast_manager.show_info("打开数据导入对话框")
        elif action == "data_validation":
            self.validate_imported_data()
        elif action == "change_detection":
            self.show_detection_window()
        elif action == "report_generation":
            self.show_report_window()
        elif action == "word_report":
            self.generate_word_report()
        elif action == "excel_report":
            self.generate_excel_report()
        elif action == "settings":
            self.show_settings_dialog()
        else:
            toast_manager.show_info(f"功能 {action} 开发中...")
    
    def resizeEvent(self, event: QResizeEvent):
        """处理窗口大小变化 - 响应式布局"""
        super().resizeEvent(event)
        
        # 获取当前窗口宽度
        width = event.size().width()
        size_category = ResponsiveLayout.get_size_category(width)
        
        # 调整侧边栏宽度
        sidebar_width = ResponsiveLayout.get_sidebar_width(size_category)
        
        # 更新分割器比例
        if hasattr(self, 'content_splitter'):
            total_width = width
            main_width = total_width - sidebar_width
            self.content_splitter.setSizes([sidebar_width, main_width])
        
        # 如果屏幕太小，可以考虑折叠侧边栏
        if ResponsiveLayout.should_collapse_sidebar(size_category):
            # 这里可以实现侧边栏折叠逻辑
            pass
    
    # === 保持与原有主窗口的API兼容性 ===
    
    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = os.path.join("resources", "icons", "app_icon.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            else:
                self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)
        
        # 时间标签
        self.time_label = QLabel()
        status_bar.addPermanentWidget(self.time_label)
        
        # 更新时间
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self._update_time)
        self.time_timer.start(1000)
        self._update_time()
    
    def _update_time(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.status_message.connect(self.show_status_message)
        self.progress_update.connect(self.update_progress)
    
    def _apply_user_preferences(self):
        """应用用户偏好设置"""
        try:
            preferences = self.user_preferences.get_all_preferences()
            
            if 'window_geometry' in preferences and preferences['window_geometry']:
                # 从base64字符串恢复QByteArray
                import base64
                from PyQt5.QtCore import QByteArray
                geometry_data = base64.b64decode(preferences['window_geometry'])
                geometry_array = QByteArray(geometry_data)
                self.restoreGeometry(geometry_array)
                
        except Exception as e:
            self.logger.warning(f"应用用户偏好设置失败: {e}")
    
    def _show_welcome_message(self):
        """显示欢迎信息"""
        self.show_status_message("欢迎使用现代化月度工资异动处理系统")
        toast_manager.show_success("系统启动成功！欢迎使用现代化界面", 4000)
    
    # === 保持原有的所有业务方法 ===
    
    def show_status_message(self, message: str, timeout: int = 5000):
        """显示状态消息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))
        self.logger.info(f"状态消息: {message}")
    
    def update_progress(self, value: int):
        """更新进度条"""
        self.progress_bar.setValue(value)
        if value == 0:
            self.progress_bar.setVisible(False)
        else:
            self.progress_bar.setVisible(True)
    
    def show_import_dialog(self):
        """显示数据导入对话框"""
        try:
            dialog = DataImportDialog(self)
            if dialog.exec_() == dialog.Accepted:
                # 获取导入的数据
                imported_data = dialog.get_imported_data()
                if imported_data and imported_data.get('data'):
                    # 显示导入的数据到主界面
                    self.main_content_widget.data_table.set_data(
                        imported_data['data'], 
                        imported_data['headers']
                    )
                    toast_manager.show_success(f"数据导入成功，共导入 {len(imported_data['data'])} 行数据")
                    self.refresh_data_display()
                else:
                    toast_manager.show_success("数据导入成功")
                    self.refresh_data_display()
        except Exception as e:
            self.logger.error(f"显示导入对话框失败: {e}")
            toast_manager.show_error(f"显示导入对话框失败: {e}")
    
    def validate_imported_data(self):
        """验证导入的数据"""
        try:
            toast_manager.show_info("开始数据验证...")
            # 这里实现数据验证逻辑
            toast_manager.show_success("数据验证完成")
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            toast_manager.show_error(f"数据验证失败: {e}")
    
    def show_detection_window(self):
        """显示异动检测窗口"""
        try:
            if self.change_detection_window is None:
                self.change_detection_window = ChangeDetectionWindow(
                    self.config_manager, 
                    self.baseline_manager,
                    self
                )
            self.change_detection_window.show()
            self.change_detection_window.raise_()
            toast_manager.show_info("打开异动检测窗口")
        except Exception as e:
            self.logger.error(f"显示检测窗口失败: {e}")
            toast_manager.show_error(f"显示检测窗口失败: {e}")
    
    def show_report_window(self):
        """显示报告生成窗口"""
        try:
            if self.report_generation_window is None:
                self.report_generation_window = ReportGenerationWindow(self)
            self.report_generation_window.show()
            self.report_generation_window.raise_()
            toast_manager.show_info("打开报告生成窗口")
        except Exception as e:
            self.logger.error(f"显示报告窗口失败: {e}")
            toast_manager.show_error(f"显示报告窗口失败: {e}")
    
    def generate_word_report(self):
        """生成Word报告"""
        try:
            toast_manager.show_info("开始生成Word报告...")
            # 这里实现Word报告生成逻辑
            toast_manager.show_success("Word报告生成完成")
        except Exception as e:
            self.logger.error(f"生成Word报告失败: {e}")
            toast_manager.show_error(f"生成Word报告失败: {e}")
    
    def generate_excel_report(self):
        """生成Excel报告"""
        try:
            toast_manager.show_info("开始生成Excel报告...")
            # 这里实现Excel报告生成逻辑
            toast_manager.show_success("Excel报告生成完成")
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {e}")
            toast_manager.show_error(f"生成Excel报告失败: {e}")
    
    def show_settings_dialog(self):
        """显示设置对话框"""
        try:
            dialog = SettingsDialog(self)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            toast_manager.show_error(f"显示设置对话框失败: {e}")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        try:
            dialog = AboutDialog(self)
            dialog.exec_()
        except Exception as e:
            self.logger.error(f"显示关于对话框失败: {e}")
            toast_manager.show_error(f"显示关于对话框失败: {e}")
    
    def refresh_data_display(self):
        """刷新数据显示"""
        try:
            # 这里实现数据刷新逻辑
            toast_manager.show_info("数据已刷新")
        except Exception as e:
            self.logger.error(f"刷新数据显示失败: {e}")
            toast_manager.show_error(f"刷新数据显示失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 保存用户偏好设置 - 正确处理window_geometry
            window_geometry = self.saveGeometry()
            if hasattr(window_geometry, 'data'):
                # 转换QByteArray为base64字符串
                import base64
                geometry_data = base64.b64encode(window_geometry.data()).decode('utf-8')
                self.user_preferences.set_preference('window_geometry', geometry_data)
            
            # 关闭子窗口
            if self.change_detection_window:
                self.change_detection_window.close()
            if self.report_generation_window:
                self.report_generation_window.close()
            
            event.accept()
            self.logger.info("主窗口已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭窗口时发生错误: {e}")
            event.accept() 