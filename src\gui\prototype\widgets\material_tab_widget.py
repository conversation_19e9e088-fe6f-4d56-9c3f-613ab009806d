"""
Material Design标签组件

该模块实现了符合Material Design规范的标签栏组件，提供现代化的视觉体验、
流畅的切换动画和响应式布局适配。

主要功能:
- Material Design 3.0色彩系统
- 60fps切换动画
- 响应式标签栏布局
- 波纹点击效果
- 上下文感知反馈
"""

import sys
import math
from typing import List, Optional, Dict, Any
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class MaterialColorSystem:
    """Material Design 3.0 色彩系统"""
    
    # Google Material Design 3.0 色彩规范
    PRIMARY = {
        'primary_10': '#001F25',
        'primary_20': '#002F36', 
        'primary_30': '#004048',
        'primary_40': '#00525D',
        'primary_50': '#006573',
        'primary_60': '#4285F4',  # Google Blue
        'primary_70': '#1976D2',  # 添加缺失的primary_70
        'primary_80': '#B3E5FC',
        'primary_90': '#E1F5FE'
    }
    
    SECONDARY = {
        'secondary_40': '#34A853',  # Google Green
        'secondary_60': '#81C784',
        'secondary_80': '#C8E6C9'
    }
    
    NEUTRAL = {
        'neutral_10': '#1A1A1A',
        'neutral_20': '#2D2D2D',
        'neutral_30': '#404040',
        'neutral_40': '#666666',
        'neutral_50': '#808080',
        'neutral_60': '#999999',
        'neutral_80': '#CCCCCC',
        'neutral_90': '#F5F5F5',
        'neutral_95': '#FAFAFA',
        'neutral_99': '#FFFFFF'
    }
    
    def get_color_for_state(self, color_role: str, state: str) -> str:
        """根据组件状态获取对应颜色"""
        state_mappings = {
            'normal': f'{color_role}_60',
            'hover': f'{color_role}_50',
            'pressed': f'{color_role}_70',
            'disabled': f'{color_role}_30',
            'active': f'{color_role}_40'
        }
        
        color_dict = getattr(self, color_role.upper(), {})
        color_key = state_mappings.get(state, f'{color_role}_60')
        return color_dict.get(color_key, '#4285F4')  # 默认返回Google Blue

class MaterialAnimationSystem(QObject):
    """Material Design动画系统"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.running_animations = {}
        
    def create_material_transition(self, widget: QWidget, property_name: str, 
                                 start_value: Any, end_value: Any, duration: int = 300) -> QPropertyAnimation:
        """创建Material Design风格的过渡动画"""
        animation = QPropertyAnimation(widget, property_name.encode())
        animation.setDuration(duration)
        animation.setStartValue(start_value)
        animation.setEndValue(end_value)
        
        # Material Design标准缓动曲线
        animation.setEasingCurve(QEasingCurve.OutCubic)
        
        return animation
    
    def create_ripple_effect(self, widget: QWidget, click_position: QPoint):
        """创建波纹效果动画"""
        # 简化版波纹效果 - 通过透明度和缩放实现
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        # 透明度动画
        fade_animation = QPropertyAnimation(effect, b"opacity")
        fade_animation.setDuration(300)
        fade_animation.setStartValue(0.7)
        fade_animation.setEndValue(1.0)
        fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 动画完成后清理
        fade_animation.finished.connect(lambda: widget.setGraphicsEffect(None))
        fade_animation.start()
        
        return fade_animation

class MaterialTabBar(QTabBar):
    """Material Design标签栏"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.color_system = MaterialColorSystem()
        self.animation_system = MaterialAnimationSystem(self)
        self._indicator_position = 0  # 使用私有变量
        self.indicator_width = 0
        self.indicator_animation = None
        
        self._setup_style()
        self._setup_animations()
        
    def _setup_style(self):
        """设置标签栏样式"""
        self.setDrawBase(False)
        self.setExpanding(False)
        
        style = f"""
        QTabBar::tab {{
                background-color: transparent;
            color: {self.color_system.NEUTRAL['neutral_40']};
                border: none;
            padding: 12px 20px;
            margin: 0px;
            font-weight: 500;
            font-size: 14px;
            min-width: 80px;
        }}
        
        QTabBar::tab:selected {{
            color: {self.color_system.PRIMARY['primary_60']};
            background-color: transparent;
        }}
        
        QTabBar::tab:hover {{
            color: {self.color_system.PRIMARY['primary_50']};
            background-color: {self.color_system.PRIMARY['primary_90']};
            border-radius: 8px;
        }}
        
        QTabBar::tab:pressed {{
            color: {self.color_system.PRIMARY['primary_70']};
            background-color: {self.color_system.PRIMARY['primary_80']};
        }}
        """
        self.setStyleSheet(style)
        
    def _setup_animations(self):
        """设置动画系统"""
        self.indicator_animation = QPropertyAnimation(self, b"indicator_position")
        self.indicator_animation.setDuration(250)
        self.indicator_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def mousePressEvent(self, event):
        """鼠标按压事件 - 添加波纹效果"""
        tab_index = self.tabAt(event.pos())
        if tab_index >= 0:
            # 波纹效果
            self.animation_system.create_ripple_effect(self, event.pos())
        
        super().mousePressEvent(event)
        
    def tabSizeHint(self, index):
        """标签尺寸提示"""
        size = super().tabSizeHint(index)
        # 确保最小宽度和高度
        size.setWidth(max(100, size.width()))
        size.setHeight(48)  # Material Design标准高度
        return size
        
    def paintEvent(self, event):
        """自定义绘制事件"""
        super().paintEvent(event)
        
        # 绘制Material Design指示器
        if self.count() > 0:
            self._paint_indicator(event)
            
    def _paint_indicator(self, event):
        """绘制活动标签指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取当前选中标签的位置和宽度
        current_tab = self.currentIndex()
        if current_tab >= 0:
            tab_rect = self.tabRect(current_tab)
            
            # 指示器颜色
            color = QColor(self.color_system.PRIMARY['primary_60'])
            painter.setBrush(QBrush(color))
            painter.setPen(Qt.NoPen)
            
            # 绘制圆角矩形指示器
            indicator_rect = QRect(
                tab_rect.left() + 10,
                tab_rect.bottom() - 3,
                tab_rect.width() - 20,
                3
            )
            painter.drawRoundedRect(indicator_rect, 2, 2)
            
    def setCurrentIndex(self, index):
        """设置当前索引，带动画效果"""
        if index != self.currentIndex():
            old_rect = self.tabRect(self.currentIndex()) if self.currentIndex() >= 0 else QRect()
            super().setCurrentIndex(index)
            new_rect = self.tabRect(index)
            
            # 动画指示器移动
            if not old_rect.isNull() and not new_rect.isNull():
                self._animate_indicator(old_rect, new_rect)
                
    def _animate_indicator(self, old_rect, new_rect):
        """动画指示器移动"""
        if self.indicator_animation.state() == QAbstractAnimation.Running:
            self.indicator_animation.stop()
            
        self.indicator_animation.setStartValue(old_rect.center().x())
        self.indicator_animation.setEndValue(new_rect.center().x())
        self.indicator_animation.start()
        
    # 指示器位置属性
    def get_indicator_position(self):
        return self._indicator_position
        
    def set_indicator_position(self, position):
        self._indicator_position = position  # 使用私有变量，避免递归
        self.update()
        
    indicator_position = pyqtProperty(float, get_indicator_position, set_indicator_position)

class MaterialTabWidget(QTabWidget):
    """Material Design标签组件"""
    
    # 信号定义
    tab_changed = pyqtSignal(int)  # 标签改变信号
    tab_closing_requested = pyqtSignal(int)  # 标签关闭请求信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.color_system = MaterialColorSystem()
        self.animation_system = MaterialAnimationSystem(self)
        self.responsive_breakpoints = {
            'xs': 480,   # 超小屏
            'sm': 768,   # 小屏
            'md': 1024,  # 中屏
            'lg': 1440,  # 大屏
            'xl': 1920   # 超大屏
        }
        
        self._setup_ui()
        self._setup_style()
        self._setup_responsive_behavior()
        
    def _setup_ui(self):
        """设置UI组件"""
        # 使用自定义标签栏
        material_tab_bar = MaterialTabBar(self)
        self.setTabBar(material_tab_bar)
        
        # 设置标签位置
        self.setTabPosition(QTabWidget.North)
        self.setMovable(True)
        self.setTabsClosable(False)  # 默认不显示关闭按钮
        
        # 连接信号
        self.currentChanged.connect(self._on_tab_changed)
        
    def _setup_style(self):
        """设置组件样式"""
        style = f"""
        QTabWidget::pane {{
            border: 1px solid {self.color_system.NEUTRAL['neutral_80']};
            border-top: none;
            background-color: {self.color_system.NEUTRAL['neutral_99']};
            border-radius: 0px 0px 8px 8px;
        }}
        
        QTabWidget::tab-bar {{
            left: 0px;
            alignment: left;
        }}
        """
        self.setStyleSheet(style)
        
    def _setup_responsive_behavior(self):
        """设置响应式行为"""
        # 监听窗口大小变化
        if self.parent():
            self.parent().resizeEvent = self._on_parent_resize
            
    def _on_parent_resize(self, event):
        """父窗口大小改变事件"""
        if hasattr(self.parent(), 'resizeEvent'):
            # 调用原始的resize事件
            original_resize = getattr(self.parent().__class__, 'resizeEvent', None)
            if original_resize:
                original_resize(self.parent(), event)
        
        # 响应式布局调整
        self._adjust_responsive_layout(event.size().width())
        
    def _adjust_responsive_layout(self, width: int):
        """调整响应式布局"""
        # 根据屏幕宽度调整标签栏行为
        if width <= self.responsive_breakpoints['xs']:
            # 超小屏：隐藏文本，只显示图标
            self._set_compact_mode(True)
        elif width <= self.responsive_breakpoints['sm']:
            # 小屏：紧凑模式
            self._set_compact_mode(True)
        else:
            # 大屏：正常模式
            self._set_compact_mode(False)
            
    def _set_compact_mode(self, compact: bool):
        """设置紧凑模式"""
        tab_bar = self.tabBar()
        if compact:
            # 紧凑模式：减小内边距
            tab_bar.setStyleSheet(tab_bar.styleSheet().replace("padding: 12px 20px", "padding: 8px 12px"))
        else:
            # 正常模式：恢复内边距
            tab_bar.setStyleSheet(tab_bar.styleSheet().replace("padding: 8px 12px", "padding: 12px 20px"))
            
    def _on_tab_changed(self, index: int):
        """标签改变事件"""
        # 发射自定义信号
        self.tab_changed.emit(index)
        
        # 添加切换动画
        self._animate_tab_change(index)
        
    def _animate_tab_change(self, index: int):
        """标签切换动画"""
        current_widget = self.currentWidget()
        if current_widget:
            # 淡入效果
            effect = QGraphicsOpacityEffect()
            current_widget.setGraphicsEffect(effect)
            
            fade_in = QPropertyAnimation(effect, b"opacity")
            fade_in.setDuration(200)
            fade_in.setStartValue(0.7)
            fade_in.setEndValue(1.0)
            fade_in.setEasingCurve(QEasingCurve.OutCubic)
            
            # 动画完成后清理
            fade_in.finished.connect(lambda: current_widget.setGraphicsEffect(None))
            fade_in.start()
            
    def add_material_tab(self, widget: QWidget, text: str, icon: QIcon = None) -> int:
        """添加Material Design风格标签"""
        if icon:
            index = self.addTab(widget, icon, text)
        else:
            index = self.addTab(widget, text)
            
        # 添加入场动画
        self._animate_tab_add(index)
        
        return index
        
    def _animate_tab_add(self, index: int):
        """标签添加动画"""
        tab_bar = self.tabBar()
        tab_rect = tab_bar.tabRect(index)
        
        # 缩放入场动画
        if hasattr(tab_bar, 'animate_tab_add'):
            tab_bar.animate_tab_add(index)
            
    def remove_material_tab(self, index: int):
        """移除标签（带动画）"""
        if 0 <= index < self.count():
            # 移除动画
            self._animate_tab_remove(index)
            
            # 延迟移除
            QTimer.singleShot(200, lambda: self.removeTab(index))
            
    def _animate_tab_remove(self, index: int):
        """标签移除动画"""
        widget = self.widget(index)
        if widget:
            # 淡出效果
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
            
            fade_out = QPropertyAnimation(effect, b"opacity")
            fade_out.setDuration(200)
            fade_out.setStartValue(1.0)
            fade_out.setEndValue(0.0)
            fade_out.setEasingCurve(QEasingCurve.InCubic)
            fade_out.start()
            
    def set_tab_closable(self, closable: bool):
        """设置标签是否可关闭"""
        self.setTabsClosable(closable)
        if closable:
            self.tabCloseRequested.connect(self.tab_closing_requested.emit)
            
    def get_current_breakpoint(self) -> str:
        """获取当前响应式断点"""
        width = self.width()
        for name, breakpoint in sorted(self.responsive_breakpoints.items(), 
                                     key=lambda x: x[1], reverse=True):
            if width >= breakpoint:
                return name
        return 'xs'

# 测试和演示代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("Material Design标签组件测试")
    window.setGeometry(100, 100, 800, 600)
    
    # 创建Material标签组件
    tab_widget = MaterialTabWidget()
    
    # 添加测试标签页
    for i in range(4):
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.addWidget(QLabel(f"这是第{i+1}个标签页的内容"))
        layout.addWidget(QPushButton(f"按钮 {i+1}"))
        layout.addStretch()
        
        tab_widget.add_material_tab(content, f"标签 {i+1}")
    
    # 设置为中央组件
    window.setCentralWidget(tab_widget)
    
    # 显示窗口
    window.show()
    
    # 连接测试信号
    tab_widget.tab_changed.connect(lambda index: print(f"切换到标签: {index}"))
    
    sys.exit(app.exec_()) 