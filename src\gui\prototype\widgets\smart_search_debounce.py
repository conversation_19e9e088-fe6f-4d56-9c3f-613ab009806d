"""
PyQt5重构高保真原型 - 智能防抖搜索算法

基于用户输入模式和查询复杂度的智能防抖搜索系统。
实现300ms基础延迟，智能调整延迟时间，避免无效查询并优化用户体验。

主要功能:
1. 智能延迟计算 (基于查询复杂度)
2. 用户输入模式分析
3. 查询历史管理和缓存
4. 实时搜索结果优化
5. 防抖参数自适应调整
"""

import time
import re
from typing import Dict, List, Optional, Any, Callable
from collections import deque, defaultdict
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
import logging

from src.utils.log_config import setup_logger


class InputPatternAnalyzer:
    """
    输入模式分析器
    
    分析用户输入习惯和查询模式，为智能防抖提供决策支持。
    """
    
    def __init__(self):
        self.input_history = deque(maxlen=100)  # 保留最近100次输入
        self.typing_speeds = deque(maxlen=20)   # 保留最近20次打字速度
        self.query_patterns = defaultdict(int)  # 查询模式统计
        self.logger = setup_logger(__name__ + ".InputPatternAnalyzer")
    
    def record_input(self, text: str, timestamp: float = None):
        """
        记录用户输入
        
        Args:
            text: 输入文本
            timestamp: 输入时间戳
        """
        if timestamp is None:
            timestamp = time.time()
        
        input_record = {
            'text': text,
            'timestamp': timestamp,
            'length': len(text)
        }
        
        # 计算打字速度
        if self.input_history:
            last_input = self.input_history[-1]
            time_diff = timestamp - last_input['timestamp']
            char_diff = abs(len(text) - last_input['length'])
            
            if time_diff > 0:
                typing_speed = char_diff / time_diff  # 字符/秒
                self.typing_speeds.append(typing_speed)
        
        self.input_history.append(input_record)
        
        # 分析查询模式
        self._analyze_query_pattern(text)
    
    def _analyze_query_pattern(self, text: str):
        """
        分析查询模式
        
        Args:
            text: 查询文本
        """
        # 记录查询长度模式
        length_category = self._categorize_length(len(text))
        self.query_patterns[f"length_{length_category}"] += 1
        
        # 记录查询类型模式
        query_type = self._categorize_query_type(text)
        self.query_patterns[f"type_{query_type}"] += 1
        
        # 记录特殊字符使用模式
        if self._has_special_chars(text):
            self.query_patterns["has_special_chars"] += 1
    
    def _categorize_length(self, length: int) -> str:
        """
        分类查询长度
        
        Args:
            length: 查询长度
            
        Returns:
            长度类别
        """
        if length <= 2:
            return "short"
        elif length <= 6:
            return "medium"
        elif length <= 12:
            return "long"
        else:
            return "very_long"
    
    def _categorize_query_type(self, text: str) -> str:
        """
        分类查询类型
        
        Args:
            text: 查询文本
            
        Returns:
            查询类型
        """
        if text.isdigit():
            return "numeric"
        elif re.match(r'^[a-zA-Z]+$', text):
            return "alphabetic"
        elif re.match(r'^[\u4e00-\u9fff]+$', text):
            return "chinese"
        elif ' ' in text:
            return "phrase"
        else:
            return "mixed"
    
    def _has_special_chars(self, text: str) -> bool:
        """
        检查是否包含特殊字符
        
        Args:
            text: 查询文本
            
        Returns:
            是否包含特殊字符
        """
        special_chars = ['*', '?', '+', '|', '(', ')', '[', ']', '{', '}', '^', '$', '.', '\\']
        return any(char in text for char in special_chars)
    
    def get_average_typing_speed(self) -> float:
        """
        获取平均打字速度
        
        Returns:
            平均打字速度 (字符/秒)
        """
        if not self.typing_speeds:
            return 2.0  # 默认速度
        return sum(self.typing_speeds) / len(self.typing_speeds)
    
    def predict_completion_time(self, current_text: str) -> float:
        """
        预测用户完成输入的时间
        
        Args:
            current_text: 当前输入文本
            
        Returns:
            预测完成时间 (秒)
        """
        avg_speed = self.get_average_typing_speed()
        
        # 基于历史模式预测用户可能的最终长度
        predicted_final_length = self._predict_final_length(current_text)
        remaining_chars = max(0, predicted_final_length - len(current_text))
        
        return remaining_chars / avg_speed if avg_speed > 0 else 1.0
    
    def _predict_final_length(self, current_text: str) -> int:
        """
        预测最终输入长度
        
        Args:
            current_text: 当前输入文本
            
        Returns:
            预测的最终长度
        """
        current_length = len(current_text)
        
        # 基于历史模式预测
        query_type = self._categorize_query_type(current_text)
        
        # 根据查询类型和历史模式预测
        type_pattern_key = f"type_{query_type}"
        if type_pattern_key in self.query_patterns:
            # 简单预测：当前长度 + 预期增长
            if query_type == "numeric":
                return current_length + 2  # 数字查询通常较短
            elif query_type == "chinese":
                return current_length + 3  # 中文查询适中
            elif query_type == "phrase":
                return current_length + 5  # 短语查询较长
            else:
                return current_length + 3  # 默认增长
        else:
            return current_length + 3  # 默认预测
    
    def get_user_behavior_score(self) -> float:
        """
        获取用户行为评分
        
        Returns:
            行为评分 (0-1, 越高表示用户越倾向于快速完成输入)
        """
        if not self.input_history:
            return 0.5  # 默认评分
        
        # 计算输入稳定性
        recent_inputs = list(self.input_history)[-10:]
        if len(recent_inputs) < 3:
            return 0.5
        
        # 分析输入间隔的稳定性
        intervals = []
        for i in range(1, len(recent_inputs)):
            interval = recent_inputs[i]['timestamp'] - recent_inputs[i-1]['timestamp']
            intervals.append(interval)
        
        if not intervals:
            return 0.5
        
        # 计算间隔的变异系数
        mean_interval = sum(intervals) / len(intervals)
        if mean_interval == 0:
            return 0.5
        
        variance = sum((x - mean_interval) ** 2 for x in intervals) / len(intervals)
        std_dev = variance ** 0.5
        cv = std_dev / mean_interval  # 变异系数
        
        # 变异系数越小，用户行为越稳定，评分越高
        stability_score = max(0, 1 - cv)
        
        return min(1.0, stability_score)


class QueryCache:
    """
    查询缓存管理器
    
    管理查询结果缓存，避免重复查询，提升响应速度。
    """
    
    def __init__(self, max_size: int = 100):
        self.cache = {}
        self.access_order = deque()
        self.max_size = max_size
        self.hit_count = 0
        self.miss_count = 0
        self.logger = setup_logger(__name__ + ".QueryCache")
    
    def get(self, query: str) -> Optional[Any]:
        """
        获取缓存结果
        
        Args:
            query: 查询字符串
            
        Returns:
            缓存的结果，如果不存在则返回None
        """
        if query in self.cache:
            # 更新访问顺序
            self.access_order.remove(query)
            self.access_order.append(query)
            self.hit_count += 1
            return self.cache[query]
        else:
            self.miss_count += 1
            return None
    
    def put(self, query: str, result: Any):
        """
        缓存查询结果
        
        Args:
            query: 查询字符串
            result: 查询结果
        """
        # 如果已存在，更新结果和访问顺序
        if query in self.cache:
            self.access_order.remove(query)
        
        # 如果缓存已满，移除最久未使用的
        elif len(self.cache) >= self.max_size:
            oldest = self.access_order.popleft()
            del self.cache[oldest]
        
        self.cache[query] = result
        self.access_order.append(query)
    
    def clear(self):
        """
        清空缓存
        """
        self.cache.clear()
        self.access_order.clear()
        self.hit_count = 0
        self.miss_count = 0
    
    def get_hit_rate(self) -> float:
        """
        获取缓存命中率
        
        Returns:
            命中率 (0-1)
        """
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0


class SmartSearchDebounce(QObject):
    """
    智能防抖搜索算法
    
    基于用户输入模式和查询复杂度的智能防抖搜索系统。
    """
    
    # 搜索信号
    search_triggered = pyqtSignal(str, dict)  # (query, context)
    search_cancelled = pyqtSignal(str)  # (cancelled_query)
    debounce_adjusted = pyqtSignal(int)  # (new_delay_ms)
    
    def __init__(self, base_delay: int = 300, min_delay: int = 100, max_delay: int = 1000):
        """
        初始化智能防抖搜索
        
        Args:
            base_delay: 基础延迟时间 (毫秒)
            min_delay: 最小延迟时间 (毫秒)
            max_delay: 最大延迟时间 (毫秒)
        """
        super().__init__()
        self.base_delay = base_delay
        self.min_delay = min_delay
        self.max_delay = max_delay
        
        # 防抖定时器
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self._execute_search)
        
        # 当前查询信息
        self.current_query = ""
        self.current_context = {}
        self.query_start_time = 0
        
        # 分析器和缓存
        self.pattern_analyzer = InputPatternAnalyzer()
        self.query_cache = QueryCache()
        
        # 查询历史
        self.query_history = deque(maxlen=50)
        self.successful_queries = set()
        
        # 统计信息
        self.stats = {
            'total_queries': 0,
            'cached_queries': 0,
            'cancelled_queries': 0,
            'avg_delay': base_delay
        }
        
        self.logger = setup_logger(__name__)
        self.logger.info("智能防抖搜索算法初始化完成")
    
    def search(self, query: str, context: Dict[str, Any] = None):
        """
        触发搜索
        
        Args:
            query: 搜索查询
            context: 搜索上下文
        """
        # 记录输入
        current_time = time.time()
        self.pattern_analyzer.record_input(query, current_time)
        
        # 检查缓存
        cached_result = self.query_cache.get(query)
        if cached_result is not None and len(query) >= 3:
            self.logger.debug(f"缓存命中: {query}")
            self.stats['cached_queries'] += 1
            self.search_triggered.emit(query, {'cached': True, 'result': cached_result})
            return
        
        # 取消之前的搜索
        if self.timer.isActive():
            old_query = self.current_query
            self.timer.stop()
            self.stats['cancelled_queries'] += 1
            self.search_cancelled.emit(old_query)
        
        # 更新当前查询
        self.current_query = query
        self.current_context = context or {}
        self.query_start_time = current_time
        
        # 计算智能延迟
        delay = self._calculate_smart_delay(query)
        
        # 启动定时器
        self.timer.start(delay)
        self.debounce_adjusted.emit(delay)
        
        self.logger.debug(f"搜索延迟: {delay}ms, 查询: '{query}'")
    
    def _calculate_smart_delay(self, query: str) -> int:
        """
        计算智能延迟
        
        Args:
            query: 查询字符串
            
        Returns:
            延迟时间 (毫秒)
        """
        delay = self.base_delay
        
        # 1. 基于查询长度调整
        if len(query) < 2:
            delay += 200  # 短查询延迟更长，等待用户继续输入
        elif len(query) < 3:
            delay += 100
        elif len(query) > 10:
            delay -= 50   # 长查询可能已经比较明确
        
        # 2. 基于查询复杂度调整
        complexity = self._analyze_query_complexity(query)
        if complexity > 0.7:
            delay += 100  # 复杂查询需要更多时间
        elif complexity < 0.3:
            delay -= 50   # 简单查询可以更快响应
        
        # 3. 基于历史查询调整
        if query in self.successful_queries:
            delay -= 100  # 之前成功的查询减少延迟
        
        if self._is_similar_to_recent_query(query):
            delay -= 50   # 与最近查询相似减少延迟
        
        # 4. 基于用户行为调整
        user_score = self.pattern_analyzer.get_user_behavior_score()
        if user_score > 0.7:
            delay -= 50   # 稳定用户减少延迟
        elif user_score < 0.3:
            delay += 50   # 不稳定用户增加延迟
        
        # 5. 基于打字速度调整
        typing_speed = self.pattern_analyzer.get_average_typing_speed()
        if typing_speed > 3.0:  # 快速打字
            delay -= 30
        elif typing_speed < 1.0:  # 慢速打字
            delay += 30
        
        # 6. 预测完成时间调整
        predicted_completion = self.pattern_analyzer.predict_completion_time(query)
        if predicted_completion < 0.5:  # 预计很快完成
            delay = max(delay, int(predicted_completion * 1000) + 100)
        
        # 确保延迟在合理范围内
        delay = max(self.min_delay, min(self.max_delay, delay))
        
        # 更新统计
        self.stats['avg_delay'] = (self.stats['avg_delay'] + delay) / 2
        
        return delay
    
    def _analyze_query_complexity(self, query: str) -> float:
        """
        分析查询复杂度
        
        Args:
            query: 查询字符串
            
        Returns:
            复杂度值 (0-1)
        """
        complexity = 0.0
        
        # 特殊字符增加复杂度
        special_chars = ['*', '?', '+', '|', '(', ')', '[', ']', '{', '}', '^', '$']
        special_count = sum(1 for char in special_chars if char in query)
        complexity += min(0.3, special_count * 0.1)
        
        # 长度增加复杂度
        if len(query) > 15:
            complexity += 0.2
        elif len(query) > 10:
            complexity += 0.1
        
        # 多语言混合增加复杂度
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', query))
        has_english = bool(re.search(r'[a-zA-Z]', query))
        has_number = bool(re.search(r'\d', query))
        
        mixed_count = sum([has_chinese, has_english, has_number])
        if mixed_count > 1:
            complexity += 0.2
        
        # 空格分隔的多词查询
        words = query.split()
        if len(words) > 3:
            complexity += 0.2
        elif len(words) > 1:
            complexity += 0.1
        
        return min(1.0, complexity)
    
    def _is_similar_to_recent_query(self, query: str) -> bool:
        """
        检查是否与最近的查询相似
        
        Args:
            query: 当前查询
            
        Returns:
            是否相似
        """
        if not self.query_history:
            return False
        
        recent_queries = list(self.query_history)[-5:]  # 检查最近5个查询
        
        for recent_query in recent_queries:
            # 检查是否为扩展查询 (在之前基础上添加字符)
            if query.startswith(recent_query) or recent_query.startswith(query):
                return True
            
            # 检查编辑距离
            if self._calculate_edit_distance(query, recent_query) <= 2:
                return True
        
        return False
    
    def _calculate_edit_distance(self, s1: str, s2: str) -> int:
        """
        计算编辑距离
        
        Args:
            s1: 字符串1
            s2: 字符串2
            
        Returns:
            编辑距离
        """
        if len(s1) < len(s2):
            s1, s2 = s2, s1
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def _execute_search(self):
        """
        执行搜索
        """
        if not self.current_query:
            return
        
        # 记录查询历史
        self.query_history.append(self.current_query)
        self.stats['total_queries'] += 1
        
        # 构建搜索上下文
        search_context = {
            'query_complexity': self._analyze_query_complexity(self.current_query),
            'delay_used': self.stats['avg_delay'],
            'user_behavior_score': self.pattern_analyzer.get_user_behavior_score(),
            'timestamp': time.time(),
            **self.current_context
        }
        
        # 发射搜索信号
        self.search_triggered.emit(self.current_query, search_context)
        
        self.logger.info(f"执行搜索: '{self.current_query}'")
    
    def cache_result(self, query: str, result: Any):
        """
        缓存搜索结果
        
        Args:
            query: 查询字符串
            result: 搜索结果
        """
        self.query_cache.put(query, result)
        self.successful_queries.add(query)
    
    def cancel_current_search(self):
        """
        取消当前搜索
        """
        if self.timer.isActive():
            self.timer.stop()
            self.stats['cancelled_queries'] += 1
            self.search_cancelled.emit(self.current_query)
            self.logger.debug(f"取消搜索: '{self.current_query}'")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return {
            **self.stats,
            'cache_hit_rate': self.query_cache.get_hit_rate(),
            'cache_size': len(self.query_cache.cache),
            'query_history_size': len(self.query_history),
            'successful_queries': len(self.successful_queries),
            'pattern_analyzer_stats': {
                'input_history_size': len(self.pattern_analyzer.input_history),
                'avg_typing_speed': self.pattern_analyzer.get_average_typing_speed(),
                'user_behavior_score': self.pattern_analyzer.get_user_behavior_score()
            }
        }
    
    def clear_cache(self):
        """
        清空缓存
        """
        self.query_cache.clear()
        self.successful_queries.clear()
        self.logger.info("搜索缓存已清空")
    
    def reset_statistics(self):
        """
        重置统计信息
        """
        self.stats = {
            'total_queries': 0,
            'cached_queries': 0,
            'cancelled_queries': 0,
            'avg_delay': self.base_delay
        }
        self.query_history.clear()
        self.pattern_analyzer.input_history.clear()
        self.pattern_analyzer.typing_speeds.clear()
        self.pattern_analyzer.query_patterns.clear()
        
        self.logger.info("统计信息已重置") 