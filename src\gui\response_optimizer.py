"""
GUI响应时间优化模块

该模块提供完整的用户界面响应时间优化解决方案，包括：
- 用户交互响应优化：按键、点击、滚动等交互的即时响应
- 事件处理性能提升：事件队列优化、批量处理、优先级调度
- UI线程优化：主线程保护、后台任务分离、线程池管理
- 异步操作优化：非阻塞操作、进度反馈、取消机制

技术特性：
- 响应时间监控和优化
- 事件处理队列优化
- UI线程负载管理
- 异步任务调度
- 性能指标实时监控
- 智能优化策略
"""

import time
import threading
import queue
import functools
import logging
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
from PyQt5.QtCore import (
    QObject, QTimer, QThread, pyqtSignal, QMutex, QWaitCondition,
    QCoreApplication, QEventLoop, QMetaObject, Qt
)
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtGui import QCursor


# 配置日志
logger = logging.getLogger(__name__)


class ResponsePriority(Enum):
    """响应优先级枚举"""
    CRITICAL = 1    # 关键交互（用户输入）
    HIGH = 2        # 高优先级（UI更新）
    NORMAL = 3      # 普通优先级（数据处理）
    LOW = 4         # 低优先级（后台任务）


class EventType(Enum):
    """事件类型枚举"""
    USER_INPUT = "user_input"           # 用户输入事件
    UI_UPDATE = "ui_update"             # UI更新事件
    DATA_PROCESSING = "data_processing" # 数据处理事件
    BACKGROUND_TASK = "background_task" # 后台任务事件


@dataclass
class ResponseMetrics:
    """响应时间指标"""
    event_type: str
    start_time: float
    end_time: float
    duration: float
    priority: ResponsePriority
    success: bool = True
    error_message: str = ""
    
    @property
    def response_time_ms(self) -> float:
        """获取响应时间（毫秒）"""
        return self.duration * 1000


@dataclass
class OptimizationConfig:
    """优化配置"""
    # 响应时间阈值（毫秒）
    critical_threshold: float = 16.0    # 60FPS = 16ms
    high_threshold: float = 33.0        # 30FPS = 33ms
    normal_threshold: float = 100.0     # 100ms
    low_threshold: float = 1000.0       # 1s
    
    # 线程池配置
    max_workers: int = 4
    thread_pool_timeout: float = 30.0
    
    # 事件队列配置
    max_queue_size: int = 1000
    batch_size: int = 10
    batch_timeout: float = 0.016  # 16ms
    
    # 监控配置
    metrics_buffer_size: int = 1000
    performance_check_interval: float = 1.0  # 1秒


class EventProcessor(QObject):
    """事件处理器"""
    
    # 信号定义
    event_processed = pyqtSignal(str, float)  # 事件类型，处理时间
    batch_completed = pyqtSignal(int)         # 批量处理完成，处理数量
    
    def __init__(self, config: OptimizationConfig):
        super().__init__()
        self.config = config
        self.event_queue = queue.PriorityQueue(maxsize=config.max_queue_size)
        self.processing = False
        self.batch_timer = QTimer()
        self.batch_timer.timeout.connect(self._process_batch)
        self._setup_batch_processing()
        self._sequence_number = 0  # 用于优先级队列排序
        
    def _setup_batch_processing(self):
        """设置批量处理"""
        self.batch_timer.setSingleShot(False)
        self.batch_timer.setInterval(int(self.config.batch_timeout * 1000))
        
    def add_event(self, event_type: EventType, callback: Callable, 
                  priority: ResponsePriority = ResponsePriority.NORMAL,
                  *args, **kwargs):
        """添加事件到处理队列"""
        try:
            event_data = {
                'type': event_type,
                'callback': callback,
                'args': args,
                'kwargs': kwargs,
                'timestamp': time.time()
            }
            
            # 使用优先级和序列号作为队列排序键，避免字典比较问题
            self._sequence_number += 1
            queue_item = (priority.value, self._sequence_number, event_data)
            self.event_queue.put(queue_item, block=False)
            
            # 启动批量处理定时器
            if not self.batch_timer.isActive():
                self.batch_timer.start()
                
        except queue.Full:
            logger.warning("事件队列已满，丢弃事件")
            
    def _process_batch(self):
        """批量处理事件"""
        if self.processing:
            return
            
        self.processing = True
        processed_count = 0
        
        try:
            # 处理一批事件
            for _ in range(self.config.batch_size):
                if self.event_queue.empty():
                    break
                    
                try:
                    priority, sequence, event_data = self.event_queue.get(block=False)
                    self._process_single_event(event_data)
                    processed_count += 1
                    
                except queue.Empty:
                    break
                except Exception as e:
                    logger.error(f"处理事件时出错: {e}")
                    
            # 如果队列为空，停止定时器
            if self.event_queue.empty():
                self.batch_timer.stop()
                
        finally:
            self.processing = False
            if processed_count > 0:
                self.batch_completed.emit(processed_count)
                
    def _process_single_event(self, event_data: Dict):
        """处理单个事件"""
        start_time = time.time()
        
        try:
            callback = event_data['callback']
            args = event_data.get('args', ())
            kwargs = event_data.get('kwargs', {})
            
            # 执行回调
            callback(*args, **kwargs)
            
            # 计算处理时间
            duration = time.time() - start_time
            self.event_processed.emit(event_data['type'].value, duration)
            
        except Exception as e:
            logger.error(f"执行事件回调时出错: {e}")


class UIThreadManager(QObject):
    """UI线程管理器"""
    
    # 信号定义
    thread_load_changed = pyqtSignal(float)  # 线程负载变化
    task_completed = pyqtSignal(str, bool)   # 任务完成，任务ID，是否成功
    
    def __init__(self, config: OptimizationConfig):
        super().__init__()
        self.config = config
        self.thread_pool = ThreadPoolExecutor(max_workers=config.max_workers)
        self.active_tasks: Dict[str, Future] = {}
        self.load_monitor = QTimer()
        self.load_monitor.timeout.connect(self._monitor_thread_load)
        self.load_monitor.start(1000)  # 每秒监控一次
        
    def execute_async(self, task_id: str, func: Callable, 
                     priority: ResponsePriority = ResponsePriority.NORMAL,
                     *args, **kwargs) -> str:
        """异步执行任务"""
        def wrapped_func():
            try:
                result = func(*args, **kwargs)
                QMetaObject.invokeMethod(
                    self, "_on_task_completed",
                    Qt.QueuedConnection,
                    task_id, True, ""
                )
                return result
            except Exception as e:
                QMetaObject.invokeMethod(
                    self, "_on_task_completed",
                    Qt.QueuedConnection,
                    task_id, False, str(e)
                )
                raise
                
        # 提交任务到线程池
        future = self.thread_pool.submit(wrapped_func)
        self.active_tasks[task_id] = future
        
        return task_id
        
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.active_tasks:
            future = self.active_tasks[task_id]
            success = future.cancel()
            if success:
                del self.active_tasks[task_id]
            return success
        return False
        
    def _on_task_completed(self, task_id: str, success: bool, error_msg: str = ""):
        """任务完成回调"""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        self.task_completed.emit(task_id, success)
        
        if not success and error_msg:
            logger.error(f"异步任务 {task_id} 执行失败: {error_msg}")
            
    def _monitor_thread_load(self):
        """监控线程负载"""
        active_count = len(self.active_tasks)
        load_percentage = (active_count / self.config.max_workers) * 100
        self.thread_load_changed.emit(load_percentage)
        
        # 清理已完成的任务
        completed_tasks = [
            task_id for task_id, future in self.active_tasks.items()
            if future.done()
        ]
        for task_id in completed_tasks:
            del self.active_tasks[task_id]
            
    def shutdown(self):
        """关闭线程池"""
        self.thread_pool.shutdown(wait=True)


class ResponseTimeOptimizer(QObject):
    """响应时间优化器主控制器"""
    
    # 信号定义
    metrics_updated = pyqtSignal(dict)      # 指标更新
    optimization_applied = pyqtSignal(str)  # 优化应用
    warning_issued = pyqtSignal(str)        # 警告发出
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        super().__init__()
        self.config = config or OptimizationConfig()
        
        # 初始化组件
        self.event_processor = EventProcessor(self.config)
        self.thread_manager = UIThreadManager(self.config)
        self.metrics_buffer: List[ResponseMetrics] = []
        self.optimization_strategies: Dict[str, Callable] = {}
        
        # 性能监控定时器
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._check_performance)
        self.performance_timer.start(int(self.config.performance_check_interval * 1000))
        
        # 连接信号
        self._connect_signals()
        
        # 注册默认优化策略
        self._register_default_strategies()
        
        logger.info("响应时间优化器初始化完成")
        
    def _connect_signals(self):
        """连接信号"""
        self.event_processor.event_processed.connect(self._on_event_processed)
        self.event_processor.batch_completed.connect(self._on_batch_completed)
        self.thread_manager.thread_load_changed.connect(self._on_thread_load_changed)
        self.thread_manager.task_completed.connect(self._on_task_completed)
        
    def _register_default_strategies(self):
        """注册默认优化策略"""
        self.optimization_strategies.update({
            'reduce_ui_updates': self._reduce_ui_updates,
            'batch_operations': self._batch_operations,
            'defer_heavy_tasks': self._defer_heavy_tasks,
            'optimize_event_handling': self._optimize_event_handling
        })
        
    def optimize_user_interaction(self, widget: QWidget, 
                                interaction_type: str = "click") -> Callable:
        """优化用户交互响应"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    # 设置光标为忙碌状态（如果需要）
                    if hasattr(widget, 'setCursor'):
                        original_cursor = widget.cursor()
                        
                    # 执行原始函数
                    result = func(*args, **kwargs)
                    
                    # 记录响应时间
                    duration = time.time() - start_time
                    self._record_metrics(
                        EventType.USER_INPUT,
                        start_time,
                        time.time(),
                        duration,
                        ResponsePriority.CRITICAL
                    )
                    
                    return result
                    
                except Exception as e:
                    # 记录错误指标
                    duration = time.time() - start_time
                    self._record_metrics(
                        EventType.USER_INPUT,
                        start_time,
                        time.time(),
                        duration,
                        ResponsePriority.CRITICAL,
                        success=False,
                        error_message=str(e)
                    )
                    raise
                    
                finally:
                    # 恢复光标
                    if hasattr(widget, 'setCursor') and 'original_cursor' in locals():
                        widget.setCursor(original_cursor)
                        
            return wrapper
        return decorator
        
    def optimize_event_handling(self, event_type: EventType,
                              priority: ResponsePriority = ResponsePriority.NORMAL):
        """优化事件处理"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 将事件添加到处理队列
                self.event_processor.add_event(
                    event_type, func, priority, *args, **kwargs
                )
            return wrapper
        return decorator
        
    def defer_to_background(self, task_id: Optional[str] = None,
                          priority: ResponsePriority = ResponsePriority.LOW):
        """将任务延迟到后台执行"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                actual_task_id = task_id or f"bg_task_{int(time.time() * 1000)}"
                return self.thread_manager.execute_async(
                    actual_task_id, func, priority, *args, **kwargs
                )
            return wrapper
        return decorator
        
    def batch_ui_updates(self, batch_size: int = 10, 
                        timeout_ms: int = 16):
        """批量UI更新优化"""
        def decorator(func: Callable) -> Callable:
            update_queue = []
            last_update = 0
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                nonlocal update_queue, last_update
                
                current_time = time.time() * 1000
                update_queue.append((func, args, kwargs))
                
                # 检查是否需要执行批量更新
                should_update = (
                    len(update_queue) >= batch_size or
                    current_time - last_update >= timeout_ms
                )
                
                if should_update:
                    # 执行批量更新
                    for update_func, update_args, update_kwargs in update_queue:
                        try:
                            update_func(*update_args, **update_kwargs)
                        except Exception as e:
                            logger.error(f"批量更新执行失败: {e}")
                            
                    update_queue.clear()
                    last_update = current_time
                    
            return wrapper
        return decorator
        
    def _record_metrics(self, event_type: EventType, start_time: float,
                       end_time: float, duration: float, 
                       priority: ResponsePriority, success: bool = True,
                       error_message: str = ""):
        """记录性能指标"""
        metrics = ResponseMetrics(
            event_type=event_type.value,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            priority=priority,
            success=success,
            error_message=error_message
        )
        
        # 添加到缓冲区
        self.metrics_buffer.append(metrics)
        
        # 限制缓冲区大小
        if len(self.metrics_buffer) > self.config.metrics_buffer_size:
            self.metrics_buffer.pop(0)
            
        # 检查是否超过阈值
        self._check_response_threshold(metrics)
        
    def _check_response_threshold(self, metrics: ResponseMetrics):
        """检查响应时间阈值"""
        threshold_map = {
            ResponsePriority.CRITICAL: self.config.critical_threshold,
            ResponsePriority.HIGH: self.config.high_threshold,
            ResponsePriority.NORMAL: self.config.normal_threshold,
            ResponsePriority.LOW: self.config.low_threshold
        }
        
        threshold = threshold_map.get(metrics.priority, self.config.normal_threshold)
        
        if metrics.response_time_ms > threshold:
            warning_msg = (
                f"响应时间超过阈值: {metrics.event_type} "
                f"({metrics.response_time_ms:.1f}ms > {threshold}ms)"
            )
            self.warning_issued.emit(warning_msg)
            logger.warning(warning_msg)
            
            # 应用优化策略
            self._apply_optimization_strategy(metrics)
            
    def _apply_optimization_strategy(self, metrics: ResponseMetrics):
        """应用优化策略"""
        if metrics.priority == ResponsePriority.CRITICAL:
            strategy = 'optimize_event_handling'
        elif metrics.response_time_ms > 500:  # 500ms
            strategy = 'defer_heavy_tasks'
        elif metrics.event_type == EventType.UI_UPDATE.value:
            strategy = 'batch_operations'
        else:
            strategy = 'reduce_ui_updates'
            
        if strategy in self.optimization_strategies:
            try:
                self.optimization_strategies[strategy]()
                self.optimization_applied.emit(f"应用优化策略: {strategy}")
            except Exception as e:
                logger.error(f"应用优化策略失败: {e}")
                
    def _reduce_ui_updates(self):
        """减少UI更新频率"""
        # 实现UI更新频率控制逻辑
        logger.info("应用优化策略: 减少UI更新频率")
        
    def _batch_operations(self):
        """批量操作优化"""
        # 实现批量操作逻辑
        logger.info("应用优化策略: 批量操作")
        
    def _defer_heavy_tasks(self):
        """延迟重任务执行"""
        # 实现任务延迟逻辑
        logger.info("应用优化策略: 延迟重任务执行")
        
    def _optimize_event_handling(self):
        """优化事件处理"""
        # 实现事件处理优化逻辑
        logger.info("应用优化策略: 优化事件处理")
        
    def _on_event_processed(self, event_type: str, duration: float):
        """事件处理完成回调"""
        logger.debug(f"事件处理完成: {event_type}, 耗时: {duration*1000:.1f}ms")
        
    def _on_batch_completed(self, count: int):
        """批量处理完成回调"""
        logger.debug(f"批量处理完成: {count}个事件")
        
    def _on_thread_load_changed(self, load_percentage: float):
        """线程负载变化回调"""
        if load_percentage > 80:  # 80%负载警告
            self.warning_issued.emit(f"线程负载过高: {load_percentage:.1f}%")
            
    def _on_task_completed(self, task_id: str, success: bool):
        """任务完成回调"""
        logger.debug(f"异步任务完成: {task_id}, 成功: {success}")
        
    def _check_performance(self):
        """检查性能指标"""
        if not self.metrics_buffer:
            return
            
        # 计算最近的性能指标
        recent_metrics = self.metrics_buffer[-100:]  # 最近100个指标
        
        # 计算平均响应时间
        avg_response_time = sum(m.response_time_ms for m in recent_metrics) / len(recent_metrics)
        
        # 计算成功率
        success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics) * 100
        
        # 按事件类型分组统计
        type_stats = {}
        for metrics in recent_metrics:
            event_type = metrics.event_type
            if event_type not in type_stats:
                type_stats[event_type] = []
            type_stats[event_type].append(metrics.response_time_ms)
            
        # 计算各类型平均响应时间
        type_averages = {
            event_type: sum(times) / len(times)
            for event_type, times in type_stats.items()
        }
        
        # 发送指标更新信号
        metrics_data = {
            'avg_response_time': avg_response_time,
            'success_rate': success_rate,
            'type_averages': type_averages,
            'total_events': len(recent_metrics),
            'thread_load': len(self.thread_manager.active_tasks)
        }
        
        self.metrics_updated.emit(metrics_data)
        
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.metrics_buffer:
            return {'status': 'no_data'}
            
        # 计算总体统计
        total_events = len(self.metrics_buffer)
        successful_events = sum(1 for m in self.metrics_buffer if m.success)
        failed_events = total_events - successful_events
        
        # 计算响应时间统计
        response_times = [m.response_time_ms for m in self.metrics_buffer]
        avg_response = sum(response_times) / len(response_times)
        min_response = min(response_times)
        max_response = max(response_times)
        
        # 按优先级统计
        priority_stats = {}
        for priority in ResponsePriority:
            priority_metrics = [m for m in self.metrics_buffer if m.priority == priority]
            if priority_metrics:
                priority_times = [m.response_time_ms for m in priority_metrics]
                priority_stats[priority.name] = {
                    'count': len(priority_metrics),
                    'avg_response': sum(priority_times) / len(priority_times),
                    'success_rate': sum(1 for m in priority_metrics if m.success) / len(priority_metrics) * 100
                }
                
        return {
            'status': 'success',
            'total_events': total_events,
            'successful_events': successful_events,
            'failed_events': failed_events,
            'success_rate': (successful_events / total_events) * 100,
            'avg_response_time': avg_response,
            'min_response_time': min_response,
            'max_response_time': max_response,
            'priority_stats': priority_stats,
            'active_threads': len(self.thread_manager.active_tasks),
            'timestamp': time.time()
        }
        
    def cleanup(self):
        """清理资源"""
        try:
            # 停止定时器
            if self.performance_timer.isActive():
                self.performance_timer.stop()
                
            # 关闭线程管理器
            self.thread_manager.shutdown()
            
            # 清空缓冲区
            self.metrics_buffer.clear()
            
            logger.info("响应时间优化器清理完成")
            
        except Exception as e:
            logger.error(f"清理响应时间优化器时出错: {e}")


# 便利函数和装饰器
def optimize_response_time(optimizer: ResponseTimeOptimizer,
                         event_type: EventType = EventType.USER_INPUT,
                         priority: ResponsePriority = ResponsePriority.NORMAL):
    """响应时间优化装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 记录指标
                optimizer._record_metrics(
                    event_type, start_time, time.time(), 
                    duration, priority, True
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                optimizer._record_metrics(
                    event_type, start_time, time.time(),
                    duration, priority, False, str(e)
                )
                raise
                
        return wrapper
    return decorator


def create_response_optimizer(config: Optional[OptimizationConfig] = None) -> ResponseTimeOptimizer:
    """创建响应时间优化器实例"""
    return ResponseTimeOptimizer(config)


# 导出主要类和函数
__all__ = [
    'ResponseTimeOptimizer',
    'OptimizationConfig', 
    'ResponsePriority',
    'EventType',
    'ResponseMetrics',
    'optimize_response_time',
    'create_response_optimizer'
] 