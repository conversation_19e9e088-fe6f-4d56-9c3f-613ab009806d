"""
月度工资异动处理系统 - 功能窗口模块

本模块实现系统的各个功能窗口，提供专门的功能界面。

主要窗口:
1. ChangeDetectionWindow - 异动检测窗口
2. ReportGenerationWindow - 报告生成窗口
"""

from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QTextEdit,
    QSplitter, QGroupBox, QTabWidget, QProgressBar, QComboBox,
    QSpinBox, QCheckBox, QLineEdit, QFileDialog, QMessageBox,
    QHeaderView, QAbstractItemView, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QColor, QBrush, QIcon

from src.utils.log_config import setup_logger
from src.modules.change_detection import ChangeDetector
from src.modules.report_generation import ReportManager


class ChangeDetectionWindow(QMainWindow):
    """异动检测窗口"""
    
    # 信号定义
    detection_completed = pyqtSignal(dict)  # 检测完成信号
    
    def __init__(self, config_manager=None, baseline_manager=None, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 如果没有传入管理器，创建默认的
        if config_manager is None:
            from src.core.config_manager import ConfigManager
            config_manager = ConfigManager()
        if baseline_manager is None:
            from src.modules.change_detection import BaselineManager
            baseline_manager = BaselineManager(config_manager)
            
        self.change_detector = ChangeDetector(config_manager, baseline_manager)
        self.detection_results = {}
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("异动检测")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 设置窗口图标
        self.setWindowIcon(self.style().standardIcon(self.style().SP_FileDialogDetailedView))
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 设置样式
        self._set_window_style()
        
        self.logger.info("异动检测窗口初始化完成")
    
    def _create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建控制面板
        self._create_control_panel(main_layout)
        
        # 创建结果显示区域
        self._create_results_area(main_layout)
    
    def _create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_frame.setMaximumHeight(120)
        
        control_layout = QHBoxLayout(control_frame)
        
        # 检测参数组
        params_group = QGroupBox("检测参数")
        params_layout = QGridLayout(params_group)
        
        # 检测类型
        params_layout.addWidget(QLabel("检测类型:"), 0, 0)
        self.detection_type_combo = QComboBox()
        self.detection_type_combo.addItems(["全部异动", "工资增加", "工资减少", "新增人员", "离职人员"])
        params_layout.addWidget(self.detection_type_combo, 0, 1)
        
        # 阈值设置
        params_layout.addWidget(QLabel("变化阈值:"), 1, 0)
        self.threshold_spin = QSpinBox()
        self.threshold_spin.setRange(0, 10000)
        self.threshold_spin.setValue(100)
        self.threshold_spin.setSuffix(" 元")
        params_layout.addWidget(self.threshold_spin, 1, 1)
        
        control_layout.addWidget(params_group)
        
        # 操作按钮组
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout(actions_group)
        
        # 开始检测按钮
        self.start_detection_btn = QPushButton("开始检测")
        self.start_detection_btn.setMinimumHeight(35)
        self.start_detection_btn.clicked.connect(self.start_detection)
        actions_layout.addWidget(self.start_detection_btn)
        
        # 导出结果按钮
        self.export_results_btn = QPushButton("导出结果")
        self.export_results_btn.setMinimumHeight(35)
        self.export_results_btn.setEnabled(False)
        self.export_results_btn.clicked.connect(self.export_results)
        actions_layout.addWidget(self.export_results_btn)
        
        control_layout.addWidget(actions_group)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("等待检测...")
        stats_layout.addWidget(self.stats_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        stats_layout.addWidget(self.progress_bar)
        
        control_layout.addWidget(stats_group)
        
        parent_layout.addWidget(control_frame)
    
    def _create_results_area(self, parent_layout):
        """创建结果显示区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：异动列表
        self._create_changes_list(splitter)
        
        # 右侧：详细信息
        self._create_detail_view(splitter)
        
        # 设置分割比例
        splitter.setSizes([600, 400])
        
        parent_layout.addWidget(splitter)
    
    def _create_changes_list(self, parent_splitter):
        """创建异动列表"""
        changes_widget = QWidget()
        changes_layout = QVBoxLayout(changes_widget)
        
        # 标题
        title_label = QLabel("检测到的异动")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        changes_layout.addWidget(title_label)
        
        # 异动表格
        self.changes_table = QTableWidget()
        self.changes_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.changes_table.setAlternatingRowColors(True)
        
        # 设置列
        columns = ["工号", "姓名", "异动类型", "变化金额", "原因分析", "置信度"]
        self.changes_table.setColumnCount(len(columns))
        self.changes_table.setHorizontalHeaderLabels(columns)
        
        # 设置列宽
        header = self.changes_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 工号
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 姓名
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 异动类型
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 变化金额
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # 原因分析
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 置信度
        
        # 连接选择信号
        self.changes_table.selectionModel().selectionChanged.connect(self._on_selection_changed)
        
        changes_layout.addWidget(self.changes_table)
        
        parent_splitter.addWidget(changes_widget)
    
    def _create_detail_view(self, parent_splitter):
        """创建详细信息视图"""
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        
        # 标题
        title_label = QLabel("详细信息")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        detail_layout.addWidget(title_label)
        
        # 详细信息选项卡
        self.detail_tabs = QTabWidget()
        
        # 基本信息选项卡
        self._create_basic_info_tab()
        
        # 工资明细选项卡
        self._create_salary_detail_tab()
        
        # 变化历史选项卡
        self._create_history_tab()
        
        detail_layout.addWidget(self.detail_tabs)
        
        parent_splitter.addWidget(detail_widget)
    
    def _create_basic_info_tab(self):
        """创建基本信息选项卡"""
        basic_widget = QWidget()
        basic_layout = QVBoxLayout(basic_widget)
        
        # 基本信息表格
        self.basic_info_table = QTableWidget()
        self.basic_info_table.setColumnCount(2)
        self.basic_info_table.setHorizontalHeaderLabels(["项目", "值"])
        self.basic_info_table.verticalHeader().setVisible(False)
        
        # 设置列宽
        header = self.basic_info_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        basic_layout.addWidget(self.basic_info_table)
        
        self.detail_tabs.addTab(basic_widget, "基本信息")
    
    def _create_salary_detail_tab(self):
        """创建工资明细选项卡"""
        salary_widget = QWidget()
        salary_layout = QVBoxLayout(salary_widget)
        
        # 工资明细表格
        self.salary_detail_table = QTableWidget()
        self.salary_detail_table.setColumnCount(3)
        self.salary_detail_table.setHorizontalHeaderLabels(["项目", "当月", "上月"])
        self.salary_detail_table.verticalHeader().setVisible(False)
        
        # 设置列宽
        header = self.salary_detail_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        
        salary_layout.addWidget(self.salary_detail_table)
        
        self.detail_tabs.addTab(salary_widget, "工资明细")
    
    def _create_history_tab(self):
        """创建变化历史选项卡"""
        history_widget = QWidget()
        history_layout = QVBoxLayout(history_widget)
        
        # 历史记录文本框
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        history_layout.addWidget(self.history_text)
        
        self.detail_tabs.addTab(history_widget, "变化历史")
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 检测菜单
        detection_menu = menubar.addMenu('检测(&D)')
        
        # 开始检测
        start_action = detection_menu.addAction('开始检测(&S)')
        start_action.setShortcut('F5')
        start_action.triggered.connect(self.start_detection)
        
        detection_menu.addSeparator()
        
        # 导出结果
        export_action = detection_menu.addAction('导出结果(&E)')
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_results)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 刷新
        refresh_action = view_menu.addAction('刷新(&R)')
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('检测工具栏')
        
        # 开始检测
        start_action = toolbar.addAction('开始检测')
        start_action.triggered.connect(self.start_detection)
        
        toolbar.addSeparator()
        
        # 导出结果
        export_action = toolbar.addAction('导出结果')
        export_action.triggered.connect(self.export_results)
        
        # 刷新
        refresh_action = toolbar.addAction('刷新')
        refresh_action.triggered.connect(self.refresh_data)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
    
    def _set_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QTableWidget {
                border: 1px solid #d0d0d0;
                alternate-background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
            }
        """)
    
    def start_detection(self):
        """开始异动检测"""
        try:
            self.logger.info("开始异动检测")
            self.status_bar.showMessage("正在进行异动检测...")
            
            # 禁用开始按钮
            self.start_detection_btn.setEnabled(False)
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 清空之前的结果
            self.changes_table.setRowCount(0)
            self._clear_detail_view()
            
            # 模拟检测过程
            self._simulate_detection()
            
        except Exception as e:
            self.logger.error(f"异动检测失败: {e}")
            QMessageBox.critical(self, "错误", f"异动检测失败: {e}")
            self._reset_ui_state()
    
    def _simulate_detection(self):
        """模拟检测过程"""
        # 这里应该调用实际的检测逻辑
        # 现在先模拟一些结果
        
        sample_results = [
            {
                "工号": "001", "姓名": "张三", "异动类型": "工资增加", 
                "变化金额": 500, "原因分析": "岗位工资调整", "置信度": "95%"
            },
            {
                "工号": "002", "姓名": "李四", "异动类型": "工资减少", 
                "变化金额": -200, "原因分析": "绩效扣减", "置信度": "88%"
            },
            {
                "工号": "003", "姓名": "王五", "异动类型": "新增人员", 
                "变化金额": 3000, "原因分析": "新入职", "置信度": "100%"
            }
        ]
        
        # 模拟进度更新
        for i in range(101):
            self.progress_bar.setValue(i)
            QTimer.singleShot(20, lambda: None)  # 模拟延迟
        
        # 填充结果
        self._populate_results(sample_results)
        
        # 更新统计信息
        self.stats_label.setText(f"检测完成，共发现 {len(sample_results)} 个异动")
        
        # 恢复UI状态
        self._reset_ui_state()
        
        self.logger.info(f"异动检测完成，发现 {len(sample_results)} 个异动")
    
    def _populate_results(self, results: List[Dict[str, Any]]):
        """填充检测结果"""
        self.detection_results = results
        self.changes_table.setRowCount(len(results))
        
        for row, result in enumerate(results):
            for col, key in enumerate(["工号", "姓名", "异动类型", "变化金额", "原因分析", "置信度"]):
                item = QTableWidgetItem(str(result.get(key, "")))
                
                # 根据异动类型设置颜色
                if key == "异动类型":
                    if result.get(key) == "工资增加":
                        item.setBackground(QBrush(QColor(144, 238, 144)))  # 浅绿色
                    elif result.get(key) == "工资减少":
                        item.setBackground(QBrush(QColor(255, 182, 193)))  # 浅红色
                    elif result.get(key) == "新增人员":
                        item.setBackground(QBrush(QColor(173, 216, 230)))  # 浅蓝色
                
                self.changes_table.setItem(row, col, item)
        
        # 启用导出按钮
        self.export_results_btn.setEnabled(True)
    
    def _reset_ui_state(self):
        """重置UI状态"""
        self.start_detection_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("就绪")
    
    def _on_selection_changed(self):
        """选择变化事件"""
        selected_rows = self.changes_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            if row < len(self.detection_results):
                self._show_detail_info(self.detection_results[row])
    
    def _show_detail_info(self, result: Dict[str, Any]):
        """显示详细信息"""
        # 更新基本信息
        basic_info = [
            ("工号", result.get("工号", "")),
            ("姓名", result.get("姓名", "")),
            ("异动类型", result.get("异动类型", "")),
            ("变化金额", f"{result.get('变化金额', 0)} 元"),
            ("原因分析", result.get("原因分析", "")),
            ("置信度", result.get("置信度", "")),
            ("检测时间", "2024-01-15 10:30:00")
        ]
        
        self.basic_info_table.setRowCount(len(basic_info))
        for row, (key, value) in enumerate(basic_info):
            self.basic_info_table.setItem(row, 0, QTableWidgetItem(key))
            self.basic_info_table.setItem(row, 1, QTableWidgetItem(str(value)))
        
        # 更新工资明细（模拟数据）
        salary_detail = [
            ("基本工资", "3000", "3000"),
            ("岗位工资", "2500", "2000"),
            ("绩效工资", "1500", "1700"),
            ("应发合计", "7000", "6700")
        ]
        
        self.salary_detail_table.setRowCount(len(salary_detail))
        for row, (item, current, previous) in enumerate(salary_detail):
            self.salary_detail_table.setItem(row, 0, QTableWidgetItem(item))
            self.salary_detail_table.setItem(row, 1, QTableWidgetItem(current))
            self.salary_detail_table.setItem(row, 2, QTableWidgetItem(previous))
        
        # 更新历史记录
        history_text = f"""
工号: {result.get('工号', '')}
姓名: {result.get('姓名', '')}

变化历史:
2024-01-15: {result.get('异动类型', '')} {result.get('变化金额', 0)} 元
原因: {result.get('原因分析', '')}
置信度: {result.get('置信度', '')}

详细分析:
通过对比本月与上月工资数据，发现该员工工资发生变化。
系统自动分析认为此变化属于正常的{result.get('原因分析', '')}。
        """
        self.history_text.setText(history_text.strip())
    
    def _clear_detail_view(self):
        """清空详细信息视图"""
        self.basic_info_table.setRowCount(0)
        self.salary_detail_table.setRowCount(0)
        self.history_text.clear()
    
    def export_results(self):
        """导出检测结果"""
        try:
            if not self.detection_results:
                QMessageBox.warning(self, "警告", "没有检测结果可导出")
                return
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出检测结果", 
                f"异动检测结果_{QTimer().currentTime().toString('yyyyMMdd_hhmmss')}.xlsx",
                "Excel文件 (*.xlsx)"
            )
            
            if file_path:
                # 这里实现实际的导出逻辑
                QMessageBox.information(self, "提示", f"检测结果已导出到: {file_path}")
                self.logger.info(f"检测结果已导出到: {file_path}")
                
        except Exception as e:
            self.logger.error(f"导出检测结果失败: {e}")
            QMessageBox.critical(self, "错误", f"导出检测结果失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            self.logger.info("刷新异动检测数据")
            # 这里实现数据刷新逻辑
            self.status_bar.showMessage("数据已刷新", 3000)
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            QMessageBox.warning(self, "警告", f"刷新数据失败: {e}")


class ReportGenerationWindow(QMainWindow):
    """报告生成窗口"""
    
    # 信号定义
    report_generated = pyqtSignal(str)  # 报告生成完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.report_manager = ReportManager()
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("报告生成")
        self.setMinimumSize(900, 600)
        self.resize(1000, 700)
        
        # 设置窗口图标
        self.setWindowIcon(self.style().standardIcon(self.style().SP_FileDialogDetailedView))
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 设置样式
        self._set_window_style()
        
        self.logger.info("报告生成窗口初始化完成")
    
    def _create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建报告配置面板
        self._create_config_panel(main_layout)
        
        # 创建预览区域
        self._create_preview_area(main_layout)
    
    def _create_config_panel(self, parent_layout):
        """创建报告配置面板"""
        config_frame = QFrame()
        config_frame.setFrameStyle(QFrame.StyledPanel)
        config_frame.setMaximumHeight(180)
        
        config_layout = QHBoxLayout(config_frame)
        
        # 报告类型组
        type_group = QGroupBox("报告类型")
        type_layout = QVBoxLayout(type_group)
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "月度异动汇总报告", "个人异动详细报告", 
            "部门异动统计报告", "自定义报告"
        ])
        type_layout.addWidget(self.report_type_combo)
        
        config_layout.addWidget(type_group)
        
        # 报告格式组
        format_group = QGroupBox("输出格式")
        format_layout = QVBoxLayout(format_group)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["Word文档 (.docx)", "Excel表格 (.xlsx)", "PDF文档 (.pdf)"])
        format_layout.addWidget(self.format_combo)
        
        config_layout.addWidget(format_group)
        
        # 数据范围组
        range_group = QGroupBox("数据范围")
        range_layout = QGridLayout(range_group)
        
        range_layout.addWidget(QLabel("起始日期:"), 0, 0)
        self.start_date_edit = QLineEdit("2024-01-01")
        range_layout.addWidget(self.start_date_edit, 0, 1)
        
        range_layout.addWidget(QLabel("结束日期:"), 1, 0)
        self.end_date_edit = QLineEdit("2024-01-31")
        range_layout.addWidget(self.end_date_edit, 1, 1)
        
        config_layout.addWidget(range_group)
        
        # 操作按钮组
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout(actions_group)
        
        # 预览报告按钮
        self.preview_btn = QPushButton("预览报告")
        self.preview_btn.setMinimumHeight(35)
        self.preview_btn.clicked.connect(self.preview_report)
        actions_layout.addWidget(self.preview_btn)
        
        # 生成报告按钮
        self.generate_btn = QPushButton("生成报告")
        self.generate_btn.setMinimumHeight(35)
        self.generate_btn.clicked.connect(self.generate_report)
        actions_layout.addWidget(self.generate_btn)
        
        config_layout.addWidget(actions_group)
        
        parent_layout.addWidget(config_frame)
    
    def _create_preview_area(self, parent_layout):
        """创建预览区域"""
        preview_group = QGroupBox("报告预览")
        preview_layout = QVBoxLayout(preview_group)
        
        # 预览文本框
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #d0d0d0;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        # 设置默认预览内容
        self._set_default_preview()
        
        preview_layout.addWidget(self.preview_text)
        
        parent_layout.addWidget(preview_group)
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 报告菜单
        report_menu = menubar.addMenu('报告(&R)')
        
        # 预览报告
        preview_action = report_menu.addAction('预览报告(&P)')
        preview_action.setShortcut('Ctrl+P')
        preview_action.triggered.connect(self.preview_report)
        
        # 生成报告
        generate_action = report_menu.addAction('生成报告(&G)')
        generate_action.setShortcut('Ctrl+G')
        generate_action.triggered.connect(self.generate_report)
        
        report_menu.addSeparator()
        
        # 导出设置
        export_settings_action = report_menu.addAction('导出设置(&E)')
        export_settings_action.triggered.connect(self.export_settings)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('报告工具栏')
        
        # 预览报告
        preview_action = toolbar.addAction('预览报告')
        preview_action.triggered.connect(self.preview_report)
        
        toolbar.addSeparator()
        
        # 生成报告
        generate_action = toolbar.addAction('生成报告')
        generate_action.triggered.connect(self.generate_report)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def _set_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
    
    def _set_default_preview(self):
        """设置默认预览内容"""
        default_content = """
        <h2 style="text-align: center; color: #2c3e50;">月度工资异动汇总报告</h2>
        
        <p><strong>报告生成时间:</strong> 2024年1月15日</p>
        <p><strong>数据统计期间:</strong> 2024年1月1日 - 2024年1月31日</p>
        
        <h3>一、异动概览</h3>
        <ul>
            <li>总异动人数: 15人</li>
            <li>工资增加: 8人</li>
            <li>工资减少: 3人</li>
            <li>新增人员: 2人</li>
            <li>离职人员: 2人</li>
        </ul>
        
        <h3>二、主要异动情况</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #f2f2f2;">
                <th>工号</th>
                <th>姓名</th>
                <th>异动类型</th>
                <th>变化金额</th>
                <th>原因</th>
            </tr>
            <tr>
                <td>001</td>
                <td>张三</td>
                <td>工资增加</td>
                <td>+500元</td>
                <td>岗位工资调整</td>
            </tr>
            <tr>
                <td>002</td>
                <td>李四</td>
                <td>工资减少</td>
                <td>-200元</td>
                <td>绩效扣减</td>
            </tr>
        </table>
        
        <h3>三、分析建议</h3>
        <p>本月工资异动主要集中在岗位工资调整和绩效变化方面，整体变化幅度在合理范围内。
        建议关注绩效扣减人员的后续表现，确保薪酬管理的公平性和激励效果。</p>
        """
        self.preview_text.setHtml(default_content)
    
    def preview_report(self):
        """预览报告"""
        try:
            self.logger.info("开始预览报告")
            self.status_bar.showMessage("正在生成预览...")
            
            # 这里应该根据配置生成实际的报告预览
            report_type = self.report_type_combo.currentText()
            
            if report_type == "个人异动详细报告":
                self._preview_personal_report()
            elif report_type == "部门异动统计报告":
                self._preview_department_report()
            else:
                self._preview_summary_report()
            
            self.status_bar.showMessage("预览生成完成", 3000)
            
        except Exception as e:
            self.logger.error(f"预览报告失败: {e}")
            QMessageBox.critical(self, "错误", f"预览报告失败: {e}")
            self.status_bar.showMessage("预览失败")
    
    def _preview_summary_report(self):
        """预览汇总报告"""
        # 这里生成汇总报告的预览内容
        self._set_default_preview()
    
    def _preview_personal_report(self):
        """预览个人报告"""
        content = """
        <h2 style="text-align: center; color: #2c3e50;">个人工资异动详细报告</h2>
        
        <p><strong>员工工号:</strong> 001</p>
        <p><strong>员工姓名:</strong> 张三</p>
        <p><strong>所属部门:</strong> 财务部</p>
        <p><strong>统计期间:</strong> 2024年1月1日 - 2024年1月31日</p>
        
        <h3>工资变化明细</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #f2f2f2;">
                <th>项目</th>
                <th>上月金额</th>
                <th>本月金额</th>
                <th>变化金额</th>
            </tr>
            <tr>
                <td>基本工资</td>
                <td>3000</td>
                <td>3000</td>
                <td>0</td>
            </tr>
            <tr>
                <td>岗位工资</td>
                <td>2000</td>
                <td>2500</td>
                <td>+500</td>
            </tr>
            <tr>
                <td>绩效工资</td>
                <td>1700</td>
                <td>1500</td>
                <td>-200</td>
            </tr>
        </table>
        """
        self.preview_text.setHtml(content)
    
    def _preview_department_report(self):
        """预览部门报告"""
        content = """
        <h2 style="text-align: center; color: #2c3e50;">部门工资异动统计报告</h2>
        
        <p><strong>部门名称:</strong> 财务部</p>
        <p><strong>统计期间:</strong> 2024年1月1日 - 2024年1月31日</p>
        
        <h3>部门异动统计</h3>
        <ul>
            <li>部门总人数: 12人</li>
            <li>异动人数: 5人</li>
            <li>异动比例: 41.7%</li>
        </ul>
        
        <h3>异动分布</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #f2f2f2;">
                <th>异动类型</th>
                <th>人数</th>
                <th>比例</th>
                <th>总金额变化</th>
            </tr>
            <tr>
                <td>工资增加</td>
                <td>3人</td>
                <td>25%</td>
                <td>+1200元</td>
            </tr>
            <tr>
                <td>工资减少</td>
                <td>2人</td>
                <td>16.7%</td>
                <td>-400元</td>
            </tr>
        </table>
        """
        self.preview_text.setHtml(content)
    
    def generate_report(self):
        """生成报告"""
        try:
            self.logger.info("开始生成报告")
            
            # 获取配置
            report_type = self.report_type_combo.currentText()
            output_format = self.format_combo.currentText()
            
            # 选择保存路径
            file_extension = output_format.split('.')[-1].rstrip(')')
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存报告", 
                f"{report_type}_{QTimer().currentTime().toString('yyyyMMdd_hhmmss')}.{file_extension}",
                f"{output_format.split(' ')[0]}文件 (*.{file_extension})"
            )
            
            if file_path:
                # 显示进度
                self.progress_bar.setVisible(True)
                self.status_bar.showMessage("正在生成报告...")
                
                # 模拟生成过程
                for i in range(101):
                    self.progress_bar.setValue(i)
                    QTimer.singleShot(20, lambda: None)
                
                # 这里调用实际的报告生成逻辑
                # self.report_manager.generate_report(...)
                
                self.progress_bar.setVisible(False)
                self.status_bar.showMessage("报告生成完成", 3000)
                
                QMessageBox.information(self, "成功", f"报告已生成: {file_path}")
                self.report_generated.emit(file_path)
                self.logger.info(f"报告生成完成: {file_path}")
                
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成报告失败: {e}")
            self.progress_bar.setVisible(False)
            self.status_bar.showMessage("生成失败")
    
    def export_settings(self):
        """导出设置"""
        try:
            QMessageBox.information(self, "提示", "导出设置功能开发中...")
        except Exception as e:
            self.logger.error(f"导出设置失败: {e}")
            QMessageBox.warning(self, "警告", f"导出设置失败: {e}") 