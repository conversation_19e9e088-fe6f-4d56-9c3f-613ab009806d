"""
配置同步管理器

功能说明:
- 管理字段映射配置的持久化和同步
- 实时保存、版本管理、冲突解决
- 批量更新、缓存优化
- 配置变更审计日志

功能函数:
- save_mapping(): 保存字段映射配置
- load_mapping(): 加载字段映射配置
- update_mapping(): 更新字段映射配置
- backup_mappings(): 备份配置
- restore_mappings(): 恢复配置

创建时间: 2025-06-23
作者: 开发团队
"""

import json
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
import shutil
import threading
from concurrent.futures import ThreadPoolExecutor

from src.utils.log_config import setup_logger


class ConfigSyncManager:
    """配置同步管理器
    
    提供字段映射配置的持久化和同步功能
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化配置同步管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = setup_logger(__name__)
        
        # 配置文件路径
        self.config_path = config_path or "state/data/field_mappings.json"
        self.backup_dir = "state/data/backups"
        self.change_log_path = "state/data/mapping_changes.log"
        
        # 内存缓存
        self.memory_cache = {}
        self.cache_lock = threading.RLock()
        
        # 缓存统计
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "updates": 0
        }
        
        # 初始化配置
        self._initialize_config()
        
        self.logger.info("配置同步管理器初始化完成")
    
    def _initialize_config(self):
        """初始化配置目录和文件"""
        # 创建必要的目录
        Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)
        Path(self.backup_dir).mkdir(parents=True, exist_ok=True)
        
        # 如果配置文件不存在，创建默认配置
        if not Path(self.config_path).exists():
            default_config = {
                "version": "2.0",
                "last_updated": datetime.now().isoformat(),
                "global_settings": {
                    "auto_generate_mappings": True,
                    "enable_smart_suggestions": True,
                    "save_edit_history": True
                },
                "table_mappings": {},
                "user_preferences": {
                    "default_field_patterns": {},
                    "recent_edits": [],
                    "favorite_mappings": []
                }
            }
            self._save_config_file(default_config)
    
    def save_mapping(self, table_name: str, mapping: Dict[str, str], 
                     metadata: Optional[Dict[str, Any]] = None) -> bool:
        """保存字段映射配置
        
        Args:
            table_name: 表名
            mapping: 字段映射
            metadata: 元数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()
                
                # 准备映射数据
                mapping_data = {
                    "metadata": {
                        "created_at": datetime.now().isoformat(),
                        "last_modified": datetime.now().isoformat(),
                        "auto_generated": metadata.get("auto_generated", True) if metadata else True,
                        "user_modified": metadata.get("user_modified", False) if metadata else False
                    },
                    "field_mappings": mapping,
                    "edit_history": []
                }
                
                # 如果已存在，保留历史记录
                if table_name in config.get("table_mappings", {}):
                    existing_data = config["table_mappings"][table_name]
                    mapping_data["edit_history"] = existing_data.get("edit_history", [])
                    mapping_data["metadata"]["created_at"] = existing_data.get("metadata", {}).get("created_at", mapping_data["metadata"]["created_at"])
                
                # 更新配置
                config["table_mappings"][table_name] = mapping_data
                config["last_updated"] = datetime.now().isoformat()
                
                # 保存到文件
                success = self._save_config_file(config)
                
                if success:
                    # 更新内存缓存
                    self.memory_cache[table_name] = mapping.copy()
                    self.cache_stats["updates"] += 1
                    
                    # 记录变更日志
                    self._log_mapping_change(table_name, "save", mapping)
                    
                    self.logger.info(f"字段映射保存成功: {table_name}")
                
                return success
                
        except Exception as e:
            self.logger.error(f"保存字段映射失败 {table_name}: {e}")
            return False
    
    def load_mapping(self, table_name: str) -> Optional[Dict[str, str]]:
        """加载字段映射配置
        
        Args:
            table_name: 表名
            
        Returns:
            Optional[Dict[str, str]]: 字段映射，如果不存在返回None
        """
        try:
            with self.cache_lock:
                # 检查内存缓存
                if table_name in self.memory_cache:
                    self.cache_stats["hits"] += 1
                    return self.memory_cache[table_name].copy()
                
                # 从文件加载
                config = self._load_config_file()
                table_mappings = config.get("table_mappings", {})
                
                if table_name in table_mappings:
                    mapping = table_mappings[table_name].get("field_mappings", {})
                    # 更新缓存
                    self.memory_cache[table_name] = mapping.copy()
                    self.cache_stats["misses"] += 1
                    return mapping
                
                self.cache_stats["misses"] += 1
                return None
                
        except Exception as e:
            self.logger.error(f"加载字段映射失败 {table_name}: {e}")
            return None
    
    def update_mapping(self, table_name: str, field_name: str, new_display_name: str) -> bool:
        """更新单个字段映射

        Args:
            table_name: 表名
            field_name: 字段名
            new_display_name: 新的显示名称

        Returns:
            bool: 是否更新成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 检查表是否存在，如果不存在则创建
                if table_name not in config.get("table_mappings", {}):
                    self.logger.info(f"表 {table_name} 配置不存在，创建新的字段映射配置")
                    config.setdefault("table_mappings", {})[table_name] = {
                        "metadata": {
                            "created_at": datetime.now().isoformat(),
                            "last_modified": datetime.now().isoformat(),
                            "auto_generated": False,
                            "user_modified": True
                        },
                        "field_mappings": {},
                        "edit_history": []
                    }

                # 获取当前映射
                table_data = config["table_mappings"][table_name]
                field_mappings = table_data.get("field_mappings", {})

                # 检查字段是否存在，如果不存在则创建新字段
                old_display_name = field_mappings.get(field_name, field_name)
                if field_name not in field_mappings:
                    self.logger.info(f"字段 {field_name} 在表 {table_name} 中不存在，创建新字段映射")
                    field_mappings[field_name] = field_name  # 初始值为原字段名

                # 记录历史变更
                edit_history = table_data.get("edit_history", [])
                edit_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "field": field_name,
                    "old_value": old_display_name,
                    "new_value": new_display_name,
                    "user_action": True
                })

                # 更新映射
                field_mappings[field_name] = new_display_name
                table_data["field_mappings"] = field_mappings
                table_data["edit_history"] = edit_history
                table_data["metadata"]["last_modified"] = datetime.now().isoformat()
                table_data["metadata"]["user_modified"] = True

                # 更新配置
                config["table_mappings"][table_name] = table_data
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存
                    if table_name in self.memory_cache:
                        self.memory_cache[table_name][field_name] = new_display_name

                    # 记录变更日志
                    self._log_field_change(table_name, field_name, old_display_name, new_display_name)

                    self.logger.info(f"字段映射更新成功: {table_name}.{field_name} -> {new_display_name}")

                return success

        except Exception as e:
            self.logger.error(f"更新字段映射失败: {e}")
            return False

    def update_single_field_mapping(self, table_name: str, db_field: str, display_name: str) -> bool:
        """更新单个字段的显示名称（新方法，按照解决方案设计）

        Args:
            table_name: 表名
            db_field: 数据库字段名
            display_name: 显示名称

        Returns:
            bool: 是否更新成功
        """
        try:
            config = self._load_config_file()

            if table_name not in config.get("table_mappings", {}):
                self.logger.error(f"表 {table_name} 的映射配置不存在")
                return False

            # 更新字段映射
            config["table_mappings"][table_name]["field_mappings"][db_field] = display_name

            # 更新元数据
            config["table_mappings"][table_name]["metadata"]["user_modified"] = True
            config["table_mappings"][table_name]["metadata"]["last_modified"] = datetime.now().isoformat()

            # 添加编辑历史
            if "edit_history" not in config["table_mappings"][table_name]:
                config["table_mappings"][table_name]["edit_history"] = []

            config["table_mappings"][table_name]["edit_history"].append({
                "timestamp": datetime.now().isoformat(),
                "field": db_field,
                "new_value": display_name,
                "action": "user_edit"
            })

            # 保存配置
            success = self._save_config_file(config)

            if success:
                # 更新内存缓存
                if table_name in self.memory_cache:
                    self.memory_cache[table_name][db_field] = display_name

                self.logger.info(f"字段映射更新成功: {table_name}.{db_field} -> {display_name}")

            return success

        except Exception as e:
            self.logger.error(f"更新单个字段映射失败: {e}")
            return False

    def save_complete_mapping(self, table_name: str, mapping_data: Dict[str, Any]) -> bool:
        """保存完整的字段映射配置（新方法，按照解决方案设计）

        Args:
            table_name: 表名
            mapping_data: 完整的映射数据，包含field_mappings、original_excel_headers、metadata等

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 确保必要的键存在
                if "table_mappings" not in config:
                    config["table_mappings"] = {}

                # 保存映射数据
                config["table_mappings"][table_name] = mapping_data
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 更新内存缓存
                    field_mappings = mapping_data.get("field_mappings", {})
                    self.memory_cache[table_name] = field_mappings.copy()
                    self.cache_stats["updates"] += 1

                    self.logger.info(f"完整字段映射保存成功: {table_name}")

                return success

        except Exception as e:
            self.logger.error(f"保存完整字段映射失败: {e}")
            return False

    def batch_update_mappings(self, updates: List[Dict[str, Any]]) -> bool:
        """批量更新字段映射
        
        Args:
            updates: 更新列表，每个元素包含 table_name, field_name, new_display_name
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()
                
                # 处理所有更新
                for update in updates:
                    table_name = update.get("table_name")
                    field_name = update.get("field_name")
                    new_display_name = update.get("new_display_name")
                    
                    if not all([table_name, field_name, new_display_name]):
                        continue
                    
                    # 更新单个字段
                    if table_name in config.get("table_mappings", {}):
                        table_data = config["table_mappings"][table_name]
                        field_mappings = table_data.get("field_mappings", {})
                        
                        if field_name in field_mappings:
                            old_value = field_mappings[field_name]
                            field_mappings[field_name] = new_display_name
                            
                            # 记录历史
                            edit_history = table_data.get("edit_history", [])
                            edit_history.append({
                                "timestamp": datetime.now().isoformat(),
                                "field": field_name,
                                "old_value": old_value,
                                "new_value": new_display_name,
                                "user_action": True
                            })
                            
                            table_data["edit_history"] = edit_history
                            table_data["metadata"]["last_modified"] = datetime.now().isoformat()
                            table_data["metadata"]["user_modified"] = True
                
                # 更新全局配置
                config["last_updated"] = datetime.now().isoformat()
                
                # 保存到文件
                success = self._save_config_file(config)
                
                if success:
                    # 清除相关缓存
                    updated_tables = {update.get("table_name") for update in updates}
                    for table_name in updated_tables:
                        if table_name and table_name in self.memory_cache:
                            del self.memory_cache[table_name]
                    
                    self.logger.info(f"批量更新完成，共处理 {len(updates)} 个字段")
                
                return success
                
        except Exception as e:
            self.logger.error(f"批量更新字段映射失败: {e}")
            return False
    
    def backup_mappings(self) -> bool:
        """备份字段映射配置
        
        Returns:
            bool: 是否备份成功
        """
        try:
            if not Path(self.config_path).exists():
                self.logger.warning("配置文件不存在，跳过备份")
                return True
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"field_mappings_backup_{timestamp}.json"
            backup_path = Path(self.backup_dir) / backup_filename
            
            # 复制配置文件
            shutil.copy2(self.config_path, backup_path)
            
            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups(max_backups=10)
            
            self.logger.info(f"配置备份成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"备份配置失败: {e}")
            return False
    
    def restore_mappings(self, backup_filename: str) -> bool:
        """恢复字段映射配置
        
        Args:
            backup_filename: 备份文件名
            
        Returns:
            bool: 是否恢复成功
        """
        try:
            backup_path = Path(self.backup_dir) / backup_filename
            
            if not backup_path.exists():
                self.logger.error(f"备份文件不存在: {backup_path}")
                return False
            
            # 先备份当前配置
            if not self.backup_mappings():
                self.logger.warning("备份当前配置失败，但继续恢复操作")
            
            # 恢复配置
            shutil.copy2(backup_path, self.config_path)
            
            # 清除内存缓存
            with self.cache_lock:
                self.memory_cache.clear()
            
            self.logger.info(f"配置恢复成功: {backup_filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复配置失败: {e}")
            return False
    
    def _load_config_file(self) -> Dict[str, Any]:
        """加载配置文件
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 确保必要的键存在
                if "table_mappings" not in config:
                    config["table_mappings"] = {}
                return config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            # 返回默认结构
            return {
                "version": "2.0",
                "last_updated": datetime.now().isoformat(),
                "global_settings": {
                    "auto_generate_mappings": True,
                    "enable_smart_suggestions": True,
                    "save_edit_history": True
                },
                "table_mappings": {},
                "user_preferences": {
                    "default_field_patterns": {},
                    "recent_edits": [],
                    "favorite_mappings": []
                }
            }
    
    def _save_config_file(self, config: Dict[str, Any]) -> bool:
        """保存配置文件
        
        Args:
            config: 配置数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 创建临时文件
            temp_path = f"{self.config_path}.tmp"
            
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 原子性替换
            os.replace(temp_path, self.config_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            # 清理临时文件
            temp_path = f"{self.config_path}.tmp"
            if Path(temp_path).exists():
                Path(temp_path).unlink()
            return False
    
    def _log_mapping_change(self, table_name: str, action: str, mapping: Dict[str, str]):
        """记录映射变更日志
        
        Args:
            table_name: 表名
            action: 操作类型
            mapping: 映射数据
        """
        try:
            change_record = {
                "timestamp": datetime.now().isoformat(),
                "table_name": table_name,
                "action": action,
                "field_count": len(mapping),
                "fields": list(mapping.keys())
            }
            
            with open(self.change_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(change_record, ensure_ascii=False) + "\n")
                
        except Exception as e:
            self.logger.warning(f"记录变更日志失败: {e}")
    
    def _log_field_change(self, table_name: str, field_name: str, old_value: str, new_value: str):
        """记录字段变更日志
        
        Args:
            table_name: 表名
            field_name: 字段名
            old_value: 旧值
            new_value: 新值
        """
        try:
            change_record = {
                "timestamp": datetime.now().isoformat(),
                "table_name": table_name,
                "action": "field_update",
                "field_name": field_name,
                "old_value": old_value,
                "new_value": new_value
            }
            
            with open(self.change_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(change_record, ensure_ascii=False) + "\n")
                
        except Exception as e:
            self.logger.warning(f"记录字段变更日志失败: {e}")
    
    def _cleanup_old_backups(self, max_backups: int = 10):
        """清理旧备份文件
        
        Args:
            max_backups: 最大保留备份数量
        """
        try:
            backup_files = list(Path(self.backup_dir).glob("field_mappings_backup_*.json"))
            backup_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            
            # 删除多余的备份
            for old_backup in backup_files[max_backups:]:
                old_backup.unlink()
                self.logger.debug(f"删除旧备份: {old_backup}")
                
        except Exception as e:
            self.logger.warning(f"清理旧备份失败: {e}")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计
        """
        with self.cache_lock:
            total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
            hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
            
            return {
                "cache_size": len(self.memory_cache),
                "hit_rate": round(hit_rate, 3),
                "total_requests": total_requests,
                **self.cache_stats
            }
    
    def clear_cache(self):
        """清除内存缓存"""
        with self.cache_lock:
            self.memory_cache.clear()
            self.logger.info("内存缓存已清除")
    
    def get_mapping(self, table_name: str) -> Optional[Dict[str, str]]:
        """获取字段映射配置（load_mapping的别名方法）
        
        Args:
            table_name: 表名
            
        Returns:
            Optional[Dict[str, str]]: 字段映射，如果不存在返回None
        """
        return self.load_mapping(table_name)
    
    def get_all_table_names(self) -> List[str]:
        """获取所有表名列表

        Returns:
            List[str]: 表名列表
        """
        try:
            config = self._load_config_file()
            return list(config.get("table_mappings", {}).keys())
        except Exception as e:
            self.logger.error(f"获取表名列表失败: {e}")
            return []

    def save_user_header_preference(self, table_name: str, selected_fields: List[str]) -> bool:
        """保存用户表头显示偏好

        Args:
            table_name: 表名
            selected_fields: 用户选择的字段列表

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 确保user_preferences存在
                if 'user_preferences' not in config:
                    config['user_preferences'] = {
                        "default_field_patterns": {},
                        "recent_edits": [],
                        "favorite_mappings": [],
                        "header_preferences": {}
                    }

                if 'header_preferences' not in config['user_preferences']:
                    config['user_preferences']['header_preferences'] = {}

                # 保存表头偏好
                config['user_preferences']['header_preferences'][table_name] = {
                    "selected_fields": selected_fields,
                    "updated_at": datetime.now().isoformat(),
                    "field_count": len(selected_fields)
                }

                # 更新配置
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 记录变更日志
                    self._log_mapping_change(table_name, "save_header_preference", {
                        "selected_fields": selected_fields,
                        "field_count": len(selected_fields)
                    })

                    self.logger.info(f"用户表头偏好保存成功: {table_name}, {len(selected_fields)} 个字段")

                return success

        except Exception as e:
            self.logger.error(f"保存用户表头偏好失败 {table_name}: {e}")
            return False

    def get_user_header_preference(self, table_name: str) -> Optional[List[str]]:
        """获取用户表头显示偏好

        Args:
            table_name: 表名

        Returns:
            Optional[List[str]]: 用户选择的字段列表，如果不存在返回None
        """
        try:
            config = self._load_config_file()

            # 检查用户偏好是否存在
            user_prefs = config.get('user_preferences', {})
            header_prefs = user_prefs.get('header_preferences', {})

            if table_name in header_prefs:
                preference_data = header_prefs[table_name]
                if isinstance(preference_data, dict):
                    return preference_data.get('selected_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    return preference_data

            return None

        except Exception as e:
            self.logger.error(f"获取用户表头偏好失败 {table_name}: {e}")
            return None

    def remove_user_header_preference(self, table_name: str) -> bool:
        """删除用户表头显示偏好

        Args:
            table_name: 表名

        Returns:
            bool: 是否删除成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 检查偏好是否存在
                user_prefs = config.get('user_preferences', {})
                header_prefs = user_prefs.get('header_preferences', {})

                if table_name in header_prefs:
                    del header_prefs[table_name]

                    # 更新配置
                    config["last_updated"] = datetime.now().isoformat()

                    # 保存到文件
                    success = self._save_config_file(config)

                    if success:
                        self.logger.info(f"用户表头偏好删除成功: {table_name}")

                    return success
                else:
                    self.logger.debug(f"表 {table_name} 没有用户表头偏好，无需删除")
                    return True

        except Exception as e:
            self.logger.error(f"删除用户表头偏好失败 {table_name}: {e}")
            return False

    def get_all_user_header_preferences(self) -> Dict[str, List[str]]:
        """获取所有用户表头显示偏好

        Returns:
            Dict[str, List[str]]: 所有表的用户偏好 {表名: 字段列表}
        """
        try:
            config = self._load_config_file()

            user_prefs = config.get('user_preferences', {})
            header_prefs = user_prefs.get('header_preferences', {})

            result = {}
            for table_name, preference_data in header_prefs.items():
                if isinstance(preference_data, dict):
                    result[table_name] = preference_data.get('selected_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    result[table_name] = preference_data

            return result

        except Exception as e:
            self.logger.error(f"获取所有用户表头偏好失败: {e}")
            return {}

    def save_table_field_preference(self, table_name: str, selected_fields: List[str]) -> bool:
        """保存表级字段显示偏好（替代全局偏好）

        Args:
            table_name: 表名
            selected_fields: 用户选择的字段列表

        Returns:
            bool: 是否保存成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 确保table_preferences存在
                if 'table_preferences' not in config:
                    config['table_preferences'] = {}

                # 保存表级字段偏好
                config['table_preferences'][table_name] = {
                    "preferred_fields": selected_fields,
                    "updated_at": datetime.now().isoformat(),
                    "field_count": len(selected_fields)
                }

                # 更新配置
                config["last_updated"] = datetime.now().isoformat()

                # 保存到文件
                success = self._save_config_file(config)

                if success:
                    # 记录变更日志
                    self._log_mapping_change(table_name, "save_table_field_preference", {
                        "preferred_fields": selected_fields,
                        "field_count": len(selected_fields)
                    })

                    self.logger.info(f"表级字段偏好保存成功: {table_name}, {len(selected_fields)} 个字段")

                return success

        except Exception as e:
            self.logger.error(f"保存表级字段偏好失败 {table_name}: {e}")
            return False

    def get_table_field_preference(self, table_name: str) -> Optional[List[str]]:
        """获取表级字段显示偏好

        Args:
            table_name: 表名

        Returns:
            Optional[List[str]]: 用户选择的字段列表，如果不存在返回None
        """
        try:
            config = self._load_config_file()

            # 检查表级偏好是否存在
            table_prefs = config.get('table_preferences', {})

            if table_name in table_prefs:
                preference_data = table_prefs[table_name]
                if isinstance(preference_data, dict):
                    return preference_data.get('preferred_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    return preference_data

            return None

        except Exception as e:
            self.logger.error(f"获取表级字段偏好失败 {table_name}: {e}")
            return None

    def remove_table_field_preference(self, table_name: str) -> bool:
        """删除表级字段显示偏好

        Args:
            table_name: 表名

        Returns:
            bool: 是否删除成功
        """
        try:
            with self.cache_lock:
                # 加载当前配置
                config = self._load_config_file()

                # 检查偏好是否存在
                table_prefs = config.get('table_preferences', {})

                if table_name in table_prefs:
                    del table_prefs[table_name]

                    # 更新配置
                    config["last_updated"] = datetime.now().isoformat()

                    # 保存到文件
                    success = self._save_config_file(config)

                    if success:
                        self.logger.info(f"表级字段偏好删除成功: {table_name}")

                    return success
                else:
                    self.logger.debug(f"表 {table_name} 没有字段偏好，无需删除")
                    return True

        except Exception as e:
            self.logger.error(f"删除表级字段偏好失败 {table_name}: {e}")
            return False

    def get_all_table_field_preferences(self) -> Dict[str, List[str]]:
        """获取所有表级字段显示偏好

        Returns:
            Dict[str, List[str]]: 所有表的字段偏好 {表名: 字段列表}
        """
        try:
            config = self._load_config_file()

            table_prefs = config.get('table_preferences', {})

            result = {}
            for table_name, preference_data in table_prefs.items():
                if isinstance(preference_data, dict):
                    result[table_name] = preference_data.get('preferred_fields', [])
                elif isinstance(preference_data, list):
                    # 兼容旧格式
                    result[table_name] = preference_data

            return result

        except Exception as e:
            self.logger.error(f"获取所有表级字段偏好失败: {e}")
            return {}

    def migrate_global_to_table_preferences(self, target_table_names: List[str]) -> bool:
        """将全局用户偏好迁移到表级偏好

        Args:
            target_table_names: 需要迁移的表名列表

        Returns:
            bool: 是否迁移成功
        """
        try:
            with self.cache_lock:
                config = self._load_config_file()

                # 获取全局用户偏好
                user_prefs = config.get('user_preferences', {})
                header_prefs = user_prefs.get('header_preferences', {})

                if not header_prefs:
                    self.logger.info("无全局用户偏好需要迁移")
                    return True

                # 确保table_preferences存在
                if 'table_preferences' not in config:
                    config['table_preferences'] = {}

                migrated_count = 0
                for table_name in target_table_names:
                    # 检查是否有全局偏好且表级偏好不存在
                    if table_name in header_prefs and table_name not in config['table_preferences']:
                        global_pref = header_prefs[table_name]
                        if isinstance(global_pref, dict):
                            fields = global_pref.get('selected_fields', [])
                        elif isinstance(global_pref, list):
                            fields = global_pref
                        else:
                            continue

                        # 迁移到表级偏好
                        config['table_preferences'][table_name] = {
                            "preferred_fields": fields,
                            "updated_at": datetime.now().isoformat(),
                            "field_count": len(fields),
                            "migrated_from": "global_preferences"
                        }
                        migrated_count += 1

                if migrated_count > 0:
                    config["last_updated"] = datetime.now().isoformat()
                    success = self._save_config_file(config)

                    if success:
                        self.logger.info(f"成功迁移 {migrated_count} 个表的偏好设置到表级配置")
                    return success
                else:
                    self.logger.info("无需迁移偏好设置")
                    return True

        except Exception as e:
            self.logger.error(f"迁移偏好设置失败: {e}")
            return False