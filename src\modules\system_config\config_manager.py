"""
月度工资异动处理系统 - 配置管理器

本模块实现系统配置文件的管理功能：
- 配置文件读取和保存
- 配置项验证和类型检查
- 配置文件备份和恢复
- 默认配置生成

支持的配置格式：JSON

功能函数：
- load_config(): 加载配置文件
- save_config(): 保存配置文件  
- validate_config(): 验证配置项
- backup_config(): 备份配置文件
- restore_config(): 恢复配置文件
- get_default_config(): 获取默认配置

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from datetime import datetime

# 导入项目内部模块
from src.utils.log_config import setup_logger

# 初始化日志
logger = setup_logger("system_config.config_manager")


class ConfigValidator:
    """配置验证器，负责验证配置项的类型和值"""
    
    def __init__(self):
        """初始化配置验证器"""
        self.logger = setup_logger("system_config.config_validator")
        self._validation_rules = self._define_validation_rules()
    
    def _define_validation_rules(self) -> Dict[str, Dict[str, Any]]:
        """
        定义配置项的验证规则
        
        Returns:
            Dict[str, Dict[str, Any]]: 验证规则字典
        """
        return {
            "version": {
                "type": str,
                "required": True,
                "pattern": r"^\d+\.\d+\.\d+$"
            },
            "debug_mode": {
                "type": bool,
                "required": True,
                "default": False
            },
            "log_level": {
                "type": str,
                "required": True,
                "choices": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                "default": "INFO"
            },
            "database.db_name": {
                "type": str,
                "required": True,
                "min_length": 1,
                "default": "salary_system.db"
            },
            "database.backup_enabled": {
                "type": bool,
                "required": True,
                "default": True
            },
            "database.backup_count": {
                "type": int,
                "required": True,
                "min_value": 1,
                "max_value": 10,
                "default": 5
            },
            "import_settings.max_file_size_mb": {
                "type": int,
                "required": True,
                "min_value": 1,
                "max_value": 1000,
                "default": 100
            },
            "processing.match_threshold": {
                "type": float,
                "required": True,
                "min_value": 0.0,
                "max_value": 1.0,
                "default": 0.95
            },
            "ui.window_width": {
                "type": int,
                "required": True,
                "min_value": 800,
                "max_value": 3840,
                "default": 1200
            },
            "ui.window_height": {
                "type": int,
                "required": True,
                "min_value": 600,
                "max_value": 2160,
                "default": 800
            }
        }
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        验证配置文件的完整性和正确性
        
        Args:
            config: 配置字典
            
        Returns:
            tuple[bool, List[str]]: (是否有效, 错误列表)
        """
        errors = []
        
        try:
            for key_path, rule in self._validation_rules.items():
                value = self._get_nested_value(config, key_path)
                field_errors = self._validate_single_field(key_path, value, rule)
                errors.extend(field_errors)
            
            return self._generate_validation_result(errors)
            
        except Exception as e:
            error_msg = f"配置验证过程中发生异常: {str(e)}"
            self.logger.error(error_msg)
            return False, [error_msg]
    
    def _validate_single_field(self, key_path: str, value: Any, rule: Dict[str, Any]) -> List[str]:
        """
        验证单个配置字段
        
        Args:
            key_path: 配置项路径
            value: 配置值
            rule: 验证规则
            
        Returns:
            List[str]: 错误列表
        """
        errors = []
        
        # 检查必需字段
        if self._is_required_field_missing(value, rule):
            errors.append(f"缺少必需配置项: {key_path}")
            return errors
        
        # 如果值为None且不是必需的，跳过验证
        if value is None:
            return errors
        
        # 类型检查
        type_error = self._validate_field_type(key_path, value, rule)
        if type_error:
            errors.append(type_error)
            return errors
        
        # 值范围和约束检查
        constraint_errors = self._validate_field_constraints(key_path, value, rule)
        errors.extend(constraint_errors)
        
        return errors
    
    def _is_required_field_missing(self, value: Any, rule: Dict[str, Any]) -> bool:
        """检查必需字段是否缺失"""
        return rule.get("required", False) and value is None
    
    def _validate_field_type(self, key_path: str, value: Any, rule: Dict[str, Any]) -> Optional[str]:
        """
        验证字段类型
        
        Returns:
            Optional[str]: 类型错误信息，如果没有错误返回None
        """
        expected_type = rule.get("type")
        if expected_type and not isinstance(value, expected_type):
            return f"配置项 {key_path} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"
        return None
    
    def _validate_field_constraints(self, key_path: str, value: Any, rule: Dict[str, Any]) -> List[str]:
        """
        验证字段约束（长度、范围、选择值等）
        
        Returns:
            List[str]: 约束错误列表
        """
        errors = []
        
        # 字符串长度检查
        if isinstance(value, str):
            length_error = self._validate_string_length(key_path, value, rule)
            if length_error:
                errors.append(length_error)
        
        # 数值范围检查
        if isinstance(value, (int, float)):
            range_errors = self._validate_numeric_range(key_path, value, rule)
            errors.extend(range_errors)
        
        # 选择值检查
        choice_error = self._validate_choices(key_path, value, rule)
        if choice_error:
            errors.append(choice_error)
        
        return errors
    
    def _validate_string_length(self, key_path: str, value: str, rule: Dict[str, Any]) -> Optional[str]:
        """验证字符串长度"""
        min_length = rule.get("min_length")
        if min_length and len(value) < min_length:
            return f"配置项 {key_path} 长度不足，最小长度 {min_length}"
        return None
    
    def _validate_numeric_range(self, key_path: str, value: Union[int, float], rule: Dict[str, Any]) -> List[str]:
        """验证数值范围"""
        errors = []
        min_value = rule.get("min_value")
        max_value = rule.get("max_value")
        
        if min_value is not None and value < min_value:
            errors.append(f"配置项 {key_path} 值过小，最小值 {min_value}")
        if max_value is not None and value > max_value:
            errors.append(f"配置项 {key_path} 值过大，最大值 {max_value}")
        
        return errors
    
    def _validate_choices(self, key_path: str, value: Any, rule: Dict[str, Any]) -> Optional[str]:
        """验证选择值"""
        choices = rule.get("choices")
        if choices and value not in choices:
            return f"配置项 {key_path} 值无效，可选值: {choices}"
        return None
    
    def _generate_validation_result(self, errors: List[str]) -> tuple[bool, List[str]]:
        """生成验证结果并记录日志"""
        is_valid = len(errors) == 0
        if is_valid:
            self.logger.info("配置文件验证通过")
        else:
            self.logger.error(f"配置文件验证失败，发现 {len(errors)} 个错误")
            for error in errors:
                self.logger.error(f"  - {error}")
        
        return is_valid, errors
    
    def _get_nested_value(self, config: Dict[str, Any], key_path: str) -> Any:
        """
        获取嵌套字典中的值
        
        Args:
            config: 配置字典
            key_path: 键路径，如 "database.db_name"
            
        Returns:
            Any: 配置值，如果不存在返回None
        """
        try:
            keys = key_path.split(".")
            value = config
            for key in keys:
                value = value.get(key)
                if value is None:
                    return None
            return value
        except (AttributeError, TypeError):
            return None


class ConfigManager:
    """配置管理器，负责系统配置的读取、保存和管理"""
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为项目根目录下的config.json
        """
        self.logger = setup_logger("system_config.config_manager")
        
        # 确定配置文件路径
        if config_file is None:
            # 获取项目根目录
            current_file = Path(__file__)
            project_root = current_file.parent.parent.parent.parent
            config_file = project_root / "config.json"
        
        self.config_file = Path(config_file)
        self.backup_dir = self.config_file.parent / "config_backups"
        
        # 初始化配置验证器
        self.validator = ConfigValidator()
        
        # 当前配置
        self._config = None
        
        self.logger.info(f"配置管理器初始化完成，配置文件: {self.config_file}")
    
    def load_config(self, create_if_missing: bool = True) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            create_if_missing: 如果配置文件不存在是否创建默认配置
            
        Returns:
            Dict[str, Any]: 配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在且create_if_missing为False
            ValueError: 配置文件格式错误
        """
        try:
            if not self.config_file.exists():
                if create_if_missing:
                    self.logger.warning(f"配置文件不存在，创建默认配置: {self.config_file}")
                    self._config = self.get_default_config()
                    self.save_config(self._config)
                    return self._config
                else:
                    raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
            
            # 读取配置文件
            self.logger.info(f"正在加载配置文件: {self.config_file}")
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证配置
            is_valid, errors = self.validator.validate_config(config)
            if not is_valid:
                error_msg = f"配置文件验证失败: {'; '.join(errors)}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            
            self._config = config
            self.logger.info("配置文件加载成功")
            return config
            
        except json.JSONDecodeError as e:
            error_msg = f"配置文件JSON格式错误: {str(e)}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        except Exception as e:
            error_msg = f"加载配置文件时发生错误: {str(e)}"
            self.logger.error(error_msg)
            raise
    
    def save_config(self, config: Dict[str, Any], backup: bool = True) -> bool:
        """
        保存配置文件
        
        Args:
            config: 配置字典
            backup: 是否创建备份
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 验证配置
            is_valid, errors = self.validator.validate_config(config)
            if not is_valid:
                error_msg = f"配置验证失败，无法保存: {'; '.join(errors)}"
                self.logger.error(error_msg)
                return False
            
            # 创建备份
            if backup and self.config_file.exists():
                self._create_backup()
            
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置文件
            self.logger.info(f"正在保存配置文件: {self.config_file}")
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self._config = config
            self.logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            error_msg = f"保存配置文件时发生错误: {str(e)}"
            self.logger.error(error_msg)
            return False
    
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 "database.db_name"
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        if self._config is None:
            self.load_config()
        
        return self.validator._get_nested_value(self._config, key_path) or default
    
    def set_config_value(self, key_path: str, value: Any, save_immediately: bool = False) -> bool:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径，如 "database.db_name"
            value: 配置值
            save_immediately: 是否立即保存到文件
            
        Returns:
            bool: 是否设置成功
        """
        try:
            if self._config is None:
                self.load_config()
            
            # 设置嵌套值
            keys = key_path.split(".")
            current = self._config
            
            # 遍历到最后一个键的父级
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            # 验证修改后的配置
            is_valid, errors = self.validator.validate_config(self._config)
            if not is_valid:
                error_msg = f"设置配置值后验证失败: {'; '.join(errors)}"
                self.logger.error(error_msg)
                return False
            
            if save_immediately:
                return self.save_config(self._config)
            
            self.logger.debug(f"配置值已设置: {key_path} = {value}")
            return True
            
        except Exception as e:
            error_msg = f"设置配置值时发生错误: {str(e)}"
            self.logger.error(error_msg)
            return False
    
    def _create_backup(self) -> bool:
        """
        创建配置文件备份
        
        Returns:
            bool: 是否备份成功
        """
        try:
            # 确保备份目录存在
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"config_{timestamp}.json"
            
            # 复制文件
            shutil.copy2(self.config_file, backup_file)
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            self.logger.info(f"配置文件备份已创建: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建配置备份时发生错误: {str(e)}")
            return False
    
    def _cleanup_old_backups(self, max_backups: int = 10) -> None:
        """
        清理旧的备份文件
        
        Args:
            max_backups: 保留的最大备份数量
        """
        try:
            backup_files = list(self.backup_dir.glob("config_*.json"))
            if len(backup_files) > max_backups:
                # 按修改时间排序，删除最旧的
                backup_files.sort(key=lambda x: x.stat().st_mtime)
                files_to_delete = backup_files[:-max_backups]
                
                for file_to_delete in files_to_delete:
                    file_to_delete.unlink()
                    self.logger.debug(f"已删除旧备份文件: {file_to_delete}")
        
        except Exception as e:
            self.logger.warning(f"清理旧备份文件时发生错误: {str(e)}")
    
    def get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            "version": "0.1.0",
            "debug_mode": False,
            "log_level": "INFO",
            "database": {
                "db_name": "salary_system.db",
                "db_path": "data/db",
                "backup_enabled": True,
                "backup_count": 5,
                "connection_timeout": 30
            },
            "import_settings": {
                "supported_formats": [".xlsx", ".xls", ".csv"],
                "max_file_size_mb": 100,
                "chunk_size": 1000,
                "encoding_priority": ["utf-8", "gbk", "gb2312"]
            },
            "processing": {
                "match_threshold": 0.95,
                "calculation_precision": 2,
                "async_processing": True,
                "max_workers": 4
            },
            "ui": {
                "theme": "default",
                "window_width": 1200,
                "window_height": 800,
                "auto_save_interval": 300,
                "show_progress": True
            },
            "reports": {
                "default_template_dir": "template",
                "output_dir": "output",
                "auto_open_report": True,
                "export_formats": ["docx", "pdf", "xlsx"]
            }
        } 