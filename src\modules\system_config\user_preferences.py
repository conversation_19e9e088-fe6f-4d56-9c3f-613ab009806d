"""
月度工资异动处理系统 - 用户偏好设置模块

本模块实现用户偏好设置的管理功能：
- 界面偏好设置（主题、字体、窗口大小等）
- 功能偏好设置（自动保存、默认路径等）
- 用户行为习惯记录
- 偏好设置的导入导出

功能函数：
- get_preference(): 获取偏好设置
- set_preference(): 设置偏好值
- reset_preferences(): 重置偏好设置
- export_preferences(): 导出偏好设置
- import_preferences(): 导入偏好设置

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from datetime import datetime

# 导入项目内部模块
from src.utils.log_config import setup_logger
from .config_manager import ConfigManager

# 初始化日志
logger = setup_logger("system_config.user_preferences")


class UserPreferences:
    """用户偏好设置管理器"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        初始化用户偏好设置管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.logger = setup_logger("system_config.user_preferences")
        
        # 配置管理器
        self.config_manager = config_manager or ConfigManager()
        
        # 偏好设置文件路径
        self.preferences_file = self._get_preferences_file_path()
        
        # 当前偏好设置
        self._preferences = None
        
        # 默认偏好设置
        self._default_preferences = self._get_default_preferences()
        
        # 偏好设置变更历史
        self._change_history = []
        
        self.logger.info(f"用户偏好设置管理器初始化完成，偏好文件: {self.preferences_file}")
    
    def _get_preferences_file_path(self) -> Path:
        """
        获取偏好设置文件路径
        
        Returns:
            Path: 偏好设置文件路径
        """
        try:
            # 获取项目根目录
            current_file = Path(__file__)
            project_root = current_file.parent.parent.parent.parent
            
            # 偏好设置文件放在项目根目录
            return project_root / "user_preferences.json"
            
        except Exception as e:
            self.logger.error(f"获取偏好设置文件路径失败: {str(e)}")
            # 使用默认路径
            return Path.cwd() / "user_preferences.json"
    
    def _get_default_preferences(self) -> Dict[str, Any]:
        """
        获取默认偏好设置
        
        Returns:
            Dict[str, Any]: 默认偏好设置字典
        """
        return {
            "ui": {
                "theme": "default",
                "font_family": "Microsoft YaHei",
                "font_size": 10,
                "window_maximized": False,
                "window_position": {"x": 100, "y": 100},
                "window_size": {"width": 1200, "height": 800},
                "show_toolbar": True,
                "show_statusbar": True,
                "show_sidebar": True,
                "show_menubar": False,  # 默认隐藏菜单栏
                "sidebar_width": 200,
                "auto_save_layout": True
            },
            "behavior": {
                "auto_save_enabled": True,
                "auto_save_interval": 300,  # 秒
                "confirm_before_exit": True,
                "confirm_before_delete": True,
                "remember_last_files": True,
                "max_recent_files": 10,
                "auto_backup": True,
                "show_welcome_dialog": True
            },
            "data_processing": {
                "default_import_encoding": "utf-8",
                "chunk_size": 1000,
                "progress_update_interval": 100,
                "validation_level": "strict",
                "auto_detect_encoding": True,
                "skip_empty_rows": True,
                "trim_whitespace": True
            },
            "reports": {
                "default_output_format": "docx",
                "auto_open_reports": True,
                "save_report_history": True,
                "default_output_directory": "output",
                "include_timestamp_in_filename": True,
                "compress_large_reports": False,
                "report_quality": "high"
            },
            "advanced": {
                "enable_debug_mode": False,
                "log_level": "INFO",
                "memory_limit_mb": 1000,
                "max_parallel_processes": 4,
                "cache_enabled": True,
                "cache_size_mb": 100,
                "performance_monitoring": False
            },
            "shortcuts": {
                "new_file": "Ctrl+N",
                "open_file": "Ctrl+O",
                "save_file": "Ctrl+S",
                "save_as": "Ctrl+Shift+S",
                "exit": "Ctrl+Q",
                "undo": "Ctrl+Z",
                "redo": "Ctrl+Y",
                "copy": "Ctrl+C",
                "paste": "Ctrl+V",
                "find": "Ctrl+F",
                "replace": "Ctrl+H"
            },
            "last_used": {
                "import_directory": "",
                "export_directory": "",
                "template_directory": "",
                "last_opened_files": [],
                "last_used_templates": [],
                "last_session_time": None
            },
            "version": "0.1.0",
            "created_time": datetime.now().isoformat(),
            "last_modified": datetime.now().isoformat()
        }
    
    def load_preferences(self) -> Dict[str, Any]:
        """
        加载用户偏好设置
        
        Returns:
            Dict[str, Any]: 偏好设置字典
        """
        try:
            if not self.preferences_file.exists():
                self.logger.info("偏好设置文件不存在，创建默认偏好设置")
                self._preferences = self._default_preferences.copy()
                self.save_preferences()
                return self._preferences
            
            # 读取偏好设置文件
            self.logger.info(f"正在加载偏好设置: {self.preferences_file}")
            with open(self.preferences_file, 'r', encoding='utf-8') as f:
                loaded_preferences = json.load(f)
            
            # 合并默认设置（处理新增的偏好项）
            self._preferences = self._merge_with_defaults(loaded_preferences)
            
            # 更新最后修改时间
            self._preferences["last_modified"] = datetime.now().isoformat()
            
            self.logger.info("偏好设置加载成功")
            return self._preferences
            
        except json.JSONDecodeError as e:
            self.logger.error(f"偏好设置文件JSON格式错误: {str(e)}")
            self.logger.warning("使用默认偏好设置")
            self._preferences = self._default_preferences.copy()
            return self._preferences
            
        except Exception as e:
            self.logger.error(f"加载偏好设置失败: {str(e)}")
            self.logger.warning("使用默认偏好设置")
            self._preferences = self._default_preferences.copy()
            return self._preferences
    
    def _merge_with_defaults(self, loaded_preferences: Dict[str, Any]) -> Dict[str, Any]:
        """
        将加载的偏好设置与默认设置合并
        
        Args:
            loaded_preferences: 从文件加载的偏好设置
            
        Returns:
            Dict[str, Any]: 合并后的偏好设置
        """
        def merge_dict(default: dict, loaded: dict) -> dict:
            result = default.copy()
            for key, value in loaded.items():
                if key in result:
                    if isinstance(result[key], dict) and isinstance(value, dict):
                        result[key] = merge_dict(result[key], value)
                    else:
                        result[key] = value
                else:
                    result[key] = value
            return result
        
        return merge_dict(self._default_preferences, loaded_preferences)
    
    def save_preferences(self) -> bool:
        """
        保存偏好设置到文件
        
        Returns:
            bool: 是否保存成功
        """
        try:
            if self._preferences is None:
                self.logger.warning("偏好设置未加载，无法保存")
                return False
            
            # 更新最后修改时间
            self._preferences["last_modified"] = datetime.now().isoformat()
            
            # 确保目录存在
            self.preferences_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存偏好设置
            self.logger.info(f"正在保存偏好设置: {self.preferences_file}")
            with open(self.preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self._preferences, f, indent=2, ensure_ascii=False)
            
            self.logger.info("偏好设置保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存偏好设置失败: {str(e)}")
            return False
    
    def get_preference(self, key_path: str, default: Any = None) -> Any:
        """
        获取偏好设置值
        
        Args:
            key_path: 设置键路径，使用点分隔（如: "ui.theme"）
            default: 默认值
            
        Returns:
            Any: 偏好设置值
        """
        try:
            # 确保偏好设置已加载
            if self._preferences is None:
                self.load_preferences()
            
            # 解析键路径
            keys = key_path.split('.')
            current = self._preferences
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    self.logger.debug(f"偏好设置键不存在: {key_path}，使用默认值: {default}")
                    return default
            
            return current
            
        except Exception as e:
            self.logger.error(f"获取偏好设置失败: {str(e)}")
            return default
    
    def get_all_preferences(self) -> Dict[str, Any]:
        """
        获取所有偏好设置
        
        Returns:
            Dict[str, Any]: 完整的偏好设置字典
        """
        try:
            # 确保偏好设置已加载
            if self._preferences is None:
                self.load_preferences()
            
            return self._preferences.copy()
            
        except Exception as e:
            self.logger.error(f"获取所有偏好设置失败: {str(e)}")
            return self._default_preferences.copy()
    
    def set_preference(
        self, 
        key_path: str, 
        value: Any, 
        save_immediately: bool = True
    ) -> bool:
        """
        设置偏好设置值
        
        Args:
            key_path: 偏好设置键路径，如 "ui.theme"
            value: 偏好设置值
            save_immediately: 是否立即保存到文件
            
        Returns:
            bool: 是否设置成功
        """
        try:
            if self._preferences is None:
                self.load_preferences()
            
            # 记录变更历史
            old_value = self.get_preference(key_path)
            if old_value != value:
                self._record_change(key_path, old_value, value)
            
            # 设置嵌套值
            keys = key_path.split(".")
            current = self._preferences
            
            # 遍历到最后一个键的父级
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            self.logger.debug(f"偏好设置已更新: {key_path} = {value}")
            
            if save_immediately:
                return self.save_preferences()
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置偏好失败: {str(e)}")
            return False
    
    def _record_change(self, key_path: str, old_value: Any, new_value: Any) -> None:
        """
        记录偏好设置变更历史
        
        Args:
            key_path: 偏好设置键路径
            old_value: 旧值
            new_value: 新值
        """
        change_record = {
            "timestamp": datetime.now().isoformat(),
            "key_path": key_path,
            "old_value": old_value,
            "new_value": new_value
        }
        
        self._change_history.append(change_record)
        
        # 限制历史记录数量
        max_history = 100
        if len(self._change_history) > max_history:
            self._change_history = self._change_history[-max_history:]
    
    def reset_preferences(self, section: Optional[str] = None) -> bool:
        """
        重置偏好设置
        
        Args:
            section: 要重置的部分，None表示重置所有
            
        Returns:
            bool: 是否重置成功
        """
        try:
            if self._preferences is None:
                self.load_preferences()
            
            if section is None:
                # 重置所有偏好设置
                self._preferences = self._default_preferences.copy()
                self.logger.info("所有偏好设置已重置为默认值")
            else:
                # 重置指定部分
                if section in self._default_preferences:
                    self._preferences[section] = self._default_preferences[section].copy()
                    self.logger.info(f"偏好设置部分 '{section}' 已重置为默认值")
                else:
                    self.logger.warning(f"未找到偏好设置部分: {section}")
                    return False
            
            return self.save_preferences()
            
        except Exception as e:
            self.logger.error(f"重置偏好设置失败: {str(e)}")
            return False
    
    def export_preferences(self, export_path: Union[str, Path]) -> bool:
        """
        导出偏好设置到文件
        
        Args:
            export_path: 导出文件路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            if self._preferences is None:
                self.load_preferences()
            
            export_path = Path(export_path)
            
            # 确保目录存在
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 导出偏好设置
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self._preferences, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"偏好设置已导出到: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出偏好设置失败: {str(e)}")
            return False
    
    def import_preferences(
        self, 
        import_path: Union[str, Path], 
        merge_mode: bool = True
    ) -> bool:
        """
        从文件导入偏好设置
        
        Args:
            import_path: 导入文件路径
            merge_mode: 是否合并模式（True合并，False覆盖）
            
        Returns:
            bool: 是否导入成功
        """
        try:
            import_path = Path(import_path)
            
            if not import_path.exists():
                self.logger.error(f"导入文件不存在: {import_path}")
                return False
            
            # 读取偏好设置
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_preferences = json.load(f)
            
            if merge_mode:
                # 合并模式
                if self._preferences is None:
                    self.load_preferences()
                self._preferences = self._merge_with_defaults(imported_preferences)
            else:
                # 覆盖模式
                self._preferences = imported_preferences
            
            # 保存导入的偏好设置
            success = self.save_preferences()
            
            if success:
                self.logger.info(f"偏好设置已从 {import_path} 导入")
            
            return success
            
        except json.JSONDecodeError as e:
            self.logger.error(f"导入文件JSON格式错误: {str(e)}")
            return False
            
        except Exception as e:
            self.logger.error(f"导入偏好设置失败: {str(e)}")
            return False
    
    def update_last_used(self, category: str, item: Any) -> bool:
        """
        更新最近使用项目
        
        Args:
            category: 类别 (import_directory, export_directory等)
            item: 项目
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if self._preferences is None:
                self.load_preferences()
            
            if "last_used" not in self._preferences:
                self._preferences["last_used"] = {}
            
            # 更新最近使用项目
            if category.endswith("_files") or category.endswith("_templates"):
                # 列表类型的最近使用项目
                if category not in self._preferences["last_used"]:
                    self._preferences["last_used"][category] = []
                
                items_list = self._preferences["last_used"][category]
                
                # 移除重复项
                if item in items_list:
                    items_list.remove(item)
                
                # 添加到开头
                items_list.insert(0, item)
                
                # 限制数量
                max_items = self.get_preference("behavior.max_recent_files", 10)
                if len(items_list) > max_items:
                    items_list[:] = items_list[:max_items]
            else:
                # 单个值类型
                self._preferences["last_used"][category] = item
            
            # 更新会话时间
            self._preferences["last_used"]["last_session_time"] = datetime.now().isoformat()
            
            return self.save_preferences()
            
        except Exception as e:
            self.logger.error(f"更新最近使用项目失败: {str(e)}")
            return False
    
    def get_change_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取偏好设置变更历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 变更历史列表
        """
        if limit is None:
            return self._change_history.copy()
        else:
            return self._change_history[-limit:] if limit > 0 else []
    
    def clear_change_history(self) -> None:
        """清空变更历史"""
        self._change_history.clear()
        self.logger.info("偏好设置变更历史已清空") 