{"table_mappings": {"工资数据表": {"metadata": {"created_at": "2025-06-24T10:59:03.531419", "last_modified": "2025-06-26T17:50:52.674764", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.674764", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "basic_salary": "基本工资", "position_salary": "岗位工资888", "performance_bonus": "绩效工资", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.674764", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 6}]}, "异动人员表": {"metadata": {"created_at": "2025-06-24T10:59:03.547578", "last_modified": "2025-06-26T17:50:52.680922", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.680922", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"员工编号": "员工编号", "employee_name": "员工姓名", "异动类型": "异动类型", "异动原因": "异动原因", "生效日期": "生效日期"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.680922", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 5}]}, "salary_data_2025_01_pension_employees": {"metadata": {"created_at": "2025-06-24T15:12:06.965774", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号"}, "edit_history": [{"timestamp": "2025-06-24T15:12:06.966838", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-24T19:02:53.927943", "field": "employee_id", "old_value": "工号", "new_value": "工号", "user_action": true}]}, "salary_data_2022_01_a_grade_employees": {"metadata": {"created_at": "2025-06-24T15:15:20.939489", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-24T15:15:20.939489", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2021_12_retired_employees": {"metadata": {"created_at": "2025-06-24T15:20:28.043654", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-24T15:20:28.043654", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2021_01_retired_employees": {"metadata": {"created_at": "2025-06-24T23:39:13.925997", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2021_01_pension_employees": {"metadata": {"created_at": "2025-06-24T23:39:13.957596", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2021_01_active_employees": {"metadata": {"created_at": "2025-06-24T23:39:13.973439", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2021_01_a_grade_employees": {"metadata": {"created_at": "2025-06-24T23:39:14.017464", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2025_05_active_employees": {"metadata": {"created_at": "2025-06-25T00:02:48.587584", "last_modified": "2025-06-26T17:50:52.682974", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.682974", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门", "2025年岗位工资": "岗位工资", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.682974", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 5}]}, "salary_data_2020_01_retired_employees": {"metadata": {"created_at": "2025-06-25T00:09:46.636003", "last_modified": "2025-06-25T00:09:54.330938", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T00:09:46.636003", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T00:09:54.330938", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2020_01_pension_employees": {"metadata": {"created_at": "2025-06-25T00:10:04.919630", "last_modified": "2025-06-25T00:10:11.027218", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T00:10:04.919630", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T00:10:11.027218", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2020_01_a_grade_employees": {"metadata": {"created_at": "2025-06-25T00:10:28.241246", "last_modified": "2025-06-25T00:10:34.902365", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T00:10:28.241246", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T00:10:34.902365", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2019_12_a_grade_employees": {"metadata": {"created_at": "2025-06-25T08:30:36.174070", "last_modified": "2025-06-25T08:30:36.189757", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T08:30:36.189757", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2018_06_retired_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.279592", "last_modified": "2025-06-26T17:50:52.687591", "auto_generated": true, "user_modified": false, "fixed_mapping_key": true, "original_key": "salary_data_retired_employees_2025_06_离休人员工资表", "migrated_at": "2025-06-26T17:50:52.687591", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.687591", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 12}]}, "salary_data_2018_06_pension_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.453826", "last_modified": "2025-06-26T17:50:52.698542", "auto_generated": true, "user_modified": false, "fixed_mapping_key": true, "original_key": "salary_data_pension_employees_2025_06_退休人员工资表", "migrated_at": "2025-06-26T17:50:52.698542", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "津贴补贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.698542", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 21}]}, "salary_data_2018_06_active_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.652931", "last_modified": "2025-06-26T17:50:52.716045", "auto_generated": true, "user_modified": false, "fixed_mapping_key": true, "original_key": "salary_data_active_employees_2025_全部在职人员工资表", "migrated_at": "2025-06-26T17:50:52.716045", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "绩效工资", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.716045", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 22}]}, "salary_data_2018_06_a_grade_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.862152", "last_modified": "2025-06-26T17:50:52.720745", "auto_generated": true, "user_modified": true, "fixed_mapping_key": true, "original_key": "salary_data_a_grade_employees_2025_A岗职工", "migrated_at": "2025-06-26T17:50:52.720745", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "2025年岗位工资": "岗位工资", "2025年校龄工资": "2025年校龄工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "绩效工资", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金"}, "edit_history": [{"timestamp": "2025-06-25T10:28:10.351626", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T10:28:17.201869", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}, {"timestamp": "2025-06-26T17:50:52.720745", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2017_01_retired_employees": {"metadata": {"created_at": "2025-06-25T10:29:41.566408", "last_modified": "2025-06-26T17:50:52.727965", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.727965", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.727965", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 12}]}, "salary_data_2017_01_pension_employees": {"metadata": {"created_at": "2025-06-25T10:29:41.829478", "last_modified": "2025-06-26T17:50:52.864048", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.864048", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "津贴补贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.864048", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 21}]}, "salary_data_2017_01_active_employees": {"metadata": {"created_at": "2025-06-25T10:29:42.136176", "last_modified": "2025-06-26T17:50:52.906530", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.906530", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "绩效工资", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.906530", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 22}]}, "salary_data_2017_01_a_grade_employees": {"metadata": {"created_at": "2025-06-25T10:29:42.397346", "last_modified": "2025-06-26T17:50:52.917691", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.917691", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "2025年岗位工资": "岗位工资", "2025年校龄工资": "2025年校龄工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "绩效工资", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.917691", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2022_12_retired_employees": {"metadata": {"created_at": "2025-06-25T11:05:58.123951", "last_modified": "2025-06-25T11:05:58.124993", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T11:05:58.124993", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2016_01_retired_employees": {"metadata": {"created_at": "2025-06-25T11:08:27.647111", "last_modified": "2025-06-25T11:27:40", "auto_generated": true, "user_modified": true, "fix_applied": "2016年表字段映射修复 - 英文字段名"}, "field_mappings": {"id": "ID", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效奖金", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2016_01_pension_employees": {"metadata": {"created_at": "2025-06-25T11:08:27.846032", "last_modified": "2025-06-25T11:27:40", "auto_generated": true, "user_modified": true, "fix_applied": "2016年表字段映射修复 - 英文字段名"}, "field_mappings": {"id": "ID", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效奖金", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2016_01_active_employees": {"metadata": {"created_at": "2025-06-25T11:08:28.052142", "last_modified": "2025-06-25T11:27:40", "auto_generated": true, "user_modified": true, "fix_applied": "2016年表字段映射修复 - 英文字段名"}, "field_mappings": {"id": "ID", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效奖金", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2016_01_a_grade_employees": {"metadata": {"created_at": "2025-06-25T11:08:28.264080", "last_modified": "2025-06-25T11:27:40", "auto_generated": true, "user_modified": true, "fix_applied": "2016年表字段映射修复 - 英文字段名"}, "field_mappings": {"id": "ID", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效奖金", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2026_02_retired_employees": {"metadata": {"created_at": "2025-06-25T19:44:17.545544", "last_modified": "2025-06-26T17:50:52.921868", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.921868", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.921868", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 12}]}, "salary_data_2026_02_pension_employees": {"metadata": {"created_at": "2025-06-25T19:44:17.859723", "last_modified": "2025-06-26T17:50:52.932955", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.932955", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "津贴补贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.932955", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 21}]}, "salary_data_2026_02_active_employees": {"metadata": {"created_at": "2025-06-25T19:44:18.361844", "last_modified": "2025-06-26T17:50:52.947164", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.947164", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "绩效工资", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.947164", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 22}]}, "salary_data_2026_02_a_grade_employees": {"metadata": {"created_at": "2025-06-25T19:44:18.719392", "last_modified": "2025-06-26T17:50:52.953385", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.953385", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "2025年岗位工资": "岗位工资", "2025年校龄工资": "2025年校龄工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "绩效工资", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.953385", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2026_11_retired_employees": {"metadata": {"created_at": "2025-06-25T21:46:16.692572", "last_modified": "2025-06-26T17:50:52.959097", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.959097", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.959097", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 12}]}, "salary_data_2026_11_pension_employees": {"metadata": {"created_at": "2025-06-25T21:46:16.966711", "last_modified": "2025-06-26T17:50:52.971180", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.971180", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "津贴补贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.971180", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 21}]}, "salary_data_2026_11_active_employees": {"metadata": {"created_at": "2025-06-25T21:46:17.299260", "last_modified": "2025-06-26T17:50:52.985060", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.985060", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "绩效工资", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.985060", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 22}]}, "salary_data_2026_11_a_grade_employees": {"metadata": {"created_at": "2025-06-25T21:46:17.591087", "last_modified": "2025-06-26T17:50:52.993838", "auto_generated": true, "user_modified": true, "migrated_at": "2025-06-26T17:50:52.993838", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "2025年岗位工资": "岗位工资", "2025年校龄工资": "2025年校龄工资", "allowance": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "绩效工资", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金"}, "edit_history": [{"timestamp": "2025-06-25T21:47:33.008378", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T21:47:39.743743", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}, {"timestamp": "2025-06-26T17:50:52.993838", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2026_11": {"metadata": {"created_at": "2025-06-25T21:58:58.190218", "last_modified": "2025-06-26T17:50:52.994884", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:52.994884", "migration_from": "excel_column", "migration_to": "db_field"}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门", "2026年岗位工资": "2026年岗位工资", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:52.994884", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 5}]}, "salary_data_2026_03_retired_employees": {"metadata": {"created_at": "2025-06-25T22:46:00.553940", "last_modified": "2025-06-25T23:16:40.445", "auto_generated": true, "user_modified": true}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "basic_salary": "基本\n离休费", "allowance": "护理费", "total_salary": "合计"}, "edit_history": [{"timestamp": "2025-06-25T23:16:40.445", "field": "auto_fix", "old_value": "excel_column_mapping", "new_value": "db_field_mapping", "user_action": true, "fix_reason": "Convert Excel column mapping to database field mapping"}]}, "salary_data_2026_03_pension_employees": {"metadata": {"created_at": "2025-06-25T22:46:00.885995", "last_modified": "2025-06-25T23:16:40.450", "auto_generated": true, "user_modified": true}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "basic_salary": "基本退休费", "allowance": "2023待遇调整", "overtime_pay": "增资预付", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-25T23:16:40.450", "field": "auto_fix", "old_value": "excel_column_mapping", "new_value": "db_field_mapping", "user_action": true, "fix_reason": "Convert Excel column mapping to database field mapping"}]}, "salary_data_2026_03_active_employees": {"metadata": {"created_at": "2025-06-25T22:46:01.215528", "last_modified": "2025-06-25T23:16:40.455", "auto_generated": true, "user_modified": true}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "basic_salary": "薪级工资", "allowance": "通讯补贴", "performance_bonus": "绩效工资", "overtime_pay": "补发", "deduction": "代扣代存养老保险", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-25T23:16:40.455", "field": "auto_fix", "old_value": "excel_column_mapping", "new_value": "db_field_mapping", "user_action": true, "fix_reason": "Convert Excel column mapping to database field mapping"}]}, "salary_data_2026_03_a_grade_employees": {"metadata": {"created_at": "2025-06-25T22:46:01.555079", "last_modified": "2025-06-25T23:16:40.460", "auto_generated": true, "user_modified": true}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "basic_salary": "2025年校龄工资", "allowance": "2025年生活补贴", "performance_bonus": "绩效工资", "deduction": "2025公积金", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-25T23:16:40.460", "field": "auto_fix", "old_value": "excel_column_mapping", "new_value": "db_field_mapping", "user_action": true, "fix_reason": "Convert Excel column mapping to database field mapping"}]}, "salary_data_2026_04_retired_employees": {"metadata": {"created_at": "2025-06-26T00:04:14.279415", "last_modified": "2025-06-26T17:50:53.002198", "auto_generated": true, "user_modified": true, "migrated_at": "2025-06-26T17:50:53.002198", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本离休费": "基本\n离休费", "allowance": "离休\n补贴", "护理费": "护理费", "合计": "合计", "total_salary": "合计"}, "edit_history": [{"timestamp": "2025-06-26T09:17:00.681704", "field": "total_salary", "old_value": "total_salary", "new_value": "合计", "user_action": true}, {"timestamp": "2025-06-26T17:50:53.002198", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 9}]}, "salary_data_2026_04_pension_employees": {"metadata": {"created_at": "2025-06-26T00:04:14.608760", "last_modified": "2025-06-26T17:50:53.017215", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.017215", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.017215", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 17}]}, "salary_data_2026_04_active_employees": {"metadata": {"created_at": "2025-06-26T00:04:14.904611", "last_modified": "2025-06-26T17:50:53.030119", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.030119", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "position_salary": "岗位工资", "grade_salary": "薪级工资", "allowance": "通讯补贴", "performance": "绩效工资", "卫生费": "卫生费", "车补": "车补", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.030119", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2026_04_a_grade_employees": {"metadata": {"created_at": "2025-06-26T00:04:15.235569", "last_modified": "2025-06-26T17:50:53.036330", "auto_generated": true, "user_modified": true, "migrated_at": "2025-06-26T17:50:53.036330", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "position_salary": "岗位工资", "2025年校龄salary": "2025年校龄工资", "allowance": "2025年生活补贴", "performance": "绩效工资", "卫生费": "卫生费", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "basic_salary": "基本工资"}, "edit_history": [{"timestamp": "2025-06-26T14:21:45.123252", "field": "basic_salary", "old_value": "basic_salary", "new_value": "基本工资", "user_action": true}, {"timestamp": "2025-06-26T17:50:53.036330", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 14}]}, "salary_data_2026_05_retired_employees": {"metadata": {"created_at": "2025-06-26T14:25:07.325238", "last_modified": "2025-06-26T17:50:53.048048", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.048048", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本离休费": "基本\n离休费", "allowance": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.048048", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 8}]}, "salary_data_2026_05_pension_employees": {"metadata": {"created_at": "2025-06-26T14:25:07.571938", "last_modified": "2025-06-26T17:50:53.060435", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.060435", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.060435", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 17}]}, "salary_data_2026_05_active_employees": {"metadata": {"created_at": "2025-06-26T14:25:07.795138", "last_modified": "2025-06-26T17:50:53.077163", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.077163", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "position_salary": "岗位工资", "grade_salary": "薪级工资", "allowance": "通讯补贴", "performance": "绩效工资", "卫生费": "卫生费", "车补": "车补", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.077163", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2026_05_a_grade_employees": {"metadata": {"created_at": "2025-06-26T14:25:08.181445", "last_modified": "2025-06-26T17:50:53.088020", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.088020", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "position_salary": "岗位工资", "2025年校龄salary": "2025年校龄工资", "allowance": "2025年生活补贴", "performance": "绩效工资", "卫生费": "卫生费", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.088020", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 13}]}, "salary_data_2026_06_retired_employees": {"metadata": {"created_at": "2025-06-26T15:26:05.364196", "last_modified": "2025-06-26T17:50:53.094321", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.094321", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本离休费": "基本\n离休费", "allowance": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.094321", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 8}]}, "salary_data_2026_06_pension_employees": {"metadata": {"created_at": "2025-06-26T15:26:05.546591", "last_modified": "2025-06-26T17:50:53.108068", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.108068", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.108068", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 17}]}, "salary_data_2026_06_active_employees": {"metadata": {"created_at": "2025-06-26T15:26:05.765753", "last_modified": "2025-06-26T17:50:53.122866", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.122866", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "position_salary": "岗位工资", "grade_salary": "薪级工资", "allowance": "通讯补贴", "performance": "绩效工资", "卫生费": "卫生费", "车补": "车补", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.122866", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2026_06_a_grade_employees": {"metadata": {"created_at": "2025-06-26T15:26:05.998557", "last_modified": "2025-06-26T17:50:53.135580", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.135580", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "position_salary": "岗位工资", "2025年校龄salary": "2025年校龄工资", "allowance": "2025年生活补贴", "performance": "绩效工资", "卫生费": "卫生费", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.135580", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 13}]}, "salary_data_2026_07_retired_employees": {"metadata": {"created_at": "2025-06-26T15:52:46.430632", "last_modified": "2025-06-26T17:50:53.141199", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.141199", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "基本离休费": "基本\n离休费", "allowance": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.141199", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 8}]}, "salary_data_2026_07_pension_employees": {"metadata": {"created_at": "2025-06-26T15:52:46.648352", "last_modified": "2025-06-26T17:50:53.154009", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.154009", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门", "position": "人员类别代码", "基本退休费": "基本退休费", "allowance": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发合计"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.154009", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 17}]}, "salary_data_2026_07_active_employees": {"metadata": {"created_at": "2025-06-26T15:52:46.856329", "last_modified": "2025-06-26T17:50:53.163501", "auto_generated": true, "user_modified": false, "migrated_at": "2025-06-26T17:50:53.163501", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门", "position": "人员类别", "position_salary": "岗位工资", "grade_salary": "薪级工资", "allowance": "通讯补贴", "performance": "绩效工资", "卫生费": "卫生费", "车补": "车补", "补发": "补发", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.163501", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2026_07_a_grade_employees": {"metadata": {"created_at": "2025-06-26T15:52:47.064358", "last_modified": "2025-06-26T17:50:53.178975", "auto_generated": true, "user_modified": true, "migrated_at": "2025-06-26T17:50:53.178975", "migration_from": "mixed", "migration_to": "db_field"}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名1", "department": "部门", "position": "人员类别代码", "position_salary": "岗位工资", "2025年校龄salary": "2025年校龄工资", "allowance": "2025年生活补贴", "performance": "绩效工资", "卫生费": "卫生费", "借支": "借支", "total_salary": "应发合计", "social_insurance": "2025公积金", "id_card": "身份证"}, "edit_history": [{"timestamp": "2025-06-26T16:48:01.389115", "field": "id_card", "old_value": "id_card", "new_value": "身份证", "user_action": true}, {"timestamp": "2025-06-26T16:48:17.326331", "field": "姓名", "old_value": "姓名", "new_value": "姓名1", "user_action": true}, {"timestamp": "2025-06-26T17:50:53.178975", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 14}]}, "salary_data_2026_01_pension_employees": {"metadata": {"created_at": "2025-06-26T16:50:04.301831", "last_modified": "2025-06-26T16:50:04.301831", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-26T16:50:04.301831", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2026_01_active_employees": {"metadata": {"created_at": "2025-06-26T16:50:22.072464", "last_modified": "2025-06-26T16:50:22.072464", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-26T16:50:22.072464", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2026_08_retired_employees": {"field_mappings": {"employee_name": "姓名", "department": "部门名称", "id": "序号", "employee_id": "人员代码", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "original_excel_headers": {"序号": "序号", "人员代码": "人员代码", "employee_name": "姓名", "department": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:36:00.583760", "sheet_name": "离休人员工资表", "has_chinese_headers": true, "last_modified": "2025-06-26T17:50:53.191552", "migrated_at": "2025-06-26T17:50:53.191552", "migration_from": "excel_column", "migration_to": "db_field"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.191552", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 12}]}, "salary_data_2026_08_pension_employees": {"field_mappings": {"employee_name": "姓名", "department": "部门名称", "allowance": "津贴", "total_salary": "应发工资", "id": "序号", "employee_id": "人员代码", "position": "人员类别代码", "基本退休费": "基本退休费", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整"}, "original_excel_headers": {"序号": "序号", "人员代码": "人员代码", "employee_name": "姓名", "department": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "allowance": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发工资"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:36:00.870165", "sheet_name": "退休人员工资表", "has_chinese_headers": true, "last_modified": "2025-06-26T17:50:53.206787", "migrated_at": "2025-06-26T17:50:53.206787", "migration_from": "excel_column", "migration_to": "db_field"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.206787", "action": "field_mapping_migration", "from_format": "excel_column", "to_format": "db_field", "migrated_fields": 21}]}, "salary_data_2026_08_active_employees": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "allowance": "津贴", "total_salary": "应发工资", "id": "序号", "position": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "social_insurance": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "original_excel_headers": {"序号": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "allowance": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "total_salary": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": true, "created_at": "2025-06-26T17:36:01.105420", "sheet_name": "全部在职人员工资表", "has_chinese_headers": true, "last_modified": "2025-06-26T17:52:51.363332", "migrated_at": "2025-06-26T17:50:53.219262", "migration_from": "mixed", "migration_to": "db_field"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.219262", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 22}, {"timestamp": "2025-06-26T17:52:51.340214", "field": "employee_id", "new_value": "工号_测试修改", "action": "user_edit"}, {"timestamp": "2025-06-26T17:52:51.363332", "field": "employee_id", "new_value": "工号", "action": "user_edit"}]}, "salary_data_2026_08_a_grade_employees": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "allowance": "津贴", "total_salary": "应发工资", "id": "序号", "position": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "借支": "借支", "social_insurance": "2025公积金"}, "original_excel_headers": {"序号": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "allowance": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "借支": "借支", "total_salary": "应发工资", "2025公积金": "2025公积金"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:36:01.364720", "sheet_name": "A岗职工", "has_chinese_headers": true, "last_modified": "2025-06-26T17:50:53.228179", "migrated_at": "2025-06-26T17:50:53.228179", "migration_from": "mixed", "migration_to": "db_field"}, "edit_history": [{"timestamp": "2025-06-26T17:50:53.228179", "action": "field_mapping_migration", "from_format": "mixed", "to_format": "db_field", "migrated_fields": 16}]}, "salary_data_2025_10_retired_employees": {"field_mappings": {"employee_name": "姓名", "department": "部门名称", "序号": "序号", "人员代码": "人员代码", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "original_excel_headers": {"序号": "序号", "人员代码": "人员代码", "employee_name": "姓名", "department": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:52:28.208134", "sheet_name": "离休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_10_pension_employees": {"field_mappings": {"employee_name": "姓名", "department": "部门名称", "allowance": "津贴", "total_salary": "应发工资", "序号": "序号", "人员代码": "人员代码", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整"}, "original_excel_headers": {"序号": "序号", "人员代码": "人员代码", "employee_name": "姓名", "department": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "allowance": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "total_salary": "应发工资"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:52:28.357574", "sheet_name": "退休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_10_active_employees": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "allowance": "津贴", "total_salary": "应发工资", "序号": "序号", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "original_excel_headers": {"序号": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "allowance": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "total_salary": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:52:28.539928", "sheet_name": "全部在职人员工资表", "has_chinese_headers": true}}, "salary_data_2025_10_a_grade_employees": {"field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "allowance": "津贴", "total_salary": "应发工资", "序号": "序号", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "借支": "借支", "2025公积金": "2025公积金"}, "original_excel_headers": {"序号": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "allowance": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "借支": "借支", "total_salary": "应发工资", "2025公积金": "2025公积金"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-06-26T17:52:28.703711", "sheet_name": "A岗职工", "has_chinese_headers": true}}}, "last_updated": "2025-06-26T17:52:28.705720", "user_preferences": {"default_field_patterns": {}, "recent_edits": [], "favorite_mappings": [], "header_preferences": {"salary_data_2026_11": {"selected_fields": ["工号", "姓名", "应发工资"], "updated_at": "2025-06-25T21:58:58.197090", "field_count": 3}, "test_table": {"selected_fields": ["工号", "姓名"], "updated_at": "2025-06-25T22:00:57.816461", "field_count": 2}, "test_table_2026_11": {"selected_fields": ["工号", "姓名"], "updated_at": "2025-06-25T22:35:24.588154", "field_count": 2}, "salary_data_2026_03_active_employees": {"selected_fields": ["id", "employee_id", "employee_name", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary"], "updated_at": "2025-06-25T23:23:32.421626", "field_count": 11}, "salary_data_2026_03_a_grade_employees": {"selected_fields": ["id", "employee_id", "employee_name", "department", "position", "basic_salary", "performance_bonus", "allowance", "deduction", "total_salary"], "updated_at": "2025-06-25T23:25:08.923589", "field_count": 10}, "salary_data_2026_04_active_employees": {"selected_fields": ["id", "employee_id", "employee_name", "department", "position", "basic_salary", "performance_bonus", "overtime_pay", "allowance", "deduction", "total_salary"], "updated_at": "2025-06-26T09:20:28.927207", "field_count": 11}}}, "table_preferences": {"salary_data_2026_01_a_grade_employees": {"preferred_fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary"], "updated_at": "2025-06-26T11:02:33.068828", "field_count": 7}, "salary_data_2026_04_a_grade_employees": {"preferred_fields": ["id", "employee_id", "employee_name", "id_card", "department", "position", "basic_salary"], "updated_at": "2025-06-26T11:02:33.136675", "field_count": 7}, "salary_data_2026_04_active_employees": {"preferred_fields": ["id", "employee_id", "employee_name", "department", "position", "allowance", "total_salary"], "updated_at": "2025-06-26T14:21:01.100753", "field_count": 7}}, "migration_info": {"migrated_at": "2025-06-26T17:50:53.228179", "migration_stats": {"total_tables": 62, "migrated_tables": 40, "skipped_tables": 22, "error_tables": 0, "migrated_table_names": ["工资数据表", "异动人员表", "salary_data_2025_05_active_employees", "salary_data_2018_06_retired_employees", "salary_data_2018_06_pension_employees", "salary_data_2018_06_active_employees", "salary_data_2018_06_a_grade_employees", "salary_data_2017_01_retired_employees", "salary_data_2017_01_pension_employees", "salary_data_2017_01_active_employees", "salary_data_2017_01_a_grade_employees", "salary_data_2026_02_retired_employees", "salary_data_2026_02_pension_employees", "salary_data_2026_02_active_employees", "salary_data_2026_02_a_grade_employees", "salary_data_2026_11_retired_employees", "salary_data_2026_11_pension_employees", "salary_data_2026_11_active_employees", "salary_data_2026_11_a_grade_employees", "salary_data_2026_11", "salary_data_2026_04_retired_employees", "salary_data_2026_04_pension_employees", "salary_data_2026_04_active_employees", "salary_data_2026_04_a_grade_employees", "salary_data_2026_05_retired_employees", "salary_data_2026_05_pension_employees", "salary_data_2026_05_active_employees", "salary_data_2026_05_a_grade_employees", "salary_data_2026_06_retired_employees", "salary_data_2026_06_pension_employees", "salary_data_2026_06_active_employees", "salary_data_2026_06_a_grade_employees", "salary_data_2026_07_retired_employees", "salary_data_2026_07_pension_employees", "salary_data_2026_07_active_employees", "salary_data_2026_07_a_grade_employees", "salary_data_2026_08_retired_employees", "salary_data_2026_08_pension_employees", "salary_data_2026_08_active_employees", "salary_data_2026_08_a_grade_employees"], "error_details": []}}}