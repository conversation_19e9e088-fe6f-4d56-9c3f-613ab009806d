{"table_mappings": {"salary_data_2025_05_test": {"metadata": {"created_at": "2025-06-24T10:27:43.927800", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": false}, "field_mappings": {"工号": "工号", "姓名": "姓名", "部门名称": "部门", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "津贴": "津贴补贴", "应发工资": "应发合计"}, "edit_history": []}, "工资数据表": {"metadata": {"created_at": "2025-06-24T10:59:03.531419", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": false}, "field_mappings": {"工号": "工号", "姓名": "姓名", "基本工资": "基本工资", "岗位工资": "岗位工资888", "绩效工资": "绩效工资", "应发合计": "应发合计"}, "edit_history": []}, "异动人员表": {"metadata": {"created_at": "2025-06-24T10:59:03.547578", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": false}, "field_mappings": {"员工编号": "员工编号", "员工姓名": "员工姓名", "异动类型": "异动类型", "异动原因": "异动原因", "生效日期": "生效日期"}, "edit_history": []}, "test_field_mapping_table": {"metadata": {"created_at": "2025-06-24T14:05:16.947949", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": false}, "field_mappings": {"工号": "工号", "姓名": "姓名", "部门名称": "部门", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "津贴": "津贴补贴", "2025年奖励性绩效预发": "奖金", "应发工资": "应发合计", "2025公积金": "五险一金个人"}, "edit_history": []}, "test_table": {"metadata": {"created_at": "2025-06-24T14:05:41.430386", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": true}, "field_mappings": {"test_field": "新测试字段", "test_field2": "测试字段2"}, "edit_history": [{"timestamp": "2025-06-24T14:14:28.812030", "field": "test_field", "old_value": "测试字段", "new_value": "新测试字段", "user_action": true}]}, "persistence_test_table": {"metadata": {"created_at": "2025-06-24T14:05:41.430386", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": false}, "field_mappings": {"原始字段1": "映射字段1", "原始字段2": "映射字段2", "原始字段3": "映射字段3"}, "edit_history": []}, "salary_data_2025_01_pension_employees": {"metadata": {"created_at": "2025-06-24T15:12:06.965774", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号"}, "edit_history": [{"timestamp": "2025-06-24T15:12:06.966838", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-24T19:02:53.927943", "field": "employee_id", "old_value": "工号", "new_value": "工号", "user_action": true}]}, "salary_data_2022_01_a_grade_employees": {"metadata": {"created_at": "2025-06-24T15:15:20.939489", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-24T15:15:20.939489", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2021_12_retired_employees": {"metadata": {"created_at": "2025-06-24T15:20:28.043654", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-24T15:20:28.043654", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2025_01_test": {"metadata": {"created_at": "2025-06-24T23:19:04.059834", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": true}, "field_mappings": {"工号": "员工编号", "姓名": "员工姓名", "部门名称": "所属部门", "基本工资": "基础薪资", "绩效工资": "绩效奖金"}, "edit_history": [{"timestamp": "2025-06-24T23:19:04.075576", "field": "工号", "old_value": "员工工号", "new_value": "员工编号", "user_action": true}]}, "salary_data_2021_01_retired_employees": {"metadata": {"created_at": "2025-06-24T23:39:13.925997", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2021_01_pension_employees": {"metadata": {"created_at": "2025-06-24T23:39:13.957596", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2021_01_active_employees": {"metadata": {"created_at": "2025-06-24T23:39:13.973439", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2021_01_a_grade_employees": {"metadata": {"created_at": "2025-06-24T23:39:14.017464", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": false, "user_modified": false}, "field_mappings": {"id": "序号", "employee_id": "工号", "employee_name": "姓名", "id_card": "身份证号", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效工资", "overtime_pay": "加班费", "allowance": "津贴补贴", "deduction": "扣除项", "total_salary": "应发合计", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "edit_history": []}, "salary_data_2025_05_active_employees": {"metadata": {"created_at": "2025-06-25T00:02:48.587584", "last_modified": "2025-06-25T00:02:48.587584", "auto_generated": true, "user_modified": false, "migrated_from_old_format": true}, "field_mappings": {"工号": "工号", "姓名": "姓名", "部门名称": "部门", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "津贴": "津贴补贴", "2025年奖励性绩效预发": "奖金", "应发工资": "应发合计", "2025公积金": "五险一金个人"}, "edit_history": []}, "salary_data_2020_01_retired_employees": {"metadata": {"created_at": "2025-06-25T00:09:46.636003", "last_modified": "2025-06-25T00:09:54.330938", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T00:09:46.636003", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T00:09:54.330938", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2020_01_pension_employees": {"metadata": {"created_at": "2025-06-25T00:10:04.919630", "last_modified": "2025-06-25T00:10:11.027218", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T00:10:04.919630", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T00:10:11.027218", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2020_01_a_grade_employees": {"metadata": {"created_at": "2025-06-25T00:10:28.241246", "last_modified": "2025-06-25T00:10:34.902365", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T00:10:28.241246", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T00:10:34.902365", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2019_12_a_grade_employees": {"metadata": {"created_at": "2025-06-25T08:30:36.174070", "last_modified": "2025-06-25T08:30:36.189757", "auto_generated": false, "user_modified": true}, "field_mappings": {"employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T08:30:36.189757", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2018_06_retired_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.279592", "last_modified": "2025-06-25T10:21:47.841901", "auto_generated": true, "user_modified": false, "fixed_mapping_key": true, "original_key": "salary_data_retired_employees_2025_06_离休人员工资表"}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": []}, "salary_data_2018_06_pension_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.453826", "last_modified": "2025-06-25T10:21:47.841901", "auto_generated": true, "user_modified": false, "fixed_mapping_key": true, "original_key": "salary_data_pension_employees_2025_06_退休人员工资表"}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴补贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "应发工资": "应发合计"}, "edit_history": []}, "salary_data_2018_06_active_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.652931", "last_modified": "2025-06-25T10:21:47.857634", "auto_generated": true, "user_modified": false, "fixed_mapping_key": true, "original_key": "salary_data_active_employees_2025_全部在职人员工资表"}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "津贴": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "绩效工资", "补发": "补发", "借支": "借支", "应发工资": "应发合计", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "salary_data_2018_06_a_grade_employees": {"metadata": {"created_at": "2025-06-25T10:11:24.862152", "last_modified": "2025-06-25T10:28:17.201869", "auto_generated": true, "user_modified": true, "fixed_mapping_key": true, "original_key": "salary_data_a_grade_employees_2025_A岗职工"}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "绩效工资", "借支": "借支", "应发工资": "应发合计", "2025公积金": "2025公积金", "employee_id": "工号", "employee_name": "姓名"}, "edit_history": [{"timestamp": "2025-06-25T10:28:10.351626", "field": "employee_id", "old_value": "employee_id", "new_value": "工号", "user_action": true}, {"timestamp": "2025-06-25T10:28:17.201869", "field": "employee_name", "old_value": "employee_name", "new_value": "姓名", "user_action": true}]}, "salary_data_2017_01_retired_employees": {"metadata": {"created_at": "2025-06-25T10:29:41.566408", "last_modified": "2025-06-25T10:29:41.566408", "auto_generated": true, "user_modified": false}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "合计": "合计"}, "edit_history": []}, "salary_data_2017_01_pension_employees": {"metadata": {"created_at": "2025-06-25T10:29:41.829478", "last_modified": "2025-06-25T10:29:41.829478", "auto_generated": true, "user_modified": false}, "field_mappings": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴补贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "应发工资": "应发合计"}, "edit_history": []}, "salary_data_2017_01_active_employees": {"metadata": {"created_at": "2025-06-25T10:29:42.136176", "last_modified": "2025-06-25T10:29:42.136176", "auto_generated": true, "user_modified": false}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "岗位工资", "2025年薪级工资": "薪级工资", "津贴": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "绩效工资", "补发": "补发", "借支": "借支", "应发工资": "应发合计", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "edit_history": []}, "salary_data_2017_01_a_grade_employees": {"metadata": {"created_at": "2025-06-25T10:29:42.397346", "last_modified": "2025-06-25T10:29:42.397346", "auto_generated": true, "user_modified": false}, "field_mappings": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴补贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "2025年奖励性绩效预发": "绩效工资", "借支": "借支", "应发工资": "应发合计", "2025公积金": "2025公积金"}, "edit_history": []}}, "last_updated": "2025-06-25T10:29:42.397346", "salary_data_2018_06_a_grade_employees": {"id": "id", "employee_id": "序号", "employee_name": "姓名", "id_card": "身份证", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效", "overtime_pay": "加班费", "allowance": "津贴", "deduction": "扣款", "total_salary": "合计", "month": "month", "year": "year", "created_at": "created_at", "updated_at": "updated_at"}, "salary_data_2018_06_active_employees": {"id": "id", "employee_id": "序号", "employee_name": "姓名", "id_card": "身份证", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效", "overtime_pay": "加班费", "allowance": "津贴", "deduction": "扣款", "total_salary": "合计", "month": "month", "year": "year", "created_at": "created_at", "updated_at": "updated_at"}, "salary_data_2018_06_pension_employees": {"id": "id", "employee_id": "序号", "employee_name": "姓名", "id_card": "身份证", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效", "overtime_pay": "加班费", "allowance": "津贴", "deduction": "扣款", "total_salary": "合计", "month": "month", "year": "year", "created_at": "created_at", "updated_at": "updated_at"}, "salary_data_2018_06_retired_employees": {"id": "id", "employee_id": "序号", "employee_name": "姓名", "id_card": "身份证", "department": "部门", "position": "职位", "basic_salary": "基本工资", "performance_bonus": "绩效", "overtime_pay": "加班费", "allowance": "津贴", "deduction": "扣款", "total_salary": "合计", "month": "month", "year": "year", "created_at": "created_at", "updated_at": "updated_at"}}