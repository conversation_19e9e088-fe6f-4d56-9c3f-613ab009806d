#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n自动为新导入的表生成字段映射配置\n\n解决问题：新导入的表没有字段映射，表头显示英文\n解决方案：基于现有映射模式为新表生成映射\n\"\"\"\n\nimport sys\nimport json\nfrom pathlib import Path\nfrom typing import Dict, Any\n\n# 添加项目根目录到Python路径\nproject_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\n\ndef generate_field_mapping_for_new_tables():\n    \"\"\"为新导入的表生成字段映射\"\"\"\n    print(\"=\" * 60)\n    print(\"自动生成字段映射配置\")\n    print(\"=\" * 60)\n    \n    # 1. 读取现有字段映射\n    field_mappings_file = project_root / \"state\" / \"data\" / \"field_mappings.json\"\n    \n    if not field_mappings_file.exists():\n        print(f\"❌ 字段映射文件不存在: {field_mappings_file}\")\n        return False\n    \n    try:\n        with open(field_mappings_file, 'r', encoding='utf-8') as f:\n            config_data = json.load(f)\n        \n        table_mappings = config_data.get(\"table_mappings\", {})\n        print(f\"✅ 已加载 {len(table_mappings)} 个表的映射配置\")\n    except Exception as e:\n        print(f\"❌ 读取字段映射文件失败: {e}\")\n        return False\n    \n    # 2. 分析现有映射模式\n    print(f\"\\n2. 分析现有映射模式...\")\n    \n    # 找到一个有完整映射的表作为模板\n    template_mapping = None\n    template_table = None\n    \n    # 优先使用2021年的映射作为模板（因为日志显示2021年显示正常）\n    for table_name, table_config in table_mappings.items():\n        if \"2021\" in table_name and \"field_mappings\" in table_config:\n            field_mapping = table_config[\"field_mappings\"]\n            if len(field_mapping) > 10:  # 确保有足够的字段映射\n                template_mapping = field_mapping\n                template_table = table_name\n                break\n    \n    if not template_mapping:\n        print(f\"❌ 没有找到合适的映射模板\")\n        return False\n    \n    print(f\"✅ 使用模板: {template_table}\")\n    print(f\"   模板包含 {len(template_mapping)} 个字段映射\")\n    \n    # 3. 为2016年的表生成映射\n    print(f\"\\n3. 为2016年的表生成映射...\")\n    \n    tables_to_update = []\n    for table_name in table_mappings.keys():\n        if \"2016\" in table_name:\n            current_config = table_mappings[table_name]\n            current_mapping = current_config.get(\"field_mappings\", {})\n            \n            # 如果映射为空或只有很少字段，需要更新\n            if len(current_mapping) < 5:\n                tables_to_update.append(table_name)\n    \n    print(f\"找到需要更新的表: {tables_to_update}\")\n    \n    if not tables_to_update:\n        print(f\"✅ 所有2016年的表都已有映射配置\")\n        return True\n    \n    # 4. 生成新的映射配置\n    print(f\"\\n4. 生成新的映射配置...\")\n    \n    for table_name in tables_to_update:\n        print(f\"\\n处理表: {table_name}\")\n        \n        # 创建新的映射配置\n        new_mapping = template_mapping.copy()\n        \n        # 更新表配置\n        if table_name not in table_mappings:\n            table_mappings[table_name] = {}\n        \n        table_mappings[table_name][\"field_mappings\"] = new_mapping\n        table_mappings[table_name][\"metadata\"] = {\n            \"auto_generated\": True,\n            \"template_source\": template_table,\n            \"generated_time\": \"2025-06-25 11:20:00\",\n            \"user_modified\": False\n        }\n        \n        print(f\"  ✅ 已生成 {len(new_mapping)} 个字段映射\")\n        \n        # 显示部分映射示例\n        sample_mappings = list(new_mapping.items())[:5]\n        for eng, chn in sample_mappings:\n            print(f\"     {eng} -> {chn}\")\n        if len(new_mapping) > 5:\n            print(f\"     ... 还有 {len(new_mapping) - 5} 个映射\")\n    \n    # 5. 保存更新的配置\n    print(f\"\\n5. 保存更新的配置...\")\n    \n    try:\n        # 备份原文件\n        backup_file = project_root / \"state\" / \"data\" / f\"field_mappings_backup_{int(__import__('time').time())}.json\"\n        with open(backup_file, 'w', encoding='utf-8') as f:\n            json.dump(config_data, f, ensure_ascii=False, indent=2)\n        print(f\"✅ 已备份原配置: {backup_file.name}\")\n        \n        # 保存新配置\n        with open(field_mappings_file, 'w', encoding='utf-8') as f:\n            json.dump(config_data, f, ensure_ascii=False, indent=2)\n        \n        print(f\"✅ 配置已保存: {field_mappings_file}\")\n        \n    except Exception as e:\n        print(f\"❌ 保存配置失败: {e}\")\n        return False\n    \n    print(f\"\\n🎉 字段映射生成完成！\")\n    print(f\"\\n生成的映射：\")\n    for table_name in tables_to_update:\n        mapping_count = len(table_mappings[table_name][\"field_mappings\"])\n        print(f\"  - {table_name}: {mapping_count} 个字段映射\")\n    \n    print(f\"\\n请重新启动系统测试表头显示效果。\")\n    return True\n\nif __name__ == \"__main__\":\n    success = generate_field_mapping_for_new_tables()\n    \n    if success:\n        print(f\"\\n✅ 任务完成！现在2016年的表应该能正常显示中文表头了。\")\n    else:\n        print(f\"\\n❌ 任务失败！请检查错误信息。\")\n 