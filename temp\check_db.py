import sqlite3
import pandas as pd

# 连接数据库
conn = sqlite3.connect('data/db/salary_system.db')

# 查看所有表
cursor = conn.cursor()
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()
print('数据库中的表:')
for table in tables:
    print(f'  - {table[0]}')

# 查看basic_info_2025_06表的数据
print('\n检查basic_info_2025_06表:')
try:
    df = pd.read_sql_query('SELECT COUNT(*) as count FROM basic_info_2025_06', conn)
    print(f'basic_info_2025_06表记录数: {df.iloc[0]["count"]}')
    
    # 查看表结构
    cursor.execute('PRAGMA table_info(basic_info_2025_06)')
    columns = cursor.fetchall()
    print('表结构:')
    for col in columns:
        print(f'  - {col[1]} ({col[2]})')
        
    # 查看数据样本
    sample_df = pd.read_sql_query('SELECT * FROM basic_info_2025_06 LIMIT 3', conn)
    print('\n数据样本:')
    print(sample_df.to_string())
        
except Exception as e:
    print(f'查询失败: {e}')

conn.close() 