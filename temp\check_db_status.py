#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""检查数据库状态，确认数据库是否被意外删除"""

import sys
import os
import sqlite3

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager

def check_database_status():
    """检查数据库状态"""
    
    print("🔍 检查数据库状态...")
    
    # 检查数据库文件是否存在
    db_file = "data/db/salary_system.db"
    print(f"📁 数据库文件路径: {db_file}")
    print(f"📝 文件是否存在: {os.path.exists(db_file)}")
    
    if os.path.exists(db_file):
        file_size = os.path.getsize(db_file)
        print(f"📏 文件大小: {file_size} 字节")
        
        # 尝试直接连接数据库查看表
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 查询所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = cursor.fetchall()
            
            print(f"📊 数据库中的表数量: {len(tables)}")
            if tables:
                print("📋 表列表:")
                for table in tables:
                    print(f"  - {table[0]}")
                    
                    # 检查每个表的记录数
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM '{table[0]}'")
                        count = cursor.fetchone()[0]
                        print(f"    记录数: {count}")
                    except Exception as e:
                        print(f"    记录数查询失败: {e}")
            else:
                print("⚠️  数据库中没有用户表")
                
            conn.close()
            
        except Exception as e:
            print(f"❌ 直接连接数据库失败: {e}")
    else:
        print("❌ 数据库文件不存在")
        
    print("\n" + "="*50)
    
    # 通过系统管理器检查
    try:
        print("🔧 通过系统管理器检查...")
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        
        # 获取所有表
        all_tables = table_manager.get_table_list()
        print(f"📊 系统查询到的表数量: {len(all_tables)}")
        
        # 获取特定类型的表
        salary_tables = table_manager.get_table_list('salary_data')
        print(f"💰 工资数据表数量: {len(salary_tables)}")
        
        if salary_tables:
            print("📋 工资数据表列表:")
            for table_info in salary_tables[:5]:  # 只显示前5个
                table_name = table_info.get('table_name', 'Unknown')
                print(f"  - {table_name}")
        else:
            print("⚠️  未找到工资数据表")
            
    except Exception as e:
        print(f"❌ 系统管理器检查失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

if __name__ == "__main__":
    check_database_status() 