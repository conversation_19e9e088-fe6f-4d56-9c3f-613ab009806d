import sys
from pathlib import Path

# 将项目根目录添加到Python路径中，以便可以导入src中的模块
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.modules.data_storage.database_manager import DatabaseManager

def check_database_tables():
    """
    连接到项目数据库，列出所有表，并专门筛选出工资数据表，
    以诊断数据加载问题。
    """
    db_path = project_root / "data" / "db" / "salary_system.db"
    
    if not db_path.exists():
        print(f"错误: 找不到数据库文件于: {db_path}")
        print("请确认该路径是否正确。")
        return

    print(f"正在连接数据库: {db_path}...")
    
    try:
        # 直接使用默认配置初始化数据库管理器
        db_manager = DatabaseManager()
        
        print("\n--- 1. 查询数据库中所有的表 ---")
        query = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"
        all_tables = db_manager.execute_query(query)

        if not all_tables:
            print("结果: 数据库中未找到任何表。")
            return

        print(f"结果: 找到 {len(all_tables)} 个表:")
        for table in all_tables:
            print(f"  - {table['name']}")
        
        print("\n--- 2. 筛选工资数据表 (以 'salary_data_' 开头) ---")
        salary_tables = [table['name'] for table in all_tables if table['name'].startswith('salary_data_')]

        if not salary_tables:
            print("结果: 在所有表中，未找到任何以 'salary_data_' 开头的工资表。")
        else:
            print(f"结果: 找到 {len(salary_tables)} 个工资数据表:")
            for table_name in salary_tables:
                print(f"  - {table_name}")

        print("\n诊断完成。")

    except Exception as e:
        print(f"\n执行诊断时发生严重错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_tables() 