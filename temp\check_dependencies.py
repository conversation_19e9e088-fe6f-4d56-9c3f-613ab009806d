#!/usr/bin/env python3
"""
检查项目所需依赖包的安装状态
"""

def check_package(package_name, import_name=None):
    """检查单个包的安装状态"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✓ {package_name}: 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name}: 缺失")
        return False

def main():
    """主检查函数"""
    print("📦 检查项目依赖包状态...\n")
    
    # 需要检查的包列表
    dependencies = [
        ("PyQt5", "PyQt5"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("python-docx", "docx"),
        ("docxtpl", "docxtpl"),
        ("xlrd", "xlrd"),
        ("xlsxwriter", "xlsxwriter"),
        ("Jinja2", "jinja2"),
        ("loguru", "loguru"),
        ("click", "click"),
        ("tqdm", "tqdm"),
        ("pytest", "pytest"),
        ("black", "black"),
        ("flake8", "flake8")
    ]
    
    installed = 0
    missing = 0
    missing_packages = []
    
    for package_name, import_name in dependencies:
        if check_package(package_name, import_name):
            installed += 1
        else:
            missing += 1
            missing_packages.append(package_name)
    
    print(f"\n📊 统计结果:")
    print(f"✓ 已安装: {installed} 个")
    print(f"✗ 缺失: {missing} 个")
    
    if missing_packages:
        print(f"\n❌ 缺失的包: {', '.join(missing_packages)}")
        return False
    else:
        print("\n🎉 所有依赖包都已安装!")
        return True

if __name__ == "__main__":
    main() 