#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中2016年表的真实字段名

在修复映射之前，必须先了解真实的数据库结构
"""

import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_real_field_names():
    """检查数据库中的真实字段名"""
    print("=" * 60)
    print("检查数据库中2016年表的真实字段名")
    print("=" * 60)
    
    # 连接数据库
    db_path = project_root / "data" / "db" / "salary_system.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = [row[0] for row in cursor.fetchall()]
        
        # 找到2016年的表
        tables_2016 = [t for t in all_tables if "2016" in t]
        
        print(f"数据库中的2016年表: {len(tables_2016)} 个")
        for table in tables_2016:
            print(f"  - {table}")
        
        if not tables_2016:
            print("没有找到2016年的表")
            return False
        
        # 检查每个表的字段结构
        print(f"\n详细字段信息:")
        for table_name in tables_2016:
            print(f"\n📋 表: {table_name}")
            
            try:
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                if not columns:
                    print("  ⚠️  表结构为空")
                    continue
                
                print(f"  字段数量: {len(columns)}")
                print("  字段列表:")
                for i, col in enumerate(columns, 1):
                    col_name = col[1]  # 字段名
                    col_type = col[2]  # 字段类型
                    print(f"    {i:2d}. {col_name} ({col_type})")
                
                # 获取几条样本数据看看内容
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  数据行数: {count}")
                
                if count > 0:
                    # 获取前3行数据的列名
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample_data = cursor.fetchall()
                    if sample_data:
                        print("  样本数据字段名:")
                        field_names = [description[0] for description in cursor.description]
                        for i, field in enumerate(field_names[:10]):  # 只显示前10个
                            print(f"    {i+1:2d}. {field}")
                        if len(field_names) > 10:
                            print(f"    ... 还有 {len(field_names) - 10} 个字段")
                
            except Exception as e:
                print(f"  ❌ 查询表 {table_name} 失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    print(f"\n🎯 现在我们知道了真实的字段名，可以创建正确的映射了！")
    return True

if __name__ == "__main__":
    check_real_field_names() 