#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查工资数据表记录数量脚本

用于分析各个工资数据表的记录数量，帮助理解分页组件显示逻辑
"""

import sqlite3
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_salary_table_records():
    """检查所有工资数据表的记录数量"""
    
    db_path = "data/db/salary_system.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有以salary_data开头的表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE 'salary_data_%'
            ORDER BY name DESC
        """)
        
        salary_tables = cursor.fetchall()
        
        print("工资数据表记录数量统计:")
        print("=" * 80)
        
        # 按年月分组统计
        table_data = {}
        
        for table_name, in salary_tables:
            try:
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                count = cursor.fetchone()[0]
                
                # 解析表名
                parts = table_name.split('_')
                if len(parts) >= 5:
                    year = parts[2]
                    month = parts[3]
                    category = '_'.join(parts[4:])
                    
                    key = f"{year}_{month}"
                    if key not in table_data:
                        table_data[key] = {}
                    
                    table_data[key][category] = count
                    
            except Exception as e:
                print(f"查询表 {table_name} 失败: {e}")
        
        # 输出结果
        for period in sorted(table_data.keys(), reverse=True):
            year, month = period.split('_')
            print(f"\n{year}年{month}月数据:")
            print("-" * 40)
            
            categories = table_data[period]
            for category, count in categories.items():
                # 转换为中文显示
                category_map = {
                    'active_employees': '全部在职人员',
                    'retired_employees': '退休人员', 
                    'pension_employees': '离休人员',
                    'a_grade_employees': 'A岗职工'
                }
                
                display_name = category_map.get(category, category)
                print(f"  {display_name}: {count:,}条记录")
        
        print("\n" + "=" * 80)
        print("分页显示阈值分析:")
        print("- 分页阈值: 100条记录")
        print("- 超过100条的表将显示分页组件")
        print("- 100条以下的表将隐藏分页组件")
        
        # 分析哪些表会显示分页
        print("\n需要分页的表:")
        for period in sorted(table_data.keys(), reverse=True):
            year, month = period.split('_')
            categories = table_data[period]
            
            for category, count in categories.items():
                if count > 100:
                    category_map = {
                        'active_employees': '全部在职人员',
                        'retired_employees': '退休人员', 
                        'pension_employees': '离休人员',
                        'a_grade_employees': 'A岗职工'
                    }
                    display_name = category_map.get(category, category)
                    print(f"  {year}年{month}月 {display_name}: {count:,}条 ✓")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")

if __name__ == "__main__":
    check_salary_table_records() 