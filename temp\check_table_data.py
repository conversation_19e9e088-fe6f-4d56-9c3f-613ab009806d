#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""检查数据库中有数据的表"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager

def check_tables():
    """检查数据库中的表和数据"""
    
    print("🔍 检查数据库中的表和数据...")
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        
        # 获取所有表
        tables = table_manager.get_table_list()
        print(f"📊 总共找到 {len(tables)} 个表")
        
        # 检查有数据的表
        tables_with_data = []
        
        for table_info in tables:
            # 提取表名
            if isinstance(table_info, dict):
                table_name = table_info.get('table_name', str(table_info))
            else:
                table_name = str(table_info)
                
            try:
                count = table_manager.get_table_record_count(table_name)
                if count > 0:
                    tables_with_data.append((table_name, count))
                    print(f"  ✅ {table_name}: {count} 条记录")
                else:
                    print(f"  ⚪ {table_name}: 0 条记录")
            except Exception as e:
                print(f"  ❌ {table_name}: 检查失败 - {e}")
        
        print(f"\n📈 有数据的表 ({len(tables_with_data)} 个):")
        for table, count in sorted(tables_with_data, key=lambda x: x[1], reverse=True):
            print(f"  {table}: {count} 条")
            
        # 推荐测试表
        if tables_with_data:
            recommended = max(tables_with_data, key=lambda x: x[1])
            print(f"\n🎯 推荐用于测试的表: {recommended[0]} (有 {recommended[1]} 条记录)")
            return recommended[0]
        else:
            print("\n⚠️  没有找到有数据的表")
            return None
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

if __name__ == "__main__":
    check_tables() 