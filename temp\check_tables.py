import sqlite3
import os

db_path = 'data/db/salary_system.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%salary%' OR name LIKE '%misc%');")
    tables = cursor.fetchall()
    print(f"找到 {len(tables)} 个相关表:")
    for table in tables:
        table_name = table[0]
        print(f"表名: {table_name}")
        # 检查每个表的记录数
        cursor.execute(f"SELECT COUNT(*) FROM '{table_name}';")
        count = cursor.fetchone()[0]
        print(f"  记录数: {count}")
    conn.close()
else:
    print('数据库文件不存在') 