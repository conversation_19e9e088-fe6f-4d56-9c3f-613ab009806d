#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看用户字段偏好设置
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager

def main():
    config_sync = ConfigSyncManager()
    
    # 查看特定表的用户偏好
    table_name = "salary_data_2026_04_a_grade_employees"
    user_fields = config_sync.get_user_header_preference(table_name)
    
    print(f"表 {table_name} 的用户字段偏好:")
    if user_fields:
        print(f"  选定字段: {user_fields}")
        print(f"  字段数量: {len(user_fields)}")
    else:
        print("  未设置偏好（将显示所有字段）")
    
    # 查看所有表的用户偏好
    all_prefs = config_sync.get_all_user_header_preferences()
    print(f"\n所有表的用户偏好:")
    for table, fields in all_prefs.items():
        print(f"  {table}: {len(fields)} 个字段")
    
    # 查看字段映射
    mapping = config_sync.load_mapping(table_name)
    print(f"\n表 {table_name} 的字段映射:")
    if mapping:
        for eng_field, cn_field in mapping.items():
            print(f"  {eng_field} -> {cn_field}")
    else:
        print("  未设置映射")

if __name__ == "__main__":
    main() 