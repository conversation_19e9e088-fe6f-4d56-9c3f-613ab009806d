import sqlite3
import os

db_path = 'data/db/salary_system.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    tables_to_drop = [
        'salary_data_2025_05',
        'salary_data_retired_employees_2025_05', 
        'salary_data_pension_employees_2025_05',
        'salary_data_active_employees_2025_05',
        'misc_data_2025_05'
    ]
    for table in tables_to_drop:
        try:
            cursor.execute(f'DROP TABLE IF EXISTS "{table}";')
            print(f'删除表: {table}')
        except Exception as e:
            print(f'删除表 {table} 失败: {e}')
    conn.commit()
    conn.close()
    print('数据清理完成')
else:
    print('数据库文件不存在') 