#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面比较脚本
显示新旧界面的主要差异
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_interface_differences():
    """检查界面差异"""
    print("界面更新检查")
    print("=" * 60)
    
    try:
        # 导入现代化样式
        from src.gui.modern_style import MaterialDesignPalette, ModernStyleSheet
        
        print("✓ 现代化样式模块可用")
        print(f"  主色调: {MaterialDesignPalette.PRIMARY['main']} (现代蓝色)")
        print(f"  背景色: {MaterialDesignPalette.BACKGROUND['default']} (浅灰背景)")
        print()
        
        # 检查主窗口样式
        main_style = ModernStyleSheet.get_complete_style()
        print(f"✓ 完整样式表长度: {len(main_style)} 字符")
        
        # 关键样式特征
        modern_features = [
            ("渐变头部", "qlineargradient" in main_style),
            ("Material Design颜色", "#1976D2" in main_style),
            ("圆角按钮", "border-radius" in main_style),
            ("阴影效果", "box-shadow" in main_style),
            ("现代字体", "Segoe UI" in main_style)
        ]
        
        print("\n现代化特征检查:")
        for feature, available in modern_features:
            status = "✓" if available else "❌"
            print(f"  {status} {feature}")
        
        print()
        
        # 导入现代化主窗口
        from src.gui.modern_main_window import ModernMainWindow
        print("✓ 现代化主窗口类可用")
        
        # 检查窗口组件
        modern_components = [
            "ModernHeaderWidget",
            "ModernSidebarWidget", 
            "ModernMainContentWidget"
        ]
        
        print("\n现代化组件检查:")
        for component in modern_components:
            try:
                from src.gui.modern_main_window import ModernHeaderWidget, ModernSidebarWidget, ModernMainContentWidget
                print(f"  ✓ {component}")
            except ImportError:
                print(f"  ❌ {component}")
        
        print()
        print("如果所有检查都通过，那么您的界面应该已经更新为现代化样式！")
        print("\n主要视觉变化包括:")
        print("• 蓝色渐变头部 (替代原来的简单灰色头部)")
        print("• 现代化侧边栏 (更清晰的导航结构)")
        print("• Material Design配色方案")
        print("• 圆角按钮和卡片设计")
        print("• 改进的字体和间距")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查错误: {e}")
        return False

def show_startup_instructions():
    """显示启动说明"""
    print("\n启动说明:")
    print("=" * 60)
    print("请按以下步骤启动程序:")
    print("1. 关闭当前运行的程序实例 (如果有)")
    print("2. 运行命令: python main.py")
    print("3. 检查界面是否显示现代化样式")
    print()
    print("预期的界面变化:")
    print("• 窗口顶部应显示蓝色渐变头部")
    print("• 左侧边栏应具有现代化导航菜单")
    print("• 主内容区域应显示欢迎卡片")
    print("• 整体配色应为Material Design风格")
    print()
    print("如果界面仍然是旧样式，请:")
    print("1. 确认没有其他Python进程在运行")
    print("2. 重新启动命令行终端")
    print("3. 再次运行 python main.py")

if __name__ == "__main__":
    success = check_interface_differences()
    
    if success:
        show_startup_instructions()
    else:
        print("界面检查失败，请检查代码完整性") 