#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件清理分析脚本
分析配置文件内容，提供清理建议
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime, timedelta

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_config_file():
    """分析配置文件内容"""
    config_path = "state/data/field_mappings.json"
    
    if not Path(config_path).exists():
        print("❌ 配置文件不存在")
        return
    
    # 获取文件信息
    file_size = Path(config_path).stat().st_size
    print(f"📊 配置文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    # 读取配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 分析内容
    table_mappings = config.get("table_mappings", {})
    user_preferences = config.get("user_preferences", {})
    
    print(f"\n📋 配置内容分析:")
    print(f"  - 表映射数量: {len(table_mappings)}")
    print(f"  - 用户偏好表数量: {len(user_preferences.get('header_preferences', {}))}")
    
    # 分析表类型
    test_tables = []
    production_tables = []
    old_tables = []
    
    current_time = datetime.now()
    
    for table_name, table_config in table_mappings.items():
        metadata = table_config.get("metadata", {})
        created_at = metadata.get("created_at", "")
        
        # 判断表类型
        if any(keyword in table_name.lower() for keyword in ['test', 'temp', 'demo', 'sample']):
            test_tables.append(table_name)
        elif created_at:
            try:
                create_time = datetime.fromisoformat(created_at.replace('Z', '+00:00').replace('+00:00', ''))
                if current_time - create_time > timedelta(days=90):  # 3个月前
                    old_tables.append((table_name, created_at))
                else:
                    production_tables.append(table_name)
            except:
                production_tables.append(table_name)
        else:
            production_tables.append(table_name)
    
    print(f"\n🔍 表分类分析:")
    print(f"  - 生产表: {len(production_tables)}")
    print(f"  - 测试表: {len(test_tables)}")
    print(f"  - 旧表 (>90天): {len(old_tables)}")
    
    if test_tables:
        print(f"\n🧪 测试表列表:")
        for table in test_tables:
            print(f"    - {table}")
    
    if old_tables:
        print(f"\n⏰ 旧表列表 (创建时间超过90天):")
        for table, created_at in old_tables:
            print(f"    - {table} (创建于: {created_at[:10]})")
    
    # 分析编辑历史
    total_history_entries = 0
    for table_name, table_config in table_mappings.items():
        edit_history = table_config.get("edit_history", [])
        total_history_entries += len(edit_history)
    
    print(f"\n📝 编辑历史统计:")
    print(f"  - 总编辑记录数: {total_history_entries}")
    
    return {
        'file_size': file_size,
        'total_tables': len(table_mappings),
        'test_tables': test_tables,
        'old_tables': [table for table, _ in old_tables],
        'production_tables': production_tables,
        'total_history_entries': total_history_entries
    }

def generate_cleanup_recommendations(analysis_result):
    """生成清理建议"""
    print(f"\n💡 清理建议:")
    
    # 计算可节省的空间
    total_tables = analysis_result['total_tables']
    test_tables_count = len(analysis_result['test_tables'])
    old_tables_count = len(analysis_result['old_tables'])
    
    cleanup_ratio = (test_tables_count + old_tables_count) / total_tables if total_tables > 0 else 0
    estimated_savings = analysis_result['file_size'] * cleanup_ratio
    
    print(f"  1. 可清理测试表: {test_tables_count} 个")
    print(f"  2. 可清理旧表: {old_tables_count} 个")
    print(f"  3. 预计节省空间: {estimated_savings/1024:.1f} KB ({cleanup_ratio*100:.1f}%)")
    
    print(f"\n🛡️ 安全清理策略:")
    print(f"  - 系统会自动重新生成字段映射配置")
    print(f"  - 用户自定义的映射会在重新导入时丢失")
    print(f"  - 建议先备份配置文件")
    
    return {
        'can_cleanup': test_tables_count + old_tables_count > 0,
        'estimated_savings': estimated_savings,
        'cleanup_tables': analysis_result['test_tables'] + analysis_result['old_tables']
    }

def create_cleanup_script():
    """创建安全的清理脚本"""
    cleanup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全清理配置文件脚本
"""

import json
import shutil
from pathlib import Path
from datetime import datetime

def backup_config():
    """备份配置文件"""
    config_path = "state/data/field_mappings.json"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"state/data/backups/field_mappings_before_cleanup_{timestamp}.json"
    
    Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(config_path, backup_path)
    print(f"✅ 配置文件已备份到: {backup_path}")
    
    return backup_path

def clean_config():
    """清理配置文件"""
    config_path = "state/data/field_mappings.json"
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 保留的表（生产环境表）
    table_mappings = config.get("table_mappings", {})
    cleaned_mappings = {}
    
    for table_name, table_config in table_mappings.items():
        # 跳过测试表
        if any(keyword in table_name.lower() for keyword in ['test', 'temp', 'demo', 'sample']):
            print(f"🗑️ 清理测试表: {table_name}")
            continue
        
        # 保留生产表，但清理编辑历史
        table_config_clean = table_config.copy()
        edit_history = table_config_clean.get("edit_history", [])
        if len(edit_history) > 5:  # 只保留最近5条编辑记录
            table_config_clean["edit_history"] = edit_history[-5:]
            print(f"📝 压缩编辑历史: {table_name} ({len(edit_history)} -> 5)")
        
        cleaned_mappings[table_name] = table_config_clean
    
    # 更新配置
    config["table_mappings"] = cleaned_mappings
    config["last_updated"] = datetime.now().isoformat()
    
    # 保存清理后的配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 配置文件清理完成")

if __name__ == "__main__":
    print("🧹 开始清理配置文件...")
    backup_path = backup_config()
    clean_config()
    print(f"\n✨ 清理完成！如需恢复，请使用备份文件: {backup_path}")
'''
    
    with open("temp/safe_config_cleanup.py", 'w', encoding='utf-8') as f:
        f.write(cleanup_script)
    
    print(f"✅ 安全清理脚本已创建: temp/safe_config_cleanup.py")

def main():
    print("🔎 开始分析配置文件...")
    analysis_result = analyze_config_file()
    
    if analysis_result:
        cleanup_recommendations = generate_cleanup_recommendations(analysis_result)
        
        if cleanup_recommendations['can_cleanup']:
            create_cleanup_script()
            print(f"\n🚀 执行清理:")
            print(f"  python temp/safe_config_cleanup.py")
        else:
            print(f"\n✨ 配置文件已经很干净，无需清理")

if __name__ == "__main__":
    main() 