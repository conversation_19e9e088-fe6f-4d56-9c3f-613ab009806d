#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""调试count查询的返回结果格式"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager

def debug_count_query():
    """调试count查询的返回结果"""
    
    print("🔍 调试count查询返回结果格式...")
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        
        # 直接测试count查询
        print("\n📊 直接执行count查询...")
        
        # 测试system_config表（应该有数据）
        table_name = "system_config"
        query = f'SELECT COUNT(*) FROM "{table_name}"'
        print(f"执行查询: {query}")
        
        result = db_manager.execute_query(query)
        print(f"原始结果: {result}")
        print(f"结果类型: {type(result)}")
        
        if result:
            print(f"结果长度: {len(result)}")
            print(f"第一项: {result[0]}")
            print(f"第一项类型: {type(result[0])}")
            
            if hasattr(result[0], 'keys'):
                print(f"第一项的键: {list(result[0].keys())}")
                
            # 尝试不同的访问方式
            try:
                print(f"result[0][0]: {result[0][0]}")
            except Exception as e:
                print(f"result[0][0] 失败: {e}")
                
            try:
                print(f"result[0]['COUNT(*)']: {result[0]['COUNT(*)']}")
            except Exception as e:
                print(f"result[0]['COUNT(*)'] 失败: {e}")
                
            try:
                count_key = list(result[0].keys())[0]
                print(f"使用第一个键 '{count_key}': {result[0][count_key]}")
            except Exception as e:
                print(f"使用第一个键失败: {e}")
        
        # 测试其他表
        print("\n📋 测试其他表...")
        table_manager = DynamicTableManager(db_manager)
        tables = table_manager.get_table_list()
        
        for i, table_info in enumerate(tables[:3]):  # 只测试前3个表
            table_name = table_info.get('table_name', str(table_info))
            print(f"\n测试表 {i+1}: {table_name}")
            
            try:
                query = f'SELECT COUNT(*) FROM "{table_name}"'
                result = db_manager.execute_query(query)
                
                if result and len(result) > 0:
                    count_result = result[0]
                    print(f"  查询结果: {count_result}")
                    
                    # 尝试正确的访问方式
                    if hasattr(count_result, 'keys'):
                        keys = list(count_result.keys())
                        if keys:
                            count = count_result[keys[0]]
                            print(f"  ✅ 记录数: {count}")
                        else:
                            print(f"  ❌ 无键值")
                    else:
                        # 如果是tuple或list
                        try:
                            count = count_result[0]
                            print(f"  ✅ 记录数 (索引): {count}")
                        except:
                            print(f"  ❌ 无法访问第0个元素")
                else:
                    print(f"  ⚪ 查询无结果")
                    
            except Exception as e:
                print(f"  ❌ 查询失败: {e}")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    debug_count_query() 