"""
导航信号连接问题调试脚本
=========================

用于检测和修复导航点击没有响应的问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot

# 导入项目模块
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager  
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NavigationSignalTester(QObject):
    """导航信号测试器"""
    
    def __init__(self):
        super().__init__()
        self.signal_received_count = 0
        
    @pyqtSlot(str, dict)
    def on_navigation_test(self, path: str, context: dict):
        """测试信号接收"""
        self.signal_received_count += 1
        logger.info(f"🟢 导航信号接收测试成功 #{self.signal_received_count}: {path}")
        logger.info(f"   上下文: {context}")

def main():
    """主测试函数"""
    try:
        logger.info("🔍 开始导航信号连接调试...")
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 初始化核心管理器
        logger.info("📋 初始化核心管理器...")
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 创建主窗口
        logger.info("🏠 创建主窗口...")
        main_window = PrototypeMainWindow(config_manager, db_manager, dynamic_table_manager)
        
        # 创建信号测试器
        signal_tester = NavigationSignalTester()
        
        # 检查导航面板是否存在
        if hasattr(main_window, 'navigation_panel') and main_window.navigation_panel:
            logger.info("✅ 导航面板已创建")
            
            # 检查信号是否存在
            if hasattr(main_window.navigation_panel, 'navigation_changed'):
                logger.info("✅ navigation_changed 信号存在")
                
                # 尝试连接测试信号
                main_window.navigation_panel.navigation_changed.connect(signal_tester.on_navigation_test)
                logger.info("✅ 测试信号连接成功")
                
                # 检查主窗口的信号连接
                logger.info("🔍 检查主窗口信号连接...")
                
                # 检查是否有responsive_layout_manager属性
                if hasattr(main_window, 'responsive_layout_manager'):
                    logger.info("✅ responsive_layout_manager 属性存在")
                else:
                    logger.error("❌ responsive_layout_manager 属性不存在！这是问题所在！")
                
                # 检查是否有_on_navigation_changed方法
                if hasattr(main_window, '_on_navigation_changed'):
                    logger.info("✅ _on_navigation_changed 方法存在")
                else:
                    logger.error("❌ _on_navigation_changed 方法不存在！")
                
                # 手动连接导航信号到主窗口
                try:
                    main_window.navigation_panel.navigation_changed.connect(main_window._on_navigation_changed)
                    logger.info("✅ 手动连接导航信号到主窗口成功")
                except Exception as e:
                    logger.error(f"❌ 手动连接失败: {e}")
                
            else:
                logger.error("❌ navigation_changed 信号不存在")
        else:
            logger.error("❌ 导航面板未创建")
        
        # 检查导航面板中的实际路径数据
        if hasattr(main_window, 'navigation_panel') and main_window.navigation_panel:
            logger.info("🔍 检查导航数据...")
            
            # 模拟选择一个导航项目
            if hasattr(main_window.navigation_panel, 'tree_widget'):
                tree = main_window.navigation_panel.tree_widget
                logger.info(f"📊 树形控件项目数量: {tree.topLevelItemCount()}")
                
                # 遍历顶级项目
                for i in range(tree.topLevelItemCount()):
                    item = tree.topLevelItem(i)
                    if item:
                        path = item.data(0, 2)  # Qt.UserRole = 32 = 2
                        logger.info(f"   顶级项目 {i}: {item.text(0)} -> 路径: {path}")
                        
                        # 检查子项目
                        for j in range(item.childCount()):
                            child = item.child(j)
                            child_path = child.data(0, 2)
                            logger.info(f"     子项目 {j}: {child.text(0)} -> 路径: {child_path}")
        
        logger.info("🔍 调试检查完成")
        
        # 不启动GUI，只检查
        
    except Exception as e:
        logger.error(f"❌ 调试过程出错: {e}", exc_info=True)

if __name__ == "__main__":
    main() 