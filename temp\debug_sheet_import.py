#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_import.multi_sheet_importer import MultiSheetImporter

def test_sheet_data_type():
    """测试Sheet数据类型识别"""
    importer = MultiSheetImporter()
    
    test_sheets = [
        "离休人员工资表",
        "退休人员工资表", 
        "全部在职人员工资表",
        "A岗职工"
    ]
    
    print("测试Sheet数据类型识别:")
    for sheet_name in test_sheets:
        data_type = importer._get_sheet_data_type(sheet_name)
        print(f"  {sheet_name} -> {data_type}")
        
    print("\n测试表名生成:")
    year = 2025
    month = 6
    
    for sheet_name in test_sheets:
        data_type = importer._get_sheet_data_type(sheet_name)
        
        # 模拟表名生成逻辑
        if data_type.startswith("salary_data_"):
            employee_type = data_type.replace("salary_data_", "")
            table_name = f"salary_data_{year}_{month:02d}_{employee_type}"
        elif data_type == "salary_data":
            table_name = f"salary_data_{year}_{month:02d}"
        else:
            table_name = f"{data_type}_{year}_{month:02d}"
            
        print(f"  {sheet_name} -> {table_name}")

if __name__ == "__main__":
    test_sheet_data_type() 