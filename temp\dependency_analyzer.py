#!/usr/bin/env python3
"""
模块依赖关系分析工具

分析项目中模块间的依赖关系，识别循环依赖和高耦合模块
"""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict, deque


class DependencyAnalyzer:
    """依赖关系分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.src_dir = self.project_root / "src"
        
        # 依赖关系存储
        self.dependencies: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_dependencies: Dict[str, Set[str]] = defaultdict(set)
        
        # 模块信息
        self.modules: Set[str] = set()
        self.module_files: Dict[str, Path] = {}
        
    def analyze_project(self) -> Dict[str, any]:
        """
        分析整个项目的依赖关系
        
        Returns:
            Dict: 分析结果
        """
        print("🔍 开始分析项目依赖关系...")
        
        # 1. 扫描所有Python文件
        self._scan_python_files()
        
        # 2. 分析导入关系
        self._analyze_imports()
        
        # 3. 检测循环依赖
        cycles = self._detect_cycles()
        
        # 4. 计算耦合度指标
        coupling_metrics = self._calculate_coupling_metrics()
        
        # 5. 生成分析报告
        report = {
            "modules_count": len(self.modules),
            "total_dependencies": sum(len(deps) for deps in self.dependencies.values()),
            "cycles": cycles,
            "coupling_metrics": coupling_metrics,
            "dependency_graph": dict(self.dependencies),
            "module_classification": self._classify_modules()
        }
        
        print(f"✅ 分析完成，发现 {len(self.modules)} 个模块，{report['total_dependencies']} 个依赖关系")
        return report
    
    def _scan_python_files(self):
        """扫描所有Python文件，建立模块映射"""
        for py_file in self.src_dir.rglob("*.py"):
            if py_file.name == "__init__.py":
                continue
                
            # 计算模块路径
            relative_path = py_file.relative_to(self.src_dir)
            module_path = str(relative_path.with_suffix("")).replace(os.sep, ".")
            
            self.modules.add(module_path)
            self.module_files[module_path] = py_file
    
    def _analyze_imports(self):
        """分析所有模块的导入关系"""
        for module_name, file_path in self.module_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = self._extract_imports(tree, module_name)
                
                for imported_module in imports:
                    # 只关注项目内部的依赖
                    if self._is_internal_module(imported_module):
                        self.dependencies[module_name].add(imported_module)
                        self.reverse_dependencies[imported_module].add(module_name)
                        
            except Exception as e:
                print(f"⚠️ 解析文件失败: {file_path} - {e}")
    
    def _extract_imports(self, tree: ast.AST, current_module: str) -> Set[str]:
        """从AST中提取导入语句"""
        imports = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name)
                    
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    # 处理相对导入
                    if node.level > 0:
                        base_module = self._resolve_relative_import(current_module, node.level)
                        if node.module:
                            module_name = f"{base_module}.{node.module}"
                        else:
                            module_name = base_module
                    else:
                        module_name = node.module
                    
                    imports.add(module_name)
                    
                    # 也添加具体导入的子模块
                    for alias in node.names:
                        if alias.name != "*":
                            sub_module = f"{module_name}.{alias.name}"
                            imports.add(sub_module)
        
        return imports
    
    def _resolve_relative_import(self, current_module: str, level: int) -> str:
        """解析相对导入的目标模块"""
        parts = current_module.split(".")
        if level > len(parts):
            return ""
        return ".".join(parts[:-level] if level > 0 else parts)
    
    def _is_internal_module(self, module_name: str) -> bool:
        """判断是否为项目内部模块"""
        # 检查是否以src开头或者在已知模块中
        return (module_name.startswith("src.") or 
                module_name in self.modules or
                any(module_name.startswith(m + ".") for m in self.modules))
    
    def _detect_cycles(self) -> List[List[str]]:
        """检测循环依赖"""
        cycles = []
        visited = set()
        rec_stack = set()
        
        def dfs(node: str, path: List[str]) -> None:
            if node in rec_stack:
                # 找到循环
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return
            
            if node in visited:
                return
            
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in self.dependencies.get(node, []):
                dfs(neighbor, path[:])
            
            rec_stack.remove(node)
            path.pop()
        
        for module in self.modules:
            if module not in visited:
                dfs(module, [])
        
        return cycles
    
    def _calculate_coupling_metrics(self) -> Dict[str, Dict[str, float]]:
        """计算耦合度指标"""
        metrics = {}
        
        for module in self.modules:
            fan_out = len(self.dependencies[module])  # 出度：该模块依赖的其他模块数
            fan_in = len(self.reverse_dependencies[module])  # 入度：依赖该模块的其他模块数
            
            # 不稳定性指标 I = fan_out / (fan_in + fan_out)
            instability = fan_out / (fan_in + fan_out) if (fan_in + fan_out) > 0 else 0
            
            # 耦合强度
            coupling_strength = fan_in + fan_out
            
            metrics[module] = {
                "fan_in": fan_in,
                "fan_out": fan_out,
                "instability": instability,
                "coupling_strength": coupling_strength
            }
        
        return metrics
    
    def _classify_modules(self) -> Dict[str, List[str]]:
        """对模块进行分类"""
        classification = {
            "core_modules": [],        # 核心模块（高fan_in，低fan_out）
            "leaf_modules": [],        # 叶子模块（低fan_in，高fan_out）
            "hub_modules": [],         # 中心模块（高fan_in，高fan_out）
            "isolated_modules": [],    # 孤立模块（低fan_in，低fan_out）
            "highly_coupled": []       # 高耦合模块
        }
        
        for module in self.modules:
            fan_in = len(self.reverse_dependencies[module])
            fan_out = len(self.dependencies[module])
            total_coupling = fan_in + fan_out
            
            # 分类阈值
            high_threshold = 5
            
            if total_coupling > high_threshold:
                classification["highly_coupled"].append(module)
            
            if fan_in > high_threshold and fan_out < 3:
                classification["core_modules"].append(module)
            elif fan_in < 3 and fan_out > high_threshold:
                classification["leaf_modules"].append(module)
            elif fan_in > high_threshold and fan_out > high_threshold:
                classification["hub_modules"].append(module)
            elif fan_in <= 2 and fan_out <= 2:
                classification["isolated_modules"].append(module)
        
        return classification
    
    def generate_mermaid_graph(self) -> str:
        """生成Mermaid依赖关系图"""
        lines = ["graph TD"]
        
        # 添加节点和边
        for module, deps in self.dependencies.items():
            # 简化模块名显示
            module_short = module.split(".")[-1]
            
            for dep in deps:
                dep_short = dep.split(".")[-1]
                lines.append(f"    {module_short} --> {dep_short}")
        
        return "\n".join(lines)
    
    def print_summary_report(self, report: Dict):
        """打印摘要报告"""
        print("\n" + "="*60)
        print("📊 模块依赖关系分析报告")
        print("="*60)
        
        print(f"\n📈 整体统计:")
        print(f"  • 模块总数: {report['modules_count']}")
        print(f"  • 依赖关系总数: {report['total_dependencies']}")
        print(f"  • 平均每模块依赖数: {report['total_dependencies']/report['modules_count']:.2f}")
        
        # 循环依赖
        if report['cycles']:
            print(f"\n🔄 循环依赖 ({len(report['cycles'])} 个):")
            for i, cycle in enumerate(report['cycles'], 1):
                cycle_str = " -> ".join(c.split(".")[-1] for c in cycle)
                print(f"  {i}. {cycle_str}")
        else:
            print(f"\n✅ 未发现循环依赖")
        
        # 模块分类
        classification = report['module_classification']
        print(f"\n📋 模块分类:")
        
        for category, modules in classification.items():
            if modules:
                category_names = {
                    "core_modules": "核心模块",
                    "leaf_modules": "叶子模块", 
                    "hub_modules": "中心模块",
                    "isolated_modules": "孤立模块",
                    "highly_coupled": "高耦合模块"
                }
                print(f"  • {category_names.get(category, category)} ({len(modules)} 个):")
                for module in modules[:5]:  # 最多显示5个
                    print(f"    - {module.split('.')[-1]}")
                if len(modules) > 5:
                    print(f"    ... 还有 {len(modules)-5} 个")
        
        # 耦合度最高的模块
        coupling_metrics = report['coupling_metrics']
        top_coupled = sorted(
            coupling_metrics.items(), 
            key=lambda x: x[1]['coupling_strength'], 
            reverse=True
        )[:5]
        
        print(f"\n🔗 耦合度最高的模块:")
        for module, metrics in top_coupled:
            print(f"  • {module.split('.')[-1]}: "
                  f"入度={metrics['fan_in']}, "
                  f"出度={metrics['fan_out']}, "
                  f"不稳定性={metrics['instability']:.2f}")


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent.absolute()
    
    analyzer = DependencyAnalyzer(str(project_root))
    report = analyzer.analyze_project()
    analyzer.print_summary_report(report)
    
    # 生成Mermaid图
    mermaid_graph = analyzer.generate_mermaid_graph()
    
    # 保存结果到文件
    output_dir = project_root / "temp"
    output_dir.mkdir(exist_ok=True)
    
    with open(output_dir / "dependency_report.txt", "w", encoding="utf-8") as f:
        f.write("="*60 + "\n")
        f.write("模块依赖关系详细报告\n")
        f.write("="*60 + "\n\n")
        
        # 详细的依赖关系
        f.write("详细依赖关系:\n")
        for module, deps in report['dependency_graph'].items():
            f.write(f"\n{module}:\n")
            for dep in sorted(deps):
                f.write(f"  -> {dep}\n")
    
    with open(output_dir / "dependency_graph.mermaid", "w", encoding="utf-8") as f:
        f.write(mermaid_graph)
    
    print(f"\n📄 详细报告已保存到: {output_dir / 'dependency_report.txt'}")
    print(f"📊 Mermaid图已保存到: {output_dir / 'dependency_graph.mermaid'}")


if __name__ == "__main__":
    main() 