graph TD
    config_manager --> get_module_logger
    config_manager --> log_file_operation
    config_manager --> log_error_with_context
    config_manager --> log_function_call
    config_manager --> log_config
    dialogs --> log_config
    dialogs --> setup_logger
    main_window --> SettingsDialog
    main_window --> ConfigManager
    main_window --> windows
    main_window --> ChangeDetector
    main_window --> system_config
    main_window --> ReportManager
    main_window --> report_generation
    main_window --> DataImportDialog
    main_window --> dialogs
    main_window --> ChangeDetectionWindow
    main_window --> log_config
    main_window --> widgets
    main_window --> AboutDialog
    main_window --> DataValidator
    main_window --> data_import
    main_window --> UserPreferences
    main_window --> config_manager
    main_window --> ExcelImporter
    main_window --> change_detection
    main_window --> ReportGenerationWindow
    main_window --> ProgressDialog
    main_window --> DataTableWidget
    main_window --> setup_logger
    main_window --> BaselineManager
    widgets --> log_config
    widgets --> setup_logger
    windows --> ReportManager
    windows --> ConfigManager
    windows --> change_detection
    windows --> report_generation
    windows --> ChangeDetector
    windows --> config_manager
    windows --> log_config
    windows --> setup_logger
    windows --> BaselineManager
    amount_calculator --> config_manager
    amount_calculator --> get_module_logger
    amount_calculator --> log_config
    amount_calculator --> ConfigManager
    baseline_manager --> config_manager
    baseline_manager --> get_module_logger
    baseline_manager --> log_config
    baseline_manager --> ConfigManager
    change_detector --> get_module_logger
    change_detector --> ConfigManager
    change_detector --> config_manager
    change_detector --> log_config
    change_detector --> baseline_manager
    change_detector --> BaselineManager
    reason_analyzer --> config_manager
    reason_analyzer --> get_module_logger
    reason_analyzer --> log_config
    reason_analyzer --> ConfigManager
    csv_importer --> log_config
    csv_importer --> setup_logger
    data_validator --> log_file_operation
    data_validator --> log_config
    data_validator --> setup_logger
    excel_importer --> log_file_operation
    excel_importer --> log_config
    excel_importer --> setup_logger
    field_mapper --> log_config
    field_mapper --> setup_logger
    database_manager --> setup_logger
    database_manager --> system_config
    database_manager --> log_config
    database_manager --> ConfigManager
    dynamic_table_manager --> get_database_manager
    dynamic_table_manager --> ConfigManager
    dynamic_table_manager --> database_manager
    dynamic_table_manager --> DatabaseManager
    dynamic_table_manager --> system_config
    dynamic_table_manager --> log_config
    dynamic_table_manager --> setup_logger
    demo_test --> get_module_logger
    demo_test --> ReportManager
    demo_test --> ExcelGenerator
    demo_test --> TemplateEngine
    demo_test --> report_generation
    demo_test --> WordGenerator
    demo_test --> PreviewManager
    demo_test --> log_config
    excel_generator --> get_module_logger
    excel_generator --> template_engine
    excel_generator --> log_config
    excel_generator --> TemplateEngine
    preview_manager --> get_module_logger
    preview_manager --> log_config
    report_manager --> get_module_logger
    report_manager --> ExcelGenerator
    report_manager --> PreviewManager
    report_manager --> excel_generator
    report_manager --> TemplateEngine
    report_manager --> preview_manager
    report_manager --> log_config
    report_manager --> template_engine
    report_manager --> word_generator
    report_manager --> WordGenerator
    template_engine --> get_module_logger
    template_engine --> log_config
    word_generator --> get_module_logger
    word_generator --> template_engine
    word_generator --> log_config
    word_generator --> TemplateEngine
    config_manager --> log_config
    config_manager --> setup_logger
    system_maintenance --> config_manager
    system_maintenance --> log_config
    system_maintenance --> setup_logger
    system_maintenance --> ConfigManager
    template_manager --> config_manager
    template_manager --> log_config
    template_manager --> setup_logger
    template_manager --> ConfigManager
    user_preferences --> config_manager
    user_preferences --> log_config
    user_preferences --> setup_logger
    user_preferences --> ConfigManager