============================================================
模块依赖关系详细报告
============================================================

详细依赖关系:

core.config_manager:
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger
  -> src.utils.log_config.log_error_with_context
  -> src.utils.log_config.log_file_operation
  -> src.utils.log_config.log_function_call

gui.dialogs:
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

gui.main_window:
  -> gui.dialogs
  -> gui.dialogs.AboutDialog
  -> gui.dialogs.DataImportDialog
  -> gui.dialogs.ProgressDialog
  -> gui.dialogs.SettingsDialog
  -> gui.widgets
  -> gui.widgets.DataTableWidget
  -> gui.windows
  -> gui.windows.ChangeDetectionWindow
  -> gui.windows.ReportGenerationWindow
  -> src.core.config_manager
  -> src.core.config_manager.ConfigManager
  -> src.modules.change_detection
  -> src.modules.change_detection.BaselineManager
  -> src.modules.change_detection.ChangeDetector
  -> src.modules.data_import
  -> src.modules.data_import.DataValidator
  -> src.modules.data_import.ExcelImporter
  -> src.modules.report_generation
  -> src.modules.report_generation.ReportManager
  -> src.modules.system_config
  -> src.modules.system_config.UserPreferences
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

gui.widgets:
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

gui.windows:
  -> src.core.config_manager
  -> src.core.config_manager.ConfigManager
  -> src.modules.change_detection
  -> src.modules.change_detection.BaselineManager
  -> src.modules.change_detection.ChangeDetector
  -> src.modules.report_generation
  -> src.modules.report_generation.ReportManager
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.change_detection.amount_calculator:
  -> src.core.config_manager
  -> src.core.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.change_detection.baseline_manager:
  -> src.core.config_manager
  -> src.core.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.change_detection.change_detector:
  -> modules.change_detection.baseline_manager
  -> modules.change_detection.baseline_manager.BaselineManager
  -> src.core.config_manager
  -> src.core.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.change_detection.reason_analyzer:
  -> src.core.config_manager
  -> src.core.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.data_import.csv_importer:
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.data_import.data_validator:
  -> src.utils.log_config
  -> src.utils.log_config.log_file_operation
  -> src.utils.log_config.setup_logger

modules.data_import.excel_importer:
  -> src.utils.log_config
  -> src.utils.log_config.log_file_operation
  -> src.utils.log_config.setup_logger

modules.data_import.field_mapper:
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.data_storage.database_manager:
  -> src.modules.system_config
  -> src.modules.system_config.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.data_storage.dynamic_table_manager:
  -> modules.data_storage.database_manager
  -> modules.data_storage.database_manager.DatabaseManager
  -> modules.data_storage.database_manager.get_database_manager
  -> src.modules.system_config
  -> src.modules.system_config.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.report_generation.demo_test:
  -> src.modules.report_generation
  -> src.modules.report_generation.ExcelGenerator
  -> src.modules.report_generation.PreviewManager
  -> src.modules.report_generation.ReportManager
  -> src.modules.report_generation.TemplateEngine
  -> src.modules.report_generation.WordGenerator
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.report_generation.excel_generator:
  -> modules.report_generation.template_engine
  -> modules.report_generation.template_engine.TemplateEngine
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.report_generation.preview_manager:
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.report_generation.report_manager:
  -> modules.report_generation.excel_generator
  -> modules.report_generation.excel_generator.ExcelGenerator
  -> modules.report_generation.preview_manager
  -> modules.report_generation.preview_manager.PreviewManager
  -> modules.report_generation.template_engine
  -> modules.report_generation.template_engine.TemplateEngine
  -> modules.report_generation.word_generator
  -> modules.report_generation.word_generator.WordGenerator
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.report_generation.template_engine:
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.report_generation.word_generator:
  -> modules.report_generation.template_engine
  -> modules.report_generation.template_engine.TemplateEngine
  -> src.utils.log_config
  -> src.utils.log_config.get_module_logger

modules.system_config.config_manager:
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.system_config.system_maintenance:
  -> modules.system_config.config_manager
  -> modules.system_config.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.system_config.template_manager:
  -> modules.system_config.config_manager
  -> modules.system_config.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

modules.system_config.user_preferences:
  -> modules.system_config.config_manager
  -> modules.system_config.config_manager.ConfigManager
  -> src.utils.log_config
  -> src.utils.log_config.setup_logger

utils.log_config:
