#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
表头映射功能诊断脚本

用于检查表头映射与自定义功能是否正常工作
"""

import sys
import os
import json
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(project_root))

def check_files_exist():
    """检查关键文件是否存在"""
    print("=" * 60)
    print("1. 检查关键文件是否存在")
    print("=" * 60)
    
    files_to_check = [
        "src/modules/data_import/auto_field_mapping_generator.py",
        "src/modules/data_import/config_sync_manager.py", 
        "src/gui/prototype/widgets/virtualized_expandable_table.py",
        "src/gui/prototype/prototype_main_window.py",
        "src/gui/dialogs.py"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        full_path = project_root / file_path
        exists = full_path.exists()
        status = "✅" if exists else "❌"
        print(f"{status} {file_path}")
        if not exists:
            all_exist = False
    
    return all_exist

def check_imports():
    """检查模块导入是否正常"""
    print("\n" + "=" * 60)
    print("2. 检查模块导入是否正常")
    print("=" * 60)
    
    modules_to_check = [
        ("ConfigSyncManager", "src.modules.data_import.config_sync_manager"),
        ("AutoFieldMappingGenerator", "src.modules.data_import.auto_field_mapping_generator"),
        ("HeaderCustomizationDialog", "src.gui.dialogs"),
        ("VirtualizedExpandableTable", "src.gui.prototype.widgets.virtualized_expandable_table")
    ]
    
    all_imported = True
    for class_name, module_name in modules_to_check:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {class_name} from {module_name}")
        except Exception as e:
            print(f"❌ {class_name} from {module_name}: {e}")
            all_imported = False
    
    return all_imported

def check_config_file():
    """检查配置文件"""
    print("\n" + "=" * 60)
    print("3. 检查配置文件")
    print("=" * 60)
    
    config_file = project_root / "config" / "field_mappings.json"
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 配置文件存在: {config_file}")
        
        # 检查配置结构
        if "table_mappings" in config:
            print(f"✅ table_mappings 节点存在，包含 {len(config['table_mappings'])} 个表映射")
        else:
            print("❌ table_mappings 节点不存在")
        
        if "user_preferences" in config:
            header_prefs = config["user_preferences"].get("header_preferences", {})
            print(f"✅ user_preferences 节点存在，包含 {len(header_prefs)} 个用户偏好")
        else:
            print("❌ user_preferences 节点不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def check_functionality():
    """检查功能是否正常"""
    print("\n" + "=" * 60)
    print("4. 检查功能是否正常")
    print("=" * 60)
    
    try:
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
        
        # 测试配置管理器
        config_sync = ConfigSyncManager()
        print("✅ ConfigSyncManager 初始化成功")
        
        # 测试自动映射生成器
        auto_mapping = AutoFieldMappingGenerator()
        print("✅ AutoFieldMappingGenerator 初始化成功")
        
        # 测试映射生成
        test_columns = ['工号', '姓名', '基本工资']
        mapping = auto_mapping.generate_mapping(test_columns, "test_table")
        if mapping and len(mapping) == len(test_columns):
            print("✅ 字段映射生成功能正常")
        else:
            print("❌ 字段映射生成功能异常")
            return False
        
        # 测试配置保存和加载
        test_mapping = {'工号': '工号', '姓名': '姓名'}
        save_success = config_sync.save_mapping("test_table", test_mapping)
        if save_success:
            print("✅ 映射保存功能正常")
        else:
            print("❌ 映射保存功能异常")
            return False
        
        loaded_mapping = config_sync.load_mapping("test_table")
        if loaded_mapping == test_mapping:
            print("✅ 映射加载功能正常")
        else:
            print("❌ 映射加载功能异常")
            return False
        
        # 测试用户偏好
        test_fields = ['工号', '姓名']
        pref_success = config_sync.save_user_header_preference("test_table", test_fields)
        if pref_success:
            print("✅ 用户偏好保存功能正常")
        else:
            print("❌ 用户偏好保存功能异常")
            return False
        
        loaded_fields = config_sync.get_user_header_preference("test_table")
        if loaded_fields == test_fields:
            print("✅ 用户偏好加载功能正常")
        else:
            print("❌ 用户偏好加载功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 功能检查失败: {e}")
        return False

def check_menu_integration():
    """检查菜单集成"""
    print("\n" + "=" * 60)
    print("5. 检查菜单集成")
    print("=" * 60)
    
    try:
        # 检查主窗口文件中是否包含菜单相关代码
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("自定义表头菜单", "自定义表头"),
            ("重置表头菜单", "重置表头"),
            ("自定义表头处理", "_show_header_customization_dialog"),
            ("重置表头处理", "_reset_headers_to_default"),
            ("HeaderCustomizationDialog导入", "HeaderCustomizationDialog")
        ]
        
        all_found = True
        for check_name, search_text in checks:
            if search_text in content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ 菜单集成检查失败: {e}")
        return False

def generate_report(results):
    """生成诊断报告"""
    print("\n" + "=" * 60)
    print("诊断报告")
    print("=" * 60)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"成功率: {(passed_checks / total_checks * 100):.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查都通过！表头映射功能应该正常工作。")
        print("\n使用提示:")
        print("1. 启动系统: python main.py")
        print("2. 点击右上角设置按钮（⚙️）显示菜单栏")
        print("3. 导入Excel数据会自动生成字段映射")
        print("4. 使用'数据' → '自定义表头'功能自定义显示字段")
    else:
        print("\n⚠️ 部分检查未通过，功能可能无法正常工作。")
        print("\n建议:")
        print("1. 检查失败的项目并修复")
        print("2. 确保所有必要的文件都存在")
        print("3. 重新运行诊断脚本确认修复")

def main():
    """主函数"""
    print("表头映射功能诊断脚本")
    print("=" * 60)
    
    results = {}
    
    # 执行各项检查
    results["文件存在性"] = check_files_exist()
    results["模块导入"] = check_imports()
    results["配置文件"] = check_config_file()
    results["核心功能"] = check_functionality()
    results["菜单集成"] = check_menu_integration()
    
    # 生成报告
    generate_report(results)

if __name__ == "__main__":
    main()
