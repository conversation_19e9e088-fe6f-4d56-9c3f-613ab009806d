#!/usr/bin/env python3
"""
分页字段显示不一致问题诊断工具

问题现象:
- 英文表头表分页正常（加载所有字段）
- 中文表头表分页异常（仅加载7个用户偏好字段）

诊断目标:
1. 检查用户表头偏好配置
2. 分析字段映射存在性
3. 验证分页加载逻辑一致性
"""

import json
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager


def diagnose_pagination_field_issue():
    """诊断分页字段问题"""
    print("=" * 80)
    print("分页字段显示不一致问题诊断")
    print("=" * 80)
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        config_sync = ConfigSyncManager()
        table_manager = DynamicTableManager(config_manager)
        
        # 测试表名
        test_tables = [
            "salary_data_2026_01_a_grade_employees",  # 英文表头（正常）
            "salary_data_2026_04_a_grade_employees"   # 中文表头（异常）
        ]
        
        print("\n1. 检查表的存在性:")
        print("-" * 50)
        
        existing_tables = []
        for table_name in test_tables:
            # 使用数据库管理器检查表存在性
            exists = db_manager.table_exists(table_name)
            status = "✓ 存在" if exists else "✗ 不存在"
            print(f"  {table_name}: {status}")
            if exists:
                existing_tables.append(table_name)
                # 获取表的记录数
                try:
                    total_count = table_manager.get_record_count(table_name)
                    print(f"    记录数: {total_count}")
                except Exception as e:
                    print(f"    记录数获取失败: {e}")
        
        print("\n2. 检查字段映射配置:")
        print("-" * 50)
        
        for table_name in existing_tables:
            field_mapping = config_sync.get_mapping(table_name)
            has_mapping = field_mapping is not None and len(field_mapping) > 0
            status = "✓ 有映射" if has_mapping else "✗ 无映射"
            print(f"  {table_name}: {status}")
            
            if has_mapping:
                print(f"    映射字段数: {len(field_mapping)}")
                print(f"    前5个映射: {dict(list(field_mapping.items())[:5])}")
        
        print("\n3. 检查用户表头偏好:")
        print("-" * 50)
        
        for table_name in existing_tables:
            user_preference = config_sync.get_user_header_preference(table_name)
            has_preference = user_preference is not None and len(user_preference) > 0
            status = "✓ 有偏好" if has_preference else "✗ 无偏好"
            print(f"  {table_name}: {status}")
            
            if has_preference:
                print(f"    偏好字段数: {len(user_preference)}")
                print(f"    偏好字段: {user_preference}")
        
        print("\n4. 模拟分页加载逻辑:")
        print("-" * 50)
        
        for table_name in existing_tables:
            print(f"  表: {table_name}")
            
            # 检查用户偏好字段
            user_selected_fields = config_sync.get_user_header_preference(table_name)
            
            if user_selected_fields:
                print(f"    → 使用用户偏好字段: {len(user_selected_fields)}个")
                print(f"      字段: {user_selected_fields}")
                
                # 尝试按需加载
                try:
                    df, total_count = table_manager.get_dataframe_with_selected_fields(
                        table_name, user_selected_fields, 1, 50
                    )
                    if df is not None:
                        print(f"      加载结果: {len(df)}行, {len(df.columns)}列")
                        print(f"      实际字段: {df.columns.tolist()}")
                    else:
                        print("      加载失败: 返回None")
                except Exception as e:
                    print(f"      加载异常: {e}")
            else:
                print(f"    → 使用所有字段")
                
                # 尝试分页加载所有字段
                try:
                    df, total_count = table_manager.get_dataframe_paginated(
                        table_name, 1, 50
                    )
                    if df is not None:
                        print(f"      加载结果: {len(df)}行, {len(df.columns)}列")
                        print(f"      实际字段: {df.columns.tolist()}")
                    else:
                        print("      加载失败: 返回None")
                except Exception as e:
                    print(f"      加载异常: {e}")
        
        print("\n5. 根据日志分析问题根本原因:")
        print("-" * 50)
        
        print("从终端日志可以看出：")
        print("• 2026年1月表：'表 salary_data_2026_01_a_grade_employees 没有保存的字段映射信息'")
        print("  → 执行'分页加载所有字段' → 加载16个字段")
        print("• 2026年4月表：'应用字段映射到表 salary_data_2026_04_a_grade_employees: 7 个字段重命名'")
        print("  → 执行'按用户偏好分页加载字段' → 只加载7个偏好字段")
        print()
        print("问题核心：")
        print("1. 有字段映射的表会触发用户偏好字段加载逻辑")
        print("2. 无字段映射的表会加载所有字段")
        print("3. 这导致分页时字段数量不一致，影响数据显示")
        
        print("\n6. 具体解决方案:")
        print("-" * 50)
        print("【推荐方案】修改分页加载逻辑，统一字段加载策略：")
        print("  - 不管是否有字段映射，都加载所有字段")
        print("  - 在数据显示时应用字段映射和用户偏好")
        print("  - 确保分页前后字段一致性")
        
    except Exception as e:
        print(f"诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    diagnose_pagination_field_issue() 