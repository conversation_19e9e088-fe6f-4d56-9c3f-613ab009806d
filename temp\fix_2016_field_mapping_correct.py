#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实数据库字段名修复2016年表的字段映射

问题：数据库字段是英文，但映射配置期望中文字段名
解决：创建正确的英文->中文映射
"""

import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def fix_2016_field_mapping():
    """基于真实数据库字段修复2016年表的字段映射"""
    print("=" * 60)
    print("修复2016年表的字段映射（基于真实数据库字段）")
    print("=" * 60)
    
    # 基于真实数据库字段的映射（从上面的检查结果得出）
    real_field_mapping = {
        "id": "ID",
        "employee_id": "工号", 
        "employee_name": "姓名",
        "id_card": "身份证号",
        "department": "部门",
        "position": "职位",
        "basic_salary": "基本工资",
        "performance_bonus": "绩效奖金",
        "overtime_pay": "加班费",
        "allowance": "津贴补贴",
        "deduction": "扣除",
        "total_salary": "应发合计",
        "month": "月份",
        "year": "年份",
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }
    
    print("✅ 真实数据库字段映射:")
    for eng, chn in real_field_mapping.items():
        print(f"   {eng} -> {chn}")
    
    # 读取现有配置
    field_mappings_file = project_root / "state" / "data" / "field_mappings.json"
    
    try:
        with open(field_mappings_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        table_mappings = config_data.get("table_mappings", {})
        print(f"\\n✅ 已加载 {len(table_mappings)} 个表的映射配置")
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False
    
    # 找到2016年的表
    tables_2016 = []
    for table_name in table_mappings.keys():
        if "2016" in table_name:
            tables_2016.append(table_name)
    
    print(f"\\n找到2016年表: {tables_2016}")
    
    if not tables_2016:
        print("没有找到2016年的表")
        return False
    
    # 备份当前配置
    backup_file = project_root / "state" / "data" / f"field_mappings_before_2016_fix_{int(time.time())}.json"
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        print(f"✅ 已备份到: {backup_file.name}")
    except Exception as e:
        print(f"⚠️  备份失败: {e}")
    
    # 修复每个2016年表的映射
    print(f"\\n开始修复字段映射...")
    
    for table_name in tables_2016:
        print(f"\\n修复表: {table_name}")
        
        # 显示修复前的映射
        old_mapping = table_mappings[table_name]["field_mappings"]
        print(f"  修复前: {len(old_mapping)} 个字段 (中文->中文)")
        for i, (k, v) in enumerate(list(old_mapping.items())[:3]):
            print(f"    {k} -> {v}")
        if len(old_mapping) > 3:
            print(f"    ... 还有 {len(old_mapping) - 3} 个")
        
        # 应用新的映射
        table_mappings[table_name]["field_mappings"] = real_field_mapping.copy()
        table_mappings[table_name]["metadata"]["last_modified"] = time.strftime("%Y-%m-%dT%H:%M:%S")
        table_mappings[table_name]["metadata"]["user_modified"] = True
        table_mappings[table_name]["metadata"]["fix_applied"] = "2016年表字段映射修复 - 英文字段名"
        
        print(f"  修复后: {len(real_field_mapping)} 个字段 (英文->中文)")
        for i, (k, v) in enumerate(list(real_field_mapping.items())[:3]):
            print(f"    {k} -> {v}")
        if len(real_field_mapping) > 3:
            print(f"    ... 还有 {len(real_field_mapping) - 3} 个")
        
        print(f"  ✅ {table_name} 修复完成")
    
    # 保存修复后的配置
    print(f"\\n保存修复后的配置...")
    
    try:
        config_data["last_updated"] = time.strftime("%Y-%m-%dT%H:%M:%S")
        with open(field_mappings_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存到: {field_mappings_file}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False
    
    print(f"\\n🎉 2016年表字段映射修复完成！")
    print(f"\\n修复内容总结：")
    print(f"- 修复了 {len(tables_2016)} 个2016年表的字段映射")
    print(f"- 将映射从 中文字段名->中文显示名 改为 英文字段名->中文显示名")
    print(f"- 使用真实的数据库字段名作为映射源")
    print(f"- 备份文件: {backup_file.name}")
    
    print(f"\\n📋 下一步：")
    print(f"1. 重新启动系统")
    print(f"2. 导航到2016年1月的任一表")
    print(f"3. 查看表头是否显示为中文")
    
    return True

if __name__ == "__main__":
    success = fix_2016_field_mapping()
    
    if success:
        print(f"\\n✅ 修复完成！现在2016年的表应该能正确显示中文表头了。")
    else:
        print(f"\\n❌ 修复失败！请检查错误信息。") 