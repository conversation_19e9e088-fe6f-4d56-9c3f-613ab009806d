#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2018年6月数据字段映射完整修复脚本
修复字段映射键名与实际数据库表名不一致的问题
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主修复函数"""
    print("=" * 60)
    print("2018年6月数据字段映射完整修复脚本")
    print("=" * 60)
    
    # 字段映射文件路径
    mapping_file = project_root / "state" / "data" / "field_mappings.json"
    
    if not mapping_file.exists():
        print(f"错误：字段映射文件不存在: {mapping_file}")
        return False
    
    # 备份原文件
    backup_file = mapping_file.parent / f"field_mappings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        # 读取原映射数据
        print(f"读取字段映射文件: {mapping_file}")
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
        
        # 备份原文件
        print(f"备份原文件到: {backup_file}")
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)
        
        # 需要修复的映射键名对应关系
        mapping_fixes = {
            # 2018年6月数据的映射修复
            "salary_data_retired_employees_2025_06_离休人员工资表": "salary_data_2018_06_retired_employees",
            "salary_data_pension_employees_2025_06_退休人员工资表": "salary_data_2018_06_pension_employees", 
            "salary_data_active_employees_2025_全部在职人员工资表": "salary_data_2018_06_active_employees",
            "salary_data_a_grade_employees_2025_A岗职工": "salary_data_2018_06_a_grade_employees"
        }
        
        # 执行映射修复
        table_mappings = mapping_data.get("table_mappings", {})
        updated_count = 0
        
        for old_key, new_key in mapping_fixes.items():
            if old_key in table_mappings:
                print(f"修复映射: {old_key} -> {new_key}")
                
                # 复制映射到新键名
                table_mappings[new_key] = table_mappings[old_key].copy()
                
                # 更新元数据
                if "metadata" in table_mappings[new_key]:
                    table_mappings[new_key]["metadata"]["last_modified"] = datetime.now().isoformat()
                    table_mappings[new_key]["metadata"]["fixed_mapping_key"] = True
                    table_mappings[new_key]["metadata"]["original_key"] = old_key
                
                # 删除旧键名
                del table_mappings[old_key]
                updated_count += 1
                
                # 显示映射内容
                field_mappings = table_mappings[new_key].get("field_mappings", {})
                print(f"  包含字段映射: {len(field_mappings)} 个")
                for orig_field, mapped_field in field_mappings.items():
                    print(f"    {orig_field} -> {mapped_field}")
                print()
            else:
                print(f"警告：未找到映射键: {old_key}")
        
        # 更新最后修改时间戳
        mapping_data["last_updated"] = datetime.now().isoformat()
        
        # 保存修复后的映射
        print(f"保存修复后的映射到: {mapping_file}")
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)
        
        print("=" * 60)
        print(f"字段映射修复完成！")
        print(f"修复映射数量: {updated_count}")
        print(f"备份文件: {backup_file}")
        print("=" * 60)
        
        # 验证修复结果
        print("\n验证修复结果:")
        for new_key in mapping_fixes.values():
            if new_key in table_mappings:
                field_count = len(table_mappings[new_key].get("field_mappings", {}))
                print(f"✓ {new_key}: {field_count} 个字段映射")
            else:
                print(f"✗ {new_key}: 映射缺失")
        
        return True
        
    except Exception as e:
        print(f"错误：修复过程中出现异常: {e}")
        # 尝试恢复备份
        if backup_file.exists():
            print("尝试恢复原文件...")
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)
                with open(mapping_file, 'w', encoding='utf-8') as f:
                    json.dump(original_data, f, ensure_ascii=False, indent=2)
                print("已恢复原文件")
            except Exception as restore_error:
                print(f"恢复失败: {restore_error}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 