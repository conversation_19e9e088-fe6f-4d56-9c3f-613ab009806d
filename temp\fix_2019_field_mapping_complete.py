#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
2019年12月表字段映射完整修复脚本

解决新导入数据表头显示英文、分页切换后丢失中文映射的问题
"""

import sys
import sqlite3
from pathlib import Path
import json
from datetime import datetime
from typing import Dict, List, Optional

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))

from modules.data_storage.dynamic_table_manager import DynamicTableManager
from modules.data_import.config_sync_manager import ConfigSyncManager
from utils.log_config import setup_logger

class FieldMappingCompleteFixer:
    """2019年12月表字段映射完整修复器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # 数据库路径
        self.db_path = project_root / "data" / "db" / "salary_system.db"
        
        # 配置管理器
        self.config_sync_manager = ConfigSyncManager()
        
        # 表管理器
        self.table_manager = DynamicTableManager(str(self.db_path))
        
        # 需要修复的表
        self.target_tables = [
            "salary_data_2019_12_active_employees",    # 全部在职人员
            "salary_data_2019_12_retired_employees",   # 离休人员
            "salary_data_2019_12_pension_employees",   # 退休人员
            "salary_data_2019_12_a_grade_employees"    # A岗职工
        ]
        
        # 标准字段映射模板
        self.standard_field_mappings = {
            # 基础信息字段
            "id": "序号",
            "employee_id": "工号", 
            "employee_name": "姓名",
            "id_card": "身份证号",
            "department": "部门",
            "position": "职位",
            
            # 工资字段
            "basic_salary": "基本工资",
            "position_salary": "岗位工资", 
            "grade_salary": "薪级工资",
            "performance_bonus": "绩效工资",
            "overtime_pay": "加班费",
            "allowance": "津贴补贴",
            "deduction": "扣除项",
            "total_salary": "应发合计",
            
            # 时间字段
            "month": "月份",
            "year": "年份", 
            "created_at": "创建时间",
            "updated_at": "更新时间"
        }
    
    def run_complete_fix(self):
        """执行完整修复"""
        try:
            self.logger.info("🔧 开始2019年12月表字段映射完整修复...")
            
            # 1. 检查表存在性
            existing_tables = self._check_table_existence()
            
            # 2. 分析当前字段映射状态
            current_mappings = self._analyze_current_mappings(existing_tables)
            
            # 3. 生成完整的字段映射
            complete_mappings = self._generate_complete_mappings(existing_tables)
            
            # 4. 应用字段映射修复
            self._apply_mapping_fixes(complete_mappings)
            
            # 5. 验证修复结果
            self._verify_fix_results(existing_tables)
            
            self.logger.info("✅ 2019年12月表字段映射完整修复完成!")
            
        except Exception as e:
            self.logger.error(f"❌ 完整修复失败: {e}")
            raise
    
    def _check_table_existence(self) -> List[str]:
        """检查表存在性"""
        existing_tables = []
        
        self.logger.info("📋 检查表存在性...")
        
        for table_name in self.target_tables:
            if self.table_manager.table_exists(table_name):
                existing_tables.append(table_name)
                self.logger.info(f"  ✓ 表存在: {table_name}")
            else:
                self.logger.warning(f"  ⚠️ 表不存在: {table_name}")
        
        self.logger.info(f"共找到 {len(existing_tables)} 个需要修复的表")
        return existing_tables
    
    def _analyze_current_mappings(self, existing_tables: List[str]) -> Dict[str, Dict]:
        """分析当前字段映射状态"""
        current_mappings = {}
        
        self.logger.info("🔍 分析当前字段映射状态...")
        
        for table_name in existing_tables:
            mapping = self.config_sync_manager.load_mapping(table_name)
            if mapping:
                current_mappings[table_name] = mapping
                self.logger.info(f"  ✓ {table_name}: 已有 {len(mapping)} 个字段映射")
            else:
                current_mappings[table_name] = {}
                self.logger.warning(f"  ⚠️ {table_name}: 无字段映射配置")
        
        return current_mappings
    
    def _generate_complete_mappings(self, existing_tables: List[str]) -> Dict[str, Dict[str, str]]:
        """生成完整的字段映射"""
        complete_mappings = {}
        
        self.logger.info("🛠️ 生成完整字段映射...")
        
        for table_name in existing_tables:
            # 获取表的实际列信息
            columns = self.table_manager.get_table_columns(table_name)
            if not columns:
                self.logger.warning(f"  ⚠️ 无法获取表 {table_name} 的列信息")
                continue
            
            # 生成字段映射
            table_mapping = {}
            actual_columns = [col['name'] for col in columns]
            
            for col_name in actual_columns:
                # 使用标准映射或保持原名
                if col_name in self.standard_field_mappings:
                    table_mapping[col_name] = self.standard_field_mappings[col_name]
                else:
                    # 对于Excel直接导入的中文列名，保持不变
                    table_mapping[col_name] = col_name
            
            complete_mappings[table_name] = table_mapping
            self.logger.info(f"  ✓ {table_name}: 生成 {len(table_mapping)} 个字段映射")
            
            # 显示具体映射（前5个）
            sample_mappings = list(table_mapping.items())[:5]
            for col, display in sample_mappings:
                self.logger.info(f"    • {col} -> {display}")
        
        return complete_mappings
    
    def _apply_mapping_fixes(self, complete_mappings: Dict[str, Dict[str, str]]):
        """应用字段映射修复"""
        self.logger.info("💾 应用字段映射修复...")
        
        for table_name, field_mappings in complete_mappings.items():
            try:
                # 保存字段映射
                success = self.config_sync_manager.save_mapping(
                    table_name=table_name,
                    mapping=field_mappings,
                    metadata={
                        "created_at": datetime.now().isoformat(),
                        "last_modified": datetime.now().isoformat(),
                        "auto_generated": True,
                        "user_modified": False,
                        "fix_applied": True,
                        "fix_reason": "2019年12月表字段映射完整修复"
                    }
                )
                
                if success:
                    self.logger.info(f"  ✅ {table_name}: 字段映射保存成功")
                else:
                    self.logger.error(f"  ❌ {table_name}: 字段映射保存失败")
                    
            except Exception as e:
                self.logger.error(f"  ❌ {table_name}: 应用修复失败 - {e}")
    
    def _verify_fix_results(self, existing_tables: List[str]):
        """验证修复结果"""
        self.logger.info("🔍 验证修复结果...")
        
        fix_summary = {
            "total_tables": len(existing_tables),
            "success_count": 0,
            "failed_count": 0,
            "details": {}
        }
        
        for table_name in existing_tables:
            try:
                # 重新加载字段映射
                mapping = self.config_sync_manager.load_mapping(table_name)
                
                if mapping and len(mapping) > 0:
                    fix_summary["success_count"] += 1
                    fix_summary["details"][table_name] = {
                        "status": "success",
                        "field_count": len(mapping),
                        "sample_mappings": dict(list(mapping.items())[:3])
                    }
                    self.logger.info(f"  ✅ {table_name}: 验证成功，{len(mapping)} 个字段映射")
                else:
                    fix_summary["failed_count"] += 1
                    fix_summary["details"][table_name] = {
                        "status": "failed", 
                        "reason": "无字段映射"
                    }
                    self.logger.error(f"  ❌ {table_name}: 验证失败，无字段映射")
                    
            except Exception as e:
                fix_summary["failed_count"] += 1
                fix_summary["details"][table_name] = {
                    "status": "error",
                    "reason": str(e)
                }
                self.logger.error(f"  ❌ {table_name}: 验证出错 - {e}")
        
        # 输出修复总结
        self.logger.info("📊 修复结果总结:")
        self.logger.info(f"  总表数: {fix_summary['total_tables']}")
        self.logger.info(f"  成功: {fix_summary['success_count']}")
        self.logger.info(f"  失败: {fix_summary['failed_count']}")
        
        if fix_summary["success_count"] == fix_summary["total_tables"]:
            self.logger.info("🎉 所有表修复成功!")
        else:
            self.logger.warning(f"⚠️ {fix_summary['failed_count']} 个表未完全修复")
    
    def get_table_columns_info(self, table_name: str) -> Optional[List[Dict]]:
        """获取表列信息（调试用）"""
        try:
            return self.table_manager.get_table_columns(table_name)
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 列信息失败: {e}")
            return None

def main():
    """主函数"""
    try:
        # 创建修复器
        fixer = FieldMappingCompleteFixer()
        
        # 执行修复
        fixer.run_complete_fix()
        
        print("\n" + "="*60)
        print("🎉 2019年12月表字段映射完整修复完成!")
        print("现在所有表都应该有完整的字段映射配置")
        print("分页切换时将正确显示中文表头")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 