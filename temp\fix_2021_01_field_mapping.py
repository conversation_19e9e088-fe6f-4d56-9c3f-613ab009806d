#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复2021年1月数据字段映射问题
为新导入的4个表自动生成字段映射配置
"""

import sys
import os

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

# 添加项目根目录到路径  
project_root = os.path.join(current_dir, '..')
sys.path.insert(0, project_root)

import pandas as pd
from typing import Dict, List
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
from src.modules.system_config.config_manager import ConfigManager
from src.utils.log_config import setup_logger

def main():
    """主函数"""
    # 初始化日志
    logger = setup_logger()
    logger.info("开始修复2021年1月数据字段映射问题")
    
    try:
        # 1. 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager=db_manager)
        config_sync_manager = ConfigSyncManager()
        auto_mapping_generator = AutoFieldMappingGenerator()
        
        # 2. 需要修复的表列表
        tables_to_fix = [
            "salary_data_2021_01_retired_employees",    # 离休人员
            "salary_data_2021_01_pension_employees",    # 退休人员  
            "salary_data_2021_01_active_employees",     # 全部在职人员
            "salary_data_2021_01_a_grade_employees"     # A岗职工
        ]
        
        logger.info(f"准备为 {len(tables_to_fix)} 个表生成字段映射")
        
        # 3. 为每个表生成字段映射
        success_count = 0
        for table_name in tables_to_fix:
            try:
                logger.info(f"正在处理表: {table_name}")
                
                # 检查表是否存在
                if not table_manager.table_exists(table_name):
                    logger.warning(f"表 {table_name} 不存在，跳过")
                    continue
                
                # 获取表的列信息
                columns_info = table_manager.get_table_columns(table_name)
                column_names = [col['name'] for col in columns_info]
                
                logger.info(f"表 {table_name} 有 {len(column_names)} 列: {column_names}")
                
                # 生成字段映射
                auto_mapping = auto_mapping_generator.generate_mapping(
                    excel_columns=column_names,
                    table_name=table_name
                )
                
                if auto_mapping:
                    # 保存映射配置
                    config_sync_manager.save_mapping(
                        table_name=table_name,
                        mapping=auto_mapping,
                        metadata={
                            "auto_generated": True,
                            "user_modified": False,
                            "created_by": "fix_script",
                            "description": f"自动修复生成的字段映射 - {table_name}"
                        }
                    )
                    
                    logger.info(f"✅ 表 {table_name} 字段映射生成成功: {len(auto_mapping)} 个字段")
                    logger.info(f"   字段映射: {auto_mapping}")
                    success_count += 1
                else:
                    logger.warning(f"❌ 表 {table_name} 字段映射生成失败")
                    
            except Exception as e:
                logger.error(f"❌ 处理表 {table_name} 时发生错误: {e}")
        
        # 4. 创建通用的中文字段映射
        logger.info("创建增强的中文字段映射...")
        
        # 常见工资字段的中文映射
        enhanced_mappings = {
            # 基础信息
            "employee_id": "工号",
            "employee_name": "姓名", 
            "id_card": "身份证号",
            "department": "部门",
            "position": "职位",
            
            # 工资项目
            "basic_salary": "基本工资",
            "performance_bonus": "绩效工资",
            "overtime_pay": "加班费",
            "allowance": "津贴补贴",
            "deduction": "扣除项",
            "total_salary": "应发合计",
            
            # 时间信息
            "month": "月份",
            "year": "年份",
            "created_at": "创建时间",
            "updated_at": "更新时间",
            
            # 具体工资项目（如果存在）
            "工号": "工号",
            "姓名": "姓名",
            "津贴": "津贴补贴",
            "应发工资": "应发合计",
            "人员代码": "工号",
            "部门名称": "部门"
        }
        
        # 为每个表保存增强映射
        for table_name in tables_to_fix:
            try:
                if table_manager.table_exists(table_name):
                    # 获取现有映射
                    existing_mapping = config_sync_manager.load_mapping(table_name)
                    
                    # 合并映射（保留现有的，补充新的）
                    final_mapping = enhanced_mappings.copy()
                    if existing_mapping:
                        final_mapping.update(existing_mapping)
                    
                    # 只保留表中实际存在的列
                    columns_info = table_manager.get_table_columns(table_name)
                    existing_columns = {col['name'] for col in columns_info}
                    
                    filtered_mapping = {
                        k: v for k, v in final_mapping.items() 
                        if k in existing_columns
                    }
                    
                    if filtered_mapping:
                        config_sync_manager.save_mapping(
                            table_name=table_name,
                            mapping=filtered_mapping,
                            metadata={
                                "auto_generated": False,
                                "user_modified": False, 
                                "enhanced": True,
                                "created_by": "fix_script_enhanced",
                                "description": f"增强修复的字段映射 - {table_name}"
                            }
                        )
                        
                        logger.info(f"✅ 表 {table_name} 增强字段映射保存成功: {len(filtered_mapping)} 个字段")
                        logger.info(f"   增强映射: {filtered_mapping}")
                        
            except Exception as e:
                logger.error(f"❌ 保存表 {table_name} 增强映射时发生错误: {e}")
        
        # 5. 验证修复结果
        logger.info("验证修复结果...")
        
        for table_name in tables_to_fix:
            try:
                mapping = config_sync_manager.load_mapping(table_name)
                if mapping:
                    logger.info(f"✅ 表 {table_name} 映射验证成功: {len(mapping)} 个字段")
                else:
                    logger.warning(f"❌ 表 {table_name} 映射验证失败: 无映射配置")
                    
            except Exception as e:
                logger.error(f"❌ 验证表 {table_name} 映射时发生错误: {e}")
        
        logger.info(f"🎉 字段映射修复完成! 成功处理 {success_count}/{len(tables_to_fix)} 个表")
        
        # 6. 测试字段映射效果
        logger.info("测试字段映射效果...")
        test_table = "salary_data_2021_01_a_grade_employees"
        
        if table_manager.table_exists(test_table):
            # 获取一小部分数据测试
            test_df = table_manager.get_dataframe_from_table(test_table)
            logger.info(f"测试表 {test_table} 原始列名: {list(test_df.columns)}")
            
            # 应用字段映射
            mapping = config_sync_manager.load_mapping(test_table)
            if mapping:
                mapped_columns = []
                for col in test_df.columns:
                    mapped_name = mapping.get(col, col)
                    mapped_columns.append(mapped_name)
                
                logger.info(f"测试表 {test_table} 映射后列名: {mapped_columns}")
                logger.info(f"映射规则: {mapping}")
            else:
                logger.warning(f"测试表 {test_table} 没有找到字段映射")
        
        return True
        
    except Exception as e:
        logger.error(f"修复过程中发生严重错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 字段映射修复成功完成！")
    else:
        print("❌ 字段映射修复失败！")
        sys.exit(1)