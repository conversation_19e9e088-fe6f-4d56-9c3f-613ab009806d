#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复2026年3月数据字段映射问题

问题：字段映射配置中存储的是Excel列名到显示名的映射，
但主界面显示时使用的是数据库字段名，导致映射不匹配。

解决方案：将Excel列名映射转换为数据库字段名映射。
"""

import sys
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(project_root))

def get_db_field_mapping() -> Dict[str, str]:
    """获取Excel列名到数据库字段名的映射关系"""
    return {
        # 基础字段
        "序号": "id",
        "工号": "employee_id", 
        "人员代码": "employee_id",
        "姓名": "employee_name",
        "部门名称": "department",
        "部门": "department",
        "人员类别": "position",
        "人员类别代码": "position",
        "身份证号": "id_card",
        
        # 工资字段
        "基本工资": "basic_salary",
        "基本退休费": "basic_salary",
        "基本\n离休费": "basic_salary",
        "2025年岗位工资": "basic_salary",
        "岗位工资": "basic_salary",
        "2025年薪级工资": "basic_salary",
        "薪级工资": "basic_salary",
        "2025年校龄工资": "basic_salary",
        
        # 绩效奖金
        "绩效工资": "performance_bonus",
        "2025年奖励性绩效预发": "performance_bonus",
        "2025年基础性绩效": "performance_bonus",
        
        # 津贴补贴
        "津贴": "allowance",
        "津贴补贴": "allowance",
        "结余津贴": "allowance",
        "离退休生活补贴": "allowance",
        "交通补贴": "allowance",
        "物业补贴": "allowance",
        "住房补贴": "allowance",
        "车补": "allowance",
        "通讯补贴": "allowance",
        "卫生费": "allowance",
        "2025年生活补贴": "allowance",
        "生活\n补贴": "allowance",
        "住房\n补贴": "allowance",
        "物业\n补贴": "allowance",
        "离休\n补贴": "allowance",
        "护理费": "allowance",
        "结余\n津贴": "allowance",
        
        # 扣款
        "借支": "deduction",
        "2025公积金": "deduction",
        "代扣代存养老保险": "deduction",
        
        # 其他
        "补发": "overtime_pay",
        "增资预付": "overtime_pay",
        
        # 历年调整 - 都映射到allowance
        "2016待遇调整": "allowance",
        "2017待遇调整": "allowance", 
        "2018待遇调整": "allowance",
        "2019待遇调整": "allowance",
        "2020待遇调整": "allowance",
        "2021待遇调整": "allowance",
        "2022待遇调整": "allowance",
        "2023待遇调整": "allowance",
        
        # 总工资
        "应发工资": "total_salary",
        "应发合计": "total_salary",
        "合计": "total_salary",
    }

def fix_table_mapping(table_name: str, current_mapping: Dict[str, str]) -> Dict[str, str]:
    """修复单个表的字段映射"""
    db_field_mapping = get_db_field_mapping()
    fixed_mapping = {}
    
    print(f"  修复表 {table_name} 的字段映射:")
    
    for excel_col, display_name in current_mapping.items():
        # 查找Excel列名对应的数据库字段名
        db_field = db_field_mapping.get(excel_col)
        if db_field:
            fixed_mapping[db_field] = display_name
            print(f"    {excel_col} -> {db_field}: {display_name}")
        else:
            # 如果找不到映射，保持原样（可能是直接的数据库字段名）
            fixed_mapping[excel_col] = display_name
            print(f"    保持原样: {excel_col}: {display_name}")
    
    return fixed_mapping

def main():
    """主函数"""
    print("开始修复2026年3月数据的字段映射配置...")
    
    # 配置文件路径
    config_file = project_root / "state" / "data" / "field_mappings.json"
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 备份
        backup_file = config_file.parent / f"field_mappings_before_2026_03_fix_{int(time.time())}.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"✅ 已备份原配置到: {backup_file}")
        
        # 要修复的表
        tables_to_fix = [
            "salary_data_2026_03_retired_employees",
            "salary_data_2026_03_pension_employees", 
            "salary_data_2026_03_active_employees",
            "salary_data_2026_03_a_grade_employees"
        ]
        
        fixed_count = 0
        for table_name in tables_to_fix:
            if table_name in config.get("table_mappings", {}):
                table_config = config["table_mappings"][table_name]
                current_mapping = table_config.get("field_mappings", {})
                
                if current_mapping:
                    print(f"\n修复表 {table_name}:")
                    fixed_mapping = fix_table_mapping(table_name, current_mapping)
                    
                    # 更新映射
                    table_config["field_mappings"] = fixed_mapping
                    
                    # 更新元数据
                    if "metadata" in table_config:
                        table_config["metadata"]["last_modified"] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]
                        table_config["metadata"]["user_modified"] = True
                    
                    # 添加修复记录
                    if "edit_history" not in table_config:
                        table_config["edit_history"] = []
                    
                    table_config["edit_history"].append({
                        "timestamp": datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3],
                        "field": "auto_fix",
                        "old_value": "excel_column_mapping",
                        "new_value": "db_field_mapping", 
                        "user_action": True,
                        "fix_reason": "Convert Excel column mapping to database field mapping"
                    })
                    
                    fixed_count += 1
                    print(f"  ✅ 已修复")
                else:
                    print(f"  ⚠️ 表 {table_name} 没有字段映射配置")
            else:
                print(f"  ❌ 未找到表 {table_name} 的配置")
        
        if fixed_count > 0:
            # 更新全局时间戳
            config["last_updated"] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]
            
            # 保存修复后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 修复完成！共修复了 {fixed_count} 个表的字段映射")
            print(f"✅ 配置已保存到: {config_file}")
            print(f"✅ 备份文件: {backup_file}")
            
            print("\n修复后请重启系统，新的字段映射将生效。")
            return True
        else:
            print("\n⚠️ 没有找到需要修复的表配置")
            return False
            
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 字段映射修复成功！")
    else:
        print("\n❌ 字段映射修复失败！") 