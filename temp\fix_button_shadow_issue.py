"""
临时修复菜单栏切换时表头按钮重影问题

问题分析：
1. 在菜单栏显示/隐藏切换时，布局刷新可能导致表头按钮重复绘制
2. 响应式布局管理器可能与菜单栏布局刷新存在冲突
3. QTableWidget的表头和视口更新可能不同步

解决方案：
1. 优化布局刷新顺序，避免重复更新
2. 增加延迟刷新机制，确保组件稳定后再更新
3. 强制清除表头缓存，重新绘制表头
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QTableWidget, QHeaderView, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QTimer, pyqtSlot
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.utils.log_config import setup_logger

class ButtonShadowFixer:
    """
    按钮重影修复器
    """
    
    def __init__(self):
        self.logger = setup_logger(__name__ + ".ButtonShadowFixer")
    
    def fix_menu_bar_layout_refresh(self, menu_manager):
        """
        修复菜单栏布局刷新方法
        
        Args:
            menu_manager: 菜单栏管理器实例
        """
        try:
            # 备份原始方法
            original_refresh = menu_manager._refresh_main_window_layout
            
            def enhanced_refresh_layout():
                """增强的布局刷新方法"""
                try:
                    main_window = menu_manager.main_window
                    
                    # 1. 暂停响应式管理器以避免冲突
                    if hasattr(main_window, 'responsive_manager') and main_window.responsive_manager:
                        if hasattr(main_window.responsive_manager, 'pause_updates'):
                            main_window.responsive_manager.pause_updates()
                    
                    # 2. 清除表头缓存和重影
                    self._clear_table_header_cache(main_window)
                    
                    # 3. 执行基础布局更新
                    main_window.update()
                    
                    # 4. 延迟执行详细布局刷新
                    QTimer.singleShot(50, lambda: self._delayed_layout_refresh(main_window))
                    
                    # 5. 重新启动响应式管理器
                    if hasattr(main_window, 'responsive_manager') and main_window.responsive_manager:
                        if hasattr(main_window.responsive_manager, 'resume_updates'):
                            QTimer.singleShot(150, main_window.responsive_manager.resume_updates)
                    
                    self.logger.info("增强布局刷新完成")
                    
                except Exception as e:
                    self.logger.error(f"增强布局刷新失败: {e}")
                    # 回滚到原始方法
                    try:
                        original_refresh()
                    except:
                        pass
            
            # 替换方法
            menu_manager._refresh_main_window_layout = enhanced_refresh_layout
            self.logger.info("菜单栏布局刷新方法已增强")
            
        except Exception as e:
            self.logger.error(f"修复菜单栏布局刷新失败: {e}")
    
    def _clear_table_header_cache(self, main_window):
        """
        清除表头缓存，解决按钮重影问题
        
        Args:
            main_window: 主窗口实例
        """
        try:
            # 查找所有表格组件并清除表头缓存
            tables = self._find_all_tables(main_window)
            
            for table in tables:
                if isinstance(table, QTableWidget):
                    # 清除表头缓存
                    h_header = table.horizontalHeader()
                    v_header = table.verticalHeader()
                    
                    if h_header:
                        # 强制重绘表头
                        h_header.updateGeometry()
                        h_header.update()
                        h_header.repaint()
                        
                        # 清除选择状态避免重影
                        if hasattr(h_header, 'clearSelection'):
                            h_header.clearSelection()
                    
                    if v_header:
                        v_header.updateGeometry()
                        v_header.update()
                        v_header.repaint()
                    
                    # 清除表格视口缓存
                    viewport = table.viewport()
                    if viewport:
                        viewport.update()
                        viewport.repaint()
                    
                    self.logger.debug(f"已清除表格 {table.objectName()} 的表头缓存")
            
        except Exception as e:
            self.logger.error(f"清除表头缓存失败: {e}")
    
    def _find_all_tables(self, widget):
        """
        递归查找所有表格组件
        
        Args:
            widget: 要搜索的组件
            
        Returns:
            找到的表格组件列表
        """
        tables = []
        
        try:
            if isinstance(widget, QTableWidget):
                tables.append(widget)
            
            # 递归搜索子组件
            for child in widget.findChildren(QTableWidget):
                tables.append(child)
                
        except Exception as e:
            self.logger.error(f"查找表格组件失败: {e}")
        
        return tables
    
    def _delayed_layout_refresh(self, main_window):
        """
        延迟布局刷新
        
        Args:
            main_window: 主窗口实例
        """
        try:
            # 1. 更新中央部件
            central_widget = main_window.centralWidget()
            if central_widget:
                central_widget.updateGeometry()
                central_widget.update()
            
            # 2. 更新主要组件（避免重复更新）
            components_to_update = [
                ('main_workspace', getattr(main_window, 'main_workspace', None)),
                ('navigation_panel', getattr(main_window, 'navigation_panel', None)),
            ]
            
            for name, component in components_to_update:
                if component:
                    component.updateGeometry()
                    component.update()
                    
                    # 特殊处理表格组件
                    if hasattr(component, 'expandable_table'):
                        table = component.expandable_table
                        if table:
                            self._refresh_single_table(table)
                    
                    self.logger.debug(f"已更新组件: {name}")
            
            # 3. 最后清理一次表头
            QTimer.singleShot(50, lambda: self._final_header_cleanup(main_window))
            
        except Exception as e:
            self.logger.error(f"延迟布局刷新失败: {e}")
    
    def _refresh_single_table(self, table):
        """
        刷新单个表格组件
        
        Args:
            table: 表格组件
        """
        try:
            if isinstance(table, QTableWidget):
                # 1. 更新表格本身
                table.updateGeometry()
                table.update()
                
                # 2. 重新设置表头模式以避免重影
                h_header = table.horizontalHeader()
                if h_header:
                    # 保存当前设置
                    current_resize_mode = QHeaderView.Stretch
                    if table.columnCount() > 0:
                        try:
                            current_resize_mode = h_header.sectionResizeMode(0)
                        except:
                            current_resize_mode = QHeaderView.Stretch
                    
                    # 重新应用设置
                    h_header.setSectionResizeMode(current_resize_mode)
                    h_header.setStretchLastSection(True)
                    
                    # 强制更新
                    h_header.updateGeometry()
                    h_header.update()
                
                # 3. 更新视口
                viewport = table.viewport()
                if viewport:
                    viewport.update()
                
                self.logger.debug("单个表格刷新完成")
                
        except Exception as e:
            self.logger.error(f"刷新单个表格失败: {e}")
    
    def _final_header_cleanup(self, main_window):
        """
        最终表头清理，确保没有重影
        
        Args:
            main_window: 主窗口实例
        """
        try:
            tables = self._find_all_tables(main_window)
            
            for table in tables:
                if isinstance(table, QTableWidget):
                    # 最后一次强制重绘
                    h_header = table.horizontalHeader()
                    if h_header:
                        h_header.repaint()
                    
                    v_header = table.verticalHeader()
                    if v_header:
                        v_header.repaint()
                    
                    # 确保视口清洁
                    viewport = table.viewport()
                    if viewport:
                        viewport.repaint()
            
            self.logger.debug("最终表头清理完成")
            
        except Exception as e:
            self.logger.error(f"最终表头清理失败: {e}")


def apply_button_shadow_fix():
    """
    应用按钮重影修复
    """
    try:
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        from src.core.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 创建必要的管理器实例
        config_manager = ConfigManager()
        db_manager = DatabaseManager()
        dynamic_table_manager = DynamicTableManager(db_manager)
        
        # 创建主窗口
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        main_window = PrototypeMainWindow(config_manager, db_manager, dynamic_table_manager)
        
        # 应用修复
        fixer = ButtonShadowFixer()
        if hasattr(main_window, 'menu_bar_manager'):
            fixer.fix_menu_bar_layout_refresh(main_window.menu_bar_manager)
            print("✅ 按钮重影修复已应用")
        else:
            print("❌ 未找到菜单栏管理器")
        
        # 显示窗口进行测试
        main_window.show()
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        return 1


if __name__ == "__main__":
    apply_button_shadow_fix() 