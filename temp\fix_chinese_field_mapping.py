#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强修复2021年1月数据字段映射
生成正确的中文字段映射配置
"""

import sys
import os

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

# 添加项目根目录到路径  
project_root = os.path.join(current_dir, '..')
sys.path.insert(0, project_root)

import pandas as pd
from typing import Dict, List
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.utils.log_config import setup_logger

def main():
    """主函数"""
    # 初始化日志
    logger = setup_logger()
    logger.info("开始增强修复2021年1月数据字段映射，生成中文映射")
    
    try:
        # 1. 初始化配置同步管理器
        config_sync_manager = ConfigSyncManager()
        
        # 2. 需要修复的表列表
        tables_to_fix = [
            "salary_data_2021_01_retired_employees",    # 离休人员
            "salary_data_2021_01_pension_employees",    # 退休人员  
            "salary_data_2021_01_active_employees",     # 全部在职人员
            "salary_data_2021_01_a_grade_employees"     # A岗职工
        ]
        
        # 3. 定义标准的中文字段映射
        chinese_field_mappings = {
            # 基础信息字段
            "id": "序号",
            "employee_id": "工号", 
            "employee_name": "姓名",
            "id_card": "身份证号",
            "department": "部门",
            "position": "职位",
            
            # 工资项目字段
            "basic_salary": "基本工资",
            "performance_bonus": "绩效工资",
            "overtime_pay": "加班费", 
            "allowance": "津贴补贴",
            "deduction": "扣除项",
            "total_salary": "应发合计",
            
            # 时间字段
            "month": "月份",
            "year": "年份",
            "created_at": "创建时间",
            "updated_at": "更新时间"
        }
        
        logger.info(f"准备为 {len(tables_to_fix)} 个表生成中文字段映射")
        logger.info(f"标准中文映射: {chinese_field_mappings}")
        
        # 4. 为每个表生成并保存中文字段映射
        success_count = 0
        
        for table_name in tables_to_fix:
            try:
                logger.info(f"正在处理表: {table_name}")
                
                # 保存中文字段映射
                config_sync_manager.save_mapping(
                    table_name=table_name,
                    mapping=chinese_field_mappings,
                    metadata={
                        "auto_generated": False,
                        "user_modified": False,
                        "enhanced": True,
                        "chinese_mapping": True,
                        "created_by": "chinese_fix_script",
                        "description": f"中文字段映射修复 - {table_name}"
                    }
                )
                
                logger.info(f"✅ 表 {table_name} 中文字段映射保存成功: {len(chinese_field_mappings)} 个字段")
                success_count += 1
                
            except Exception as e:
                logger.error(f"❌ 处理表 {table_name} 时发生错误: {e}")
        
        # 5. 验证修复结果
        logger.info("验证中文字段映射修复结果...")
        
        for table_name in tables_to_fix:
            try:
                mapping = config_sync_manager.load_mapping(table_name)
                if mapping:
                    # 检查是否包含中文映射
                    chinese_count = sum(1 for v in mapping.values() if any('\u4e00' <= char <= '\u9fff' for char in v))
                    logger.info(f"✅ 表 {table_name} 映射验证成功: {len(mapping)} 个字段, 其中 {chinese_count} 个中文字段")
                    
                    # 显示关键字段的映射
                    key_fields = ["employee_id", "employee_name", "total_salary", "allowance"]
                    key_mappings = {k: mapping.get(k, "未找到") for k in key_fields}
                    logger.info(f"   关键字段映射: {key_mappings}")
                else:
                    logger.warning(f"❌ 表 {table_name} 映射验证失败: 无映射配置")
                    
            except Exception as e:
                logger.error(f"❌ 验证表 {table_name} 映射时发生错误: {e}")
        
        logger.info(f"🎉 中文字段映射修复完成! 成功处理 {success_count}/{len(tables_to_fix)} 个表")
        
        # 6. 创建测试示例
        logger.info("创建测试示例...")
        
        # 展示映射效果
        example_english_columns = ["id", "employee_id", "employee_name", "department", "basic_salary", "allowance", "total_salary"]
        example_chinese_columns = [chinese_field_mappings.get(col, col) for col in example_english_columns]
        
        logger.info(f"映射示例:")
        logger.info(f"原始英文列名: {example_english_columns}")
        logger.info(f"映射中文列名: {example_chinese_columns}")
        
        return True
        
    except Exception as e:
        logger.error(f"修复过程中发生严重错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 中文字段映射修复成功完成！")
        print("🔄 请重新启动系统，然后导航到2021年1月的数据查看效果")
    else:
        print("❌ 中文字段映射修复失败！")
        sys.exit(1) 