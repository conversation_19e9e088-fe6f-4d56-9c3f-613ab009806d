#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极版本字段映射修复脚本
解决数据库英文列名与中文显示名映射问题
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_reverse_mapping():
    """创建数据库英文列名到中文显示名的反向映射"""
    
    # 标准的中文到英文映射（来自 dynamic_table_manager.py）
    standard_mappings = {
        "序号": "employee_id",  # 特殊处理
        "工号": "employee_id",
        "职工编号": "employee_id", 
        "编号": "employee_id",
        "人员代码": "employee_id",
        "姓名": "employee_name",
        "员工姓名": "employee_name",
        "身份证号": "id_card",
        "身份证": "id_card",
        "部门": "department",
        "部门名称": "department",
        "职位": "position",
        "岗位": "position",
        "基本工资": "basic_salary",
        "基础工资": "basic_salary",
        "绩效": "performance_bonus",
        "绩效奖金": "performance_bonus",
        "加班费": "overtime_pay",
        "津贴": "allowance",
        "补贴": "allowance",
        "扣款": "deduction",
        "扣除": "deduction",
        "应发工资": "total_salary",
        "实发工资": "total_salary",
        "工资合计": "total_salary",
        "合计": "total_salary",
        "应发合计": "total_salary"
    }
    
    # 2018年6月数据特殊映射
    special_2018_mappings = {
        # A岗职工特殊字段
        "岗位工资": "basic_salary",
        "薪级工资": "basic_salary", 
        "2025年岗位工资": "basic_salary",
        "2025年薪级工资": "basic_salary",
        "2025年校龄工资": "allowance",
        "津贴补贴": "allowance",
        "结余津贴": "allowance",
        "2025年基础性绩效": "performance_bonus",
        "卫生费": "allowance",
        "2025年生活补贴": "allowance",
        "2025年奖励性绩效预发": "performance_bonus",
        "绩效工资": "performance_bonus",
        "借支": "deduction",
        "2025公积金": "allowance",
        
        # 全部在职人员特殊字段
        "人员类别": "position",
        "人员类别代码": "position",
        "交通补贴": "allowance",
        "物业补贴": "allowance",
        "住房补贴": "allowance",
        "车补": "allowance",
        "通讯补贴": "allowance",
        "补发": "allowance",
        "代扣代存养老保险": "deduction",
        
        # 退休人员特殊字段
        "基本退休费": "basic_salary",
        "增资预付": "allowance",
        "2016待遇调整": "allowance",
        "2017待遇调整": "allowance", 
        "2018待遇调整": "allowance",
        "2019待遇调整": "allowance",
        "2020待遇调整": "allowance",
        "2021待遇调整": "allowance",
        "2022待遇调整": "allowance",
        "2023待遇调整": "allowance",
        "离退休生活补贴": "allowance",
        
        # 离休人员特殊字段
        "基本离休费": "basic_salary",
        "生活补贴": "allowance",
        "离休补贴": "allowance",
        "护理费": "allowance"
    }
    
    # 合并所有映射
    all_mappings = {**standard_mappings, **special_2018_mappings}
    
    # 创建反向映射：英文列名 -> 中文显示名
    reverse_mapping = {}
    for chinese_name, english_name in all_mappings.items():
        if english_name not in reverse_mapping:
            reverse_mapping[english_name] = chinese_name
        # 如果已存在，保持最合适的中文名称
        elif len(chinese_name) < len(reverse_mapping[english_name]):
            reverse_mapping[english_name] = chinese_name
    
    return reverse_mapping

def main():
    """主修复函数"""
    print("=" * 60)
    print("终极版本字段映射修复脚本")
    print("=" * 60)
    
    # 字段映射文件路径
    mapping_file = project_root / "state" / "data" / "field_mappings.json"
    
    if not mapping_file.exists():
        print(f"错误：字段映射文件不存在: {mapping_file}")
        return False
    
    # 备份原文件
    backup_file = mapping_file.parent / f"field_mappings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        # 读取原映射数据
        print(f"读取字段映射文件: {mapping_file}")
        with open(mapping_file, 'r', encoding='utf-8') as f:
            original_mappings = json.load(f)
        
        # 备份原文件
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(original_mappings, f, ensure_ascii=False, indent=2)
        print(f"备份原文件到: {backup_file}")
        
        # 创建反向映射
        reverse_mapping = create_reverse_mapping()
        print(f"创建反向映射: {len(reverse_mapping)} 个英文列名")
        
        # 针对2018年6月的表创建新的映射
        tables_to_fix = [
            "salary_data_2018_06_a_grade_employees",
            "salary_data_2018_06_active_employees", 
            "salary_data_2018_06_pension_employees",
            "salary_data_2018_06_retired_employees"
        ]
        
        updated_mappings = original_mappings.copy()
        
        for table_name in tables_to_fix:
            print(f"\n处理表: {table_name}")
                
            # 获取数据库列名（通过检查数据库或使用标准列名）
            db_columns = [
                'id', 'employee_id', 'employee_name', 'id_card', 
                'department', 'position', 'basic_salary', 'performance_bonus', 
                'overtime_pay', 'allowance', 'deduction', 'total_salary', 
                'month', 'year', 'created_at', 'updated_at'
            ]
            
            # 创建英文列名到中文显示名的映射
            new_mapping = {}
            for db_col in db_columns:
                if db_col in reverse_mapping:
                    new_mapping[db_col] = reverse_mapping[db_col]
                else:
                    # 保持原样
                    new_mapping[db_col] = db_col
            
            # 更新映射
            updated_mappings[table_name] = new_mapping
            print(f"  创建新映射: {len(new_mapping)} 个字段")
            
            # 显示映射详情
            for eng_col, chn_name in new_mapping.items():
                if eng_col != chn_name:  # 只显示有变化的
                    print(f"    {eng_col} -> {chn_name}")
        
        # 保存更新的映射
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(updated_mappings, f, ensure_ascii=False, indent=2)
        print(f"\n保存更新后的映射到: {mapping_file}")
        
        print("=" * 60)
        print("终极版本字段映射修复完成！")
        print(f"处理表数量: {len(tables_to_fix)}")
        print(f"备份文件: {backup_file}")
        print("=" * 60)
        
        # 验证修复结果
        print("\n验证修复结果:")
        for table_name in tables_to_fix:
            if table_name in updated_mappings:
                mapping_count = len(updated_mappings[table_name])
                print(f"✓ {table_name}: {mapping_count} 个字段映射")
            else:
                print(f"✗ {table_name}: 未找到映射")
        
        return True
        
    except Exception as e:
        print(f"修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 