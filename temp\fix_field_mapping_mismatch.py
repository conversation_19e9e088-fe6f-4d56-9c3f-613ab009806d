#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n修复字段映射不匹配问题\n\n问题：数据库字段是英文，但映射配置的源字段名是中文\n解决：创建正确的英文->中文映射\n\"\"\"\n\nimport sys\nimport json\nfrom pathlib import Path\n\n# 添加项目根目录到Python路径\nproject_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\n\ndef fix_field_mapping_mismatch():\n    \"\"\"修复字段映射不匹配问题\"\"\"\n    print(\"=\" * 60)\n    print(\"修复字段映射不匹配问题\")\n    print(\"=\" * 60)\n    \n    # 标准的英文->中文字段映射（基于日志中看到的字段）\n    standard_mapping = {\n        \"id\": \"ID\",\n        \"employee_id\": \"工号\", \n        \"employee_name\": \"姓名\",\n        \"id_card\": \"身份证号\",\n        \"department\": \"部门\",\n        \"position\": \"职位\",\n        \"basic_salary\": \"基本工资\",\n        \"performance_bonus\": \"绩效奖金\",\n        \"overtime_pay\": \"加班费\",\n        \"allowance\": \"津贴补贴\",\n        \"deduction\": \"扣除\",\n        \"total_salary\": \"应发合计\",\n        \"month\": \"月份\",\n        \"year\": \"年份\",\n        \"created_at\": \"创建时间\",\n        \"updated_at\": \"更新时间\"\n    }\n    \n    # 读取现有配置\n    field_mappings_file = project_root / \"state\" / \"data\" / \"field_mappings.json\"\n    \n    try:\n        with open(field_mappings_file, 'r', encoding='utf-8') as f:\n            config_data = json.load(f)\n        \n        table_mappings = config_data.get(\"table_mappings\", {})\n        print(f\"✅ 已加载 {len(table_mappings)} 个表的映射配置\")\n    except Exception as e:\n        print(f\"❌ 读取配置失败: {e}\")\n        return False\n    \n    # 找到需要修复的2016年表\n    tables_to_fix = []\n    for table_name in table_mappings.keys():\n        if \"2016\" in table_name:\n            tables_to_fix.append(table_name)\n    \n    print(f\"\\n找到需要修复的2016年表: {tables_to_fix}\")\n    \n    if not tables_to_fix:\n        print(f\"没有找到2016年的表\")\n        return False\n    \n    # 修复每个表的映射\n    print(f\"\\n开始修复字段映射...\")\n    \n    for table_name in tables_to_fix:\n        print(f\"\\n修复表: {table_name}\")\n        \n        # 更新为标准的英文->中文映射\n        table_mappings[table_name][\"field_mappings\"] = standard_mapping.copy()\n        table_mappings[table_name][\"metadata\"][\"last_modified\"] = \"2025-06-25T11:20:00\"\n        table_mappings[table_name][\"metadata\"][\"user_modified\"] = True\n        table_mappings[table_name][\"metadata\"][\"fix_applied\"] = \"英文字段名映射修复\"\n        \n        print(f\"  ✅ 已更新 {len(standard_mapping)} 个字段映射\")\n        \n        # 显示映射示例\n        for i, (eng, chn) in enumerate(standard_mapping.items()):\n            if i < 5:  # 只显示前5个\n                print(f\"     {eng} -> {chn}\")\n        print(f\"     ... 还有 {len(standard_mapping) - 5} 个映射\")\n    \n    # 保存修复后的配置\n    print(f\"\\n保存修复后的配置...\")\n    \n    try:\n        # 备份\n        import time\n        backup_file = project_root / \"state\" / \"data\" / f\"field_mappings_backup_fix_{int(time.time())}.json\"\n        with open(backup_file, 'w', encoding='utf-8') as f:\n            json.dump(config_data, f, ensure_ascii=False, indent=2)\n        print(f\"✅ 已备份: {backup_file.name}\")\n        \n        # 保存修复后的配置\n        config_data[\"last_updated\"] = \"2025-06-25T11:20:00\"\n        with open(field_mappings_file, 'w', encoding='utf-8') as f:\n            json.dump(config_data, f, ensure_ascii=False, indent=2)\n        \n        print(f\"✅ 配置已保存\")\n        \n    except Exception as e:\n        print(f\"❌ 保存失败: {e}\")\n        return False\n    \n    print(f\"\\n🎉 字段映射修复完成！\")\n    print(f\"\\n修复内容：\")\n    print(f\"- 将所有2016年表的字段映射从中文->中文改为英文->中文\")\n    print(f\"- 使用标准的数据库字段名作为源字段\")\n    print(f\"- 映射到用户友好的中文显示名称\")\n    \n    print(f\"\\n请重新启动系统测试表头显示效果。\")\n    return True\n\nif __name__ == \"__main__\":\n    success = fix_field_mapping_mismatch()\n    \n    if success:\n        print(f\"\\n✅ 修复完成！现在2016年的表应该能正确显示中文表头了。\")\n    else:\n        print(f\"\\n❌ 修复失败！请检查错误信息。\")\n 