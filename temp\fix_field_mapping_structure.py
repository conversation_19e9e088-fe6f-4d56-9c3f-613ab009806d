#!/usr/bin/env python3
"""
修复字段映射配置文件结构
移除JSON根级别的重复2018年表映射配置
"""

import json
import os
from datetime import datetime

def fix_field_mapping_structure():
    """修复字段映射配置文件的JSON结构问题"""
    
    field_mappings_path = "state/data/field_mappings.json"
    backup_path = f"state/data/field_mappings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    print(f"开始修复字段映射配置文件结构: {field_mappings_path}")
    
    # 读取当前配置
    try:
        with open(field_mappings_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✓ 成功读取配置文件")
    except Exception as e:
        print(f"✗ 读取配置文件失败: {e}")
        return False
    
    # 备份原始文件
    try:
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"✓ 配置文件已备份到: {backup_path}")
    except Exception as e:
        print(f"✗ 备份失败: {e}")
        return False
    
    # 检查并修复结构
    fixed_count = 0
    problematic_keys = []
    
    # 查找根级别的2018年表映射配置
    for key in list(config.keys()):
        if key.startswith('salary_data_2018_06_') and key != 'table_mappings' and key != 'last_updated':
            problematic_keys.append(key)
    
    print(f"发现 {len(problematic_keys)} 个根级别的重复映射配置:")
    for key in problematic_keys:
        print(f"  - {key}")
    
    # 移除根级别的重复配置
    for key in problematic_keys:
        del config[key]
        fixed_count += 1
        print(f"✓ 已移除根级别配置: {key}")
    
    # 验证table_mappings内的2018年配置是否存在且正确
    if 'table_mappings' in config:
        table_mappings = config['table_mappings']
        
        required_2018_tables = [
            'salary_data_2018_06_a_grade_employees',
            'salary_data_2018_06_active_employees', 
            'salary_data_2018_06_pension_employees',
            'salary_data_2018_06_retired_employees'
        ]
        
        print("\n检查table_mappings内的2018年表配置:")
        for table_name in required_2018_tables:
            if table_name in table_mappings:
                field_count = len(table_mappings[table_name].get('field_mappings', {}))
                print(f"✓ {table_name}: {field_count} 个字段映射")
            else:
                print(f"✗ 缺失配置: {table_name}")
    
    # 更新last_updated时间戳
    config['last_updated'] = datetime.now().isoformat()
    
    # 保存修复后的配置
    try:
        with open(field_mappings_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"\n✓ 配置文件结构修复完成，共修复 {fixed_count} 个问题")
        return True
    except Exception as e:
        print(f"✗ 保存修复后的配置失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("字段映射配置文件结构修复工具")
    print("=" * 60)
    
    success = fix_field_mapping_structure()
    
    if success:
        print("\n🎉 修复完成！请重启系统测试表头显示。")
    else:
        print("\n❌ 修复失败，请检查错误信息。") 