#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射系统根本性修复脚本

解决新导入数据表头仍显示英文（数据库列名）的根本问题

问题分析：
1. 主界面的_apply_field_mapping_to_dataframe方法逻辑有缺陷
2. ConfigSyncManager.load_mapping返回的数据格式不统一
3. 新导入时自动生成的映射配置没有被正确应用到主界面显示
4. 存在新旧两套配置格式，导致读取错误

修复策略：
1. 统一配置格式，清理旧格式数据
2. 修复主界面字段映射应用逻辑
3. 确保新导入数据能自动应用字段映射
4. 解决中文编码问题
"""

import sys
import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger
from src.core.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager

class FieldMappingSystemFixer:
    """字段映射系统修复器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # 初始化管理器
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.table_manager = DynamicTableManager(self.db_manager)
        self.config_sync_manager = ConfigSyncManager()
        
        # 配置路径
        self.config_path = Path("state/data/field_mappings.json")
        self.backup_path = Path("state/data/backups")
        
        # 修复统计
        self.fix_stats = {
            "tables_processed": 0,
            "mappings_fixed": 0,
            "encoding_issues_fixed": 0,
            "format_unified": 0,
            "backup_created": False
        }
        
    def run_complete_fix(self):
        """运行完整的修复流程"""
        try:
            self.logger.info("🚀 开始字段映射系统根本性修复")
            
            # 1. 创建配置备份
            self._create_config_backup()
            
            # 2. 分析当前问题
            self._analyze_current_issues()
            
            # 3. 统一配置格式
            self._unify_config_format()
            
            # 4. 修复编码问题
            self._fix_encoding_issues()
            
            # 5. 生成缺失的映射
            self._generate_missing_mappings()
            
            # 6. 验证修复结果
            self._verify_fix_results()
            
            # 7. 生成主界面修复代码
            self._generate_main_window_fix()
            
            self.logger.info("✅ 字段映射系统修复完成！")
            self._print_fix_summary()
            
        except Exception as e:
            self.logger.error(f"❌ 修复过程中发生错误: {e}")
            raise
    
    def _create_config_backup(self):
        """创建配置文件备份"""
        try:
            if not self.config_path.exists():
                self.logger.warning("配置文件不存在，跳过备份")
                return
            
            # 确保备份目录存在
            self.backup_path.mkdir(parents=True, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_path / f"field_mappings_before_fix_{timestamp}.json"
            
            shutil.copy2(self.config_path, backup_file)
            self.fix_stats["backup_created"] = True
            
            self.logger.info(f"✅ 配置备份已创建: {backup_file}")
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            
    def _analyze_current_issues(self):
        """分析当前配置文件存在的问题"""
        try:
            self.logger.info("🔍 分析当前配置问题...")
            
            if not self.config_path.exists():
                self.logger.warning("配置文件不存在")
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            issues = []
            
            # 检查格式问题
            root_mappings = {k: v for k, v in config.items() 
                           if k not in ['table_mappings', 'last_updated']}
            if root_mappings:
                issues.append(f"发现旧格式映射: {len(root_mappings)} 个表")
            
            # 检查新格式
            table_mappings = config.get('table_mappings', {})
            if table_mappings:
                issues.append(f"新格式映射: {len(table_mappings)} 个表")
            
            # 检查编码问题
            encoding_issues = 0
            for key in config.keys():
                if '宸ュ彿' in key or '濮撳悕' in key:  # 检测乱码字符
                    encoding_issues += 1
            
            if encoding_issues > 0:
                issues.append(f"编码问题: {encoding_issues} 个项目")
            
            self.logger.info(f"发现问题: {'; '.join(issues) if issues else '无明显问题'}")
            
        except Exception as e:
            self.logger.error(f"分析配置问题失败: {e}")
    
    def _unify_config_format(self):
        """统一配置格式到新版本"""
        try:
            self.logger.info("🔧 统一配置格式...")
            
            if not self.config_path.exists():
                # 创建新的空配置
                unified_config = {
                    "table_mappings": {},
                    "last_updated": datetime.now().isoformat()
                }
            else:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    old_config = json.load(f)
                
                # 创建新格式配置
                unified_config = {
                    "table_mappings": {},
                    "last_updated": datetime.now().isoformat()
                }
                
                # 保留新格式的映射
                if 'table_mappings' in old_config:
                    unified_config['table_mappings'] = old_config['table_mappings']
                
                # 转换旧格式映射
                old_mappings = {k: v for k, v in old_config.items() 
                              if k not in ['table_mappings', 'last_updated'] and isinstance(v, dict)}
                
                for table_name, mapping in old_mappings.items():
                    if table_name not in unified_config['table_mappings']:
                        unified_config['table_mappings'][table_name] = {
                            "metadata": {
                                "created_at": datetime.now().isoformat(),
                                "last_modified": datetime.now().isoformat(),
                                "auto_generated": True,
                                "user_modified": False,
                                "migrated_from_old_format": True
                            },
                            "field_mappings": mapping,
                            "edit_history": []
                        }
                        self.fix_stats["format_unified"] += 1
            
            # 保存统一后的配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(unified_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 配置格式统一完成，迁移了 {self.fix_stats['format_unified']} 个表的映射")
            
        except Exception as e:
            self.logger.error(f"统一配置格式失败: {e}")
    
    def _fix_encoding_issues(self):
        """修复编码问题"""
        try:
            self.logger.info("🔧 修复编码问题...")
            
            # 定义乱码到正确中文的映射
            encoding_fixes = {
                "宸ュ彿": "工号",
                "濮撳悕": "姓名", 
                "閮ㄩ棬鍚嶇О": "部门名称",
                "閮ㄩ棬": "部门",
                "宀椾綅宸ヨ祫": "岗位工资",
                "钖骇宸ヨ祫": "薪级工资",
                "娲ヨ创琛ヨ创": "津贴补贴",
                "娲ヨ创": "津贴",
                "濂栭噾": "奖金",
                "搴斿彂鍚堣": "应发合计",
                "搴斿彂宸ヨ祫": "应发工资",
                "浜旈櫓涓€閲戜釜浜?": "五险一金个人",
                "缁╂晥宸ヨ祫": "绩效工资",
                "鍩烘湰宸ヨ祫": "基本工资",
                "鍩虹钖祫": "基础薪资",
                "缁╂晥濂栭噾": "绩效奖金",
                "鍔犵彮璐?": "加班费",
                "鎵ｉ櫎椤?": "扣除项",
                "鏈堜唤": "月份",
                "骞翠唤": "年份",
                "鍒涘缓鏃堕棿": "创建时间",
                "鏇存柊鏃堕棿": "更新时间",
                "韬唤璇佸彿": "身份证号",
                "鑱屼綅": "职位",
                "鎵€灞為儴闂?": "所属部门",
                "鍛樺伐缂栧彿": "员工编号",
                "鍛樺伐濮撳悕": "员工姓名",
                "搴忓彿": "序号"
            }
            
            if not self.config_path.exists():
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 修复table_mappings中的编码问题
            table_mappings = config.get('table_mappings', {})
            fixed_table_mappings = {}
            
            for table_name, table_data in table_mappings.items():
                # 修复表名
                fixed_table_name = table_name
                for garbled, correct in encoding_fixes.items():
                    if garbled in fixed_table_name:
                        fixed_table_name = fixed_table_name.replace(garbled, correct)
                
                # 修复字段映射
                field_mappings = table_data.get('field_mappings', {})
                fixed_field_mappings = {}
                
                for field_key, field_value in field_mappings.items():
                    # 修复键名
                    fixed_key = field_key
                    for garbled, correct in encoding_fixes.items():
                        if garbled in fixed_key:
                            fixed_key = fixed_key.replace(garbled, correct)
                    
                    # 修复值
                    fixed_value = field_value
                    for garbled, correct in encoding_fixes.items():
                        if garbled in fixed_value:
                            fixed_value = fixed_value.replace(garbled, correct)
                            self.fix_stats["encoding_issues_fixed"] += 1
                    
                    fixed_field_mappings[fixed_key] = fixed_value
                
                # 重建表数据
                fixed_table_data = table_data.copy()
                fixed_table_data['field_mappings'] = fixed_field_mappings
                fixed_table_data['metadata']['last_modified'] = datetime.now().isoformat()
                
                fixed_table_mappings[fixed_table_name] = fixed_table_data
            
            # 更新配置
            config['table_mappings'] = fixed_table_mappings
            config['last_updated'] = datetime.now().isoformat()
            
            # 保存修复后的配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 编码问题修复完成，修复了 {self.fix_stats['encoding_issues_fixed']} 个字段")
            
        except Exception as e:
            self.logger.error(f"修复编码问题失败: {e}")
    
    def _generate_missing_mappings(self):
        """为数据库中存在但配置中缺失的表生成字段映射"""
        try:
            self.logger.info("🔧 生成缺失的字段映射...")
            
            # 获取数据库中的所有表
            db_tables = self.table_manager.get_all_table_names()
            
            # 加载当前配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            table_mappings = config.get('table_mappings', {})
            
            # 标准字段映射规则
            standard_mappings = {
                'id': '序号',
                'employee_id': '工号', 
                'employee_name': '姓名',
                'department': '部门',
                'position': '职位',
                'basic_salary': '基本工资',
                'position_salary': '岗位工资',
                'grade_salary': '薪级工资',
                'performance_bonus': '绩效工资',
                'allowance': '津贴补贴',
                'overtime_pay': '加班费',
                'deduction': '扣除项',
                'total_salary': '应发合计',
                'id_card': '身份证号',
                'month': '月份',
                'year': '年份',
                'created_at': '创建时间',
                'updated_at': '更新时间',
                'data_source': '数据来源',
                'import_time': '导入时间'
            }
            
            for table_name in db_tables:
                if table_name not in table_mappings:
                    # 获取表的列信息
                    try:
                        columns = self.table_manager.get_table_columns(table_name)
                        if not columns:
                            continue
                        
                        # 生成字段映射
                        field_mappings = {}
                        for col in columns:
                            col_name = col['name']
                            # 使用标准映射或保持原名
                            field_mappings[col_name] = standard_mappings.get(col_name, col_name)
                        
                        # 创建表映射配置
                        table_mappings[table_name] = {
                            "metadata": {
                                "created_at": datetime.now().isoformat(),
                                "last_modified": datetime.now().isoformat(),
                                "auto_generated": True,
                                "user_modified": False,
                                "generated_by_fix": True
                            },
                            "field_mappings": field_mappings,
                            "edit_history": []
                        }
                        
                        self.fix_stats["mappings_fixed"] += 1
                        self.fix_stats["tables_processed"] += 1
                        
                        self.logger.info(f"为表 {table_name} 生成了 {len(field_mappings)} 个字段映射")
                        
                    except Exception as e:
                        self.logger.error(f"为表 {table_name} 生成映射失败: {e}")
            
            # 保存更新后的配置
            config['table_mappings'] = table_mappings
            config['last_updated'] = datetime.now().isoformat()
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 缺失映射生成完成，处理了 {self.fix_stats['tables_processed']} 个表")
            
        except Exception as e:
            self.logger.error(f"生成缺失映射失败: {e}")
    
    def _verify_fix_results(self):
        """验证修复结果"""
        try:
            self.logger.info("🔍 验证修复结果...")
            
            # 验证配置文件格式
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必需的顶级键
            required_keys = ['table_mappings', 'last_updated']
            missing_keys = [key for key in required_keys if key not in config]
            
            if missing_keys:
                self.logger.warning(f"配置文件缺少必需键: {missing_keys}")
                return False
            
            # 检查每个表的映射格式
            table_mappings = config['table_mappings']
            valid_tables = 0
            
            for table_name, table_data in table_mappings.items():
                if all(key in table_data for key in ['metadata', 'field_mappings', 'edit_history']):
                    valid_tables += 1
                else:
                    self.logger.warning(f"表 {table_name} 的映射格式不完整")
            
            self.logger.info(f"✅ 验证完成: {valid_tables}/{len(table_mappings)} 个表的格式正确")
            
            # 测试ConfigSyncManager加载
            try:
                test_manager = ConfigSyncManager()
                all_tables = test_manager.get_all_table_names()
                self.logger.info(f"✅ ConfigSyncManager测试成功，可加载 {len(all_tables)} 个表的映射")
            except Exception as e:
                self.logger.error(f"ConfigSyncManager测试失败: {e}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证修复结果失败: {e}")
            return False
    
    def _generate_main_window_fix(self):
        """生成主界面代码修复文件"""
        try:
            self.logger.info("🔧 生成主界面代码修复...")
            
            fix_code = '''# 主界面字段映射修复代码
# 需要修改 src/gui/prototype/prototype_main_window.py 文件

def _apply_field_mapping_to_dataframe_fixed(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
    """修复版本：对数据框应用字段映射，将数据库列名转换为中文显示名"""
    try:
        # 使用统一的ConfigSyncManager获取字段映射
        if hasattr(self, 'config_sync_manager'):
            field_mapping = self.config_sync_manager.load_mapping(table_name)
        else:
            field_mapping = self.field_mappings.get(table_name, {})
        
        if not field_mapping:
            self.logger.info(f"表 {table_name} 没有保存的字段映射信息")
            return df
        
        # 当前数据框的列名
        current_columns = list(df.columns)
        
        # 创建列名映射：当前列名 -> 映射后的显示名
        column_rename_map = {}
        
        for db_col in current_columns:
            # 直接从字段映射中查找显示名称
            if db_col in field_mapping:
                display_name = field_mapping[db_col]
                if display_name and display_name != db_col:  # 确保显示名不为空且不同于原名
                    column_rename_map[db_col] = display_name
        
        # 应用列名映射
        if column_rename_map:
            df_renamed = df.rename(columns=column_rename_map)
            self.logger.info(f"应用字段映射到表 {table_name}: {len(column_rename_map)} 个字段重命名")
            self.logger.debug(f"列名映射: {column_rename_map}")
            return df_renamed
        else:
            self.logger.info(f"表 {table_name} 无需字段重命名")
            return df
            
    except Exception as e:
        self.logger.error(f"应用字段映射失败: {e}", exc_info=True)
        return df

# 使用方法：
# 1. 将上述代码中的 _apply_field_mapping_to_dataframe_fixed 方法
# 2. 替换原文件中的 _apply_field_mapping_to_dataframe 方法
# 3. 重启系统测试
'''
            
            # 保存修复代码
            patch_file = Path("temp/main_window_field_mapping_fix.py")
            with open(patch_file, 'w', encoding='utf-8') as f:
                f.write(fix_code)
            
            self.logger.info(f"✅ 主界面修复代码已生成: {patch_file}")
            
        except Exception as e:
            self.logger.error(f"生成主界面修复代码失败: {e}")
    
    def _print_fix_summary(self):
        """打印修复总结"""
        print("\n" + "="*80)
        print("🎉 字段映射系统修复总结")
        print("="*80)
        print(f"📊 处理表数量: {self.fix_stats['tables_processed']}")
        print(f"🔧 修复映射数量: {self.fix_stats['mappings_fixed']}")
        print(f"🌟 编码问题修复: {self.fix_stats['encoding_issues_fixed']}")
        print(f"📝 格式统一: {self.fix_stats['format_unified']}")
        print(f"💾 备份创建: {'是' if self.fix_stats['backup_created'] else '否'}")
        print("\n📋 后续操作建议:")
        print("1. 运行系统测试字段映射是否正常工作")
        print("2. 如有问题，查看生成的修复代码文件")
        print("="*80)

def main():
    """主函数"""
    print("🚀 字段映射系统根本性修复工具")
    print("=" * 50)
    
    fixer = FieldMappingSystemFixer()
    fixer.run_complete_fix()

if __name__ == "__main__":
    main() 