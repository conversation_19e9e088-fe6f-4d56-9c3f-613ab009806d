#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\n修复表头显示问题的脚本\n\n问题：VirtualizedExpandableTable在set_data时直接使用原始英文字段名\n解决：在设置表头后立即应用字段映射\n\"\"\"\n\nimport sys\nimport os\nfrom pathlib import Path\n\n# 添加项目根目录到Python路径\nproject_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\n\ndef fix_header_display_issue():\n    \"\"\"修复表头显示问题\"\"\"\n    print(\"=\" * 60)\n    print(\"修复表头显示问题\")\n    print(\"=\" * 60)\n    \n    # 1. 备份原文件\n    target_file = project_root / \"src\" / \"gui\" / \"prototype\" / \"widgets\" / \"virtualized_expandable_table.py\"\n    backup_file = project_root / \"temp\" / \"virtualized_expandable_table_backup.py\"\n    \n    print(f\"\\n1. 备份原文件...\")\n    if not backup_file.parent.exists():\n        backup_file.parent.mkdir(parents=True, exist_ok=True)\n    \n    try:\n        with open(target_file, 'r', encoding='utf-8') as f:\n            content = f.read()\n        \n        with open(backup_file, 'w', encoding='utf-8') as f:\n            f.write(content)\n        \n        print(f\"✅ 备份完成: {backup_file}\")\n    except Exception as e:\n        print(f\"❌ 备份失败: {e}\")\n        return False\n    \n    # 2. 修复set_data方法\n    print(f\"\\n2. 修复set_data方法...\")\n    \n    try:\n        # 找到需要修复的代码块\n        old_code = '''# 设置表头\n            self.setColumnCount(len(headers))\n            self.setHorizontalHeaderLabels(headers)'''\n        \n        new_code = '''# 设置表头\n            self.setColumnCount(len(headers))\n            self.setHorizontalHeaderLabels(headers)\n            \n            # 应用字段映射到表头显示\n            if hasattr(self, 'current_table_name') and self.current_table_name:\n                self._refresh_header_display()'''\n        \n        # 执行替换\n        if old_code in content:\n            new_content = content.replace(old_code, new_code)\n            \n            # 写回文件\n            with open(target_file, 'w', encoding='utf-8') as f:\n                f.write(new_content)\n            \n            print(f\"✅ set_data方法修复完成\")\n        else:\n            print(f\"⚠️  未找到需要修复的代码块，可能已经修复或代码结构有变化\")\n            return False\n    \n    except Exception as e:\n        print(f\"❌ 修复失败: {e}\")\n        # 恢复备份\n        try:\n            with open(backup_file, 'r', encoding='utf-8') as f:\n                content = f.read()\n            with open(target_file, 'w', encoding='utf-8') as f:\n                f.write(content)\n            print(f\"已恢复备份文件\")\n        except:\n            pass\n        return False\n    \n    print(f\"\\n3. 修复完成！\")\n    print(f\"\\n修复内容：\")\n    print(f\"- 在VirtualizedExpandableTable.set_data()方法中\")\n    print(f\"- 设置表头后立即调用_refresh_header_display()\")\n    print(f\"- 这样就会应用字段映射到表头显示\")\n    \n    return True\n\nif __name__ == \"__main__\":\n    success = fix_header_display_issue()\n    \n    if success:\n        print(f\"\\n🎉 修复成功！请重新运行系统测试表头显示是否正常。\")\n    else:\n        print(f\"\\n❌ 修复失败！请检查错误信息并手动修复。\")\n 