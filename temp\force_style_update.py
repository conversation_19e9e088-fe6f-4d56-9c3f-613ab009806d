#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制样式更新脚本
确保现代化界面的蓝色头部正确显示
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def clear_all_caches():
    """清除所有Python缓存"""
    import shutil
    
    cache_dirs = [
        "src/__pycache__",
        "src/gui/__pycache__",
        "src/core/__pycache__",
        "src/modules/__pycache__",
        "src/utils/__pycache__",
        "__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        cache_path = Path(cache_dir)
        if cache_path.exists():
            shutil.rmtree(cache_path)
            print(f"✓ 清除缓存: {cache_dir}")

def test_modern_styles():
    """测试现代化样式"""
    try:
        from src.gui.modern_style import MaterialDesignPalette, ModernStyleSheet
        
        # 测试配色方案
        print("配色方案测试:")
        print(f"  主色调: {MaterialDesignPalette.PRIMARY['main']}")
        print(f"  次要色调: {MaterialDesignPalette.SECONDARY['main']}")
        print(f"  背景色: {MaterialDesignPalette.BACKGROUND['default']}")
        
        # 测试样式表
        complete_style = ModernStyleSheet.get_complete_style()
        print(f"\n完整样式表长度: {len(complete_style)} 字符")
        
        # 检查关键样式
        key_styles = {
            "蓝色渐变": "#1976D2" in complete_style,
            "青色渐变": "#00BCD4" in complete_style, 
            "线性渐变": "qlineargradient" in complete_style,
            "圆角边框": "border-radius" in complete_style,
            "现代字体": "Segoe UI" in complete_style
        }
        
        print("\n关键样式检查:")
        for style, exists in key_styles.items():
            status = "✓" if exists else "❌"
            print(f"  {status} {style}")
        
        return all(key_styles.values())
        
    except Exception as e:
        print(f"❌ 样式测试失败: {e}")
        return False

def launch_with_forced_styles():
    """启动程序并强制应用样式"""
    try:
        print("正在启动现代化界面...")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        
        # 导入现代化主窗口
        from src.gui.modern_main_window import ModernMainWindow
        
        # 创建主窗口
        window = ModernMainWindow()
        
        # 强制重新应用样式
        def force_style_refresh():
            try:
                from src.gui.modern_style import ModernStyleSheet
                complete_style = ModernStyleSheet.get_complete_style()
                window.setStyleSheet("")  # 先清空
                window.setStyleSheet(complete_style)  # 重新应用
                
                # 强制刷新所有子组件
                for child in window.findChildren(object):
                    if hasattr(child, 'update'):
                        child.update()
                
                window.update()
                window.repaint()
                
                print("✓ 强制样式刷新完成")
                
                # 检查头部组件
                if hasattr(window, 'header_widget'):
                    header_style = window.header_widget.styleSheet()
                    if "qlineargradient" in header_style:
                        print("✓ 头部蓝色渐变样式已应用")
                    else:
                        print("⚠ 头部渐变样式可能未正确应用")
                
            except Exception as e:
                print(f"❌ 强制样式刷新失败: {e}")
        
        # 延迟执行样式刷新
        QTimer.singleShot(1000, force_style_refresh)
        
        # 显示窗口
        window.show()
        
        print("✓ 现代化界面已启动")
        print("请检查窗口顶部是否显示蓝色渐变头部")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("强制样式更新脚本")
    print("=" * 50)
    
    # 1. 清除缓存
    print("1. 清除Python缓存...")
    clear_all_caches()
    print()
    
    # 2. 测试样式
    print("2. 测试现代化样式...")
    style_ok = test_modern_styles()
    print()
    
    if not style_ok:
        print("❌ 样式测试失败，请检查样式文件")
        return 1
    
    # 3. 启动程序
    print("3. 启动现代化界面...")
    return launch_with_forced_styles()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 