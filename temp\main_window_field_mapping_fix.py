# 主界面字段映射修复代码
# 需要修改 src/gui/prototype/prototype_main_window.py 文件

def _apply_field_mapping_to_dataframe_fixed(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
    """修复版本：对数据框应用字段映射，将数据库列名转换为中文显示名"""
    try:
        # 使用统一的ConfigSyncManager获取字段映射
        if hasattr(self, 'config_sync_manager'):
            field_mapping = self.config_sync_manager.load_mapping(table_name)
        else:
            field_mapping = self.field_mappings.get(table_name, {})
        
        if not field_mapping:
            self.logger.info(f"表 {table_name} 没有保存的字段映射信息")
            return df
        
        # 当前数据框的列名
        current_columns = list(df.columns)
        
        # 创建列名映射：当前列名 -> 映射后的显示名
        column_rename_map = {}
        
        for db_col in current_columns:
            # 直接从字段映射中查找显示名称
            if db_col in field_mapping:
                display_name = field_mapping[db_col]
                if display_name and display_name != db_col:  # 确保显示名不为空且不同于原名
                    column_rename_map[db_col] = display_name
        
        # 应用列名映射
        if column_rename_map:
            df_renamed = df.rename(columns=column_rename_map)
            self.logger.info(f"应用字段映射到表 {table_name}: {len(column_rename_map)} 个字段重命名")
            self.logger.debug(f"列名映射: {column_rename_map}")
            return df_renamed
        else:
            self.logger.info(f"表 {table_name} 无需字段重命名")
            return df
            
    except Exception as e:
        self.logger.error(f"应用字段映射失败: {e}", exc_info=True)
        return df

# 使用方法：
# 1. 将上述代码中的 _apply_field_mapping_to_dataframe_fixed 方法
# 2. 替换原文件中的 _apply_field_mapping_to_dataframe 方法
# 3. 重启系统测试
