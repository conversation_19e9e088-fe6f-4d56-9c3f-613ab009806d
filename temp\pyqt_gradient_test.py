"""
PyQt5渐变背景技术验证测试
验证QPainter + QLinearGradient在PyQt5中的实现可行性
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt, QRect
from PyQt5.QtGui import QPainter, QLinearGradient, QColor, QFont

class GradientTestWidget(QWidget):
    """渐变背景测试组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(80)
        
        # 创建布局和内容
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # 标题标签
        title_label = QLabel("PyQt5 渐变背景技术验证")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white; background: transparent;")
        layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("Material Design 蓝绿渐变 (#4285F4 → #34A853)")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); background: transparent; font-size: 10pt;")
        layout.addWidget(subtitle_label)
    
    def paintEvent(self, event):
        """自定义绘制事件 - 实现渐变背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing, True)
        
        # 创建线性渐变
        gradient = QLinearGradient(0, 0, self.width(), 0)  # 水平渐变
        gradient.setColorAt(0, QColor("#4285F4"))  # 蓝色起点
        gradient.setColorAt(1, QColor("#34A853"))  # 绿色终点
        
        # 设置画刷并填充整个区域
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawRect(self.rect())
        
        painter.end()

class MaterialDesignStyleTestWidget(QWidget):
    """Material Design样式表测试组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 测试标题
        title = QLabel("Material Design 样式表测试")
        title.setObjectName("TestTitle")
        layout.addWidget(title)
        
        # 测试按钮组
        test_buttons = []
        button_configs = [
            ("Primary Button", "primary"),
            ("Secondary Button", "secondary"),
            ("Success Button", "success"),
            ("Warning Button", "warning"),
            ("Error Button", "error")
        ]
        
        for text, style_class in button_configs:
            from PyQt5.QtWidgets import QPushButton
            btn = QPushButton(text)
            btn.setObjectName(f"test_button_{style_class}")
            btn.setProperty("class", f"MaterialButton-{style_class}")
            layout.addWidget(btn)
        
        # 应用Material Design样式表
        self.setStyleSheet(self._get_material_design_stylesheet())
    
    def _get_material_design_stylesheet(self):
        """获取Material Design样式表"""
        return """
        QLabel#TestTitle {
            font-size: 16pt;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 10px;
        }
        
        QPushButton[class~="MaterialButton-primary"] {
            background-color: #4285F4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 11pt;
            font-weight: 500;
        }
        
        QPushButton[class~="MaterialButton-primary"]:hover {
            background-color: #3367D6;
        }
        
        QPushButton[class~="MaterialButton-primary"]:pressed {
            background-color: #2E57C7;
        }
        
        QPushButton[class~="MaterialButton-secondary"] {
            background-color: #F5F5F5;
            color: #5F6368;
            border: 1px solid #DADCE0;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 11pt;
            font-weight: 500;
        }
        
        QPushButton[class~="MaterialButton-secondary"]:hover {
            background-color: #E8F0FE;
            border-color: #4285F4;
        }
        
        QPushButton[class~="MaterialButton-success"] {
            background-color: #34A853;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 11pt;
            font-weight: 500;
        }
        
        QPushButton[class~="MaterialButton-success"]:hover {
            background-color: #2E7D32;
        }
        
        QPushButton[class~="MaterialButton-warning"] {
            background-color: #FBBC04;
            color: #202124;
            border: none;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 11pt;
            font-weight: 500;
        }
        
        QPushButton[class~="MaterialButton-warning"]:hover {
            background-color: #F9AB00;
        }
        
        QPushButton[class~="MaterialButton-error"] {
            background-color: #EA4335;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 11pt;
            font-weight: 500;
        }
        
        QPushButton[class~="MaterialButton-error"]:hover {
            background-color: #D93025;
        }
        """

class TechnicalValidationWindow(QMainWindow):
    """技术验证测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt5重构项目 - 技术验证测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 添加渐变背景测试
        gradient_test = GradientTestWidget()
        main_layout.addWidget(gradient_test)
        
        # 添加Material Design样式测试
        style_test = MaterialDesignStyleTestWidget()
        main_layout.addWidget(style_test)
        
        # 状态信息
        from PyQt5.QtWidgets import QTextEdit
        status_text = QTextEdit()
        status_text.setMaximumHeight(150)
        status_text.setReadOnly(True)
        status_text.append("🚀 PyQt5技术验证测试启动")
        status_text.append("✅ 渐变背景: QPainter + QLinearGradient 实现成功")
        status_text.append("✅ Material Design样式表: 可用性验证成功")
        status_text.append("✅ 响应式布局: QVBoxLayout/QHBoxLayout 基础支持")
        status_text.append("⚠️  需要进一步验证: 复杂交互组件、性能优化")
        main_layout.addWidget(status_text)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TechnicalValidationWindow()
    window.show()
    
    print("🚀 PyQt5技术验证测试启动")
    print("✅ 渐变背景: QPainter + QLinearGradient")
    print("✅ Material Design: QStyleSheet支持")
    print("✅ 基础组件: QWidget, QLayout正常")
    print("📊 测试窗口已打开，验证视觉效果...")
    
    # 运行事件循环
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 