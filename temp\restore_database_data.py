#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""数据库数据恢复脚本 - 重新导入现有的Excel数据"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_import.excel_importer import ExcelImporter

def restore_database_data():
    """恢复数据库数据"""
    
    print("🔄 开始数据库数据恢复...")
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        excel_importer = ExcelImporter()
        
        # 检查数据目录中的Excel文件
        data_dir = Path("data")
        excel_files = []
        
        # 查找工资表文件
        salary_dir = data_dir / "工资表"
        if salary_dir.exists():
            for file in salary_dir.glob("*.xls*"):
                excel_files.append(("salary", file))
                print(f"📁 找到工资表文件: {file}")
        
        # 查找异动人员文件  
        change_dir = data_dir / "异动人员"
        if change_dir.exists():
            for file in change_dir.glob("*.xls*"):
                excel_files.append(("change", file))
                print(f"📁 找到异动文件: {file}")
        
        if not excel_files:
            print("❌ 未找到可导入的Excel文件")
            print("请确保以下目录中有Excel文件：")
            print("  - data/工资表/")
            print("  - data/异动人员/")
            return False
        
        # 导入文件
        imported_count = 0
        for file_type, file_path in excel_files:
            try:
                print(f"\n📊 开始导入: {file_path.name}")
                
                # 读取Excel文件
                result = excel_importer.import_excel_file(str(file_path))
                
                if result["success"]:
                    # 获取导入的数据
                    imported_data = result["data"]
                    
                    if imported_data and not imported_data.empty:
                        # 生成表名
                        if file_type == "salary":
                            # 从文件名解析年月（假设格式包含年月信息）
                            file_name = file_path.stem
                            if "2025" in file_name and "5" in file_name:
                                table_name = "salary_data_2025_05_active_employees"
                            else:
                                table_name = "salary_data_2025_05_imported_data"
                        else:
                            table_name = "salary_changes_2025_05_data"
                        
                        # 保存到数据库
                        save_success, save_message = table_manager.save_dataframe_to_table(
                            imported_data, table_name
                        )
                        
                        if save_success:
                            print(f"✅ 成功导入到表: {table_name}")
                            print(f"   记录数: {len(imported_data)}")
                            imported_count += 1
                        else:
                            print(f"❌ 保存到数据库失败: {save_message}")
                    else:
                        print(f"⚠️  文件 {file_path.name} 没有有效数据")
                else:
                    print(f"❌ 导入文件失败: {result.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ 处理文件 {file_path.name} 时出错: {e}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
        
        print(f"\n🎉 数据恢复完成！成功导入 {imported_count} 个文件")
        
        # 验证导入结果
        print("\n📋 验证导入结果...")
        all_tables = table_manager.get_table_list()
        salary_tables = table_manager.get_table_list('salary_data')
        
        print(f"💾 数据库中总表数: {len(all_tables)}")
        print(f"💰 工资数据表数: {len(salary_tables)}")
        
        if salary_tables:
            print("📊 工资数据表列表:")
            for table_info in salary_tables:
                table_name = table_info.get('table_name', 'Unknown')
                try:
                    count = table_manager.get_table_record_count(table_name)
                    print(f"  - {table_name}: {count} 条记录")
                except Exception as e:
                    print(f"  - {table_name}: 记录数查询失败 - {e}")
        
        return imported_count > 0
        
    except Exception as e:
        print(f"❌ 数据恢复过程失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = restore_database_data()
    if success:
        print("\n✅ 数据恢复成功！现在可以重新启动系统了。")
    else:
        print("\n❌ 数据恢复失败，请检查错误信息并手动导入数据。") 