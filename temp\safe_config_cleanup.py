#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全清理配置文件脚本
"""

import json
import shutil
from pathlib import Path
from datetime import datetime

def backup_config():
    """备份配置文件"""
    config_path = "state/data/field_mappings.json"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"state/data/backups/field_mappings_before_cleanup_{timestamp}.json"
    
    Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(config_path, backup_path)
    print(f"✅ 配置文件已备份到: {backup_path}")
    
    return backup_path

def clean_config():
    """清理配置文件"""
    config_path = "state/data/field_mappings.json"
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 保留的表（生产环境表）
    table_mappings = config.get("table_mappings", {})
    cleaned_mappings = {}
    
    for table_name, table_config in table_mappings.items():
        # 跳过测试表
        if any(keyword in table_name.lower() for keyword in ['test', 'temp', 'demo', 'sample']):
            print(f"🗑️ 清理测试表: {table_name}")
            continue
        
        # 保留生产表，但清理编辑历史
        table_config_clean = table_config.copy()
        edit_history = table_config_clean.get("edit_history", [])
        if len(edit_history) > 5:  # 只保留最近5条编辑记录
            table_config_clean["edit_history"] = edit_history[-5:]
            print(f"📝 压缩编辑历史: {table_name} ({len(edit_history)} -> 5)")
        
        cleaned_mappings[table_name] = table_config_clean
    
    # 更新配置
    config["table_mappings"] = cleaned_mappings
    config["last_updated"] = datetime.now().isoformat()
    
    # 保存清理后的配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 配置文件清理完成")

if __name__ == "__main__":
    print("🧹 开始清理配置文件...")
    backup_path = backup_config()
    clean_config()
    print(f"\n✨ 清理完成！如需恢复，请使用备份文件: {backup_path}")
