#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的表头映射调试脚本

快速找出为什么表头显示英文而不是中文
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def debug_simple():
    """简单调试字段映射"""
    print("=" * 50)
    print("简化调试：表头显示英文问题")
    print("=" * 50)
    
    # 1. 检查字段映射文件是否存在
    field_mappings_file = project_root / "state" / "data" / "field_mappings.json"
    print(f"字段映射文件：{field_mappings_file}")
    print(f"文件存在：{field_mappings_file.exists()}")
    
    if not field_mappings_file.exists():
        print("❌ 致命错误：字段映射文件不存在！")
        return
    
    # 2. 读取映射数据
    try:
        with open(field_mappings_file, 'r', encoding='utf-8') as f:
            mappings_data = json.load(f)
        
        table_mappings = mappings_data.get('table_mappings', {})
        print(f"✅ 映射文件读取成功，包含 {len(table_mappings)} 个表")
        
        # 3. 检查一个具体的表映射
        print("\n检查具体的表映射：")
        if 'salary_data_2025_05_active_employees' in table_mappings:
            table_name = 'salary_data_2025_05_active_employees'
            field_mapping = table_mappings[table_name].get('field_mappings', {})
            print(f"表名：{table_name}")
            print(f"字段映射：{field_mapping}")
            
            # 检查是否有中英文映射
            for eng_field, chn_field in field_mapping.items():
                if eng_field != chn_field:
                    print(f"  ✅ {eng_field} -> {chn_field}")
                else:
                    print(f"  ⚠️  {eng_field} -> {chn_field} (相同)")
        
        # 4. 测试ConfigSyncManager加载
        print("\n测试ConfigSyncManager：")
        try:
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            
            config_sync = ConfigSyncManager()
            loaded_mapping = config_sync.load_mapping('salary_data_2025_05_active_employees')
            print(f"ConfigSyncManager加载结果：{loaded_mapping}")
            
            if loaded_mapping:
                print("✅ ConfigSyncManager能正常加载映射")
            else:
                print("❌ ConfigSyncManager加载映射失败")
                
        except Exception as e:
            print(f"❌ ConfigSyncManager测试失败：{e}")
        
        # 5. 检查系统运行时的问题
        print("\n=== 问题分析 ===")
        print("根据测试结果，可能的问题：")
        print("1. 数据库表大多为空（0条记录）")
        print("2. 系统可能没有导入真实数据")
        print("3. 或者字段映射的应用逻辑有问题")
        print("4. 表格组件可能没有正确使用映射后的列名")
        
        print("\n=== 建议解决方案 ===")
        print("1. 先确认有实际数据的表")
        print("2. 检查系统加载数据时是否应用了字段映射")
        print("3. 确认表格组件设置表头时使用的列名来源")
        
    except Exception as e:
        print(f"❌ 读取映射文件失败：{e}")

if __name__ == "__main__":
    debug_simple() 