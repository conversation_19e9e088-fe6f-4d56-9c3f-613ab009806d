#!/usr/bin/env python3
"""
月度工资异动处理系统快速启动脚本
"""

import subprocess
import sys
import os

def main():
    print("="*60)
    print("月度工资异动处理系统")
    print("="*60)
    print("正在启动系统...")
    
    try:
        # 启动系统
        result = subprocess.run([sys.executable, 'main.py'], 
                               cwd=os.path.dirname(os.path.abspath(__file__)) + '/..')
        
        print(f"系统退出，退出码: {result.returncode}")
        
    except KeyboardInterrupt:
        print("\n用户中断启动")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == '__main__':
    main()
