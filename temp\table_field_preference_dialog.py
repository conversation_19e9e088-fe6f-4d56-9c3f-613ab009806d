#!/usr/bin/env python3
"""
表级字段显示偏好设置对话框（临时版本）

功能：
1. 针对特定表设置字段显示偏好
2. 替代全局用户偏好设置
3. 支持字段的选择、排序和预览
4. 保存/删除表级偏好配置
"""

import sys
from typing import List, Dict, Optional
from pathlib import Path

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QListWidgetItem,
    QPushButton, QCheckBox, QMessageBox, QSplitter, QTextEdit, QGroupBox,
    QButtonGroup, QScrollArea, QFrame, QGridLayout, QLineEdit, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger
from src.modules.data_import.config_sync_manager import ConfigSyncManager


class TableFieldPreferenceDialog(QDialog):
    """表级字段显示偏好设置对话框"""
    
    # 信号定义
    preference_saved = pyqtSignal(str, list)  # 偏好保存完成信号 (table_name, selected_fields)
    preference_deleted = pyqtSignal(str)      # 偏好删除完成信号 (table_name)
    
    def __init__(self, table_name: str, all_fields: List[str], 
                 field_mappings: Dict[str, str] = None, parent=None):
        """
        初始化表级字段偏好设置对话框
        
        Args:
            table_name: 表名
            all_fields: 表的所有字段列表
            field_mappings: 字段映射 {db_field: display_name}
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.logger = setup_logger(__name__)
        self.table_name = table_name
        self.all_fields = all_fields
        self.field_mappings = field_mappings or {}
        self.config_sync = ConfigSyncManager()
        
        # UI组件
        self.field_checkboxes = {}
        self.preview_area = None
        self.save_button = None
        self.delete_button = None
        self.select_all_button = None
        self.clear_all_button = None
        
        # 当前偏好字段
        self.current_preferred_fields = []
        
        self.setup_ui()
        self.load_current_preference()
        self.connect_signals()
        
        self.logger.info(f"表级字段偏好对话框初始化完成: {table_name}")

    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle(f"字段显示偏好设置 - {self.table_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.resize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 标题区域
        title_label = QLabel(f"设置表 '{self.table_name}' 的字段显示偏好")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("请选择要在主界面列表中显示的字段。未选择的字段仍然存在于数据中，只是不会显示。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        main_layout.addWidget(info_label)
        
        # 分割器：字段选择区 + 预览区
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：字段选择区
        field_selection_widget = self.create_field_selection_area()
        splitter.addWidget(field_selection_widget)
        
        # 右侧：预览区
        preview_widget = self.create_preview_area()
        splitter.addWidget(preview_widget)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 3)  # 字段选择区占3/5
        splitter.setStretchFactor(1, 2)  # 预览区占2/5
        
        # 按钮区域
        button_layout = self.create_button_area()
        main_layout.addLayout(button_layout)
        
        # 设置对话框样式
        self.setStyleSheet(self.get_dialog_styles())

    def create_field_selection_area(self) -> QGroupBox:
        """创建字段选择区域"""
        group_box = QGroupBox("字段选择")
        layout = QVBoxLayout(group_box)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        self.select_all_button = QPushButton("全选")
        self.clear_all_button = QPushButton("清空")
        reset_button = QPushButton("重置为默认")
        
        batch_layout.addWidget(self.select_all_button)
        batch_layout.addWidget(self.clear_all_button)
        batch_layout.addWidget(reset_button)
        batch_layout.addStretch()
        
        layout.addLayout(batch_layout)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入字段名进行搜索...")
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        
        # 字段复选框区域（可滚动）
        scroll_area = QScrollArea()
        scroll_widget = QFrame()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 创建字段复选框
        for field in self.all_fields:
            display_name = self.field_mappings.get(field, field)
            checkbox_text = f"{display_name}"
            if display_name != field:
                checkbox_text += f" ({field})"
            
            checkbox = QCheckBox(checkbox_text)
            checkbox.setObjectName(field)  # 设置对象名为字段名
            
            self.field_checkboxes[field] = checkbox
            scroll_layout.addWidget(checkbox)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 连接搜索功能
        reset_button.clicked.connect(self.reset_to_default)
        self.search_input.textChanged.connect(self.filter_fields)
        
        return group_box

    def create_preview_area(self) -> QGroupBox:
        """创建预览区域"""
        group_box = QGroupBox("预览效果")
        layout = QVBoxLayout(group_box)
        
        # 预览说明
        preview_info = QLabel("已选择的字段将按以下顺序显示:")
        preview_info.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(preview_info)
        
        # 预览文本区域
        self.preview_area = QTextEdit()
        self.preview_area.setReadOnly(True)
        self.preview_area.setMaximumHeight(150)
        layout.addWidget(self.preview_area)
        
        # 统计信息
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.stats_label)
        
        # 建议区域
        suggestion_label = QLabel("建议:")
        suggestion_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(suggestion_label)
        
        suggestion_text = QLabel(
            "• 选择最常用的5-8个字段以获得最佳显示效果\n"
            "• 建议包含：工号、姓名、部门等关键字段\n"
            "• 过多字段可能导致界面拥挤"
        )
        suggestion_text.setStyleSheet("color: #666; font-size: 11px;")
        suggestion_text.setWordWrap(True)
        layout.addWidget(suggestion_text)
        
        layout.addStretch()
        
        return group_box

    def create_button_area(self) -> QHBoxLayout:
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 左侧按钮
        self.delete_button = QPushButton("删除偏好设置")
        self.delete_button.setStyleSheet("QPushButton { color: #d73a49; }")
        
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        
        # 右侧按钮
        cancel_button = QPushButton("取消")
        self.save_button = QPushButton("保存")
        self.save_button.setDefault(True)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #0366d6;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #0256c2;
            }
        """)
        
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(self.save_button)
        
        # 连接按钮信号
        cancel_button.clicked.connect(self.reject)
        
        return button_layout

    def connect_signals(self):
        """连接信号"""
        # 字段选择变化
        for checkbox in self.field_checkboxes.values():
            checkbox.stateChanged.connect(self.update_preview)
        
        # 批量操作
        self.select_all_button.clicked.connect(self.select_all_fields)
        self.clear_all_button.clicked.connect(self.clear_all_fields)
        
        # 保存和删除
        self.save_button.clicked.connect(self.save_preference)
        self.delete_button.clicked.connect(self.delete_preference)

    def load_current_preference(self):
        """加载当前的偏好设置"""
        try:
            current_fields = self.config_sync.get_table_field_preference(self.table_name)
            
            if current_fields:
                self.current_preferred_fields = current_fields
                # 设置复选框状态
                for field, checkbox in self.field_checkboxes.items():
                    checkbox.setChecked(field in current_fields)
                
                self.logger.info(f"加载表 {self.table_name} 的偏好设置: {len(current_fields)} 个字段")
            else:
                # 没有偏好设置，默认选择前8个字段
                self.reset_to_default()
                self.logger.info(f"表 {self.table_name} 无偏好设置，使用默认选择")
                
            self.update_preview()
                
        except Exception as e:
            self.logger.error(f"加载偏好设置失败: {e}")

    def filter_fields(self):
        """根据搜索条件过滤字段"""
        search_text = self.search_input.text().lower()
        
        for field, checkbox in self.field_checkboxes.items():
            display_name = self.field_mappings.get(field, field)
            
            # 检查字段名或显示名是否包含搜索文本
            matches = (search_text in field.lower() or 
                      search_text in display_name.lower())
            
            checkbox.setVisible(matches)

    def select_all_fields(self):
        """选择所有可见字段"""
        for checkbox in self.field_checkboxes.values():
            if checkbox.isVisible():
                checkbox.setChecked(True)

    def clear_all_fields(self):
        """清空所有字段选择"""
        for checkbox in self.field_checkboxes.values():
            checkbox.setChecked(False)

    def reset_to_default(self):
        """重置为默认选择"""
        # 清空所有选择
        self.clear_all_fields()
        
        # 默认选择常用字段或前8个字段
        default_fields = ['id', 'employee_id', 'employee_name', 'department', 
                         'position', 'total_salary', 'basic_salary', 'allowance']
        
        selected_count = 0
        for field in self.all_fields:
            if field in default_fields or selected_count < 8:
                if field in self.field_checkboxes:
                    self.field_checkboxes[field].setChecked(True)
                    selected_count += 1

    def update_preview(self):
        """更新预览区域"""
        try:
            # 获取选中的字段
            selected_fields = []
            for field, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field)
            
            # 按原始字段顺序排序
            selected_fields.sort(key=lambda x: self.all_fields.index(x))
            
            # 生成预览文本
            preview_lines = []
            for i, field in enumerate(selected_fields, 1):
                display_name = self.field_mappings.get(field, field)
                preview_lines.append(f"{i}. {display_name}")
            
            preview_text = "\n".join(preview_lines) if preview_lines else "未选择字段"
            self.preview_area.setPlainText(preview_text)
            
            # 更新统计信息
            total_fields = len(self.all_fields)
            selected_count = len(selected_fields)
            self.stats_label.setText(f"已选择 {selected_count}/{total_fields} 个字段")
            
            # 更新保存按钮状态
            self.save_button.setEnabled(selected_count > 0)
            
        except Exception as e:
            self.logger.error(f"更新预览失败: {e}")

    def save_preference(self):
        """保存偏好设置"""
        try:
            # 获取选中的字段
            selected_fields = []
            for field, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field)
            
            if not selected_fields:
                QMessageBox.warning(self, "警告", "请至少选择一个字段!")
                return
            
            # 按原始字段顺序排序
            selected_fields.sort(key=lambda x: self.all_fields.index(x))
            
            # 保存到配置
            success = self.config_sync.save_table_field_preference(
                self.table_name, selected_fields
            )
            
            if success:
                self.logger.info(f"保存表级字段偏好成功: {self.table_name}, {len(selected_fields)} 个字段")
                
                # 发送信号
                self.preference_saved.emit(self.table_name, selected_fields)
                
                QMessageBox.information(self, "成功", 
                    f"字段显示偏好已保存!\n已选择 {len(selected_fields)} 个字段。")
                
                self.accept()
            else:
                QMessageBox.critical(self, "错误", "保存偏好设置失败!")
                
        except Exception as e:
            self.logger.error(f"保存偏好设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存偏好设置失败: {e}")

    def delete_preference(self):
        """删除偏好设置"""
        try:
            # 确认对话框
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除表 '{self.table_name}' 的字段显示偏好吗?\n"
                "删除后将显示所有字段。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success = self.config_sync.remove_table_field_preference(self.table_name)
                
                if success:
                    self.logger.info(f"删除表级字段偏好成功: {self.table_name}")
                    
                    # 发送信号
                    self.preference_deleted.emit(self.table_name)
                    
                    QMessageBox.information(self, "成功", "字段显示偏好已删除!")
                    
                    self.accept()
                else:
                    QMessageBox.critical(self, "错误", "删除偏好设置失败!")
                    
        except Exception as e:
            self.logger.error(f"删除偏好设置失败: {e}")
            QMessageBox.critical(self, "错误", f"删除偏好设置失败: {e}")

    def get_dialog_styles(self) -> str:
        """获取对话框样式"""
        return """
        QDialog {
            background-color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QCheckBox {
            padding: 3px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
        }
        
        QCheckBox::indicator:unchecked {
            border: 1px solid #d1d5da;
            background-color: #ffffff;
            border-radius: 3px;
        }
        
        QCheckBox::indicator:checked {
            border: 1px solid #0366d6;
            background-color: #0366d6;
            border-radius: 3px;
        }
        
        QTextEdit {
            border: 1px solid #d1d5da;
            border-radius: 6px;
            padding: 8px;
            background-color: #f6f8fa;
        }
        
        QScrollArea {
            border: 1px solid #d1d5da;
            border-radius: 6px;
        }
        
        QPushButton {
            padding: 6px 12px;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            background-color: #fafbfc;
        }
        
        QPushButton:hover {
            background-color: #f3f4f6;
        }
        
        QPushButton:pressed {
            background-color: #e1e4e8;
        }
        """


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 测试数据
    test_table_name = "salary_data_2026_04_a_grade_employees"
    test_fields = [
        "id", "employee_id", "employee_name", "department", "position",
        "basic_salary", "allowance", "overtime_pay", "bonus", "deduction",
        "total_salary", "tax", "insurance", "net_salary", "remark"
    ]
    test_mappings = {
        "id": "序号",
        "employee_id": "工号", 
        "employee_name": "姓名",
        "department": "部门",
        "position": "职位",
        "basic_salary": "基本工资",
        "allowance": "津贴补贴",
        "total_salary": "应发合计"
    }
    
    dialog = TableFieldPreferenceDialog(
        test_table_name, test_fields, test_mappings
    )
    
    dialog.preference_saved.connect(
        lambda table, fields: print(f"保存偏好: {table} -> {fields}")
    )
    dialog.preference_deleted.connect(
        lambda table: print(f"删除偏好: {table}")
    )
    
    dialog.show()
    sys.exit(app.exec_()) 