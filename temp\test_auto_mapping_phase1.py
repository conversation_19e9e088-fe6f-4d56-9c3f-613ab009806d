"""
Phase 1: 自动字段映射生成测试脚本

测试内容:
1. 自动字段映射生成器的基本功能
2. 配置同步管理器的保存和加载
3. 映射质量评估

运行方式:
python temp/test_auto_mapping_phase1.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import import AutoFieldMappingGenerator, ConfigSyncManager
import pandas as pd
from datetime import datetime

print("Phase 1: 自动字段映射生成功能测试")
print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

# 测试 1: 自动字段映射生成器
print("\n" + "=" * 50)
print("测试 1: 自动字段映射生成器")
print("=" * 50)

generator = AutoFieldMappingGenerator()

test_columns = [
    "工号", "姓名", "部门名称",
    "2025年岗位工资", "2025年薪级工资", 
    "津贴", "应发工资"
]

table_name = "salary_data_2025_05_test"
mapping = generator.generate_mapping(test_columns, table_name)

print("生成的字段映射:")
for original, mapped in mapping.items():
    print(f"  {original} -> {mapped}")

quality = generator.assess_mapping_quality(mapping)
print(f"\n映射质量: 覆盖率={quality['coverage_rate']:.1%}")

# 测试 2: 配置同步管理器
print("\n" + "=" * 50)
print("测试 2: 配置同步管理器")
print("=" * 50)

sync_manager = ConfigSyncManager()

success = sync_manager.save_mapping(
    table_name=table_name,
    mapping=mapping,
    metadata={"auto_generated": True}
)

print(f"保存映射: {'成功' if success else '失败'}")

loaded_mapping = sync_manager.load_mapping(table_name)
print(f"加载映射: {'成功' if loaded_mapping else '失败'}")

print("✅ Phase 1 基础功能测试完成!") 