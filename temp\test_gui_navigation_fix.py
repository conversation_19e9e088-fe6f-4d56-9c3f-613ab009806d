#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI导航层次结构修复验证脚本

验证修复后的导航层次结构在实际GUI界面中是否正确显示。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager

class NavigationTestWindow(QMainWindow):
    """导航测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("导航层次结构修复验证")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化管理器
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager(config_manager=self.config_manager)
        self.dynamic_table_manager = DynamicTableManager(
            db_manager=self.db_manager, 
            config_manager=self.config_manager
        )
        # 初始化通信管理器（需要在UI创建前）
        self.comm_manager = ComponentCommunicationManager(self)
        
        # 创建主界面
        self.setup_ui()
        
        # 延迟执行强制刷新测试
        QTimer.singleShot(1000, self.test_force_refresh)
    
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("导航层次结构修复验证")
        info_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # 添加测试说明
        test_info = QLabel(
            "此窗口用于验证导航层次结构修复效果。\n"
            "检查左侧导航是否显示为：\n"
            "📊 工资表\n"
            "  └── 📅 2025年\n"
            "       ├── 📆 1月\n"
            "       ├── 📆 2月（如果有数据）\n"
            "       └── 📆 6月\n\n"
            "如果年份节点与工资表在同一层级，说明修复未生效。"
        )
        test_info.setStyleSheet("margin: 10px; padding: 10px; background-color: #f0f0f0;")
        layout.addWidget(test_info)
        
        # 创建导航面板
        self.navigation_panel = EnhancedNavigationPanel(
            dynamic_table_manager=self.dynamic_table_manager,
            comm_manager=self.comm_manager
        )
        layout.addWidget(self.navigation_panel)
        
        # 添加刷新按钮
        refresh_button = QPushButton("强制刷新导航")
        refresh_button.clicked.connect(self.manual_refresh)
        layout.addWidget(refresh_button)
        
        # 状态标签
        self.status_label = QLabel("正在初始化...")
        self.status_label.setStyleSheet("margin: 10px; color: blue;")
        layout.addWidget(self.status_label)
    
    def test_force_refresh(self):
        """测试强制刷新功能"""
        print("=== 开始测试强制刷新导航 ===")
        self.status_label.setText("正在执行强制刷新...")
        
        try:
            # 执行强制刷新
            self.navigation_panel.force_refresh_salary_data()
            self.status_label.setText("强制刷新完成！请检查导航层次结构。")
            print("强制刷新执行完成")
            
            # 分析当前导航结构
            QTimer.singleShot(500, self.analyze_navigation_structure)
            
        except Exception as e:
            error_msg = f"强制刷新失败: {str(e)}"
            self.status_label.setText(error_msg)
            print(f"错误: {error_msg}")
    
    def manual_refresh(self):
        """手动刷新"""
        self.test_force_refresh()
    
    def analyze_navigation_structure(self):
        """分析导航结构"""
        print("\n=== 分析当前导航结构 ===")
        
        tree_widget = self.navigation_panel.tree_widget
        print(f"顶级节点数量: {tree_widget.topLevelItemCount()}")
        
        # 检查工资表节点
        salary_item = None
        for i in range(tree_widget.topLevelItemCount()):
            item = tree_widget.topLevelItem(i)
            if item and "工资表" in item.text(0):
                salary_item = item
                print(f"找到工资表节点: {item.text(0)}")
                break
        
        if salary_item:
            print(f"工资表子节点数量: {salary_item.childCount()}")
            
            # 检查年份节点
            for i in range(salary_item.childCount()):
                year_item = salary_item.child(i)
                print(f"  年份节点: {year_item.text(0)} (子节点: {year_item.childCount()}个)")
                
                # 检查月份节点
                for j in range(year_item.childCount()):
                    month_item = year_item.child(j)
                    print(f"    月份节点: {month_item.text(0)} (子节点: {month_item.childCount()}个)")
        else:
            print("未找到工资表节点！")
        
        # 检查是否存在层次结构问题
        problem_found = False
        for i in range(tree_widget.topLevelItemCount()):
            item = tree_widget.topLevelItem(i)
            if item and ("2025年" in item.text(0) or "月" in item.text(0)):
                print(f"⚠️ 发现层次结构问题: {item.text(0)} 在顶级层")
                problem_found = True
        
        if not problem_found:
            print("✅ 导航层次结构正确！")
            self.status_label.setText("✅ 导航层次结构验证通过")
        else:
            print("❌ 导航层次结构存在问题")
            self.status_label.setText("❌ 导航层次结构存在问题")

def main():
    """主函数"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = NavigationTestWindow()
    window.show()
    
    print("GUI导航层次结构测试窗口已启动")
    print("请检查窗口中的导航面板层次结构")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main()) 