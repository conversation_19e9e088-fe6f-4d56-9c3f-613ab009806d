"""
修复后的表头管理器测试脚本

验证：
1. 不会过度消耗CPU
2. 防重复清理机制有效
3. 仅在必要时执行清理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QTableWidget, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer, pyqtSlot
import time

from src.gui.table_header_manager import get_global_header_manager


class SimpleTestWindow(QMainWindow):
    """简化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.header_manager = get_global_header_manager()
        self.cleanup_count = 0
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("修复验证 - 表头管理器CPU使用测试")
        self.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_rapid_btn = QPushButton("测试快速重复清理（验证防重复机制）")
        self.test_rapid_btn.clicked.connect(self.test_rapid_cleanup)
        layout.addWidget(self.test_rapid_btn)
        
        self.test_conditional_btn = QPushButton("测试条件清理（无重影时）")
        self.test_conditional_btn.clicked.connect(self.test_conditional_cleanup)
        layout.addWidget(self.test_conditional_btn)
        
        # 创建一个测试表格
        self.test_table = QTableWidget(3, 3)
        self.test_table.setHorizontalHeaderLabels(['工号', '姓名', '部门'])
        layout.addWidget(self.test_table)
        
        # 注册表格
        self.header_manager.register_table("test_table_simple", self.test_table)
        
    def test_rapid_cleanup(self):
        """测试快速重复清理"""
        self.status_label.setText("开始快速重复清理测试...")
        self.cleanup_count = 0
        
        start_time = time.time()
        
        # 快速连续调用10次清理
        for i in range(10):
            success = self.header_manager.clear_table_header_cache_enhanced()
            if success:
                self.cleanup_count += 1
            
        end_time = time.time()
        
        # 统计信息
        stats = self.header_manager.get_statistics()
        
        result_text = f"""快速重复清理测试结果:
调用次数: 10
实际执行次数: {self.cleanup_count}
总清理次数: {stats['total_cleanups']}
耗时: {end_time - start_time:.3f}秒
防重复机制: {'有效' if self.cleanup_count < 10 else '无效'}"""
        
        self.status_label.setText(result_text)
        
    def test_conditional_cleanup(self):
        """测试条件清理（正常表格，无重影）"""
        self.status_label.setText("测试条件清理...")
        
        start_time = time.time()
        
        # 测试自动检测（应该不执行清理）
        shadow_tables = self.header_manager.auto_detect_and_fix_shadows()
        
        end_time = time.time()
        
        result_text = f"""条件清理测试结果:
检测到重影表格数: {len(shadow_tables)}
耗时: {end_time - start_time:.3f}秒
结果: {'跳过清理（正确）' if len(shadow_tables) == 0 else '执行了清理'}"""
        
        self.status_label.setText(result_text)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = SimpleTestWindow()
    window.show()
    
    # 显示初始状态
    QTimer.singleShot(1000, lambda: window.status_label.setText("就绪 - 请点击按钮测试"))
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 