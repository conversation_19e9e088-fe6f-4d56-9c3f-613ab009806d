"""
表头管理器测试脚本

测试增强版表头重影修复功能，验证实现是否正确。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QTableWidget, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout, QTableWidgetItem
from PyQt5.QtCore import QTimer, pyqtSlot
import pandas as pd

from src.gui.table_header_manager import get_global_header_manager, TableHeaderManager


class TestWindow(QMainWindow):
    """测试窗口，用于验证表头管理器功能"""
    
    def __init__(self):
        super().__init__()
        self.header_manager = get_global_header_manager()
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("表头管理器测试 - 验证重影修复功能")
        self.setGeometry(100, 100, 800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        layout.addWidget(self.status_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.create_table_btn = QPushButton("创建测试表格")
        self.create_table_btn.clicked.connect(self.create_test_table)
        button_layout.addWidget(self.create_table_btn)
        
        self.create_shadow_btn = QPushButton("创建重影表格")
        self.create_shadow_btn.clicked.connect(self.create_shadow_table)
        button_layout.addWidget(self.create_shadow_btn)
        
        self.cleanup_btn = QPushButton("执行表头清理")
        self.cleanup_btn.clicked.connect(self.test_header_cleanup)
        button_layout.addWidget(self.cleanup_btn)
        
        self.auto_fix_btn = QPushButton("自动检测修复")
        self.auto_fix_btn.clicked.connect(self.test_auto_fix)
        button_layout.addWidget(self.auto_fix_btn)
        
        self.validate_btn = QPushButton("验证表头状态")
        self.validate_btn.clicked.connect(self.test_validation)
        button_layout.addWidget(self.validate_btn)
        
        self.stats_btn = QPushButton("查看统计信息")
        self.stats_btn.clicked.connect(self.show_statistics)
        button_layout.addWidget(self.stats_btn)
        
        layout.addLayout(button_layout)
        
        # 表格容器
        self.table_container = QWidget()
        self.table_layout = QVBoxLayout(self.table_container)
        layout.addWidget(self.table_container)
        
        # 测试表格列表
        self.test_tables = []
        
    def setup_connections(self):
        """设置信号连接"""
        self.header_manager.header_cleaned.connect(self.on_header_cleaned)
        self.header_manager.shadow_detected.connect(self.on_shadow_detected)
        self.header_manager.header_updated.connect(self.on_header_updated)
    
    def create_test_table(self):
        """创建正常的测试表格"""
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(['工号', '姓名', '部门', '基本工资'])
        
        # 添加测试数据
        data = [
            ['001', '张三', '财务部', '5000'],
            ['002', '李四', '人事部', '5500'],
            ['003', '王五', '技术部', '6000'],
            ['004', '赵六', '市场部', '5200'],
            ['005', '钱七', '行政部', '4800']
        ]
        
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(value)
                table.setItem(row, col, item)
        
        # 注册到表头管理器
        table_id = f"test_table_{len(self.test_tables)}"
        self.header_manager.register_table(table_id, table)
        
        # 添加到界面
        self.table_layout.addWidget(table)
        self.test_tables.append((table_id, table))
        
        self.status_label.setText(f"已创建正常表格: {table_id}")
    
    def create_shadow_table(self):
        """创建有重影问题的测试表格"""
        table = QTableWidget(5, 6)
        
        # 故意创建重复的中英文表头
        shadow_headers = ['工号', 'employee_id', '姓名', 'name', '部门', 'department']
        table.setHorizontalHeaderLabels(shadow_headers)
        
        # 添加测试数据
        data = [
            ['001', '001', '张三', '张三', '财务部', '财务部'],
            ['002', '002', '李四', '李四', '人事部', '人事部'],
            ['003', '003', '王五', '王五', '技术部', '技术部'],
            ['004', '004', '赵六', '赵六', '市场部', '市场部'],
            ['005', '005', '钱七', '钱七', '行政部', '行政部']
        ]
        
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(value)
                table.setItem(row, col, item)
        
        # 注册到表头管理器
        table_id = f"shadow_table_{len(self.test_tables)}"
        self.header_manager.register_table(table_id, table)
        
        # 添加到界面
        self.table_layout.addWidget(table)
        self.test_tables.append((table_id, table))
        
        self.status_label.setText(f"已创建重影表格: {table_id} (包含中英文重复表头)")
    
    def test_header_cleanup(self):
        """测试表头清理功能"""
        if not self.test_tables:
            self.status_label.setText("请先创建测试表格")
            return
        
        success = self.header_manager.clear_table_header_cache_enhanced()
        
        if success:
            self.status_label.setText("表头清理已启动")
        else:
            self.status_label.setText("表头清理启动失败")
    
    def test_auto_fix(self):
        """测试自动检测和修复功能"""
        if not self.test_tables:
            self.status_label.setText("请先创建测试表格")
            return
        
        shadow_tables = self.header_manager.auto_detect_and_fix_shadows()
        
        if shadow_tables:
            shadow_info = "; ".join([f"{tid}: {labels}" for tid, labels in shadow_tables.items()])
            self.status_label.setText(f"检测到重影并已修复: {shadow_info}")
        else:
            self.status_label.setText("未检测到表头重影问题")
    
    def test_validation(self):
        """测试表头状态验证"""
        if not self.test_tables:
            self.status_label.setText("请先创建测试表格")
            return
        
        validation_results = []
        for table_id, _ in self.test_tables:
            result = self.header_manager.validate_header_state(table_id)
            status = "正常" if result['is_valid'] else "异常"
            issues = "; ".join(result['issues']) if result['issues'] else "无"
            validation_results.append(f"{table_id}: {status} ({issues})")
        
        result_text = "表头状态验证结果: " + "; ".join(validation_results)
        self.status_label.setText(result_text)
    
    def show_statistics(self):
        """显示统计信息"""
        stats = self.header_manager.get_statistics()
        
        stats_text = f"统计信息: 注册表格数: {stats['registered_tables']}, 总清理次数: {stats['total_cleanups']}, 重影修复次数: {stats['shadow_fixes']}"
        
        self.status_label.setText(stats_text)
    
    @pyqtSlot(str)
    def on_header_cleaned(self, table_id):
        """表头清理完成回调"""
        print(f"表头清理完成: {table_id}")
    
    @pyqtSlot(str, list)
    def on_shadow_detected(self, table_id, duplicate_labels):
        """检测到重影回调"""
        print(f"检测到重影 {table_id}: {duplicate_labels}")
    
    @pyqtSlot(str, list)
    def on_header_updated(self, table_id, new_labels):
        """表头更新回调"""
        print(f"表头更新 {table_id}: {new_labels}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 