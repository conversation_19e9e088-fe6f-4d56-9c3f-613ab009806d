#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试表头映射功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator

def test_field_mapping():
    """测试字段映射功能"""
    print("测试字段映射功能...")
    
    # 创建配置管理器
    config_sync = ConfigSyncManager()
    
    # 创建自动映射生成器
    auto_mapping = AutoFieldMappingGenerator()
    
    # 测试数据
    excel_columns = ['工号', '姓名', '部门名称', '2026年岗位工资', '应发工资']
    table_name = "salary_data_2026_11"
    
    # 生成映射
    mapping = auto_mapping.generate_mapping(excel_columns, table_name)
    print(f"生成的映射: {mapping}")
    
    # 保存映射
    success = config_sync.save_mapping(table_name, mapping)
    print(f"保存映射结果: {success}")
    
    # 加载映射
    loaded_mapping = config_sync.load_mapping(table_name)
    print(f"加载的映射: {loaded_mapping}")
    
    # 测试用户偏好
    selected_fields = ['工号', '姓名', '应发工资']
    success = config_sync.save_user_header_preference(table_name, selected_fields)
    print(f"保存用户偏好结果: {success}")
    
    user_fields = config_sync.get_user_header_preference(table_name)
    print(f"用户偏好字段: {user_fields}")
    
    print("测试完成！")

if __name__ == "__main__":
    test_field_mapping()
