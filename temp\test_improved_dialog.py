#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的数据导入对话框样式

本脚本用于测试和展示数据导入对话框的现代化样式改进效果。
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

from src.gui.dialogs import DataImportDialog, ProgressDialog, AboutDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据导入对话框样式测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(50, 50, 50, 50)
        
        # 测试数据导入对话框按钮
        import_btn = QPushButton("🔄 测试数据导入对话框")
        import_btn.setMinimumHeight(50)
        import_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0d6efd, stop: 1 #0b5ed7);
                color: white;
                border: 1px solid #0a58ca;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0b5ed7, stop: 1 #0a58ca);
            }
        """)
        import_btn.clicked.connect(self.test_import_dialog)
        layout.addWidget(import_btn)
        
        # 测试进度对话框按钮
        progress_btn = QPushButton("⏳ 测试进度对话框")
        progress_btn.setMinimumHeight(50)
        progress_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #198754, stop: 1 #157347);
                color: white;
                border: 1px solid #146c43;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #157347, stop: 1 #146c43);
            }
        """)
        progress_btn.clicked.connect(self.test_progress_dialog)
        layout.addWidget(progress_btn)
        
        # 测试关于对话框按钮
        about_btn = QPushButton("ℹ️ 测试关于对话框")
        about_btn.setMinimumHeight(50)
        about_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #fd7e14, stop: 1 #e8630c);
                color: white;
                border: 1px solid #dc5f00;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8630c, stop: 1 #dc5f00);
            }
        """)
        about_btn.clicked.connect(self.test_about_dialog)
        layout.addWidget(about_btn)
        
        # 设置主窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def test_import_dialog(self):
        """测试数据导入对话框"""
        try:
            dialog = DataImportDialog(self)
            dialog.exec_()
        except Exception as e:
            print(f"测试数据导入对话框时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def test_progress_dialog(self):
        """测试进度对话框"""
        try:
            dialog = ProgressDialog("测试进度显示", self)
            dialog.show()
            
            # 模拟进度更新
            from PyQt5.QtCore import QTimer
            
            def update_progress():
                current_value = dialog.progress_bar.value()
                if current_value < 100:
                    new_value = current_value + 10
                    
                    # 模拟不同阶段的状态
                    if new_value <= 20:
                        status = "正在验证文件..."
                    elif new_value <= 40:
                        status = "正在读取数据..."
                    elif new_value <= 60:
                        status = "正在处理数据..."
                    elif new_value <= 80:
                        status = "正在保存数据..."
                    elif new_value < 100:
                        status = "正在完成导入..."
                    else:
                        status = "导入完成"
                    
                    dialog.update_progress(new_value, status)
                    
                    if new_value >= 100:
                        timer.stop()
                        dialog.close()
            
            timer = QTimer()
            timer.timeout.connect(update_progress)
            timer.start(500)  # 每500ms更新一次
            
        except Exception as e:
            print(f"测试进度对话框时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def test_about_dialog(self):
        """测试关于对话框"""
        try:
            dialog = AboutDialog(self)
            dialog.exec_()
        except Exception as e:
            print(f"测试关于对话框时出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建并显示主窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 