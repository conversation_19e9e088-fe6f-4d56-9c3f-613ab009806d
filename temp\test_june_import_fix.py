#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试6月数据导入和导航刷新功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.database_manager import DatabaseManager

def test_sheet_data_type_recognition():
    """测试Sheet数据类型识别"""
    print("=" * 50)
    print("测试Sheet数据类型识别")
    print("=" * 50)
    
    importer = MultiSheetImporter()
    
    test_sheets = [
        "离休人员工资表",
        "退休人员工资表", 
        "全部在职人员工资表",
        "A岗职工",
        "基本信息"
    ]
    
    for sheet_name in test_sheets:
        data_type = importer._get_sheet_data_type(sheet_name)
        print(f"  {sheet_name:<20} -> {data_type}")

def test_table_name_generation():
    """测试表名生成"""
    print("\n" + "=" * 50)
    print("测试表名生成")
    print("=" * 50)
    
    importer = MultiSheetImporter()
    year = 2025
    month = 6
    
    test_sheets = [
        "离休人员工资表",
        "退休人员工资表", 
        "全部在职人员工资表",
        "A岗职工"
    ]
    
    for sheet_name in test_sheets:
        data_type = importer._get_sheet_data_type(sheet_name)
        
        # 模拟表名生成逻辑（来自_import_separate_strategy）
        if data_type.startswith("salary_data_"):
            employee_type = data_type.replace("salary_data_", "")
            table_name = f"salary_data_{year}_{month:02d}_{employee_type}"
        else:
            table_name = f"{data_type}_{year}_{month:02d}"
            
        print(f"  {sheet_name:<20} -> {table_name}")

def check_database_tables():
    """检查数据库中的表"""
    print("\n" + "=" * 50)
    print("检查数据库中的表")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        cursor = db_manager.connection.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;")
        tables = cursor.fetchall()
        
        print("数据库中的所有表:")
        for table in tables:
            table_name = table[0]
            
            # 获取表记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            print(f"  {table_name:<40} ({count} 条记录)")
        
        db_manager.close()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")

def test_navigation_table_parsing():
    """测试导航面板的表名解析"""
    print("\n" + "=" * 50)
    print("测试导航面板表名解析")
    print("=" * 50)
    
    # 模拟导航面板的表名解析逻辑
    test_tables = [
        "salary_data_2025_06_active_employees",
        "salary_data_2025_06_retired_employees",
        "salary_data_2025_06_pension_employees",
        "salary_data_2025_06_a_grade_employees",
        "salary_data_2025_05_active_employees",
        "basic_info_2025_06"  # 错误的表名
    ]
    
    def parse_table_name(table_name: str):
        """解析表名"""
        parts = table_name.split('_')
        if len(parts) >= 5 and parts[0] == 'salary' and parts[1] == 'data':
            try:
                year = int(parts[2])
                month = int(parts[3])
                employee_type = '_'.join(parts[4:])
                return year, month, employee_type
            except ValueError:
                pass
        return 2025, 1, 'unknown'
    
    def get_employee_type_display_name(employee_type: str) -> str:
        """获取员工类型显示名称"""
        type_map = {
            'active_employees': '全部在职人员',
            'retired_employees': '离休人员', 
            'pension_employees': '退休人员',
            'a_grade_employees': 'A岗职工'
        }
        return type_map.get(employee_type, employee_type)
    
    for table_name in test_tables:
        year, month, employee_type = parse_table_name(table_name)
        display_name = get_employee_type_display_name(employee_type)
        
        print(f"  {table_name:<40} -> {year}年{month}月 {display_name}")

if __name__ == "__main__":
    print("测试6月数据导入和导航刷新功能修复")
    print()
    
    test_sheet_data_type_recognition()
    test_table_name_generation()
    check_database_tables()
    test_navigation_table_parsing()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50) 