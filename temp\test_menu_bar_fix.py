"""
测试菜单栏切换时表头按钮重影修复效果

验证修复后的功能：
1. 菜单栏显示/隐藏切换正常
2. 表头按钮不再出现重影
3. 布局刷新流畅无闪烁
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PyQt5.QtCore import QTimer, pyqtSlot, Qt
from PyQt5.QtGui import QFont

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.utils.log_config import setup_logger

class MenuBarFixTester:
    """
    菜单栏修复测试器
    """
    
    def __init__(self):
        self.logger = setup_logger(__name__ + ".MenuBarFixTester")
        self.test_results = []
        
    def run_comprehensive_test(self):
        """
        运行综合测试
        """
        print("🔧 开始菜单栏按钮重影修复效果测试...")
        
        try:
            # 导入必要的模块
            from src.gui.prototype.prototype_main_window import PrototypeMainWindow
            from src.core.config_manager import ConfigManager
            from src.modules.data_storage.database_manager import DatabaseManager
            from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
            
            # 创建管理器实例
            config_manager = ConfigManager()
            db_manager = DatabaseManager()
            dynamic_table_manager = DynamicTableManager(db_manager)
            
            # 创建应用
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            # 创建主窗口
            main_window = PrototypeMainWindow(config_manager, db_manager, dynamic_table_manager)
            
            # 测试1: 验证修复方法是否存在
            self._test_fix_methods_existence(main_window)
            
            # 测试2: 验证菜单栏切换功能
            self._test_menu_bar_toggle(main_window)
            
            # 测试3: 验证表头重影修复
            self._test_header_shadow_fix(main_window)
            
            # 显示窗口进行可视化测试
            main_window.show()
            
            # 自动测试菜单栏切换
            self._automated_toggle_test(main_window)
            
            print("\n📊 测试结果总结:")
            for result in self.test_results:
                status = "✅" if result['passed'] else "❌"
                print(f"{status} {result['name']}: {result['message']}")
            
            return app.exec_()
            
        except Exception as e:
            print(f"❌ 测试运行失败: {e}")
            return 1
    
    def _test_fix_methods_existence(self, main_window):
        """
        测试修复方法是否存在
        """
        try:
            menu_manager = main_window.menu_bar_manager
            
            # 检查新增的修复方法
            fix_methods = [
                '_clear_table_header_cache',
                '_delayed_layout_refresh', 
                '_refresh_single_table',
                '_final_header_cleanup'
            ]
            
            missing_methods = []
            for method_name in fix_methods:
                if not hasattr(menu_manager, method_name):
                    missing_methods.append(method_name)
            
            if missing_methods:
                self.test_results.append({
                    'name': '修复方法存在性检查',
                    'passed': False,
                    'message': f'缺少方法: {", ".join(missing_methods)}'
                })
            else:
                self.test_results.append({
                    'name': '修复方法存在性检查',
                    'passed': True,
                    'message': '所有修复方法都已正确添加'
                })
                
        except Exception as e:
            self.test_results.append({
                'name': '修复方法存在性检查',
                'passed': False,
                'message': f'检查失败: {e}'
            })
    
    def _test_menu_bar_toggle(self, main_window):
        """
        测试菜单栏切换功能
        """
        try:
            menu_manager = main_window.menu_bar_manager
            
            # 测试显示菜单栏
            initial_state = menu_manager.is_visible
            menu_manager.show_menu_bar()
            show_state = menu_manager.is_visible
            
            # 测试隐藏菜单栏
            menu_manager.hide_menu_bar()
            hide_state = menu_manager.is_visible
            
            # 验证切换功能
            if show_state == True and hide_state == False:
                self.test_results.append({
                    'name': '菜单栏切换功能',
                    'passed': True,
                    'message': '菜单栏显示/隐藏切换正常'
                })
            else:
                self.test_results.append({
                    'name': '菜单栏切换功能',
                    'passed': False,
                    'message': f'切换异常: 显示={show_state}, 隐藏={hide_state}'
                })
                
        except Exception as e:
            self.test_results.append({
                'name': '菜单栏切换功能',
                'passed': False,
                'message': f'测试失败: {e}'
            })
    
    def _test_header_shadow_fix(self, main_window):
        """
        测试表头重影修复
        """
        try:
            menu_manager = main_window.menu_bar_manager
            
            # 查找表格组件
            from PyQt5.QtWidgets import QTableWidget
            tables = main_window.findChildren(QTableWidget)
            
            if tables:
                # 测试表头缓存清除方法
                menu_manager._clear_table_header_cache()
                
                self.test_results.append({
                    'name': '表头重影修复',
                    'passed': True,
                    'message': f'成功清除 {len(tables)} 个表格的表头缓存'
                })
            else:
                self.test_results.append({
                    'name': '表头重影修复',
                    'passed': True,
                    'message': '当前无表格组件，但修复方法可用'
                })
                
        except Exception as e:
            self.test_results.append({
                'name': '表头重影修复',
                'passed': False,
                'message': f'测试失败: {e}'
            })
    
    def _automated_toggle_test(self, main_window):
        """
        自动化切换测试
        """
        try:
            print("\n🔄 开始自动化菜单栏切换测试...")
            
            menu_manager = main_window.menu_bar_manager
            
            def toggle_sequence():
                """切换序列"""
                for i in range(5):
                    # 显示菜单栏
                    menu_manager.show_menu_bar()
                    QApplication.processEvents()
                    time.sleep(0.5)
                    
                    # 隐藏菜单栏
                    menu_manager.hide_menu_bar()
                    QApplication.processEvents()
                    time.sleep(0.5)
                    
                    print(f"   第 {i+1} 轮切换完成")
                
                print("✅ 自动化切换测试完成，请观察是否有按钮重影")
            
            # 延迟开始自动测试
            QTimer.singleShot(2000, toggle_sequence)
            
        except Exception as e:
            print(f"❌ 自动化测试失败: {e}")


def run_visual_inspection_test():
    """
    运行可视化检查测试
    """
    print("\n👁️ 可视化检查指南:")
    print("1. 观察菜单栏切换时主界面右侧表格表头是否有按钮重影")
    print("2. 注意观察表头区域是否有重复的按钮或文字")
    print("3. 检查布局刷新是否流畅，无明显闪烁")
    print("4. 手动点击设置按钮（⚙️）多次切换菜单栏")
    print("5. 如果没有重影现象，说明修复成功")


if __name__ == "__main__":
    print("=" * 60)
    print("菜单栏按钮重影修复效果测试")
    print("=" * 60)
    
    # 运行可视化检查指南
    run_visual_inspection_test()
    
    # 运行综合测试
    tester = MenuBarFixTester()
    tester.run_comprehensive_test() 