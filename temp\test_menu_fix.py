#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试菜单栏重复问题修复

运行此脚本可以验证菜单栏是否还存在重复问题。
"""
import sys
import os
import logging

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

def test_menu_duplication():
    """测试菜单栏重复问题"""
    try:
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        
        app = QApplication(sys.argv)
        
        # 初始化核心管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 创建主窗口
        main_window = PrototypeMainWindow(config_manager, db_manager, dynamic_table_manager)
        
        # 检查菜单栏是否有重复
        menu_bar = main_window.menuBar()
        actions = menu_bar.actions()
        
        print(f"菜单栏中的菜单数量: {len(actions)}")
        
        menu_texts = []
        for action in actions:
            if action.menu():
                menu_text = action.menu().title()
                menu_texts.append(menu_text)
                print(f"菜单: {menu_text}")
        
        # 检查是否有重复的菜单
        duplicates = []
        seen = set()
        for menu_text in menu_texts:
            if menu_text in seen:
                duplicates.append(menu_text)
            else:
                seen.add(menu_text)
        
        if duplicates:
            print(f"发现重复菜单: {duplicates}")
            return False
        else:
            print("没有发现重复菜单，修复成功！")
            return True
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_menu_duplication()
    if success:
        print("✅ 菜单栏重复问题已修复")
    else:
        print("❌ 菜单栏重复问题仍然存在") 