"""
菜单栏和工具栏显示状态测试

检查主窗口的菜单栏和工具栏是否正常显示，
并提供修复建议。
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import Qt
from unittest.mock import patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.main_window_refactored import MainWindow


def test_menu_toolbar_visibility():
    """测试菜单栏和工具栏的显示状态"""
    print("=" * 60)
    print("菜单栏和工具栏显示状态测试")
    print("=" * 60)
    
    # 创建应用
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # 模拟应用服务以避免初始化错误
        with patch('src.gui.main_window_refactored.ApplicationService') as mock_service:
            mock_service.return_value.initialize.return_value.success = True
            mock_service.return_value.initialize.return_value.message = "初始化成功"
            mock_service.return_value.get_user_preferences.return_value.success = True
            mock_service.return_value.get_user_preferences.return_value.data = {}
            
            # 创建主窗口
            main_window = MainWindow()
            
            # 检查菜单栏状态
            menu_bar = main_window.menuBar()
            print(f"菜单栏对象: {menu_bar}")
            print(f"菜单栏是否可见: {menu_bar.isVisible()}")
            print(f"菜单栏是否隐藏: {menu_bar.isHidden()}")
            print(f"菜单栏高度: {menu_bar.height()}")
            
            # 检查菜单项数量
            menu_actions = menu_bar.actions()
            print(f"菜单项数量: {len(menu_actions)}")
            
            if menu_actions:
                print("菜单项列表:")
                for i, action in enumerate(menu_actions):
                    print(f"  {i+1}. {action.text()}")
            
            print()
            
            # 检查工具栏状态
            toolbars = main_window.findChildren(main_window.__class__.addToolBar(main_window, "").parent().__class__)
            print(f"工具栏数量: {len(toolbars)}")
            
            if toolbars:
                for i, toolbar in enumerate(toolbars):
                    print(f"工具栏 {i+1}:")
                    print(f"  标题: {toolbar.windowTitle()}")
                    print(f"  是否可见: {toolbar.isVisible()}")
                    print(f"  是否隐藏: {toolbar.isHidden()}")
                    print(f"  高度: {toolbar.height()}")
                    
                    # 检查工具栏按钮
                    toolbar_actions = toolbar.actions()
                    print(f"  按钮数量: {len(toolbar_actions)}")
                    
                    if toolbar_actions:
                        print("  按钮列表:")
                        for j, action in enumerate(toolbar_actions):
                            if action.text():  # 跳过分隔符
                                print(f"    {j+1}. {action.text()}")
            
            print()
            
            # 检查窗口特殊状态
            print("窗口状态检查:")
            print(f"窗口是否全屏: {main_window.isFullScreen()}")
            print(f"窗口是否最大化: {main_window.isMaximized()}")
            print(f"窗口是否最小化: {main_window.isMinimized()}")
            print(f"菜单栏是否在原生显示: {menu_bar.isNativeMenuBar()}")
            
            # 强制显示菜单栏和工具栏
            print("\n尝试强制显示菜单栏和工具栏...")
            menu_bar.setVisible(True)
            menu_bar.show()
            
            for toolbar in toolbars:
                toolbar.setVisible(True)
                toolbar.show()
            
            # 再次检查状态
            print(f"强制显示后菜单栏是否可见: {menu_bar.isVisible()}")
            if toolbars:
                print(f"强制显示后工具栏是否可见: {toolbars[0].isVisible()}")
            
            # 显示主窗口进行视觉确认
            print("\n显示主窗口进行视觉确认...")
            main_window.show()
            main_window.raise_()
            main_window.activateWindow()
            
            # 等待用户确认
            input("请检查主窗口是否显示菜单栏和工具栏，然后按Enter继续...")
            
            main_window.close()
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    return True


def check_user_preferences():
    """检查用户偏好设置是否影响显示"""
    print("\n" + "=" * 60)
    print("用户偏好设置检查")
    print("=" * 60)
    
    try:
        # 检查可能的偏好设置文件
        preference_files = [
            'user_preferences.json',
            'state/user/navigation_state.json',
            'config.json'
        ]
        
        for file_path in preference_files:
            if os.path.exists(file_path):
                print(f"发现偏好设置文件: {file_path}")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查可能影响菜单栏和工具栏显示的设置
                problematic_keywords = [
                    'menubar', 'menu_bar', 'toolbar', 'tool_bar',
                    'visible', 'hidden', 'show', 'hide'
                ]
                
                for keyword in problematic_keywords:
                    if keyword.lower() in content.lower():
                        print(f"  发现可能的相关设置: {keyword}")
                        
                        # 显示相关行
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if keyword.lower() in line.lower():
                                print(f"    第{i+1}行: {line.strip()}")
            else:
                print(f"偏好设置文件不存在: {file_path}")
                
    except Exception as e:
        print(f"检查偏好设置时发生错误: {e}")


def provide_fix_suggestions():
    """提供修复建议"""
    print("\n" + "=" * 60)
    print("修复建议")
    print("=" * 60)
    
    suggestions = [
        "1. 检查是否有用户偏好设置隐藏了菜单栏/工具栏",
        "2. 尝试重置用户偏好设置文件",
        "3. 检查PyQt5版本兼容性",
        "4. 验证主窗口初始化顺序",
        "5. 检查样式表是否影响显示",
        "6. 确认窗口状态（全屏/最大化等）"
    ]
    
    for suggestion in suggestions:
        print(suggestion)
    
    print("\n可能的修复命令:")
    print("# 重置用户偏好设置")
    print("rm -f user_preferences.json")
    print("rm -f state/user/navigation_state.json")
    
    print("\n# 或者在Python中强制显示")
    print("main_window.menuBar().setVisible(True)")
    print("main_window.menuBar().show()")


if __name__ == '__main__':
    try:
        # 运行测试
        test_menu_toolbar_visibility()
        
        # 检查用户偏好设置
        check_user_preferences()
        
        # 提供修复建议
        provide_fix_suggestions()
        
        print("\n测试完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc() 