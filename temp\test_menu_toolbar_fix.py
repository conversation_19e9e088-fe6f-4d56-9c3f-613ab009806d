"""
菜单栏和工具栏修复效果测试

验证修复后的菜单栏和工具栏是否正常显示
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from unittest.mock import patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.main_window_refactored import MainWindow


def test_fixed_ui_elements():
    """测试修复后的UI元素显示"""
    print("=" * 60)
    print("菜单栏和工具栏修复效果测试")
    print("=" * 60)
    
    # 创建应用
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # 模拟应用服务
        with patch('src.gui.main_window_refactored.ApplicationService') as mock_service:
            mock_service.return_value.initialize.return_value.success = True
            mock_service.return_value.initialize.return_value.message = "初始化成功"
            mock_service.return_value.get_user_preferences.return_value.success = True
            mock_service.return_value.get_user_preferences.return_value.data = {
                'ui': {
                    'show_toolbar': True,
                    'show_statusbar': True,
                    'show_menubar': True
                }
            }
            
            print("创建主窗口...")
            main_window = MainWindow()
            
            print("\n修复后状态检查:")
            
            # 检查菜单栏
            menu_bar = main_window.menuBar()
            print(f"菜单栏是否可见: {menu_bar.isVisible()}")
            print(f"菜单栏高度: {menu_bar.height()}")
            print(f"菜单项数量: {len(menu_bar.actions())}")
            
            # 检查工具栏
            from PyQt5.QtWidgets import QToolBar
            toolbars = main_window.findChildren(QToolBar)
            print(f"工具栏数量: {len(toolbars)}")
            
            if toolbars:
                for i, toolbar in enumerate(toolbars):
                    print(f"工具栏{i+1}: 可见={toolbar.isVisible()}, 高度={toolbar.height()}, 按钮数={len(toolbar.actions())}")
            
            # 检查状态栏
            status_bar = main_window.statusBar()
            print(f"状态栏是否可见: {status_bar.isVisible()}")
            print(f"状态栏高度: {status_bar.height()}")
            
            # 显示窗口
            print("\n显示主窗口...")
            main_window.show()
            main_window.raise_()
            main_window.activateWindow()
            
            # 等待用户确认
            input("请检查主窗口是否显示菜单栏和工具栏，然后按Enter继续...")
            
            # 检查是否有其他问题
            print("\n诊断信息:")
            print(f"窗口标题: {main_window.windowTitle()}")
            print(f"窗口大小: {main_window.size()}")
            print(f"窗口状态: {main_window.windowState()}")
            
            main_window.close()
            
            # 判断修复是否成功
            success = menu_bar.isVisible() and len(toolbars) > 0 and status_bar.isVisible()
            if success:
                print("\n✅ 修复成功！菜单栏和工具栏应该正常显示。")
            else:
                print("\n❌ 修复不完全，仍有问题需要解决。")
                if not menu_bar.isVisible():
                    print("  - 菜单栏仍然不可见")
                if len(toolbars) == 0:
                    print("  - 工具栏仍然未创建")
                if not status_bar.isVisible():
                    print("  - 状态栏不可见")
            
            return success
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def provide_additional_fixes():
    """提供额外的修复建议"""
    print("\n" + "=" * 60)
    print("额外修复建议")
    print("=" * 60)
    
    print("如果问题仍然存在，可以尝试以下方法:")
    print("1. 完全重启系统和Python环境")
    print("2. 清除所有缓存和状态文件")
    print("3. 检查PyQt5版本兼容性")
    print("4. 使用系统原生菜单栏设置")
    
    print("\n清除缓存命令:")
    print("rm -rf state/")
    print("rm -f user_preferences.json")
    print("rm -f config.json")


if __name__ == '__main__':
    try:
        success = test_fixed_ui_elements()
        
        if not success:
            provide_additional_fixes()
        
        print(f"\n测试完成! 结果: {'成功' if success else '需要进一步修复'}")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc() 