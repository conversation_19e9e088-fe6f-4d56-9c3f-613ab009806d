#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导航点击调试脚本

调试导航点击后右侧数据不显示的问题：
1. 监听导航选择事件
2. 分析路径结构
3. 验证数据加载逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit, QPushButton
from PyQt5.QtCore import QTimer, pyqtSlot
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager

class NavigationClickDebugWindow(QMainWindow):
    """导航点击调试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("导航点击调试器")
        self.setGeometry(100, 100, 1000, 700)
        
        # 初始化管理器
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager(config_manager=self.config_manager)
        self.dynamic_table_manager = DynamicTableManager(
            db_manager=self.db_manager, 
            config_manager=self.config_manager
        )
        self.comm_manager = ComponentCommunicationManager(self)
        
        self.setup_ui()
        
        # 延迟初始化调试功能
        QTimer.singleShot(1000, self.setup_debug_monitoring)
        
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        central_widget.setLayout(layout)
        
        # 标题
        title = QLabel("导航点击调试器")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 创建导航面板
        self.navigation_panel = EnhancedNavigationPanel(
            dynamic_table_manager=self.dynamic_table_manager,
            comm_manager=self.comm_manager
        )
        self.navigation_panel.setFixedWidth(350)
        
        # 调试输出区域
        self.debug_output = QTextEdit()
        self.debug_output.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        
        # 测试按钮
        self.test_button = QPushButton("测试表名生成")
        self.test_button.clicked.connect(self.test_table_name_generation)
        
        # 水平布局
        from PyQt5.QtWidgets import QHBoxLayout, QSplitter
        from PyQt5.QtCore import Qt
        
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.navigation_panel)
        
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.addWidget(self.debug_output)
        right_layout.addWidget(self.test_button)
        
        splitter.addWidget(right_panel)
        splitter.setSizes([350, 650])
        
        layout.addWidget(splitter)
        
        self.log_debug("导航点击调试器初始化完成")
        
    def setup_debug_monitoring(self):
        """设置调试监控"""
        # 连接导航变化信号
        self.navigation_panel.navigation_changed.connect(self.on_navigation_changed_debug)
        self.log_debug("已连接导航变化信号监听")
        
        # 获取导航数据统计
        nav_stats = self.navigation_panel.get_navigation_statistics()
        self.log_debug(f"导航统计: {nav_stats}")
        
        # 检查数据库表
        try:
            tables = self.dynamic_table_manager.get_table_list()
            self.log_debug(f"数据库中的表: {len(tables)}个")
            for i, table in enumerate(tables[:5]):  # 只显示前5个
                self.log_debug(f"  {i+1}. {table}")
            if len(tables) > 5:
                self.log_debug(f"  ... 还有{len(tables)-5}个表")
        except Exception as e:
            self.log_debug(f"获取表列表失败: {e}")
    
    @pyqtSlot(str, dict)
    def on_navigation_changed_debug(self, path: str, context: dict):
        """调试导航变化事件"""
        self.log_debug("\n=== 导航变化事件 ===")
        self.log_debug(f"路径: {path}")
        self.log_debug(f"上下文: {context}")
        
        # 分析路径结构
        path_parts = path.split(' > ')
        self.log_debug(f"路径分段: {path_parts}")
        self.log_debug(f"路径级别数: {len(path_parts)}")
        
        # 分析每一级
        for i, part in enumerate(path_parts):
            self.log_debug(f"  级别{i+1}: '{part}'")
        
        # 检查是否满足数据加载条件
        if len(path_parts) == 4:
            self.log_debug("✅ 满足4级路径条件，应该加载数据")
            table_name = self.generate_table_name_from_path(path_parts)
            self.log_debug(f"生成的表名: {table_name}")
            
            # 检查表是否存在
            if self.dynamic_table_manager.table_exists(table_name):
                record_count = self.dynamic_table_manager.get_table_record_count(table_name)
                self.log_debug(f"✅ 表存在，记录数: {record_count}")
            else:
                self.log_debug(f"❌ 表不存在: {table_name}")
                
        elif len(path_parts) == 3:
            self.log_debug("⚠️ 3级路径，不满足数据加载条件")
        elif len(path_parts) == 2:
            self.log_debug("⚠️ 2级路径，不满足数据加载条件")
        elif len(path_parts) == 1:
            self.log_debug("⚠️ 1级路径，不满足数据加载条件")
        else:
            self.log_debug(f"❓ {len(path_parts)}级路径，未知情况")
    
    def generate_table_name_from_path(self, path_list):
        """生成表名（复制主窗口的逻辑）"""
        if len(path_list) >= 4:
            # 例如: ['工资表', '2025年', '1月', '全部在职人员']
            module = path_list[0]  # 工资表
            year = path_list[1].replace('年', '')  # 2025
            month = path_list[2].replace('月', '')  # 1
            category = path_list[3]  # 全部在职人员
            
            # 构造表名格式: salary_data_2025_01_全部在职人员
            month_str = f"{int(month):02d}"  # 确保月份是两位数
            table_name = f"salary_data_{year}_{month_str}_{category}"
            
            return table_name
        else:
            return f"path_too_short_{len(path_list)}_levels"
    
    def test_table_name_generation(self):
        """测试表名生成"""
        self.log_debug("\n=== 测试表名生成 ===")
        
        test_paths = [
            ['工资表'],
            ['工资表', '2025年'],
            ['工资表', '2025年', '1月'],
            ['工资表', '2025年', '1月', '全部在职人员'],
            ['工资表', '2025年', '6月', 'A岗职工'],
            ['异动人员表', '2025年', '5月', '起薪']
        ]
        
        for path in test_paths:
            table_name = self.generate_table_name_from_path(path)
            path_str = ' > '.join(path)
            self.log_debug(f"路径: {path_str}")
            self.log_debug(f"  表名: {table_name}")
            
            if len(path) >= 4 and path[0] == '工资表':
                exists = self.dynamic_table_manager.table_exists(table_name)
                self.log_debug(f"  存在: {exists}")
                if exists:
                    count = self.dynamic_table_manager.get_table_record_count(table_name)
                    self.log_debug(f"  记录数: {count}")
            
            self.log_debug("")
    
    def log_debug(self, message):
        """输出调试信息"""
        self.debug_output.append(message)
        print(message)  # 同时输出到控制台
        
        # 自动滚动到底部
        cursor = self.debug_output.textCursor()
        cursor.movePosition(cursor.End)
        self.debug_output.setTextCursor(cursor)

def main():
    app = QApplication(sys.argv)
    window = NavigationClickDebugWindow()
    window.show()
    
    print("导航点击调试器已启动")
    print("请点击左侧导航树中的不同节点，观察右侧调试输出")
    
    return app.exec_()

if __name__ == '__main__':
    sys.exit(main()) 