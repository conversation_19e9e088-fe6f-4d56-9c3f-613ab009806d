"""
导航数据加载测试脚本
===================

测试导航数据是否能完整加载到月份和员工类型级别
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 导入项目模块
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager  
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

def print_tree_structure(item, level=0):
    """递归打印树形结构"""
    indent = "  " * level
    path = item.data(0, 32)  # Qt.UserRole = 32
    print(f"{indent}{'📁' if item.childCount() > 0 else '📄'} {item.text(0)} [路径: {path}] (子项: {item.childCount()})")
    
    for i in range(item.childCount()):
        child = item.child(i)
        print_tree_structure(child, level + 1)

def main():
    """主测试函数"""
    print("🔍 开始导航数据加载测试...")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 初始化核心管理器
    print("📋 初始化核心管理器...")
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
    
    # 创建主窗口（但不显示）
    print("🏠 创建主窗口...")
    main_window = PrototypeMainWindow(config_manager, db_manager, dynamic_table_manager)
    
    print("⏰ 等待延迟加载完成...")
    
    def check_navigation_data():
        """检查导航数据的完整性"""
        print("\n🔍 检查导航数据结构...")
        
        if hasattr(main_window, 'navigation_panel') and main_window.navigation_panel:
            tree = main_window.navigation_panel.tree_widget
            print(f"📊 顶级项目数量: {tree.topLevelItemCount()}")
            
            # 打印完整的树形结构
            for i in range(tree.topLevelItemCount()):
                item = tree.topLevelItem(i)
                print(f"\n=== 顶级项目 {i} ===")
                print_tree_structure(item)
            
            # 检查是否有完整的4级路径
            print("\n🔍 检查4级路径...")
            found_complete_paths = []
            
            def find_complete_paths(item, current_path=""):
                """查找完整的4级路径"""
                path = item.data(0, 32)
                if path:
                    full_path = f"{current_path} > {path}".strip(" > ")
                    path_parts = full_path.split(" > ")
                    
                    if len(path_parts) == 4:
                        found_complete_paths.append(full_path)
                        print(f"✅ 找到完整路径: {full_path}")
                    
                    # 继续递归子项
                    for i in range(item.childCount()):
                        child = item.child(i)
                        find_complete_paths(child, full_path)
            
            for i in range(tree.topLevelItemCount()):
                item = tree.topLevelItem(i)
                find_complete_paths(item)
            
            if found_complete_paths:
                print(f"\n✅ 找到 {len(found_complete_paths)} 个完整的4级路径")
                
                # 测试一个路径的数据加载
                test_path = found_complete_paths[0]
                print(f"\n🧪 测试路径数据加载: {test_path}")
                
                # 模拟导航选择
                main_window._on_navigation_changed(test_path, {'action': 'test'})
                
            else:
                print("❌ 没有找到完整的4级路径！这是问题所在！")
                
                # 检查数据库中的表
                print("\n🔍 检查数据库中的表...")
                tables = dynamic_table_manager.get_table_list()
                print(f"📊 数据库中的表数量: {len(tables)}")
                
                for table in tables[:10]:  # 只显示前10个
                    count = dynamic_table_manager.get_table_record_count(table)
                    print(f"   📄 {table}: {count} 条记录")
        
        else:
            print("❌ 导航面板未找到")
        
        print("\n🔍 测试完成")
        app.quit()
    
    # 延迟检查，等待导航数据加载完成
    QTimer.singleShot(2000, check_navigation_data)
    
    # 运行应用（不显示窗口）
    app.exec_()

if __name__ == "__main__":
    main() 