#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试导航修复脚本
验证元数据解析和导航树构建是否正常工作
"""

import sys
import os
import time
import gc
from unittest.mock import MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

# Mock PyQt5 to avoid GUI initialization
try:
    from PyQt5.QtWidgets import QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
except ImportError:
    sys.modules['PyQt5'] = MagicMock()
    sys.modules['PyQt5.QtWidgets'] = MagicMock()
    sys.modules['PyQt5.QtCore'] = MagicMock()
    sys.modules['PyQt5.QtGui'] = MagicMock()

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import Config<PERSON><PERSON><PERSON>

def test_navigation_fix():
    """测试导航修复"""
    print("=== 测试导航修复 ===")
    
    # 初始化管理器
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    print("\n1. 测试表名解析功能...")
    test_table_names = [
        "salary_data_2025_02_retired_employees",
        "salary_data_2025_02_pension_employees", 
        "salary_data_2025_02_active_employees",
        "salary_data_2025_02_a_grade_employees"
    ]
    
    for table_name in test_table_names:
        result = table_manager._parse_table_name(table_name)
        print(f"  {table_name} -> {result}")
    
    print("\n2. 测试获取表列表功能...")
    table_list = table_manager.get_table_list(table_type='salary_data')
    print(f"找到 {len(table_list)} 个salary_data类型的表")
    
    for table in table_list:
        if 'year' in table and 'month' in table:
            print(f"  {table['table_name']}: {table['year']}年{table['month']:02d}月 - {table.get('display_name', 'N/A')}")
        else:
            print(f"  {table['table_name']}: 缺少年月信息")
    
    print("\n3. 测试导航树数据构建...")
    tree_data = table_manager.get_navigation_tree_data()
    
    if tree_data:
        print("导航树结构:")
        for year in sorted(tree_data.keys(), reverse=True):
            print(f"  {year}年:")
            for month in sorted(tree_data[year].keys(), reverse=True):
                print(f"    {month:02d}月:")
                for item in tree_data[year][month]:
                    print(f"      - {item['display_name']} ({item['table_name']})")
    else:
        print("  导航树为空！")
    
    print("\n4. 检查新导入的2月数据是否存在...")
    feb_tables = [name for name in test_table_names if "_02_" in name]
    for table_name in feb_tables:
        if table_manager.table_exists(table_name):
            df = table_manager.get_dataframe_from_table(table_name)
            if df is not None and not df.empty:
                print(f"  ✓ {table_name}: {len(df)} 行数据")
            else:
                print(f"  ✗ {table_name}: 表存在但无数据")
        else:
            print(f"  ✗ {table_name}: 表不存在")

def test_navigation_lifecycle_management():
    """测试导航生命周期管理"""
    
    print("🧪 测试导航面板生命周期管理...")
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        
        # 检查数据库中的表
        all_tables = table_manager.get_table_list()
        salary_tables = [t for t in all_tables if t.get('table_name', '').startswith('salary_data_')]
        
        print(f"📊 检测到 {len(salary_tables)} 个工资数据表:")
        for table in salary_tables:
            print(f"  - {table.get('table_name', 'Unknown')}")
        
        # 测试导航数据获取
        navigation_data = table_manager.get_navigation_tree_data()
        
        if navigation_data:
            print(f"✅ 成功获取导航数据:")
            for year, months in navigation_data.items():
                print(f"  📅 {year}年: {len(months)} 个月份")
                for month, items in months.items():
                    print(f"    📆 {month}月: {len(items)} 个数据项")
        else:
            print("❌ 未获取到导航数据，数据库可能为空")
        
        # 模拟多次刷新操作
        print("\n🔄 模拟多次导航刷新操作...")
        
        for i in range(5):
            print(f"  第 {i+1} 次刷新...")
            
            # 获取表列表
            tables = table_manager.get_table_list('salary_data')
            
            # 获取导航数据
            nav_data = table_manager.get_navigation_tree_data()
            
            if nav_data:
                total_months = sum(len(months) for months in nav_data.values())
                print(f"    ✅ 获取到 {len(nav_data)} 年份，{total_months} 个月份的数据")
            else:
                print(f"    ❌ 第 {i+1} 次刷新未获取到数据")
            
            # 模拟内存清理
            gc.collect()
            time.sleep(0.1)
        
        print("\n✅ 导航生命周期管理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False

def test_table_metadata_integrity():
    """测试表元数据完整性"""
    
    print("\n🔍 测试表元数据完整性...")
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        
        # 检查系统表
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查table_metadata表
            cursor.execute("SELECT COUNT(*) FROM table_metadata WHERE table_type = 'salary_data'")
            metadata_count = cursor.fetchone()[0]
            
            print(f"📊 table_metadata中有 {metadata_count} 条工资数据表记录")
            
            # 检查实际存在的表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_%'")
            actual_tables = cursor.fetchall()
            
            print(f"💾 数据库中实际有 {len(actual_tables)} 个工资数据表:")
            for table in actual_tables[:5]:  # 只显示前5个
                print(f"  - {table[0]}")
            
            if len(actual_tables) > 5:
                print(f"  ... 还有 {len(actual_tables) - 5} 个表")
        
        print("✅ 元数据完整性检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 元数据完整性检查失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🚀 开始导航面板修复验证测试...\n")
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 导航生命周期管理
    if test_navigation_lifecycle_management():
        success_count += 1
    
    # 测试2: 表元数据完整性
    if test_table_metadata_integrity():
        success_count += 1
    
    # 测试结果
    print(f"\n📋 测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！导航面板修复验证成功")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return success_count == total_tests

if __name__ == "__main__":
    main() 