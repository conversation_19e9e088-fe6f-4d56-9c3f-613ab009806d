"""
导航修复验证测试脚本
=====================

验证以下修复：
1. 响应式布局管理器信号修复
2. 导航路径设置修复  
3. 导航点击响应修复
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 导入项目模块
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager  
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

def print_tree_structure(item, level=0):
    """递归打印树形结构"""
    indent = "  " * level
    path = item.data(0, 32)  # Qt.UserRole = 32
    print(f"{indent}{'📁' if item.childCount() > 0 else '📄'} {item.text(0)} [路径: {path}] (子项: {item.childCount()})")
    
    for i in range(item.childCount()):
        child = item.child(i)
        print_tree_structure(child, level + 1)

class NavigationTestResult:
    """测试结果记录器"""
    def __init__(self):
        self.tests_passed = 0
        self.tests_failed = 0
        self.details = []
    
    def assert_test(self, condition, message):
        """断言测试"""
        if condition:
            self.tests_passed += 1
            print(f"✅ PASS: {message}")
            self.details.append(f"PASS: {message}")
        else:
            self.tests_failed += 1
            print(f"❌ FAIL: {message}")
            self.details.append(f"FAIL: {message}")
    
    def print_summary(self):
        """打印测试摘要"""
        total = self.tests_passed + self.tests_failed
        print(f"\n📊 测试摘要:")
        print(f"   总测试数: {total}")
        print(f"   通过: {self.tests_passed}")
        print(f"   失败: {self.tests_failed}")
        print(f"   成功率: {self.tests_passed/total*100:.1f}%" if total > 0 else "   成功率: 0%")

def main():
    """主测试函数"""
    print("🧪 开始导航修复验证测试...")
    
    result = NavigationTestResult()
    
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 初始化核心管理器
        print("📋 初始化核心管理器...")
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 创建主窗口（但不显示）
        print("🏠 创建主窗口...")
        main_window = PrototypeMainWindow(config_manager, db_manager, dynamic_table_manager)
        
        # 测试1: 检查信号连接是否成功
        result.assert_test(
            hasattr(main_window, 'responsive_manager'),
            "响应式管理器存在"
        )
        
        result.assert_test(
            hasattr(main_window.responsive_manager, 'breakpoint_changed'),
            "响应式管理器有breakpoint_changed信号"
        )
        
        # 测试2: 检查导航面板存在
        result.assert_test(
            hasattr(main_window, 'navigation_panel') and main_window.navigation_panel,
            "导航面板存在"
        )
        
        # 测试3: 检查导航信号存在
        result.assert_test(
            hasattr(main_window.navigation_panel, 'navigation_changed'),
            "导航面板有navigation_changed信号"
        )
        
        # 测试4: 模拟信号连接（检查是否会失败）
        signal_connected = False
        try:
            # 尝试连接信号
            main_window.navigation_panel.navigation_changed.connect(main_window._on_navigation_changed)
            signal_connected = True
        except Exception as e:
            print(f"信号连接失败: {e}")
        
        result.assert_test(signal_connected, "导航信号连接成功")
        
        print("⏰ 等待延迟加载完成...")
        
        def verify_navigation_structure():
            """验证导航结构"""
            print("\n🔍 验证导航结构...")
            
            if not (hasattr(main_window, 'navigation_panel') and main_window.navigation_panel):
                result.assert_test(False, "导航面板不存在")
                return
            
            tree = main_window.navigation_panel.tree_widget
            
            # 测试5: 检查顶级项目数
            top_level_count = tree.topLevelItemCount()
            result.assert_test(top_level_count >= 2, f"顶级项目数({top_level_count}) >= 2")
            
            # 测试6: 查找完整的4级路径
            found_complete_paths = []
            
            def find_complete_paths(item, current_path=""):
                """查找完整的4级路径"""
                path = item.data(0, 32)
                if path:
                    full_path = f"{current_path} > {path}".strip(" > ")
                    path_parts = full_path.split(" > ")
                    
                    if len(path_parts) == 4:
                        found_complete_paths.append(full_path)
                    
                    # 继续递归子项
                    for i in range(item.childCount()):
                        child = item.child(i)
                        find_complete_paths(child, full_path)
            
            for i in range(tree.topLevelItemCount()):
                item = tree.topLevelItem(i)
                find_complete_paths(item)
            
            result.assert_test(
                len(found_complete_paths) > 0,
                f"找到完整4级路径数量: {len(found_complete_paths)}"
            )
            
            # 测试7: 检查路径都不是None
            has_none_paths = False
            non_none_path_count = 0
            total_items = 0
            
            def check_paths(item):
                nonlocal has_none_paths, non_none_path_count, total_items
                total_items += 1
                path = item.data(0, 32)
                if path is None:
                    has_none_paths = True
                else:
                    non_none_path_count += 1
                
                for i in range(item.childCount()):
                    child = item.child(i)
                    check_paths(child)
            
            for i in range(tree.topLevelItemCount()):
                item = tree.topLevelItem(i)
                check_paths(item)
            
            result.assert_test(
                not has_none_paths,
                f"所有项目都有有效路径 ({non_none_path_count}/{total_items} 项目)"
            )
            
            # 测试8: 模拟导航点击
            if found_complete_paths:
                test_path = found_complete_paths[0]
                print(f"\n🧪 模拟导航点击: {test_path}")
                
                navigation_triggered = False
                original_on_navigation_changed = main_window._on_navigation_changed
                
                def mock_navigation_handler(path, context):
                    nonlocal navigation_triggered
                    navigation_triggered = True
                    print(f"   📍 导航处理被触发: {path}")
                    # 调用原始处理器
                    original_on_navigation_changed(path, context)
                
                # 替换处理器进行测试
                main_window._on_navigation_changed = mock_navigation_handler
                
                # 模拟导航选择
                main_window.navigation_panel.navigation_changed.emit(test_path, {'action': 'test'})
                
                result.assert_test(
                    navigation_triggered,
                    "导航点击触发了处理函数"
                )
                
                # 恢复原始处理器
                main_window._on_navigation_changed = original_on_navigation_changed
            
            # 打印详细结构用于调试
            print("\n📊 导航树结构:")
            for i in range(tree.topLevelItemCount()):
                item = tree.topLevelItem(i)
                print(f"\n=== 顶级项目 {i} ===")
                print_tree_structure(item)
            
            result.print_summary()
            app.quit()
        
        # 延迟检查，等待导航数据加载完成
        QTimer.singleShot(3000, verify_navigation_structure)
        
        # 运行应用（不显示窗口）
        app.exec_()
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        result.assert_test(False, f"测试执行失败: {e}")
        result.print_summary()

if __name__ == "__main__":
    main() 