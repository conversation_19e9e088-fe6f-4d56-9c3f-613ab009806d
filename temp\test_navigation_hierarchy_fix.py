#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导航层次结构修复验证脚本

验证force_refresh_salary_data方法修复后的效果，
确保年份节点正确创建在工资表节点下，而不是与工资表并列。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem
from PyQt5.QtCore import Qt
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.system_config.config_manager import ConfigManager

def test_navigation_hierarchy():
    """测试导航层次结构"""
    print("=== 导航层次结构修复验证测试 ===")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化数据库管理器
    db_manager = DatabaseManager(config_manager=config_manager)
    
    # 初始化动态表管理器
    dynamic_table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
    
    # 测试获取导航树数据
    print("\n1. 获取动态导航数据...")
    navigation_data = dynamic_table_manager.get_navigation_tree_data()
    
    if navigation_data:
        print(f"   成功获取导航数据，包含年份: {list(navigation_data.keys())}")
        
        for year, months_data in navigation_data.items():
            print(f"   年份 {year}: {len(months_data)} 个月份 {list(months_data.keys())}")
            
            for month, items in months_data.items():
                print(f"     月份 {month}: {len(items)} 个项目")
                for item in items:
                    print(f"       - {item.get('display_name', 'Unknown')} ({item.get('icon', '')})")
    else:
        print("   未获取到导航数据")
    
    # 测试树形结构创建逻辑
    print("\n2. 模拟树形结构创建...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建模拟树形控件
    tree_widget = QTreeWidget()
    
    # 模拟工资表根节点
    salary_item = QTreeWidgetItem(tree_widget, ["📊 工资表"])
    salary_item.setData(0, Qt.UserRole, "工资表")
    
    print(f"   创建工资表根节点: {salary_item.text(0)}")
    print(f"   工资表节点路径数据: '{salary_item.data(0, Qt.UserRole)}'")
    
    # 测试修复后的逻辑
    if navigation_data:
        for year, months_data in sorted(navigation_data.items(), reverse=True):
            # 使用修复后的逻辑
            salary_path = salary_item.data(0, Qt.UserRole) if salary_item.data(0, Qt.UserRole) else "工资表"
            
            # 模拟创建年份节点
            year_text = f"{year}年"
            if salary_path:
                year_path = f"{salary_path} > {year_text}"
            else:
                year_path = year_text
            
            year_item = QTreeWidgetItem(salary_item, [f"📅 {year_text}"])
            year_item.setData(0, Qt.UserRole, year_path)
            
            print(f"   创建年份节点: {year_item.text(0)}")
            print(f"   年份节点路径: '{year_item.data(0, Qt.UserRole)}'")
            print(f"   年份节点父级: {year_item.parent().text(0) if year_item.parent() else 'None'}")
            
            # 创建月份节点
            for month, items in sorted(months_data.items()):
                month_text = f"{month}月"
                month_path = f"{year_path} > {month_text}"
                
                month_item = QTreeWidgetItem(year_item, [f"📆 {month_text}"])
                month_item.setData(0, Qt.UserRole, month_path)
                
                print(f"     创建月份节点: {month_item.text(0)}")
                print(f"     月份节点路径: '{month_item.data(0, Qt.UserRole)}'")
                print(f"     月份节点父级: {month_item.parent().text(0) if month_item.parent() else 'None'}")
                
                # 创建员工类型节点
                for item_info in items:
                    employee_text = item_info['display_name']
                    employee_path = f"{month_path} > {employee_text}"
                    
                    employee_item = QTreeWidgetItem(month_item, [f"{item_info['icon']} {employee_text}"])
                    employee_item.setData(0, Qt.UserRole, employee_path)
                    
                    print(f"       创建员工类型节点: {employee_item.text(0)}")
                    print(f"       员工类型节点路径: '{employee_item.data(0, Qt.UserRole)}'")
    
    # 输出树形结构总结
    print("\n3. 树形结构总结:")
    print(f"   顶级节点数量: {tree_widget.topLevelItemCount()}")
    
    def print_tree_structure(item, level=0):
        indent = "  " * level
        print(f"{indent}- {item.text(0)} (路径: '{item.data(0, Qt.UserRole)}')")
        for i in range(item.childCount()):
            print_tree_structure(item.child(i), level + 1)
    
    for i in range(tree_widget.topLevelItemCount()):
        print_tree_structure(tree_widget.topLevelItem(i))
    
    # 检查当前数据库状态
    print("\n4. 数据库状态检查:")
    all_tables = dynamic_table_manager.get_table_list()
    salary_tables = [t for t in all_tables if t.get('table_name', '').startswith('salary_data_')]
    
    print(f"   数据库中工资表数量: {len(salary_tables)}")
    for table in salary_tables:
        table_name = table.get('table_name', '')
        print(f"   - {table_name}")
        
        # 解析表名信息
        parts = table_name.split('_')
        if len(parts) >= 4:
            year = parts[2]
            month = parts[3]
            employee_type = '_'.join(parts[4:]) if len(parts) > 4 else 'unknown'
            print(f"     年份: {year}, 月份: {month}, 类型: {employee_type}")
    
    print("\n=== 测试完成 ===")
    
if __name__ == "__main__":
    test_navigation_hierarchy() 