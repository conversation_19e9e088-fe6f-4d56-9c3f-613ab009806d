"""
导航重构功能测试

测试导航重构后的功能完整性：
1. 菜单栏功能测试
2. 简化后的导航面板测试
3. 功能可访问性验证
"""

import sys
import os
import unittest
import tempfile
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.main_window_refactored import MainWindow
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager


class TestNavigationRefactor(unittest.TestCase):
    """导航重构功能测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def setUp(self):
        """每个测试方法的初始化"""
        # 模拟应用服务
        with patch('src.gui.main_window_refactored.ApplicationService') as mock_service:
            mock_service.return_value.initialize.return_value.success = True
            mock_service.return_value.initialize.return_value.message = "初始化成功"
            
            self.main_window = MainWindow()
            self.main_window.app_service = mock_service.return_value
    
    def tearDown(self):
        """测试后清理"""
        self.main_window.close()
    
    def test_menu_bar_structure(self):
        """测试菜单栏结构"""
        # 获取菜单栏
        menu_bar = self.main_window.menuBar()
        
        # 检查主要菜单是否存在
        menu_titles = [action.text() for action in menu_bar.actions()]
        expected_menus = ['文件(&F)', '数据(&D)', '异动(&C)', '报告(&R)', '设置(&S)', '帮助(&H)']
        
        for expected in expected_menus:
            self.assertIn(expected, menu_titles, f"菜单 '{expected}' 不存在")
    
    def test_file_menu_actions(self):
        """测试文件菜单的动作"""
        # 获取文件菜单
        menu_bar = self.main_window.menuBar()
        file_menu = None
        for action in menu_bar.actions():
            if action.text() == '文件(&F)':
                file_menu = action.menu()
                break
        
        self.assertIsNotNone(file_menu, "文件菜单不存在")
        
        # 检查文件菜单项
        file_actions = [action.text() for action in file_menu.actions() if action.text()]
        expected_actions = ['导入数据(&I)', '导出数据(&E)', '导入历史(&H)', '退出(&X)']
        
        for expected in expected_actions:
            self.assertIn(expected, file_actions, f"文件菜单项 '{expected}' 不存在")
    
    def test_toolbar_actions(self):
        """测试工具栏动作"""
        # 获取工具栏
        toolbar = None
        for child in self.main_window.children():
            if hasattr(child, 'actions'):
                toolbar = child
                break
        
        self.assertIsNotNone(toolbar, "工具栏不存在")


def run_navigation_refactor_tests():
    """运行导航重构测试套件"""
    print("开始导航重构功能测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    loader = unittest.TestLoader()
    test_suite.addTest(loader.loadTestsFromTestCase(TestNavigationRefactor))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    print(f"\n{'='*60}")
    print("导航重构测试结果摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    # 运行测试
    success = run_navigation_refactor_tests()
    sys.exit(0 if success else 1) 