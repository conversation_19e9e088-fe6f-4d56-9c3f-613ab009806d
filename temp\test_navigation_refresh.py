#!/usr/bin/env python3
"""
测试导航面板动态刷新功能

验证：
1. 数据库连接是否正常
2. 工资数据表获取是否正常
3. 导航面板刷新是否正常
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_storage.database_manager import DatabaseManager
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from src.utils.log_config import setup_logger

logger = setup_logger("test_navigation_refresh")

def test_database_connection():
    """测试数据库连接"""
    print("=" * 50)
    print("测试数据库连接")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"数据库连接成功，共找到 {len(tables)} 个表：")
            for table in tables:
                print(f"  - {table[0]}")
                
        return True
        
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False

def test_salary_tables_query():
    """测试工资数据表查询"""
    print("\n" + "=" * 50)
    print("测试工资数据表查询")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_%';")
            tables = cursor.fetchall()
            
            print(f"找到 {len(tables)} 个工资数据表：")
            for table in tables:
                table_name = table[0]
                print(f"  - {table_name}")
                
                # 获取表的记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"    记录数: {count}")
                
        return tables
        
    except Exception as e:
        print(f"工资数据表查询失败: {e}")
        return []

def test_table_name_parsing():
    """测试表名解析功能"""
    print("\n" + "=" * 50)
    print("测试表名解析功能")
    print("=" * 50)
    
    # 创建一个临时的导航面板实例来测试解析方法
    class TestNavigationPanel:
        def _parse_table_name(self, table_name: str):
            """解析表名获取年月和员工类型"""
            parts = table_name.split('_')
            if len(parts) >= 5 and parts[0] == 'salary' and parts[1] == 'data':
                try:
                    year = int(parts[2])
                    month = int(parts[3])
                    employee_type = '_'.join(parts[4:])
                    return year, month, employee_type
                except ValueError:
                    pass
            
            # 如果解析失败，返回默认值
            return 2025, 1, 'unknown'
            
        def _get_employee_type_display_name(self, employee_type: str) -> str:
            """获取员工类型的显示名称"""
            type_map = {
                'active_employees': '全部在职人员',
                'retired_employees': '离休人员', 
                'pension_employees': '退休人员',
                'a_grade_employees': 'A岗职工'
            }
            return type_map.get(employee_type, employee_type)
    
    test_panel = TestNavigationPanel()
    
    # 测试表名
    test_tables = [
        'salary_data_2025_06_active_employees',
        'salary_data_2025_06_retired_employees',
        'salary_data_2025_06_pension_employees',
        'salary_data_2025_06_a_grade_employees',
        'salary_data_2025_05_active_employees'
    ]
    
    print("表名解析测试结果：")
    for table_name in test_tables:
        year, month, employee_type = test_panel._parse_table_name(table_name)
        display_name = test_panel._get_employee_type_display_name(employee_type)
        print(f"  {table_name}")
        print(f"    -> 年份: {year}, 月份: {month}, 类型: {employee_type}")
        print(f"    -> 显示名称: {display_name}")

def test_navigation_panel_creation():
    """测试导航面板创建"""
    print("\n" + "=" * 50)
    print("测试导航面板创建（无GUI）")
    print("=" * 50)
    
    try:
        # 由于我们在测试环境中，不能创建真正的GUI组件
        # 但可以测试数据库相关的逻辑
        
        db_manager = DatabaseManager()
        
        # 模拟导航面板的数据库查询逻辑
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_%';")
            tables = cursor.fetchall()
            
            # 按年月分组
            months_data = {}
            for table in tables:
                table_name = table[0]
                parts = table_name.split('_')
                if len(parts) >= 5 and parts[0] == 'salary' and parts[1] == 'data':
                    try:
                        year = int(parts[2])
                        month = int(parts[3])
                        employee_type = '_'.join(parts[4:])
                        
                        if year == 2025:  # 只处理2025年的数据
                            if month not in months_data:
                                months_data[month] = []
                            months_data[month].append({
                                'table_name': table_name,
                                'employee_type': employee_type
                            })
                    except ValueError:
                        continue
            
            print("导航数据分组结果：")
            for month in sorted(months_data.keys(), reverse=True):
                print(f"  {month}月:")
                for employee_info in months_data[month]:
                    print(f"    - {employee_info['employee_type']} ({employee_info['table_name']})")
                    
        print("导航面板数据准备成功")
        return True
        
    except Exception as e:
        print(f"导航面板创建测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试导航面板动态刷新功能")
    print("项目根目录:", project_root)
    
    # 测试步骤
    tests = [
        ("数据库连接", test_database_connection),
        ("工资数据表查询", test_salary_tables_query),
        ("表名解析功能", test_table_name_parsing),
        ("导航面板创建", test_navigation_panel_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！导航面板动态刷新功能应该可以正常工作。")
    else:
        print("⚠️  有测试失败，需要进一步检查。")

if __name__ == "__main__":
    main() 