#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试导航刷新修复脚本
验证多年份导航结构能否正确构建
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager

def test_navigation_refresh_fix():
    """测试导航刷新修复"""
    print("=== 测试导航刷新修复 ===")
    
    # 初始化管理器
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    print("\n1. 获取完整导航树数据...")
    navigation_data = table_manager.get_navigation_tree_data()
    
    if not navigation_data:
        print("❌ 未获取到导航数据")
        return
    
    print(f"✅ 获取到导航数据，包含 {len(navigation_data)} 个年份")
    
    print("\n2. 分析年份和月份分布...")
    total_months = 0
    total_tables = 0
    
    for year, months_data in sorted(navigation_data.items(), reverse=True):
        month_count = len(months_data)
        total_months += month_count
        
        # 计算该年份的表数量
        year_tables = sum(len(items) for items in months_data.values())
        total_tables += year_tables
        
        print(f"  📅 {year}年: {month_count} 个月份, {year_tables} 个表")
        
        # 显示具体月份
        months = sorted(months_data.keys())
        print(f"    📆 月份: {', '.join(f'{m}月' for m in months)}")
    
    print(f"\n📊 总计: {len(navigation_data)} 个年份, {total_months} 个月份, {total_tables} 个表")
    
    print("\n3. 检查特定数据...")
    
    # 检查2024年11月数据
    if 2024 in navigation_data and 11 in navigation_data[2024]:
        print("✅ 2024年11月数据存在")
        items_2024_11 = navigation_data[2024][11]
        print(f"  包含 {len(items_2024_11)} 个员工类型:")
        for item in items_2024_11:
            print(f"    - {item['display_name']} ({item['icon']})")
    else:
        print("❌ 2024年11月数据不存在")
    
    # 检查2025年1月数据  
    if 2025 in navigation_data and 1 in navigation_data[2025]:
        print("✅ 2025年1月数据存在")
        items_2025_01 = navigation_data[2025][1]
        print(f"  包含 {len(items_2025_01)} 个员工类型:")
        for item in items_2025_01:
            print(f"    - {item['display_name']} ({item['icon']})")
    else:
        print("❌ 2025年1月数据不存在")
    
    print("\n4. 模拟刷新后的导航结构...")
    
    # 模拟构建导航结构
    navigation_structure = {}
    for year, months_data in sorted(navigation_data.items(), reverse=True):
        year_key = f"{year}年"
        navigation_structure[year_key] = {}
        
        for month, items in sorted(months_data.items()):
            month_key = f"{month}月"
            navigation_structure[year_key][month_key] = [item['display_name'] for item in items]
    
    print("🌳 导航结构预览:")
    for year_name, months in navigation_structure.items():
        print(f"  📅 {year_name}")
        for month_name, employees in months.items():
            print(f"    📆 {month_name}: {', '.join(employees)}")
    
    print(f"\n✅ 测试完成，修复后应该能正确显示所有年份和月份的数据")

if __name__ == "__main__":
    test_navigation_refresh_fix() 