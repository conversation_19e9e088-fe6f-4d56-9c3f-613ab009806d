#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试导航启动修复脚本
验证新的数据库同步检查、重试机制和延迟加载是否解决了启动时导航数据丢失的问题
"""

import sys
import os
import time
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager

def test_database_sync_mechanism():
    """测试数据库同步机制"""
    print("=== 测试数据库同步机制 ===")
    
    # 初始化管理器
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    print("\n1. 测试数据库同步检查...")
    try:
        table_manager._ensure_database_sync()
        print("✅ 数据库同步检查完成")
    except Exception as e:
        print(f"❌ 数据库同步检查失败: {e}")
    
    print("\n2. 测试带重试机制的表查询...")
    start_time = time.time()
    tables = table_manager.get_table_list(table_type='salary_data')
    end_time = time.time()
    
    print(f"✅ 查询完成，耗时: {end_time - start_time:.3f}秒")
    print(f"📊 找到 {len(tables)} 个salary_data表")
    
    # 按年份月份统计
    year_month_stats = {}
    for table in tables:
        if 'year' in table and 'month' in table:
            year = table['year']
            month = table['month']
            if year not in year_month_stats:
                year_month_stats[year] = set()
            year_month_stats[year].add(month)
    
    print("\n3. 年份月份分布:")
    for year in sorted(year_month_stats.keys(), reverse=True):
        months = sorted(year_month_stats[year])
        print(f"  📅 {year}年: {len(months)} 个月份 - {months}")
    
    return len(tables), year_month_stats

def test_navigation_tree_data():
    """测试导航树数据生成"""
    print("\n=== 测试导航树数据生成 ===")
    
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    print("\n1. 生成导航树数据...")
    start_time = time.time()
    navigation_data = table_manager.get_navigation_tree_data()
    end_time = time.time()
    
    print(f"✅ 导航数据生成完成，耗时: {end_time - start_time:.3f}秒")
    
    if not navigation_data:
        print("❌ 未获取到导航数据")
        return None
    
    print(f"📊 生成了 {len(navigation_data)} 个年份的导航数据")
    
    print("\n2. 导航结构详情:")
    for year, months_data in sorted(navigation_data.items(), reverse=True):
        print(f"  📅 {year}年:")
        for month, items in sorted(months_data.items()):
            employee_types = [item['display_name'] for item in items]
            print(f"    📆 {month}月: {len(items)} 种员工类型 - {', '.join(employee_types)}")
    
    return navigation_data

def test_multiple_startup_simulations():
    """模拟多次系统启动，测试数据一致性"""
    print("\n=== 模拟多次系统启动测试 ===")
    
    results = []
    
    for i in range(3):
        print(f"\n🔄 模拟第 {i+1} 次系统启动...")
        
        # 重新创建管理器实例（模拟系统重启）
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 模拟启动时的查询
        start_time = time.time()
        tables = table_manager.get_table_list(table_type='salary_data')
        navigation_data = table_manager.get_navigation_tree_data()
        end_time = time.time()
        
        table_count = len(tables)
        navigation_years = len(navigation_data) if navigation_data else 0
        total_months = sum(len(months) for months in navigation_data.values()) if navigation_data else 0
        
        result = {
            'startup_num': i + 1,
            'table_count': table_count,
            'navigation_years': navigation_years,
            'total_months': total_months,
            'query_time': end_time - start_time
        }
        
        results.append(result)
        
        print(f"  📊 表数量: {table_count}, 年份数: {navigation_years}, 月份数: {total_months}")
        print(f"  ⏱️ 查询耗时: {result['query_time']:.3f}秒")
        
        # 模拟系统运行间隔
        time.sleep(0.5)
    
    print("\n3. 启动一致性分析:")
    table_counts = [r['table_count'] for r in results]
    navigation_years = [r['navigation_years'] for r in results]
    total_months = [r['total_months'] for r in results]
    
    print(f"  📈 表数量变化: {table_counts}")
    print(f"  📈 年份数变化: {navigation_years}")
    print(f"  📈 月份数变化: {total_months}")
    
    # 检查一致性
    if len(set(table_counts)) == 1 and len(set(navigation_years)) == 1 and len(set(total_months)) == 1:
        print("✅ 多次启动数据完全一致，修复成功！")
        return True
    else:
        print("❌ 多次启动数据不一致，仍需进一步修复")
        return False

def main():
    """主测试函数"""
    print("🔧 开始测试导航启动修复效果...")
    
    try:
        # 测试1: 数据库同步机制
        table_count, year_stats = test_database_sync_mechanism()
        
        # 测试2: 导航树数据生成
        navigation_data = test_navigation_tree_data()
        
        # 测试3: 多次启动一致性
        consistency_ok = test_multiple_startup_simulations()
        
        print("\n" + "="*50)
        print("📋 测试总结:")
        print(f"  🗄️ 数据库表总数: {table_count}")
        print(f"  📅 年份覆盖: {list(year_stats.keys()) if year_stats else '无'}")
        print(f"  🌳 导航数据: {'正常' if navigation_data else '异常'}")
        print(f"  🔄 启动一致性: {'通过' if consistency_ok else '失败'}")
        
        if consistency_ok and navigation_data:
            print("\n🎉 恭喜！导航启动修复测试全部通过！")
            print("   系统现在应该能够在重启后正确显示所有导航数据。")
        else:
            print("\n⚠️  部分测试未通过，可能需要进一步调试。")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 