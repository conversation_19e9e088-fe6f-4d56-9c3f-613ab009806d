#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新分页阈值脚本

验证将分页阈值从100条降低到30条后，哪些表会显示分页组件
"""

import sqlite3
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_new_pagination_threshold():
    """测试新的分页阈值"""
    
    NEW_THRESHOLD = 30  # 新的分页阈值
    
    db_path = "data/db/salary_system.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有以salary_data开头的表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE 'salary_data_%'
            ORDER BY name DESC
        """)
        
        salary_tables = cursor.fetchall()
        
        print(f"新分页阈值测试 (阈值: {NEW_THRESHOLD}条)")
        print("=" * 80)
        
        # 按年月分组统计
        table_data = {}
        
        for table_name, in salary_tables:
            try:
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                count = cursor.fetchone()[0]
                
                # 解析表名
                parts = table_name.split('_')
                if len(parts) >= 5:
                    year = parts[2]
                    month = parts[3]
                    category = '_'.join(parts[4:])
                    
                    key = f"{year}_{month}"
                    if key not in table_data:
                        table_data[key] = {}
                    
                    table_data[key][category] = count
                    
            except Exception as e:
                print(f"查询表 {table_name} 失败: {e}")
        
        # 分析最新的几个月的数据
        recent_periods = sorted(table_data.keys(), reverse=True)[:3]
        
        print("最近3个月的分页显示情况:")
        print("-" * 80)
        
        for period in recent_periods:
            year, month = period.split('_')
            print(f"\n{year}年{month}月:")
            
            categories = table_data[period]
            for category, count in categories.items():
                # 转换为中文显示
                category_map = {
                    'active_employees': '全部在职人员',
                    'retired_employees': '退休人员', 
                    'pension_employees': '离休人员',
                    'a_grade_employees': 'A岗职工'
                }
                
                display_name = category_map.get(category, category)
                
                # 判断是否显示分页
                will_paginate = count > NEW_THRESHOLD
                pagination_status = "✓ 显示分页" if will_paginate else "✗ 隐藏分页"
                
                print(f"  {display_name}: {count:,}条 → {pagination_status}")
        
        print("\n" + "=" * 80)
        print("分页显示统计:")
        
        # 统计会显示分页的表
        paginated_tables = []
        non_paginated_tables = []
        
        for period in table_data.keys():
            year, month = period.split('_')
            categories = table_data[period]
            
            for category, count in categories.items():
                category_map = {
                    'active_employees': '全部在职人员',
                    'retired_employees': '退休人员', 
                    'pension_employees': '离休人员',
                    'a_grade_employees': 'A岗职工'
                }
                display_name = category_map.get(category, category)
                
                if count > NEW_THRESHOLD:
                    if display_name not in [item[0] for item in paginated_tables]:
                        paginated_tables.append((display_name, count))
                else:
                    if display_name not in [item[0] for item in non_paginated_tables]:
                        non_paginated_tables.append((display_name, count))
        
        print(f"\n将显示分页组件的表类型 (>{NEW_THRESHOLD}条):")
        for table_type, count in paginated_tables:
            print(f"  ✓ {table_type}: {count:,}条")
        
        print(f"\n将隐藏分页组件的表类型 (≤{NEW_THRESHOLD}条):")
        for table_type, count in non_paginated_tables:
            print(f"  ✗ {table_type}: {count:,}条")
        
        print(f"\n改进效果:")
        print(f"  - 原阈值(100条): 只有1种表类型显示分页")
        print(f"  - 新阈值({NEW_THRESHOLD}条): {len(paginated_tables)}种表类型显示分页")
        print(f"  - 改进程度: {len(paginated_tables) - 1}种表类型新增分页功能")
        
        conn.close()
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_new_pagination_threshold() 