#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的分页设置

验证分页阈值30条、页面大小50条的设置是否正常工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.gui.widgets.pagination_widget import PaginationWidget, PaginationState
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

def test_pagination_settings():
    """测试分页组件的优化设置"""
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("分页组件优化测试")
    window.setGeometry(100, 100, 800, 200)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel("分页组件优化测试 - 新设置验证")
    info_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(info_label)
    
    # 创建分页组件
    pagination_widget = PaginationWidget()
    layout.addWidget(pagination_widget)
    
    # 测试结果标签
    result_label = QLabel()
    result_label.setAlignment(Qt.AlignCenter)
    layout.addWidget(result_label)
    
    # 验证设置
    state = pagination_widget.get_current_state()
    default_page_size = pagination_widget.default_page_size
    page_size_options = pagination_widget.page_size_options
    
    results = []
    results.append(f"✓ 默认页面大小: {default_page_size} (期望: 50)")
    results.append(f"✓ 页面大小选项: {page_size_options} (期望: [20, 50, 100, 200])")
    results.append(f"✓ 初始状态页面大小: {state.page_size} (期望: 50)")
    
    # 验证是否符合预期
    all_correct = True
    if default_page_size != 50:
        results[0] = f"✗ 默认页面大小: {default_page_size} (期望: 50)"
        all_correct = False
    
    if page_size_options != [20, 50, 100, 200]:
        results[1] = f"✗ 页面大小选项: {page_size_options} (期望: [20, 50, 100, 200])"
        all_correct = False
    
    if state.page_size != 50:
        results[2] = f"✗ 初始状态页面大小: {state.page_size} (期望: 50)"
        all_correct = False
    
    # 测试模拟数据
    print("=" * 60)
    print("分页组件优化测试结果:")
    print("=" * 60)
    
    for result in results:
        print(result)
    
    # 测试不同数据量的分页显示逻辑
    test_cases = [
        (20, "小数据量", "应该隐藏分页"),
        (50, "中等数据量", "应该显示分页"),
        (100, "大数据量", "应该显示分页"),
        (1396, "超大数据量", "应该显示分页")
    ]
    
    print("\n" + "-" * 60)
    print("不同数据量的分页显示逻辑测试 (阈值: 30条):")
    print("-" * 60)
    
    PAGINATION_THRESHOLD = 30
    
    for total_records, description, expected in test_cases:
        will_show_pagination = total_records > PAGINATION_THRESHOLD
        status = "显示分页" if will_show_pagination else "隐藏分页"
        
        if (will_show_pagination and "显示" in expected) or (not will_show_pagination and "隐藏" in expected):
            result_icon = "✓"
        else:
            result_icon = "✗"
            all_correct = False
        
        print(f"{result_icon} {total_records:4d}条 ({description:8s}) → {status:8s} ({expected})")
    
    # 计算分页数量
    print("\n" + "-" * 60)
    print("分页数量计算测试 (页面大小: 50条):")
    print("-" * 60)
    
    PAGE_SIZE = 50
    for total_records, description, _ in test_cases:
        total_pages = max(1, (total_records + PAGE_SIZE - 1) // PAGE_SIZE)
        print(f"✓ {total_records:4d}条记录 → {total_pages:2d}页 (每页{PAGE_SIZE}条)")
    
    print("\n" + "=" * 60)
    
    if all_correct:
        print("🎉 所有测试通过！分页组件优化设置正确。")
        result_text = "🎉 所有测试通过！分页组件优化设置正确。"
        result_label.setStyleSheet("color: green; font-weight: bold; font-size: 14px;")
    else:
        print("❌ 部分测试失败，请检查配置。")
        result_text = "❌ 部分测试失败，请检查配置。"
        result_label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")
    
    result_label.setText(result_text)
    
    # 测试实际分页功能
    pagination_widget.set_total_records(1396)  # 模拟全部在职人员数据
    
    print(f"\n设置总记录数为1396条后的状态:")
    updated_state = pagination_widget.get_current_state()
    print(f"✓ 当前页: {updated_state.current_page}")
    print(f"✓ 页面大小: {updated_state.page_size}")
    print(f"✓ 总页数: {updated_state.total_pages}")
    print(f"✓ 显示范围: 第{updated_state.start_record}-{updated_state.end_record}条")
    
    window.show()
    return app.exec_()

if __name__ == "__main__":
    test_pagination_settings() 