#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分页字段一致性修复效果
用于验证修复后的分页加载是否保持字段一致性
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QEventLoop
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.utils.log_config import setup_logger

class PaginationFieldConsistencyTester:
    """分页字段一致性测试器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.table_manager = DynamicTableManager(self.db_manager, self.config_manager)
        self.config_sync = ConfigSyncManager()
        
        # 测试配置
        self.test_table = "salary_data_2026_04_a_grade_employees"
        self.test_fields = ['id', 'employee_id', 'employee_name', 'department', 'position', 'allowance', 'total_salary']
        self.page_size = 50
        
    def setup_test_environment(self):
        """设置测试环境"""
        try:
            self.logger.info("=== 设置测试环境 ===")
            
            # 检查测试表是否存在
            if not self.table_manager.table_exists(self.test_table):
                self.logger.error(f"测试表 {self.test_table} 不存在")
                return False
                
            # 获取总记录数
            total_records = self.table_manager.get_table_record_count(self.test_table)
            self.logger.info(f"测试表 {self.test_table} 总记录数: {total_records}")
            
            if total_records <= self.page_size:
                self.logger.warning(f"测试表记录数不足，无法测试分页效果，需要至少{self.page_size + 1}条记录")
                return False
            
            # 调整页面大小以确保有第2页数据
            if total_records < self.page_size * 2:
                # 如果总记录数少于2页，调整页面大小
                self.page_size = total_records // 2 + 1
                self.logger.info(f"调整页面大小为 {self.page_size} 以确保有多页数据进行测试")
            
            # 设置用户字段偏好
            self.logger.info(f"设置用户字段偏好: {self.test_fields}")
            success = self.config_sync.save_user_header_preference(self.test_table, self.test_fields)
            if not success:
                self.logger.warning("保存用户字段偏好失败")
            
            # 验证字段偏好是否生效
            saved_fields = self.config_sync.get_user_header_preference(self.test_table)
            self.logger.info(f"已保存的用户字段偏好: {saved_fields}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置测试环境失败: {e}", exc_info=True)
            return False
    
    def test_pagination_worker_consistency(self):
        """测试PaginationWorker的字段一致性"""
        try:
            self.logger.info("=== 测试PaginationWorker字段一致性 ===")
            
            # 模拟PaginationWorker的加载逻辑
            user_selected_fields = self.config_sync.get_user_header_preference(self.test_table)
            
            page1_data = None
            page2_data = None
            
            # 加载第1页
            if user_selected_fields:
                self.logger.info(f"按用户偏好分页加载字段: {user_selected_fields}")
                page1_data, total_records = self.table_manager.get_dataframe_with_selected_fields(
                    self.test_table, user_selected_fields, 1, self.page_size
                )
            else:
                self.logger.info("分页加载所有字段")
                page1_data, total_records = self.table_manager.get_dataframe_paginated(
                    self.test_table, 1, self.page_size
                )
            
            # 加载第2页
            if user_selected_fields:
                page2_data, _ = self.table_manager.get_dataframe_with_selected_fields(
                    self.test_table, user_selected_fields, 2, self.page_size
                )
            else:
                page2_data, _ = self.table_manager.get_dataframe_paginated(
                    self.test_table, 2, self.page_size
                )
            
            # 验证字段一致性
            if page1_data is not None and page2_data is not None:
                page1_fields = list(page1_data.columns)
                page2_fields = list(page2_data.columns)
                
                self.logger.info(f"第1页字段: {page1_fields} (共{len(page1_fields)}个)")
                self.logger.info(f"第2页字段: {page2_fields} (共{len(page2_fields)}个)")
                
                if page1_fields == page2_fields:
                    self.logger.info("✅ PaginationWorker字段一致性测试通过")
                    return True
                else:
                    self.logger.error("❌ PaginationWorker字段一致性测试失败")
                    self.logger.error(f"第1页字段: {page1_fields}")
                    self.logger.error(f"第2页字段: {page2_fields}")
                    return False
            else:
                self.logger.error("❌ PaginationWorker数据加载失败")
                return False
                
        except Exception as e:
            self.logger.error(f"PaginationWorker字段一致性测试失败: {e}", exc_info=True)
            return False
    
    def test_mainworkspace_consistency(self):
        """测试MainWorkspaceArea回退逻辑的字段一致性"""
        try:
            self.logger.info("=== 测试MainWorkspaceArea回退逻辑字段一致性 ===")
            
            # 模拟MainWorkspaceArea._on_page_changed的修复后逻辑
            user_selected_fields = self.config_sync.get_user_header_preference(self.test_table)
            
            page1_data = None
            page2_data = None
            
            # 加载第1页（修复后的逻辑）
            if user_selected_fields:
                self.logger.info(f"按用户偏好分页加载字段: {user_selected_fields}")
                page1_data, total_count = self.table_manager.get_dataframe_with_selected_fields(
                    self.test_table, user_selected_fields, 1, self.page_size
                )
            else:
                self.logger.info("分页加载所有字段")
                page1_data, total_count = self.table_manager.get_dataframe_paginated(
                    self.test_table, 1, self.page_size
                )
            
            # 加载第2页（修复后的逻辑）
            if user_selected_fields:
                page2_data, _ = self.table_manager.get_dataframe_with_selected_fields(
                    self.test_table, user_selected_fields, 2, self.page_size
                )
            else:
                page2_data, _ = self.table_manager.get_dataframe_paginated(
                    self.test_table, 2, self.page_size
                )
            
            # 验证字段一致性
            if page1_data is not None and page2_data is not None:
                page1_fields = list(page1_data.columns)
                page2_fields = list(page2_data.columns)
                
                self.logger.info(f"第1页字段: {page1_fields} (共{len(page1_fields)}个)")
                self.logger.info(f"第2页字段: {page2_fields} (共{len(page2_fields)}个)")
                
                if page1_fields == page2_fields:
                    self.logger.info("✅ MainWorkspaceArea字段一致性测试通过")
                    return True
                else:
                    self.logger.error("❌ MainWorkspaceArea字段一致性测试失败")
                    self.logger.error(f"第1页字段: {page1_fields}")
                    self.logger.error(f"第2页字段: {page2_fields}")
                    return False
            else:
                self.logger.error("❌ MainWorkspaceArea数据加载失败")
                return False
                
        except Exception as e:
            self.logger.error(f"MainWorkspaceArea字段一致性测试失败: {e}", exc_info=True)
            return False
    
    def test_field_mapping_consistency(self):
        """测试字段映射的一致性"""
        try:
            self.logger.info("=== 测试字段映射一致性 ===")
            
            # 模拟字段映射应用
            from src.gui.prototype.prototype_main_window import PrototypeMainWindow
            
            # 创建临时主窗口实例用于测试字段映射方法
            main_window = PrototypeMainWindow(self.config_manager, self.db_manager, self.table_manager)
            
            # 获取测试数据
            user_selected_fields = self.config_sync.get_user_header_preference(self.test_table)
            if user_selected_fields:
                page1_data, _ = self.table_manager.get_dataframe_with_selected_fields(
                    self.test_table, user_selected_fields, 1, self.page_size
                )
                page2_data, _ = self.table_manager.get_dataframe_with_selected_fields(
                    self.test_table, user_selected_fields, 2, self.page_size
                )
            else:
                page1_data, _ = self.table_manager.get_dataframe_paginated(
                    self.test_table, 1, self.page_size
                )
                page2_data, _ = self.table_manager.get_dataframe_paginated(
                    self.test_table, 2, self.page_size
                )
            
            if page1_data is not None and page2_data is not None:
                # 应用字段映射
                mapped_page1 = main_window._apply_field_mapping_to_dataframe(page1_data, self.test_table)
                mapped_page2 = main_window._apply_field_mapping_to_dataframe(page2_data, self.test_table)
                
                # 验证映射后的字段一致性
                mapped_fields1 = list(mapped_page1.columns)
                mapped_fields2 = list(mapped_page2.columns)
                
                self.logger.info(f"映射后第1页字段: {mapped_fields1} (共{len(mapped_fields1)}个)")
                self.logger.info(f"映射后第2页字段: {mapped_fields2} (共{len(mapped_fields2)}个)")
                
                if mapped_fields1 == mapped_fields2:
                    self.logger.info("✅ 字段映射一致性测试通过")
                    return True
                else:
                    self.logger.error("❌ 字段映射一致性测试失败")
                    self.logger.error(f"映射后第1页字段: {mapped_fields1}")
                    self.logger.error(f"映射后第2页字段: {mapped_fields2}")
                    return False
            else:
                self.logger.error("❌ 字段映射测试数据加载失败")
                return False
                
        except Exception as e:
            self.logger.error(f"字段映射一致性测试失败: {e}", exc_info=True)
            return False
    
    def generate_test_report(self, test_results):
        """生成测试报告"""
        try:
            report = {
                "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "test_table": self.test_table,
                "test_fields": self.test_fields,
                "page_size": self.page_size,
                "test_results": test_results,
                "summary": {
                    "total_tests": len(test_results),
                    "passed_tests": sum(1 for result in test_results.values() if result),
                    "failed_tests": sum(1 for result in test_results.values() if not result),
                    "success_rate": f"{sum(1 for result in test_results.values() if result) / len(test_results) * 100:.1f}%"
                }
            }
            
            # 保存报告到文件
            report_file = Path("temp/pagination_field_consistency_test_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"测试报告已保存到: {report_file}")
            
            # 输出测试总结
            self.logger.info("=== 测试总结 ===")
            self.logger.info(f"总测试数: {report['summary']['total_tests']}")
            self.logger.info(f"通过测试: {report['summary']['passed_tests']}")
            self.logger.info(f"失败测试: {report['summary']['failed_tests']}")
            self.logger.info(f"成功率: {report['summary']['success_rate']}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成测试报告失败: {e}", exc_info=True)
            return None
    
    def run_all_tests(self):
        """运行所有测试"""
        try:
            self.logger.info("开始运行分页字段一致性测试...")
            
            # 设置测试环境
            if not self.setup_test_environment():
                self.logger.error("测试环境设置失败，无法继续测试")
                return False
            
            # 运行各项测试
            test_results = {}
            
            test_results["pagination_worker_consistency"] = self.test_pagination_worker_consistency()
            test_results["mainworkspace_consistency"] = self.test_mainworkspace_consistency()
            test_results["field_mapping_consistency"] = self.test_field_mapping_consistency()
            
            # 生成测试报告
            report = self.generate_test_report(test_results)
            
            # 返回整体测试结果
            all_passed = all(test_results.values())
            
            if all_passed:
                self.logger.info("🎉 所有测试都通过了！分页字段一致性修复生效")
            else:
                self.logger.error("❌ 部分测试失败，需要进一步修复")
            
            return all_passed
            
        except Exception as e:
            self.logger.error(f"运行测试失败: {e}", exc_info=True)
            return False

def main():
    """主函数"""
    print("=== 分页字段一致性测试 ===")
    
    # 创建应用程序（某些功能需要Qt应用程序环境）
    app = QApplication(sys.argv)
    
    try:
        # 创建测试器
        tester = PaginationFieldConsistencyTester()
        
        # 运行测试
        success = tester.run_all_tests()
        
        if success:
            print("\n✅ 测试完成：分页字段一致性修复生效")
            return 0
        else:
            print("\n❌ 测试完成：发现问题需要进一步修复")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        return 1
    finally:
        app.quit()

if __name__ == "__main__":
    sys.exit(main()) 