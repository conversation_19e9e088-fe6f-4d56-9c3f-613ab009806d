#!/usr/bin/env python3
"""
分页字段修复效果验证脚本

验证目标:
1. 确认PaginationWorker现在使用统一字段加载策略
2. 验证分页前后字段数量一致性
3. 检查字段映射在显示层正确应用
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager


def test_pagination_fix():
    """测试分页字段修复效果"""
    print("=" * 80)
    print("分页字段修复效果验证")
    print("=" * 80)
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        table_manager = DynamicTableManager(config_manager)
        config_sync = ConfigSyncManager()
        
        # 测试表名
        test_tables = [
            "salary_data_2026_01_a_grade_employees",  # 英文表头（之前正常）
            "salary_data_2026_04_a_grade_employees"   # 中文表头（之前异常）
        ]
        
        print("\n1. 查找现有表:")
        print("-" * 50)
        
        # 获取所有表名
        all_tables = table_manager.get_table_list()
        salary_tables = [t for t in all_tables if 'salary_data' in t.get('table_name', '')]
        
        print(f"找到 {len(salary_tables)} 个工资数据表:")
        for table_info in salary_tables[:10]:  # 只显示前10个
            table_name = table_info.get('table_name', '')
            print(f"  - {table_name}")
        
        # 选择有代表性的测试表
        if salary_tables:
            test_table = salary_tables[0]['table_name']
            print(f"\n使用测试表: {test_table}")
        else:
            print("没有找到工资数据表，无法进行测试")
            return
        
        print(f"\n2. 测试表 {test_table} 的字段加载:")
        print("-" * 50)
        
        # 检查字段映射
        field_mapping = config_sync.get_mapping(test_table)
        has_mapping = field_mapping is not None and len(field_mapping) > 0
        print(f"字段映射: {'✓ 有映射' if has_mapping else '✗ 无映射'}")
        
        if has_mapping:
            print(f"  映射字段数: {len(field_mapping)}")
        
        # 检查用户偏好
        user_preference = config_sync.get_user_header_preference(test_table)
        has_preference = user_preference is not None and len(user_preference) > 0
        print(f"用户偏好: {'✓ 有偏好' if has_preference else '✗ 无偏好'}")
        
        if has_preference:
            print(f"  偏好字段数: {len(user_preference)}")
            print(f"  偏好字段: {user_preference}")
        
        print(f"\n3. 模拟修复后的分页加载逻辑:")
        print("-" * 50)
        
        # 【修复后的逻辑】统一使用分页加载所有字段
        print("使用统一策略：分页加载所有字段")
        
        try:
            df, total_count = table_manager.get_dataframe_paginated(test_table, 1, 50)
            if df is not None:
                print(f"  ✓ 第1页加载成功: {len(df)}行, {len(df.columns)}列")
                print(f"    字段列表: {df.columns.tolist()}")
                
                # 测试第2页
                df2, total_count2 = table_manager.get_dataframe_paginated(test_table, 2, 50)
                if df2 is not None:
                    print(f"  ✓ 第2页加载成功: {len(df2)}行, {len(df2.columns)}列")
                    
                    # 检查字段一致性
                    if df.columns.equals(df2.columns):
                        print("  ✅ 分页字段一致性: 通过")
                    else:
                        print("  ❌ 分页字段一致性: 失败")
                        print(f"    第1页字段: {df.columns.tolist()}")
                        print(f"    第2页字段: {df2.columns.tolist()}")
                else:
                    print("  ⚠️ 第2页无数据（可能总数据不足100条）")
            else:
                print("  ❌ 分页加载失败")
        except Exception as e:
            print(f"  ❌ 分页加载异常: {e}")
        
        print(f"\n4. 验证字段映射应用:")
        print("-" * 50)
        
        if has_mapping and df is not None:
            print("字段映射应该在显示层应用：")
            
            # 模拟显示层字段映射应用
            mapped_columns = []
            for col in df.columns:
                if col in field_mapping:
                    mapped_columns.append(field_mapping[col])
                else:
                    mapped_columns.append(col)
            
            print(f"  原始字段: {df.columns.tolist()}")
            print(f"  映射后字段: {mapped_columns}")
            
            # 检查是否有中文字段
            has_chinese = any('工号' in col or '姓名' in col or '部门' in col for col in mapped_columns)
            if has_chinese:
                print("  ✅ 字段映射包含中文显示名")
            else:
                print("  ⚠️ 字段映射未包含中文显示名")
        
        print(f"\n5. 修复效果总结:")
        print("-" * 50)
        print("修复前问题:")
        print("  • 有字段映射的表使用用户偏好字段（7个）")
        print("  • 无字段映射的表加载所有字段（16个）")
        print("  • 分页时字段数量不一致")
        print()
        print("修复后效果:")
        print("  • 所有表统一使用分页加载所有字段")
        print("  • 字段映射在显示层应用")
        print("  • 确保分页前后字段一致性")
        print("  • 解决中文表头显示问题")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_pagination_fix() 