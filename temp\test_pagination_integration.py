# -*- coding: utf-8 -*-
"""
分页加载优化集成测试

验证分页功能的完整集成
"""

import sys
import os
import time
import pandas as pd
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_pagination():
    """测试数据库分页查询功能"""
    print("🔍 测试数据库分页查询...")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.system_config.config_manager import ConfigManager
        
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 获取表列表
        tables = table_manager.get_table_list()
        if not tables:
            print("❌ 没有找到数据表，请先导入数据")
            return False
        
        # 选择第一个表进行测试
        test_table = tables[0]['table_name']
        print(f"📊 使用测试表: {test_table}")
        
        # 测试记录数查询
        total_records = table_manager.get_table_record_count(test_table)
        print(f"📈 总记录数: {total_records}")
        
        if total_records == 0:
            print("❌ 测试表为空")
            return False
        
        # 测试分页查询
        page_size = 10
        total_pages = (total_records + page_size - 1) // page_size
        
        print(f"📄 分页测试 - 每页{page_size}条，共{total_pages}页")
        
        # 测试前几页
        test_pages = min(3, total_pages)
        for page in range(1, test_pages + 1):
            start_time = time.time()
            df, count = table_manager.get_dataframe_paginated(test_table, page, page_size)
            elapsed = (time.time() - start_time) * 1000
            
            if df is not None:
                print(f"  ✅ 第{page}页: {len(df)}条记录, 查询耗时: {elapsed:.1f}ms")
            else:
                print(f"  ❌ 第{page}页查询失败")
                return False
        
        print("✅ 数据库分页查询测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库分页测试失败: {e}")
        return False

def test_pagination_widget():
    """测试分页组件功能"""
    print("\n🎨 测试分页组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建Qt应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入分页组件
        from src.gui.widgets.pagination_widget import PaginationWidget, PaginationState
        
        # 创建分页组件
        pagination_widget = PaginationWidget()
        
        # 测试状态设置
        pagination_widget.set_total_records(1396)
        pagination_widget.set_current_page(1)
        pagination_widget.set_page_size(100)
        
        # 获取状态
        state = pagination_widget.get_current_state()
        
        print(f"📊 分页状态测试:")
        print(f"  总记录数: {state.total_records}")
        print(f"  当前页: {state.current_page}/{state.total_pages}")
        print(f"  页面大小: {state.page_size}")
        print(f"  显示范围: {state.start_record}-{state.end_record}")
        
        # 验证计算
        expected_pages = (1396 + 100 - 1) // 100  # 14页
        if state.total_pages == expected_pages:
            print("✅ 分页计算正确")
        else:
            print(f"❌ 分页计算错误，期望{expected_pages}页，实际{state.total_pages}页")
            return False
        
        print("✅ 分页组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 分页组件测试失败: {e}")
        return False

def test_cache_manager():
    """测试缓存管理器功能"""
    print("\n💾 测试缓存管理器...")
    
    try:
        from src.gui.widgets.pagination_cache_manager import PaginationCacheManager
        
        cache_manager = PaginationCacheManager(
            max_cache_entries=10,
            max_memory_mb=10,
            ttl_seconds=60
        )
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'id': range(1, 101),
            'name': [f'员工{i}' for i in range(1, 101)],
            'salary': [5000 + i * 100 for i in range(1, 101)]
        })
        
        table_name = "test_table"
        page = 1
        page_size = 100
        
        # 测试缓存存储
        cache_manager.put_page_data(table_name, page, page_size, test_data)
        cache_manager.put_table_info(table_name, 1000, ['id', 'name', 'salary'])
        
        # 测试缓存读取
        cached_data = cache_manager.get_page_data(table_name, page, page_size)
        cached_info = cache_manager.get_table_info(table_name)
        
        if cached_data is not None and len(cached_data) == 100:
            print("✅ 页面数据缓存成功")
        else:
            print("❌ 页面数据缓存失败")
            return False
        
        if cached_info and cached_info.total_records == 1000:
            print("✅ 表信息缓存成功")
        else:
            print("❌ 表信息缓存失败")
            return False
        
        # 测试缓存统计
        stats = cache_manager.get_cache_stats()
        print(f"📊 缓存统计: {stats}")
        
        print("✅ 缓存管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 缓存管理器测试失败: {e}")
        return False

def test_performance_comparison():
    """测试分页性能对比"""
    print("\n⚡ 分页性能对比测试...")
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.data_storage.database_manager import DatabaseManager
        from src.modules.system_config.config_manager import ConfigManager
        
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 获取测试表
        tables = table_manager.get_table_list()
        if not tables:
            print("❌ 没有找到测试表")
            return False
        
        test_table = tables[0]['table_name']
        total_records = table_manager.get_table_record_count(test_table)
        
        if total_records < 100:
            print("❌ 测试表记录数太少，无法进行性能对比")
            return False
        
        print(f"📊 性能测试表: {test_table} ({total_records}条记录)")
        
        # 测试全量加载
        print("🔄 测试全量加载...")
        start_time = time.time()
        full_df = table_manager.get_dataframe_from_table(test_table)
        full_load_time = (time.time() - start_time) * 1000
        
        if full_df is not None:
            print(f"  📈 全量加载: {len(full_df)}条记录, 耗时: {full_load_time:.1f}ms")
        else:
            print("  ❌ 全量加载失败")
            return False
        
        # 测试分页加载
        print("📄 测试分页加载...")
        page_size = 100
        
        start_time = time.time()
        page_df, count = table_manager.get_dataframe_paginated(test_table, 1, page_size)
        page_load_time = (time.time() - start_time) * 1000
        
        if page_df is not None:
            print(f"  📈 分页加载: {len(page_df)}条记录, 耗时: {page_load_time:.1f}ms")
        else:
            print("  ❌ 分页加载失败")
            return False
        
        # 计算性能提升
        if full_load_time > 0 and page_load_time > 0:
            improvement = ((full_load_time - page_load_time) / full_load_time) * 100
            print(f"🚀 性能提升: {improvement:.1f}% (从{full_load_time:.1f}ms降至{page_load_time:.1f}ms)")
            
            if improvement > 0:
                print("✅ 分页加载性能优于全量加载")
            else:
                print("⚠️ 分页加载性能未显著提升（可能是数据量较小）")
        
        print("✅ 性能对比测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 分页加载优化集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("数据库分页查询", test_database_pagination()))
    test_results.append(("分页UI组件", test_pagination_widget()))
    test_results.append(("缓存管理器", test_cache_manager()))
    test_results.append(("性能对比", test_performance_comparison()))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:.<20} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！分页加载优化集成成功！")
        print("\n📈 预期效果:")
        print("  • 导航切换响应时间: 2-3秒 → 200-300ms (85%提升)")
        print("  • 内存占用: 减少70% (只加载当前页数据)")
        print("  • 数据库负载: 减少80% (避免全表扫描)")
        print("  • 用户体验: 分页导航、缓存加速、状态持久化")
        return True
    else:
        print(f"\n⚠️ {total - passed}项测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 