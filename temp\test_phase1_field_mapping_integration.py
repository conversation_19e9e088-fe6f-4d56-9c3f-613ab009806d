#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 1 字段映射基础功能集成测试程序

测试内容:
1. HeaderEditManager 功能测试
2. ConfigSyncManager 功能测试  
3. VirtualizedExpandableTable 表头双击编辑集成测试
4. 字段映射自动应用测试
5. 配置持久化测试

创建时间: 2025-06-24
作者: 开发团队
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
    QPushButton, QLabel, QTextEdit, QGroupBox, QTabWidget,
    QTableWidget, QTableWidgetItem, QMessageBox, QSplitter,
    QFormLayout, QLineEdit, QSpinBox, QCheckBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPalette, QColor

from src.utils.log_config import setup_logger
from src.modules.data_import.header_edit_manager import HeaderEditManager
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable


class Phase1TestMainWindow(QMainWindow):
    """Phase 1 字段映射集成测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        
        # 组件管理器
        self.config_sync_manager = ConfigSyncManager()
        self.header_edit_manager = HeaderEditManager(self.config_sync_manager)
        
        # 测试状态
        self.test_results = {}
        self.current_test_table = None
        
        self.setup_ui()
        self.setup_connections()
        
        # 启动测试
        QTimer.singleShot(1000, self.start_comprehensive_test)
        
        self.logger.info("Phase 1 字段映射集成测试窗口初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Phase 1 字段映射基础功能集成测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建左右分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：测试表格区域
        self.setup_table_area(splitter)
        
        # 右侧：测试控制和结果区域
        self.setup_control_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 600])
    
    def setup_table_area(self, parent):
        """设置表格测试区域"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        
        # 表格说明
        table_label = QLabel("表头双击编辑测试区域")
        table_label.setFont(QFont("微软雅黑", 12, QFont.Bold))
        table_layout.addWidget(table_label)
        
        # 创建测试表格
        self.test_table = VirtualizedExpandableTable()
        self.test_table.set_table_name("test_field_mapping_table")
        table_layout.addWidget(self.test_table)
        
        # 表格操作按钮
        button_layout = QHBoxLayout()
        
        self.load_test_data_btn = QPushButton("加载测试数据")
        self.apply_mapping_btn = QPushButton("应用字段映射")
        self.refresh_display_btn = QPushButton("刷新显示")
        self.reset_mapping_btn = QPushButton("重置映射")
        
        button_layout.addWidget(self.load_test_data_btn)
        button_layout.addWidget(self.apply_mapping_btn)
        button_layout.addWidget(self.refresh_display_btn)
        button_layout.addWidget(self.reset_mapping_btn)
        button_layout.addStretch()
        
        table_layout.addLayout(button_layout)
        
        parent.addWidget(table_widget)
    
    def setup_control_area(self, parent):
        """设置控制和结果区域"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        control_layout.addWidget(tab_widget)
        
        # 测试控制页
        self.setup_test_control_tab(tab_widget)
        
        # 测试结果页
        self.setup_test_results_tab(tab_widget)
        
        # 配置状态页
        self.setup_config_status_tab(tab_widget)
        
        # 日志输出页
        self.setup_log_output_tab(tab_widget)
        
        parent.addWidget(control_widget)
    
    def setup_test_control_tab(self, tab_widget):
        """设置测试控制页"""
        control_tab = QWidget()
        control_layout = QVBoxLayout(control_tab)
        
        # 测试配置组
        config_group = QGroupBox("测试配置")
        config_layout = QFormLayout(config_group)
        
        self.table_name_edit = QLineEdit("test_field_mapping_table")
        self.auto_mapping_enabled = QCheckBox()
        self.auto_mapping_enabled.setChecked(True)
        self.edit_enabled = QCheckBox()
        self.edit_enabled.setChecked(True)
        
        config_layout.addRow("表名:", self.table_name_edit)
        config_layout.addRow("启用自动映射:", self.auto_mapping_enabled)
        config_layout.addRow("启用表头编辑:", self.edit_enabled)
        
        control_layout.addWidget(config_group)
        
        # 测试操作组
        test_group = QGroupBox("测试操作")
        test_layout = QVBoxLayout(test_group)
        
        self.run_component_test_btn = QPushButton("运行组件测试")
        self.run_integration_test_btn = QPushButton("运行集成测试")
        self.run_persistence_test_btn = QPushButton("运行持久化测试")
        self.run_all_tests_btn = QPushButton("运行全部测试")
        
        test_layout.addWidget(self.run_component_test_btn)
        test_layout.addWidget(self.run_integration_test_btn)
        test_layout.addWidget(self.run_persistence_test_btn)
        test_layout.addWidget(self.run_all_tests_btn)
        
        control_layout.addWidget(test_group)
        
        # 快速操作组
        quick_group = QGroupBox("快速操作")
        quick_layout = QVBoxLayout(quick_group)
        
        self.simulate_edit_btn = QPushButton("模拟表头编辑")
        self.check_mapping_btn = QPushButton("检查映射状态")
        self.export_config_btn = QPushButton("导出配置")
        self.clear_all_btn = QPushButton("清除所有配置")
        
        quick_layout.addWidget(self.simulate_edit_btn)
        quick_layout.addWidget(self.check_mapping_btn)
        quick_layout.addWidget(self.export_config_btn)
        quick_layout.addWidget(self.clear_all_btn)
        
        control_layout.addWidget(quick_group)
        control_layout.addStretch()
        
        tab_widget.addTab(control_tab, "测试控制")
    
    def setup_test_results_tab(self, tab_widget):
        """设置测试结果页"""
        results_tab = QWidget()
        results_layout = QVBoxLayout(results_tab)
        
        # 结果显示
        self.results_display = QTextEdit()
        self.results_display.setFont(QFont("Consolas", 10))
        results_layout.addWidget(self.results_display)
        
        # 结果操作按钮
        button_layout = QHBoxLayout()
        
        self.clear_results_btn = QPushButton("清除结果")
        self.save_results_btn = QPushButton("保存结果")
        self.refresh_results_btn = QPushButton("刷新结果")
        
        button_layout.addWidget(self.clear_results_btn)
        button_layout.addWidget(self.save_results_btn)
        button_layout.addWidget(self.refresh_results_btn)
        button_layout.addStretch()
        
        results_layout.addLayout(button_layout)
        
        tab_widget.addTab(results_tab, "测试结果")
    
    def setup_config_status_tab(self, tab_widget):
        """设置配置状态页"""
        status_tab = QWidget()
        status_layout = QVBoxLayout(status_tab)
        
        # 配置状态显示
        self.config_display = QTextEdit()
        self.config_display.setFont(QFont("Consolas", 10))
        status_layout.addWidget(self.config_display)
        
        # 状态操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_config_btn = QPushButton("刷新配置")
        self.validate_config_btn = QPushButton("验证配置")
        
        button_layout.addWidget(self.refresh_config_btn)
        button_layout.addWidget(self.validate_config_btn)
        button_layout.addStretch()
        
        status_layout.addLayout(button_layout)
        
        tab_widget.addTab(status_tab, "配置状态")
    
    def setup_log_output_tab(self, tab_widget):
        """设置日志输出页"""
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        
        # 日志显示
        self.log_display = QTextEdit()
        self.log_display.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_display)
        
        # 日志操作按钮
        button_layout = QHBoxLayout()
        
        self.clear_log_btn = QPushButton("清除日志")
        self.save_log_btn = QPushButton("保存日志")
        
        button_layout.addWidget(self.clear_log_btn)
        button_layout.addWidget(self.save_log_btn)
        button_layout.addStretch()
        
        log_layout.addLayout(button_layout)
        
        tab_widget.addTab(log_tab, "日志输出")
    
    def setup_connections(self):
        """设置信号连接"""
        # 表格操作连接
        self.load_test_data_btn.clicked.connect(self.load_test_data)
        self.apply_mapping_btn.clicked.connect(self.apply_test_mapping)
        self.refresh_display_btn.clicked.connect(self.refresh_table_display)
        self.reset_mapping_btn.clicked.connect(self.reset_field_mapping)
        
        # 测试操作连接
        self.run_component_test_btn.clicked.connect(self.run_component_tests)
        self.run_integration_test_btn.clicked.connect(self.run_integration_tests)
        self.run_persistence_test_btn.clicked.connect(self.run_persistence_tests)
        self.run_all_tests_btn.clicked.connect(self.run_all_tests)
        
        # 快速操作连接
        self.simulate_edit_btn.clicked.connect(self.simulate_header_edit)
        self.check_mapping_btn.clicked.connect(self.check_mapping_status)
        self.export_config_btn.clicked.connect(self.export_configuration)
        self.clear_all_btn.clicked.connect(self.clear_all_configuration)
        
        # 结果操作连接
        self.clear_results_btn.clicked.connect(self.clear_test_results)
        self.save_results_btn.clicked.connect(self.save_test_results)
        self.refresh_results_btn.clicked.connect(self.refresh_test_results)
        
        # 配置操作连接
        self.refresh_config_btn.clicked.connect(self.refresh_config_status)
        self.validate_config_btn.clicked.connect(self.validate_configuration)
        
        # 日志操作连接
        self.clear_log_btn.clicked.connect(self.clear_log_output)
        self.save_log_btn.clicked.connect(self.save_log_output)
        
        # 表格信号连接
        self.test_table.header_edited.connect(self.on_header_edited)
        self.test_table.field_mapping_updated.connect(self.on_field_mapping_updated)
    
    def start_comprehensive_test(self):
        """启动综合测试"""
        self.log_message("=== Phase 1 字段映射基础功能集成测试开始 ===")
        self.log_message(f"测试时间: {datetime.now().isoformat()}")
        
        # 加载测试数据
        self.load_test_data()
        
        # 显示初始状态
        self.refresh_config_status()
        
        self.log_message("测试环境准备完成，请手动进行测试或点击测试按钮")
    
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 创建测试数据
            test_data = [
                {"工号": "EMP001", "姓名": "张三", "部门名称": "技术部", 
                 "2025年岗位工资": 8000, "2025年薪级工资": 3000, "津贴": 500,
                 "2025年奖励性绩效预发": 2000, "应发工资": 13500, "2025公积金": 1350},
                {"工号": "EMP002", "姓名": "李四", "部门名称": "市场部", 
                 "2025年岗位工资": 7500, "2025年薪级工资": 2800, "津贴": 400,
                 "2025年奖励性绩效预发": 1800, "应发工资": 12500, "2025公积金": 1250},
                {"工号": "EMP003", "姓名": "王五", "部门名称": "财务部", 
                 "2025年岗位工资": 7000, "2025年薪级工资": 2500, "津贴": 300,
                 "2025年奖励性绩效预发": 1500, "应发工资": 11300, "2025公积金": 1130},
            ]
            
            headers = list(test_data[0].keys())
            
            # 设置数据到表格
            self.test_table.set_data(test_data, headers)
            
            # 生成自动映射
            if self.auto_mapping_enabled.isChecked():
                self.generate_auto_mapping(headers)
            
            self.log_message(f"✓ 测试数据加载成功: {len(test_data)} 行, {len(headers)} 列")
            
        except Exception as e:
            self.log_message(f"✗ 测试数据加载失败: {e}")
    
    def generate_auto_mapping(self, headers: List[str]):
        """生成自动字段映射"""
        try:
            # 模拟AutoFieldMappingGenerator的映射规则
            mapping_rules = {
                "工号": "工号",
                "姓名": "姓名", 
                "部门名称": "部门",
                "2025年岗位工资": "岗位工资",
                "2025年薪级工资": "薪级工资",
                "津贴": "津贴补贴",
                "2025年奖励性绩效预发": "奖金",
                "应发工资": "应发合计",
                "2025公积金": "五险一金个人"
            }
            
            # 生成映射配置
            auto_mapping = {}
            for header in headers:
                auto_mapping[header] = mapping_rules.get(header, header)
            
            # 保存映射
            table_name = self.table_name_edit.text()
            self.config_sync_manager.save_mapping(table_name, auto_mapping)
            
            # 应用映射
            self.test_table.apply_field_mapping(auto_mapping)
            
            self.log_message(f"✓ 自动字段映射生成完成: {len(auto_mapping)} 个字段")
            
        except Exception as e:
            self.log_message(f"✗ 自动字段映射生成失败: {e}")
    
    def apply_test_mapping(self):
        """应用测试映射"""
        try:
            # 获取当前配置
            table_name = self.table_name_edit.text()
            mapping = self.config_sync_manager.get_mapping(table_name)
            
            if mapping:
                self.test_table.apply_field_mapping(mapping.get('field_mappings', {}))
                self.log_message("✓ 字段映射应用成功")
            else:
                self.log_message("✗ 未找到字段映射配置")
                
        except Exception as e:
            self.log_message(f"✗ 字段映射应用失败: {e}")
    
    def refresh_table_display(self):
        """刷新表格显示"""
        try:
            self.test_table._refresh_header_display()
            self.log_message("✓ 表格显示刷新完成")
        except Exception as e:
            self.log_message(f"✗ 表格显示刷新失败: {e}")
    
    def reset_field_mapping(self):
        """重置字段映射"""
        try:
            table_name = self.table_name_edit.text()
            
            # 清除配置
            self.config_sync_manager.clear_mapping(table_name)
            
            # 重新加载原始表头
            self.load_test_data()
            
            self.log_message("✓ 字段映射重置完成")
            
        except Exception as e:
            self.log_message(f"✗ 字段映射重置失败: {e}")
    
    def run_component_tests(self):
        """运行组件测试"""
        self.log_message("\n=== 组件功能测试开始 ===")
        
        # 测试HeaderEditManager
        self.test_header_edit_manager()
        
        # 测试ConfigSyncManager
        self.test_config_sync_manager()
        
        self.log_message("=== 组件功能测试完成 ===\n")
    
    def test_header_edit_manager(self):
        """测试HeaderEditManager"""
        try:
            self.log_message("1. HeaderEditManager 功能测试")
            
            # 测试验证功能
            # 测试有效字段名
            result = self.header_edit_manager._validate_field_name("岗位工资", "test_table")
            if result.is_valid:
                self.log_message("  ✓ 有效字段名验证通过")
            else:
                self.log_message(f"  ✗ 有效字段名验证失败: {result.error_message}")
            
            # 测试无效字段名
            result = self.header_edit_manager._validate_field_name("", "test_table")
            if not result.is_valid:
                self.log_message("  ✓ 无效字段名验证通过")
            else:
                self.log_message("  ✗ 无效字段名验证失败")
            
            # 测试编辑统计
            stats = self.header_edit_manager.get_edit_statistics()
            self.log_message(f"  ✓ 编辑统计获取成功: {stats}")
            
        except Exception as e:
            self.log_message(f"  ✗ HeaderEditManager测试失败: {e}")
    
    def test_config_sync_manager(self):
        """测试ConfigSyncManager"""
        try:
            self.log_message("2. ConfigSyncManager 功能测试")
            
            # 测试保存映射
            test_mapping = {"test_field": "测试字段", "test_field2": "测试字段2"}
            success = self.config_sync_manager.save_mapping("test_table", test_mapping)
            
            if success:
                self.log_message("  ✓ 映射保存成功")
            else:
                self.log_message("  ✗ 映射保存失败")
            
            # 测试获取映射
            retrieved = self.config_sync_manager.get_mapping("test_table")
            if retrieved and 'field_mappings' in retrieved:
                self.log_message("  ✓ 映射获取成功")
            else:
                self.log_message("  ✗ 映射获取失败")
            
            # 测试更新映射
            success = self.config_sync_manager.update_mapping("test_table", "test_field", "新测试字段")
            if success:
                self.log_message("  ✓ 映射更新成功")
            else:
                self.log_message("  ✗ 映射更新失败")
            
        except Exception as e:
            self.log_message(f"  ✗ ConfigSyncManager测试失败: {e}")
    
    def run_integration_tests(self):
        """运行集成测试"""
        self.log_message("\n=== 集成功能测试开始 ===")
        
        # 测试表格组件集成
        self.test_table_integration()
        
        # 测试信号传递
        self.test_signal_integration()
        
        self.log_message("=== 集成功能测试完成 ===\n")
    
    def test_table_integration(self):
        """测试表格组件集成"""
        try:
            self.log_message("1. 表格组件集成测试")
            
            # 测试表名设置
            self.test_table.set_table_name("integration_test_table")
            if self.test_table.current_table_name == "integration_test_table":
                self.log_message("  ✓ 表名设置成功")
            else:
                self.log_message("  ✗ 表名设置失败")
            
            # 测试编辑功能启用/禁用
            self.test_table.set_header_edit_enabled(False)
            if not self.test_table.header_edit_enabled:
                self.log_message("  ✓ 表头编辑禁用成功")
            else:
                self.log_message("  ✗ 表头编辑禁用失败")
            
            self.test_table.set_header_edit_enabled(True)
            if self.test_table.header_edit_enabled:
                self.log_message("  ✓ 表头编辑启用成功")
            else:
                self.log_message("  ✗ 表头编辑启用失败")
            
            # 测试映射统计
            stats = self.test_table.get_field_mapping_stats()
            self.log_message(f"  ✓ 映射统计获取成功: {stats}")
            
        except Exception as e:
            self.log_message(f"  ✗ 表格组件集成测试失败: {e}")
    
    def test_signal_integration(self):
        """测试信号集成"""
        try:
            self.log_message("2. 信号集成测试")
            
            # 记录当前信号连接状态
            header_signal_connected = len(self.test_table.header_edited.receivers()) > 0
            mapping_signal_connected = len(self.test_table.field_mapping_updated.receivers()) > 0
            
            if header_signal_connected:
                self.log_message("  ✓ header_edited 信号连接正常")
            else:
                self.log_message("  ✗ header_edited 信号未连接")
            
            if mapping_signal_connected:
                self.log_message("  ✓ field_mapping_updated 信号连接正常")
            else:
                self.log_message("  ✗ field_mapping_updated 信号未连接")
            
        except Exception as e:
            self.log_message(f"  ✗ 信号集成测试失败: {e}")
    
    def run_persistence_tests(self):
        """运行持久化测试"""
        self.log_message("\n=== 持久化功能测试开始 ===")
        
        # 测试配置文件持久化
        self.test_config_persistence()
        
        self.log_message("=== 持久化功能测试完成 ===\n")
    
    def test_config_persistence(self):
        """测试配置持久化"""
        try:
            self.log_message("1. 配置持久化测试")
            
            # 创建测试配置
            test_table_name = "persistence_test_table"
            test_mapping = {
                "原始字段1": "映射字段1",
                "原始字段2": "映射字段2", 
                "原始字段3": "映射字段3"
            }
            
            # 保存配置
            success = self.config_sync_manager.save_mapping(test_table_name, test_mapping)
            if success:
                self.log_message("  ✓ 配置保存成功")
            else:
                self.log_message("  ✗ 配置保存失败")
                return
            
            # 重新创建管理器(模拟程序重启)
            new_manager = ConfigSyncManager()
            
            # 尝试加载配置
            loaded_mapping = new_manager.get_mapping(test_table_name)
            if loaded_mapping and 'field_mappings' in loaded_mapping:
                loaded_fields = loaded_mapping['field_mappings']
                if loaded_fields == test_mapping:
                    self.log_message("  ✓ 配置加载成功，数据一致")
                else:
                    self.log_message(f"  ✗ 配置加载失败，数据不一致")
                    self.log_message(f"    保存的: {test_mapping}")
                    self.log_message(f"    加载的: {loaded_fields}")
            else:
                self.log_message("  ✗ 配置加载失败")
            
        except Exception as e:
            self.log_message(f"  ✗ 配置持久化测试失败: {e}")
    
    def run_all_tests(self):
        """运行全部测试"""
        self.log_message("\n" + "="*60)
        self.log_message("开始运行全部测试...")
        self.log_message("="*60)
        
        # 清除之前的结果
        self.test_results.clear()
        
        # 依次运行所有测试
        self.run_component_tests()
        self.run_integration_tests()
        self.run_persistence_tests()
        
        # 生成测试报告
        self.generate_test_report()
        
        self.log_message("="*60)
        self.log_message("全部测试完成！")
        self.log_message("="*60 + "\n")
    
    def generate_test_report(self):
        """生成测试报告"""
        try:
            self.log_message("\n=== 测试报告生成 ===")
            
            # 统计信息
            total_tests = 0
            passed_tests = 0
            failed_tests = 0
            
            # 分析日志中的测试结果
            log_content = self.log_display.toPlainText()
            lines = log_content.split('\n')
            
            for line in lines:
                if '✓' in line:
                    total_tests += 1
                    passed_tests += 1
                elif '✗' in line:
                    total_tests += 1
                    failed_tests += 1
            
            # 生成报告
            success_rate = (passed_tests/total_tests*100) if total_tests > 0 else 0
            report = f"""
测试报告摘要:
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 失败测试: {failed_tests}
- 成功率: {success_rate:.1f}%

组件状态:
- HeaderEditManager: 可用
- ConfigSyncManager: 可用  
- VirtualizedExpandableTable: 可用
- 字段映射集成: 已完成

Phase 1 集成状态: {'✓ 成功' if failed_tests == 0 else '✗ 需要修复'}
"""
            
            self.results_display.setText(report)
            self.log_message("测试报告生成完成")
            
        except Exception as e:
            self.log_message(f"测试报告生成失败: {e}")
    
    def simulate_header_edit(self):
        """模拟表头编辑操作"""
        try:
            # 模拟双击第二列（索引1）
            if self.test_table.columnCount() > 1:
                self.test_table._on_header_double_clicked(1)
                self.log_message("✓ 模拟表头编辑操作完成")
            else:
                self.log_message("✗ 表格列数不足，无法模拟编辑")
        except Exception as e:
            self.log_message(f"✗ 模拟表头编辑失败: {e}")
    
    def check_mapping_status(self):
        """检查映射状态"""
        try:
            table_name = self.table_name_edit.text()
            stats = self.test_table.get_field_mapping_stats()
            
            self.log_message(f"当前映射状态: {json.dumps(stats, ensure_ascii=False, indent=2)}")
            
        except Exception as e:
            self.log_message(f"✗ 检查映射状态失败: {e}")
    
    def export_configuration(self):
        """导出配置"""
        try:
            table_name = self.table_name_edit.text()
            config = self.config_sync_manager.get_mapping(table_name)
            
            if config:
                config_str = json.dumps(config, ensure_ascii=False, indent=2)
                self.config_display.setText(config_str)
                self.log_message("✓ 配置导出成功")
            else:
                self.log_message("✗ 未找到配置数据")
                
        except Exception as e:
            self.log_message(f"✗ 配置导出失败: {e}")
    
    def clear_all_configuration(self):
        """清除所有配置"""
        try:
            table_name = self.table_name_edit.text()
            self.config_sync_manager.clear_mapping(table_name)
            self.log_message("✓ 配置清除成功")
        except Exception as e:
            self.log_message(f"✗ 配置清除失败: {e}")
    
    def on_header_edited(self, column: int, old_name: str, new_name: str):
        """表头编辑信号处理"""
        self.log_message(f"表头编辑信号: 列{column}, {old_name} -> {new_name}")
    
    def on_field_mapping_updated(self, table_name: str, mapping: dict):
        """字段映射更新信号处理"""
        self.log_message(f"字段映射更新信号: 表{table_name}, {len(mapping)}个字段")
    
    def refresh_config_status(self):
        """刷新配置状态"""
        try:
            table_name = self.table_name_edit.text()
            config = self.config_sync_manager.get_mapping(table_name)
            
            if config:
                config_str = json.dumps(config, ensure_ascii=False, indent=2)
                self.config_display.setText(config_str)
            else:
                self.config_display.setText("无配置数据")
                
        except Exception as e:
            self.config_display.setText(f"配置获取失败: {e}")
    
    def validate_configuration(self):
        """验证配置"""
        try:
            table_name = self.table_name_edit.text()
            config = self.config_sync_manager.get_mapping(table_name)
            
            if not config:
                self.log_message("✗ 配置验证失败: 无配置数据")
                return
            
            # 验证配置结构
            required_keys = ['metadata', 'field_mappings', 'edit_history']
            for key in required_keys:
                if key not in config:
                    self.log_message(f"✗ 配置验证失败: 缺少 {key}")
                    return
            
            self.log_message("✓ 配置验证通过")
            
        except Exception as e:
            self.log_message(f"✗ 配置验证失败: {e}")
    
    def clear_test_results(self):
        """清除测试结果"""
        self.results_display.clear()
    
    def save_test_results(self):
        """保存测试结果"""
        try:
            results = self.results_display.toPlainText()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"temp/phase1_test_results_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(results)
            
            self.log_message(f"✓ 测试结果保存到: {filename}")
            
        except Exception as e:
            self.log_message(f"✗ 测试结果保存失败: {e}")
    
    def refresh_test_results(self):
        """刷新测试结果"""
        self.generate_test_report()
    
    def clear_log_output(self):
        """清除日志输出"""
        self.log_display.clear()
    
    def save_log_output(self):
        """保存日志输出"""
        try:
            logs = self.log_display.toPlainText()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"temp/phase1_test_logs_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(logs)
            
            self.log_message(f"✓ 日志保存到: {filename}")
            
        except Exception as e:
            self.log_message(f"✗ 日志保存失败: {e}")
    
    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.log_display.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
        # 处理GUI事件
        QApplication.processEvents()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Phase 1 字段映射集成测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建并显示主窗口
    window = Phase1TestMainWindow()
    window.show()
    
    # 运行应用程序
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main()) 