"""
测试数据导入对话框预览功能修复
验证预览弹窗是否只出现一次
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import pyqtSignal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.dialogs import DataImportDialog
from src.utils.log_config import setup_logger

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据导入对话框预览功能测试")
        self.setGeometry(100, 100, 400, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        test_btn = QPushButton("打开数据导入对话框")
        test_btn.clicked.connect(self.open_import_dialog)
        layout.addWidget(test_btn)
        
        # 说明标签
        from PyQt5.QtWidgets import QLabel
        info_label = QLabel("""
测试说明：
1. 点击按钮打开数据导入对话框
2. 选择一个Excel文件（项目中的测试文件）
3. 点击"数据预览"按钮
4. 检查是否只弹出一个信息窗（修复前会弹出两个相同的窗口）

测试文件位置：
- data/工资表/2025年5月份正式工工资（报财务)  最终版.xls
- data/异动人员/5月异动人员5.7.xlsx
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { padding: 10px; background-color: #f0f0f0; border-radius: 5px; }")
        layout.addWidget(info_label)
        
    def open_import_dialog(self):
        """打开数据导入对话框"""
        try:
            dialog = DataImportDialog(self)
            
            # 记录对话框打开
            self.logger.info("数据导入对话框已打开，请测试预览功能")
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                QMessageBox.information(self, "测试结果", "导入对话框正常关闭")
            else:
                QMessageBox.information(self, "测试结果", "导入对话框被取消")
                
        except Exception as e:
            self.logger.error(f"打开导入对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开导入对话框失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("数据导入预览功能测试")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    # 显示测试说明
    QMessageBox.information(
        window, "测试说明", 
        "这是一个用于验证数据导入对话框预览功能修复的测试程序。\n\n"
        "修复内容：\n"
        "- 移除了预览按钮的重复信号连接\n"
        "- 现在预览时只会弹出一个信息窗口\n\n"
        "请按照窗口中的说明进行测试。"
    )
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 