"""
完整系统启动测试

测试修复后的main.py启动效果和UI元素显示
"""

import sys
import os
import subprocess
import time

def test_system_startup():
    """测试系统启动"""
    print("=" * 60)
    print("完整系统启动测试")
    print("=" * 60)
    
    print("1. 检查修复状态...")
    
    # 检查main.py的修改
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
        
    if 'main_window_refactored' in content:
        print("✅ main.py已更新为使用修复后的MainWindow")
    else:
        print("❌ main.py仍在使用旧的主窗口")
        return False
    
    if 'ensure_ui_visibility' in content:
        print("✅ main.py包含UI强制显示代码")
    else:
        print("❌ main.py缺少UI强制显示代码")
        return False
    
    print("\n2. 检查重构文件...")
    
    refactored_file = 'src/gui/main_window_refactored.py'
    if os.path.exists(refactored_file):
        with open(refactored_file, 'r', encoding='utf-8') as f:
            refactored_content = f.read()
        
        if '_force_show_ui_elements' in refactored_content:
            print("✅ 重构文件包含强制显示方法")
        else:
            print("❌ 重构文件缺少强制显示方法")
            return False
        
        if 'setNativeMenuBar(False)' in refactored_content:
            print("✅ 重构文件禁用了原生菜单栏")
        else:
            print("❌ 重构文件未禁用原生菜单栏")
            return False
    else:
        print("❌ 重构文件不存在")
        return False
    
    print("\n3. 所有修复检查通过！")
    return True

def provide_startup_instructions():
    """提供启动说明"""
    print("\n" + "=" * 60)
    print("系统启动说明")
    print("=" * 60)
    
    print("现在您可以使用以下命令启动系统:")
    print("python main.py")
    print()
    print("启动后应该看到:")
    print("- ✅ 完整的菜单栏 (文件、数据、异动、报告、设置、帮助)")
    print("- ✅ 功能丰富的工具栏 (11个主要功能按钮)")
    print("- ✅ 信息状态栏 (显示状态和时间)")
    print("- ✅ 简化的左侧导航 (专注数据浏览)")

def create_startup_script():
    """创建快速启动脚本"""
    print("\n" + "=" * 60)
    print("创建快速启动脚本")
    print("=" * 60)
    
    # Windows启动脚本
    windows_script = '''@echo off
echo 启动月度工资异动处理系统...
python main.py
pause
'''
    
    with open('temp/start_system.bat', 'w', encoding='utf-8') as f:
        f.write(windows_script)
    
    # Python启动脚本
    python_script = '''#!/usr/bin/env python3
"""
月度工资异动处理系统快速启动脚本
"""

import subprocess
import sys
import os

def main():
    print("="*60)
    print("月度工资异动处理系统")
    print("="*60)
    print("正在启动系统...")
    
    try:
        # 启动系统
        result = subprocess.run([sys.executable, 'main.py'], 
                               cwd=os.path.dirname(os.path.abspath(__file__)) + '/..')
        
        print(f"系统退出，退出码: {result.returncode}")
        
    except KeyboardInterrupt:
        print("\\n用户中断启动")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == '__main__':
    main()
'''
    
    with open('temp/start_system.py', 'w', encoding='utf-8') as f:
        f.write(python_script)
    
    print("快速启动脚本已创建:")
    print("- Windows: temp/start_system.bat")
    print("- Python: temp/start_system.py")

def main():
    """主函数"""
    print(f"{'='*60}")
    print("月度工资异动处理系统 - 菜单栏工具栏修复验证")
    print(f"{'='*60}")
    
    # 测试系统状态
    if test_system_startup():
        print("\n🎉 修复验证成功！")
        provide_startup_instructions()
        create_startup_script()
        return True
    else:
        print("\n❌ 发现问题，请检查修复状态")
        return False

if __name__ == '__main__':
    try:
        success = main()
        
        if success:
            print(f"\n{'='*60}")
            print("修复完成！现在可以正常启动系统了。")
            print("运行: python main.py")
            print(f"{'='*60}")
        else:
            print("\n需要进一步检查和修复")
            
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc() 