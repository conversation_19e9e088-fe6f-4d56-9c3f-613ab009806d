#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""测试系统启动修复 - 验证在数据库为空时能正常启动"""

import sys
import os
import time
from unittest.mock import MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

# Mock PyQt5 to avoid GUI initialization
sys.modules['PyQt5'] = MagicMock()
sys.modules['PyQt5.QtWidgets'] = MagicMock()
sys.modules['PyQt5.QtCore'] = MagicMock()
sys.modules['PyQt5.QtGui'] = MagicMock()

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager

def test_empty_database_handling():
    """测试空数据库处理"""
    
    print("🧪 测试空数据库处理...")
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        
        # 测试1: 检查期望表数量调整
        expected_count = table_manager._get_expected_table_count('salary_data')
        print(f"📊 期望的salary_data表数量: {expected_count}")
        
        if expected_count <= 1:
            print("✅ 期望表数量已调整为合理值")
        else:
            print("❌ 期望表数量仍然过高，可能导致误判")
        
        # 测试2: 获取表列表（测试重试逻辑）
        print("\n📋 测试表列表获取...")
        start_time = time.time()
        
        all_tables = table_manager.get_table_list()
        salary_tables = table_manager.get_table_list('salary_data')
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  获取表列表耗时: {duration:.2f}秒")
        print(f"📊 总表数: {len(all_tables)}")
        print(f"💰 工资数据表数: {len(salary_tables)}")
        
        if duration < 5.0:  # 如果在5秒内完成，说明没有过度重试
            print("✅ 表列表获取速度正常，没有过度重试")
        else:
            print("❌ 表列表获取耗时过长，可能存在重试问题")
        
        # 测试3: 导航树数据获取
        print("\n🌲 测试导航树数据获取...")
        start_time = time.time()
        
        navigation_data = table_manager.get_navigation_tree_data()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  获取导航数据耗时: {duration:.2f}秒")
        print(f"📊 导航数据结构: {type(navigation_data)}")
        print(f"📊 导航数据为空: {not navigation_data}")
        
        if duration < 3.0:
            print("✅ 导航数据获取速度正常")
        else:
            print("❌ 导航数据获取耗时过长")
        
        # 测试4: 模拟导航面板逻辑
        print("\n🧭 模拟导航面板启动逻辑...")
        
        # 模拟检查数据库是否为空的逻辑
        all_tables_check = table_manager.get_table_list()
        salary_tables_check = [t for t in all_tables_check if t.get('table_name', '').startswith('salary_data_')]
        
        if len(salary_tables_check) == 0:
            print("✅ 正确检测到数据库中没有工资数据表")
            print("✅ 系统应该直接使用fallback数据，不进行重试")
        else:
            print(f"📊 检测到 {len(salary_tables_check)} 个工资数据表")
        
        print("\n🎯 测试总结:")
        print("✅ 空数据库处理测试完成")
        print("✅ 系统应该能够在数据库为空时正常启动")
        print("✅ 不会出现无限重试循环")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False

def test_performance_with_empty_db():
    """测试空数据库性能"""
    
    print("\n⚡ 性能测试...")
    
    try:
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)
        
        # 多次调用测试稳定性
        total_time = 0
        iterations = 5
        
        for i in range(iterations):
            start_time = time.time()
            salary_tables = table_manager.get_table_list('salary_data')
            end_time = time.time()
            
            iteration_time = end_time - start_time
            total_time += iteration_time
            print(f"第{i+1}次调用耗时: {iteration_time:.3f}秒")
        
        avg_time = total_time / iterations
        print(f"\n📊 平均调用时间: {avg_time:.3f}秒")
        
        if avg_time < 0.5:
            print("✅ 性能测试通过 - 平均响应时间良好")
        else:
            print("⚠️  性能测试警告 - 平均响应时间较慢")
        
        return avg_time < 1.0  # 1秒内完成认为是可接受的
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始系统启动修复测试...\n")
    
    test1_result = test_empty_database_handling()
    test2_result = test_performance_with_empty_db()
    
    print("\n" + "="*50)
    print("📋 测试结果总结:")
    print(f"✅ 空数据库处理测试: {'通过' if test1_result else '失败'}")
    print(f"✅ 性能测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！系统修复成功。")
        print("💡 建议:")
        print("   1. 运行数据恢复脚本重新导入Excel数据")
        print("   2. 重新启动系统验证修复效果")
    else:
        print("\n❌ 部分测试失败，需要进一步检查代码。") 