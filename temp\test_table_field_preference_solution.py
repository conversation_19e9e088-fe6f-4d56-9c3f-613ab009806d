#!/usr/bin/env python3
"""
表级字段偏好解决方案测试脚本

验证内容：
1. 表级字段偏好的保存和读取
2. 分页数据一致性
3. 显示层字段过滤功能
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager


def test_table_field_preference_solution():
    """测试表级字段偏好解决方案"""
    print("=" * 80)
    print("表级字段偏好解决方案测试")
    print("=" * 80)
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(db_path=None, config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
        config_sync = ConfigSyncManager()
        
        # 测试表
        test_tables = [
            "salary_data_2026_01_a_grade_employees",  # 英文表头
            "salary_data_2026_04_a_grade_employees"   # 中文表头
        ]
        
        for table_name in test_tables:
            print(f"\n【测试表】: {table_name}")
            print("-" * 50)
            
            # 检查表是否存在
            if not table_manager.table_exists(table_name):
                print(f"⚠️  表 {table_name} 不存在，跳过测试")
                continue
            
            # 1. 获取表的所有字段
            df_sample, _ = table_manager.get_dataframe_paginated(table_name, 1, 5)
            all_fields = df_sample.columns.tolist() if df_sample is not None else []
            
            print(f"表字段总数: {len(all_fields)}")
            print(f"所有字段: {all_fields}")
            
            if not all_fields:
                print("⚠️  表无数据，跳过测试")
                continue
            
            # 2. 模拟设置表级字段偏好
            preferred_fields = all_fields[:7]  # 选择前7个字段
            print(f"\n设置偏好字段: {preferred_fields}")
            
            success = config_sync.save_table_field_preference(table_name, preferred_fields)
            print(f"保存偏好结果: {'✅ 成功' if success else '❌ 失败'}")
            
            # 3. 读取偏好设置
            loaded_fields = config_sync.get_table_field_preference(table_name)
            print(f"读取偏好字段: {loaded_fields}")
            print(f"偏好设置匹配: {'✅ 是' if loaded_fields == preferred_fields else '❌ 否'}")
            
            # 4. 测试分页数据加载（所有字段）
            df_page1, total_records = table_manager.get_dataframe_paginated(table_name, 1, 10)
            df_page2, _ = table_manager.get_dataframe_paginated(table_name, 2, 10)
            
            print(f"\n分页测试:")
            print(f"第1页字段数: {len(df_page1.columns) if df_page1 is not None else 0}")
            print(f"第2页字段数: {len(df_page2.columns) if df_page2 is not None else 0}")
            
            if df_page1 is not None and df_page2 is not None:
                fields_consistent = list(df_page1.columns) == list(df_page2.columns)
                print(f"分页字段一致性: {'✅ 一致' if fields_consistent else '❌ 不一致'}")
            
            # 5. 测试显示层字段过滤
            if df_page1 is not None and loaded_fields:
                # 模拟显示层过滤
                available_fields = [field for field in loaded_fields if field in df_page1.columns]
                if available_fields:
                    df_filtered = df_page1[available_fields].copy()
                    print(f"\n显示层过滤测试:")
                    print(f"原始字段数: {len(df_page1.columns)}")
                    print(f"过滤后字段数: {len(df_filtered.columns)}")
                    print(f"显示字段: {df_filtered.columns.tolist()}")
                    print(f"字段过滤正常: {'✅ 是' if len(df_filtered.columns) < len(df_page1.columns) else '❌ 否'}")
                else:
                    print("⚠️  偏好字段在数据中不存在")
            
            print()
        
        # 6. 测试全局偏好迁移功能
        print("\n【偏好迁移测试】")
        print("-" * 50)
        
        existing_tables = [table for table in test_tables if table_manager.table_exists(table)]
        if existing_tables:
            migration_success = config_sync.migrate_global_to_table_preferences(existing_tables)
            print(f"偏好迁移结果: {'✅ 成功' if migration_success else '❌ 失败'}")
        
        # 7. 查看所有表级偏好
        all_preferences = config_sync.get_all_table_field_preferences()
        print(f"\n当前所有表级偏好:")
        for table, fields in all_preferences.items():
            print(f"  {table}: {len(fields)} 个字段")
        
        print("\n" + "=" * 80)
        print("✅ 测试完成！")
        print("新的解决方案特点：")
        print("1. 🔄 分页数据加载：始终加载所有字段，确保一致性")
        print("2. 🎯 显示层过滤：根据表级偏好过滤显示字段")
        print("3. 📊 表级配置：每个表独立设置字段偏好")
        print("4. 🔧 向后兼容：支持全局偏好自动迁移")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def demonstrate_solution_benefits():
    """演示解决方案的优势"""
    print("\n" + "🎯 解决方案优势分析:")
    print("-" * 50)
    
    benefits = [
        "✅ 分页一致性：所有页面字段数量和类型完全一致",
        "✅ 个性化显示：每个表可独立设置显示字段",
        "✅ 数据完整性：隐藏的字段仍存在，可随时恢复显示",
        "✅ 性能优化：只在显示层过滤，不影响数据查询性能",
        "✅ 用户体验：避免了字段映射不匹配的显示问题",
        "✅ 向后兼容：自动迁移旧的全局偏好设置"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print("\n🔧 技术实现:")
    print("-" * 50)
    
    implementation = [
        "1. PaginationWorker: 移除用户偏好逻辑，统一加载所有字段",
        "2. ConfigSyncManager: 新增表级字段偏好管理方法",
        "3. MainWindow: 添加显示层字段过滤逻辑",
        "4. 配置文件: 新增table_preferences节点存储表级偏好",
        "5. 兼容性: 保留旧方法同时支持新的表级偏好"
    ]
    
    for item in implementation:
        print(f"  {item}")


if __name__ == "__main__":
    test_table_field_preference_solution()
    demonstrate_solution_benefits() 