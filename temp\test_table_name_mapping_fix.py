#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表名映射修复验证脚本

验证修复后的中英文名称映射是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager

def test_table_name_mapping():
    """测试表名映射"""
    print("=== 表名映射修复验证 ===")
    
    # 初始化管理器
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    dynamic_table_manager = DynamicTableManager(
        db_manager=db_manager, 
        config_manager=config_manager
    )
    
    # 映射逻辑（复制修复后的逻辑）
    def generate_table_name_from_path(path_list):
        if len(path_list) < 4:
            return f"path_too_short_{len(path_list)}_levels"

        base_name = "salary_data"
        year = path_list[1].replace('年', '')
        month = f"{int(path_list[2].replace('月', '')):02d}"

        category = path_list[3]
        category_en_map = {
            "退休人员": "retired_employees",
            "离休人员": "pension_employees", 
            "全部在职人员": "active_employees",
            "A岗职工": "a_grade_employees"
        }
        category_en = category_en_map.get(category, "unknown")
        
        return f"{base_name}_{year}_{month}_{category_en}"
    
    # 测试路径
    test_paths = [
        ['工资表', '2025年', '1月', '全部在职人员'],
        ['工资表', '2025年', '1月', '退休人员'],
        ['工资表', '2025年', '1月', '离休人员'],
        ['工资表', '2025年', '1月', 'A岗职工'],
        ['工资表', '2025年', '2月', '全部在职人员'],
        ['工资表', '2025年', '6月', 'A岗职工'],
    ]
    
    print("测试表名生成和数据库匹配...")
    for path in test_paths:
        table_name = generate_table_name_from_path(path)
        path_str = ' > '.join(path)
        
        print(f"\n路径: {path_str}")
        print(f"生成表名: {table_name}")
        
        # 检查表是否存在
        exists = dynamic_table_manager.table_exists(table_name)
        print(f"表存在: {exists}")
        
        if exists:
            try:
                count = dynamic_table_manager.get_table_record_count(table_name)
                print(f"记录数: {count}")
                print("✅ 匹配成功！")
            except Exception as e:
                print(f"❌ 获取记录数失败: {e}")
        else:
            print("❌ 表不存在")
    
    print("\n=== 数据库中的实际表名 ===")
    try:
        tables = dynamic_table_manager.get_table_list()
        salary_tables = [t for t in tables if isinstance(t, dict) and 'salary_data' in t.get('table_name', '')]
        
        for table in salary_tables:
            table_name = table.get('table_name', '')
            if 'salary_data' in table_name:
                print(f"  {table_name}")
                
    except Exception as e:
        print(f"获取表列表失败: {e}")

if __name__ == "__main__":
    test_table_name_mapping() 