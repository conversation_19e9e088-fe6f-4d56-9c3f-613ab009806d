#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证导航刷新修复效果

启动系统并测试：
1. 系统启动后导航面板是否显示7月数据
2. 手动刷新功能是否正常工作

创建时间: 2025-01-27
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from src.utils.log_config import setup_logger
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 设置日志
logger = setup_logger("verify_navigation_fix")

def test_navigation_display():
    """测试导航面板显示"""
    logger.info("=== 测试导航面板显示 ===")
    
    app = QApplication(sys.argv)
    
    try:
        # 创建主窗口
        main_window = PrototypeMainWindow()
        
        # 检查导航面板是否存在
        if hasattr(main_window, 'navigation_panel'):
            nav_panel = main_window.navigation_panel
            logger.info("✅ 导航面板创建成功")
            
            # 检查是否有force_refresh_salary_data方法
            if hasattr(nav_panel, 'force_refresh_salary_data'):
                logger.info("✅ force_refresh_salary_data方法存在")
                
                # 测试强制刷新
                logger.info("执行强制刷新测试...")
                nav_panel.force_refresh_salary_data()
                logger.info("✅ 强制刷新执行完成")
                
            else:
                logger.error("❌ force_refresh_salary_data方法不存在")
            
            # 检查导航到路径的方法
            if hasattr(nav_panel, 'navigate_to_path'):
                logger.info("✅ navigate_to_path方法存在")
                
                # 测试导航到7月数据
                test_path = "工资表 > 2025年 > 7月 > 全部在职人员"
                logger.info(f"测试导航到: {test_path}")
                try:
                    nav_panel.navigate_to_path(test_path)
                    logger.info("✅ 导航测试执行完成")
                except Exception as e:
                    logger.error(f"❌ 导航测试失败: {e}")
                    
            else:
                logger.error("❌ navigate_to_path方法不存在")
                
        else:
            logger.error("❌ 导航面板不存在")
        
        # 显示窗口
        main_window.show()
        logger.info("系统窗口已显示，请检查左侧导航面板是否显示7月数据")
        
        # 创建信息对话框
        msg = QMessageBox()
        msg.setWindowTitle("导航刷新修复验证")
        msg.setText("请检查以下内容：\n\n1. 左侧导航面板是否显示'7月'选项\n2. 点击7月下的各个员工类型是否能正常加载数据\n3. 点击工具栏的'刷新'按钮是否能更新导航\n\n如果以上功能都正常，说明修复成功！")
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setIcon(QMessageBox.Information)
        
        # 延迟显示对话框，确保主窗口先显示
        QTimer.singleShot(2000, msg.exec_)
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return 1

def main():
    """主函数"""
    logger.info("开始验证导航刷新修复效果")
    logger.info("=" * 50)
    logger.info("修复内容:")
    logger.info("1. 增强了数据库管理器的连接处理")
    logger.info("2. 改进了导航面板的刷新机制")
    logger.info("3. 优化了数据导入后的导航更新逻辑")
    logger.info("4. 添加了详细的日志记录和错误处理")
    logger.info("=" * 50)
    
    # 启动测试
    result = test_navigation_display()
    
    if result == 0:
        logger.info("✅ 验证完成，系统正常退出")
    else:
        logger.error("❌ 验证过程中出现问题")
    
    return result

if __name__ == "__main__":
    sys.exit(main()) 