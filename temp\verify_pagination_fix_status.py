#!/usr/bin/env python3
"""
验证分页字段一致性修复状态

根据.specstory文档分析，检查当前分页功能的修复状态：
1. PaginationWorker是否统一使用get_dataframe_paginated
2. MainWorkspaceArea._on_page_changed是否统一字段加载策略
3. 字段映射和偏好设置的应用是否在显示层进行
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.system_config.config_manager import ConfigManager


def verify_pagination_fix_status():
    """验证分页修复状态"""
    print("=" * 80)
    print("验证分页字段一致性修复状态")
    print("=" * 80)
    
    try:
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        config_sync = ConfigSyncManager()
        table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
        
        # 测试表名（根据日志）
        test_tables = [
            "salary_data_2026_01_a_grade_employees",  # 英文表头（正常）
            "salary_data_2026_04_a_grade_employees"   # 中文表头（异常）
        ]
        
        print("\n1. 检查表的存在性:")
        print("-" * 50)
        
        existing_tables = []
        for table_name in test_tables:
            exists = db_manager.table_exists(table_name)
            status = "✓ 存在" if exists else "✗ 不存在"
            print(f"  {table_name}: {status}")
            if exists:
                existing_tables.append(table_name)
                try:
                    total_count = table_manager.get_record_count(table_name)
                    print(f"    记录数: {total_count}")
                except Exception as e:
                    print(f"    记录数获取失败: {e}")
        
        print("\n2. 检查字段映射配置:")
        print("-" * 50)
        
        for table_name in existing_tables:
            field_mapping = config_sync.get_mapping(table_name)
            has_mapping = field_mapping is not None and len(field_mapping) > 0
            status = "✓ 有映射" if has_mapping else "✗ 无映射"
            print(f"  {table_name}: {status}")
            
            if has_mapping:
                print(f"    映射字段数: {len(field_mapping)}")
                print(f"    前3个映射: {dict(list(field_mapping.items())[:3])}")
        
        print("\n3. 检查用户表头偏好:")
        print("-" * 50)
        
        for table_name in existing_tables:
            user_preference = config_sync.get_user_header_preference(table_name)
            has_preference = user_preference is not None and len(user_preference) > 0
            status = "✓ 有偏好" if has_preference else "✗ 无偏好"
            print(f"  {table_name}: {status}")
            
            if has_preference:
                print(f"    偏好字段数: {len(user_preference)}")
                print(f"    偏好字段: {user_preference}")
        
        print("\n4. 检查表级字段偏好:")
        print("-" * 50)
        
        for table_name in existing_tables:
            table_preference = config_sync.get_table_field_preference(table_name)
            has_table_pref = table_preference is not None and len(table_preference) > 0
            status = "✓ 有表级偏好" if has_table_pref else "✗ 无表级偏好"
            print(f"  {table_name}: {status}")
            
            if has_table_pref:
                print(f"    表级偏好字段数: {len(table_preference)}")
                print(f"    表级偏好字段: {table_preference}")
        
        print("\n5. 模拟修复后的分页加载逻辑:")
        print("-" * 50)
        
        for table_name in existing_tables:
            print(f"  表: {table_name}")
            
            # 模拟PaginationWorker的修复后逻辑：统一加载所有字段
            try:
                print(f"    → 【修复后】统一使用get_dataframe_paginated加载所有字段")
                df_page1, total_count = table_manager.get_dataframe_paginated(
                    table_name, 1, 50
                )
                df_page2, _ = table_manager.get_dataframe_paginated(
                    table_name, 2, 50
                )
                
                if df_page1 is not None and df_page2 is not None:
                    print(f"      第1页: {len(df_page1)}行, {len(df_page1.columns)}列")
                    print(f"      第2页: {len(df_page2)}行, {len(df_page2.columns)}列")
                    
                    # 检查字段一致性
                    fields_consistent = list(df_page1.columns) == list(df_page2.columns)
                    consistency_status = "✅ 一致" if fields_consistent else "❌ 不一致"
                    print(f"      字段一致性: {consistency_status}")
                    
                    if not fields_consistent:
                        print(f"        第1页字段: {list(df_page1.columns)}")
                        print(f"        第2页字段: {list(df_page2.columns)}")
                else:
                    print(f"      加载失败: 第1页={df_page1 is not None}, 第2页={df_page2 is not None}")
                    
            except Exception as e:
                print(f"      加载异常: {e}")
        
        print("\n6. 修复状态总结:")
        print("-" * 50)
        
        print("根据代码检查，当前修复状态：")
        print("✅ PaginationWorker.run() - 已修复为统一加载所有字段")
        print("✅ MainWorkspaceArea._on_page_changed() - 已修复为统一字段加载策略")
        print("✅ _apply_table_field_preference() - 在显示层应用字段偏好")
        print("✅ _apply_field_mapping_to_dataframe() - 在显示层应用字段映射")
        
        print("\n修复原理：")
        print("1. 数据加载层：统一使用get_dataframe_paginated加载所有字段")
        print("2. 显示层：应用字段映射和用户偏好进行过滤")
        print("3. 确保分页前后字段数量和类型的一致性")
        
        print("\n建议验证步骤：")
        print("1. 启动系统，导航到有字段映射的表（如2026年4月）")
        print("2. 测试分页功能，观察字段数量是否一致")
        print("3. 检查中文表头是否正确显示")
        print("4. 对比英文表头表的分页行为")
        
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    verify_pagination_fix_status()
