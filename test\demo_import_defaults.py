#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入默认设置功能演示

演示新实现的智能默认设置功能，包括：
1. 人员类别与工作表智能匹配
2. 默认设置管理
3. 用户偏好学习

创建时间: 2025-01-20
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_import.import_defaults_manager import ImportDefaultsManager
from src.modules.data_import.smart_sheet_matcher import SmartSheetMatcher


def demo_basic_functionality():
    """演示基本功能"""
    print("=" * 60)
    print("🚀 数据导入默认设置功能演示")
    print("=" * 60)
    
    # 1. 创建管理器
    print("\n1️⃣ 初始化管理器...")
    defaults_manager = ImportDefaultsManager()
    matcher = SmartSheetMatcher(defaults_manager)
    print("✅ 管理器初始化完成")
    
    # 2. 展示默认设置
    print("\n2️⃣ 默认设置:")
    settings = defaults_manager.load_settings()
    important_settings = ['start_row', 'import_mode', 'import_strategy', 'table_template', 'auto_match_sheet']
    for key in important_settings:
        if key in settings:
            print(f"   {key}: {settings[key]}")
    print("   ...")  # 表示还有其他设置
    
    # 3. 展示类别映射
    print("\n3️⃣ 人员类别映射规则:")
    mapping = defaults_manager.get_category_sheet_mapping()
    for category, keywords in list(mapping.items())[:5]:  # 只显示前5个
        print(f"   {category}: {keywords}")
    print("   ...")
    
    return defaults_manager, matcher


def demo_smart_matching():
    """演示智能匹配功能"""
    print("\n" + "=" * 60)
    print("🧠 智能工作表匹配演示")
    print("=" * 60)
    
    defaults_manager, matcher = demo_basic_functionality()
    
    # 测试场景
    test_scenarios = [
        {
            'category': '全部在职人员',
            'sheets': ['全部在职人员', '离休人员', '退休人员', '临时工'],
            'description': '精确匹配场景'
        },
        {
            'category': '全部在职人员',
            'sheets': ['在职人员工资表', '离休干部', '退休职工', '临聘人员'],
            'description': '模糊匹配场景'
        },
        {
            'category': '离休人员',
            'sheets': ['离休干部表', '退休人员', '在职员工', '临时工'],
            'description': '部分匹配场景'
        },
        {
            'category': '实习生',
            'sheets': ['见习人员', '培训学员', '正式员工', '临时工'],
            'description': '语义匹配场景'
        },
        {
            'category': '全部在职人员',
            'sheets': ['销售数据', '财务报表', '库存信息'],
            'description': '无匹配场景'
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 场景 {i}: {scenario['description']}")
        print(f"   人员类别: {scenario['category']}")
        print(f"   工作表列表: {scenario['sheets']}")
        
        # 执行匹配
        result = matcher.find_best_match(scenario['category'], scenario['sheets'])
        
        if result:
            print(f"   ✅ 匹配结果: {result.sheet_name}")
            print(f"   📊 匹配类型: {result.match_type}")
            print(f"   🎯 匹配得分: {result.score:.3f}")
            print(f"   🔑 匹配关键词: {result.matched_keyword}")
        else:
            print("   ❌ 未找到匹配项")


def demo_smart_defaults():
    """演示智能默认设置"""
    print("\n" + "=" * 60)
    print("⚙️ 智能默认设置演示")
    print("=" * 60)
    
    defaults_manager = ImportDefaultsManager()
    
    # 测试不同类别的智能设置
    categories = ['全部在职人员', '离休人员', '退休人员', '临时工', '实习生']
    
    for category in categories:
        print(f"\n👥 人员类别: {category}")
        settings = defaults_manager.get_smart_defaults_for_category(category)
        
        print(f"   导入模式: {settings['import_mode']}")
        print(f"   导入策略: {settings['import_strategy']}")
        print(f"   表模板: {settings['table_template']}")
        print(f"   表创建模式: {settings['create_table_mode']}")
        print(f"   自动匹配: {settings['auto_match_sheet']}")


def demo_user_preference_learning():
    """演示用户偏好学习"""
    print("\n" + "=" * 60)
    print("🎓 用户偏好学习演示")
    print("=" * 60)
    
    matcher = SmartSheetMatcher()
    
    category = '全部在职人员'
    sheet_names = ['工资表A', '工资表B', '工资表C']
    
    print(f"📋 场景设置:")
    print(f"   人员类别: {category}")
    print(f"   工作表选项: {sheet_names}")
    
    # 初始匹配（无偏好）
    print(f"\n🔍 初始匹配（无用户偏好）:")
    result = matcher.find_best_match(category, sheet_names)
    if result:
        print(f"   匹配结果: {result.sheet_name} (类型: {result.match_type})")
    else:
        print("   无匹配结果")
    
    # 模拟用户多次选择工资表B
    print(f"\n📚 学习阶段 - 用户多次选择 '工资表B':")
    for i in range(3):
        matcher.record_user_choice(category, '工资表B')
        print(f"   第 {i+1} 次选择: 工资表B")
    
    # 再次匹配（有偏好）
    print(f"\n🎯 学习后匹配（有用户偏好）:")
    result = matcher.find_best_match(category, sheet_names)
    if result:
        print(f"   匹配结果: {result.sheet_name} (类型: {result.match_type})")
        if result.match_type == 'preference':
            print("   ✅ 成功应用用户偏好！")
    else:
        print("   无匹配结果")


def demo_match_suggestions():
    """演示匹配建议功能"""
    print("\n" + "=" * 60)
    print("💡 匹配建议功能演示")
    print("=" * 60)
    
    matcher = SmartSheetMatcher()
    
    category = '全部在职人员'
    sheet_names = [
        '全部在职人员',      # 精确匹配
        '在职人员工资表',    # 模糊匹配
        '现职员工数据',      # 语义匹配
        '销售数据',          # 无关
        '财务报表'           # 无关
    ]
    
    print(f"📋 场景设置:")
    print(f"   人员类别: {category}")
    print(f"   工作表选项: {sheet_names}")
    
    print(f"\n💡 获取匹配建议 (前3个):")
    suggestions = matcher.get_match_suggestions(category, sheet_names, top_n=3)
    
    if suggestions:
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion.sheet_name}")
            print(f"      类型: {suggestion.match_type}")
            print(f"      得分: {suggestion.score:.3f}")
            print(f"      关键词: {suggestion.matched_keyword}")
            print()
    else:
        print("   无匹配建议")


def main():
    """主函数"""
    try:
        # 运行所有演示
        demo_smart_matching()
        demo_smart_defaults()
        demo_user_preference_learning()
        demo_match_suggestions()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("=" * 60)
        print("\n📝 功能总结:")
        print("✅ 智能工作表匹配 - 支持精确、模糊、语义匹配")
        print("✅ 智能默认设置 - 根据人员类别自动调整")
        print("✅ 用户偏好学习 - 记录并应用用户选择习惯")
        print("✅ 匹配建议功能 - 提供多个匹配选项供用户选择")
        print("✅ 详细日志记录 - 完整的操作日志和错误处理")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
