"""
响应时间优化模块测试

测试覆盖：
- ResponseTimeOptimizer 主控制器测试
- EventProcessor 事件处理器测试
- UIThreadManager UI线程管理器测试
- 优化策略和装饰器测试
- 性能指标记录和报告测试
- 配置和错误处理测试
"""

import pytest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtTest import QTest

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../src'))

from gui.response_optimizer import (
    ResponseTimeOptimizer,
    EventProcessor,
    UIThreadManager,
    OptimizationConfig,
    ResponsePriority,
    EventType,
    ResponseMetrics,
    optimize_response_time,
    create_response_optimizer
)


class TestOptimizationConfig:
    """优化配置测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = OptimizationConfig()
        
        # 验证默认阈值
        assert config.critical_threshold == 16.0
        assert config.high_threshold == 33.0
        assert config.normal_threshold == 100.0
        assert config.low_threshold == 1000.0
        
        # 验证线程池配置
        assert config.max_workers == 4
        assert config.thread_pool_timeout == 30.0
        
        # 验证事件队列配置
        assert config.max_queue_size == 1000
        assert config.batch_size == 10
        assert config.batch_timeout == 0.016
        
        # 验证监控配置
        assert config.metrics_buffer_size == 1000
        assert config.performance_check_interval == 1.0
        
    def test_custom_config(self):
        """测试自定义配置"""
        config = OptimizationConfig(
            critical_threshold=10.0,
            max_workers=8,
            batch_size=20
        )
        
        assert config.critical_threshold == 10.0
        assert config.max_workers == 8
        assert config.batch_size == 20


class TestResponseMetrics:
    """响应时间指标测试"""
    
    def test_metrics_creation(self):
        """测试指标创建"""
        start_time = time.time()
        end_time = start_time + 0.1
        duration = end_time - start_time
        
        metrics = ResponseMetrics(
            event_type="test_event",
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            priority=ResponsePriority.HIGH,
            success=True
        )
        
        assert metrics.event_type == "test_event"
        assert metrics.start_time == start_time
        assert metrics.end_time == end_time
        assert metrics.duration == duration
        assert metrics.priority == ResponsePriority.HIGH
        assert metrics.success is True
        assert metrics.error_message == ""
        
    def test_response_time_ms_property(self):
        """测试响应时间毫秒属性"""
        metrics = ResponseMetrics(
            event_type="test",
            start_time=0,
            end_time=0.1,
            duration=0.1,
            priority=ResponsePriority.NORMAL
        )
        
        assert metrics.response_time_ms == 100.0
        
    def test_metrics_with_error(self):
        """测试错误指标"""
        metrics = ResponseMetrics(
            event_type="error_event",
            start_time=0,
            end_time=0,
            duration=0,
            priority=ResponsePriority.CRITICAL,
            success=False,
            error_message="Test error"
        )
        
        assert metrics.success is False
        assert metrics.error_message == "Test error"


@pytest.fixture
def qt_app():
    """Qt应用程序fixture"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app


class TestEventProcessor:
    """事件处理器测试"""
    
    def test_event_processor_creation(self, qt_app):
        """测试事件处理器创建"""
        config = OptimizationConfig()
        processor = EventProcessor(config)
        
        assert processor.config == config
        assert not processor.processing
        assert processor.batch_timer is not None
        
    def test_add_event(self, qt_app):
        """测试添加事件"""
        config = OptimizationConfig()
        processor = EventProcessor(config)
        
        # 创建测试回调
        callback = Mock()
        
        # 添加事件
        processor.add_event(
            EventType.USER_INPUT,
            callback,
            ResponsePriority.HIGH,
            "arg1", "arg2",
            kwarg1="value1"
        )
        
        # 验证事件队列不为空
        assert not processor.event_queue.empty()
        
    def test_batch_processing(self, qt_app):
        """测试批量处理"""
        config = OptimizationConfig(batch_size=2)
        processor = EventProcessor(config)
        
        # 创建测试回调
        callbacks = [Mock() for _ in range(3)]
        
        # 添加多个事件
        for i, callback in enumerate(callbacks):
            processor.add_event(
                EventType.DATA_PROCESSING,
                callback,
                ResponsePriority.NORMAL,
                f"arg{i}"
            )
            
        # 等待批量处理
        QTest.qWait(50)
        
        # 验证回调被调用
        for callback in callbacks[:2]:  # 批量大小为2
            callback.assert_called()
            
    def test_event_priority_ordering(self, qt_app):
        """测试事件优先级排序"""
        config = OptimizationConfig()
        processor = EventProcessor(config)
        
        # 添加不同优先级的事件
        high_callback = Mock()
        low_callback = Mock()
        
        processor.add_event(EventType.USER_INPUT, low_callback, ResponsePriority.LOW)
        processor.add_event(EventType.USER_INPUT, high_callback, ResponsePriority.CRITICAL)
        
        # 手动处理一批事件
        processor._process_batch()
        
        # 验证高优先级事件先被处理
        assert high_callback.called
        
    def test_queue_full_handling(self, qt_app):
        """测试队列满处理"""
        config = OptimizationConfig(max_queue_size=2)
        processor = EventProcessor(config)
        
        # 填满队列
        for i in range(3):  # 超过队列大小
            processor.add_event(
                EventType.USER_INPUT,
                Mock(),
                ResponsePriority.NORMAL
            )
            
        # 验证没有异常抛出（应该丢弃多余事件）
        assert processor.event_queue.qsize() <= config.max_queue_size


class TestUIThreadManager:
    """UI线程管理器测试"""
    
    def test_thread_manager_creation(self, qt_app):
        """测试线程管理器创建"""
        config = OptimizationConfig()
        manager = UIThreadManager(config)
        
        assert manager.config == config
        assert manager.thread_pool is not None
        assert len(manager.active_tasks) == 0
        
    def test_execute_async(self, qt_app):
        """测试异步执行"""
        config = OptimizationConfig()
        manager = UIThreadManager(config)
        
        # 创建测试函数
        def test_func(value):
            return value * 2
            
        # 异步执行
        task_id = manager.execute_async("test_task", test_func, ResponsePriority.NORMAL, 5)
        
        assert task_id == "test_task"
        assert "test_task" in manager.active_tasks
        
        # 等待任务完成
        time.sleep(0.1)
        
    def test_cancel_task(self, qt_app):
        """测试取消任务"""
        config = OptimizationConfig()
        manager = UIThreadManager(config)
        
        # 创建长时间运行的任务
        def long_task():
            time.sleep(1)
            return "completed"
            
        # 提交任务
        task_id = manager.execute_async("long_task", long_task)
        
        # 立即取消
        success = manager.cancel_task(task_id)
        
        # 验证取消成功（如果任务还未开始）
        if success:
            assert task_id not in manager.active_tasks
            
    def test_thread_load_monitoring(self, qt_app):
        """测试线程负载监控"""
        config = OptimizationConfig(max_workers=2)
        manager = UIThreadManager(config)
        
        # 创建信号监听器
        load_values = []
        manager.thread_load_changed.connect(lambda load: load_values.append(load))
        
        # 提交任务
        def dummy_task():
            time.sleep(0.1)
            
        manager.execute_async("task1", dummy_task)
        manager.execute_async("task2", dummy_task)
        
        # 触发负载监控
        manager._monitor_thread_load()
        
        # 验证负载计算
        if load_values:
            assert load_values[-1] >= 0
            
    def test_shutdown(self, qt_app):
        """测试关闭线程池"""
        config = OptimizationConfig()
        manager = UIThreadManager(config)
        
        # 提交一个快速任务
        manager.execute_async("quick_task", lambda: "done")
        
        # 关闭线程池
        manager.shutdown()
        
        # 验证线程池已关闭
        assert manager.thread_pool._shutdown


class TestResponseTimeOptimizer:
    """响应时间优化器测试"""
    
    def test_optimizer_creation(self, qt_app):
        """测试优化器创建"""
        optimizer = ResponseTimeOptimizer()
        
        assert optimizer.config is not None
        assert optimizer.event_processor is not None
        assert optimizer.thread_manager is not None
        assert len(optimizer.metrics_buffer) == 0
        assert len(optimizer.optimization_strategies) > 0
        
    def test_optimizer_with_custom_config(self, qt_app):
        """测试使用自定义配置的优化器"""
        config = OptimizationConfig(critical_threshold=10.0)
        optimizer = ResponseTimeOptimizer(config)
        
        assert optimizer.config.critical_threshold == 10.0
        
    def test_record_metrics(self, qt_app):
        """测试记录指标"""
        optimizer = ResponseTimeOptimizer()
        
        # 记录指标
        optimizer._record_metrics(
            EventType.USER_INPUT,
            time.time(),
            time.time() + 0.05,
            0.05,
            ResponsePriority.HIGH,
            True
        )
        
        # 验证指标被记录
        assert len(optimizer.metrics_buffer) == 1
        metrics = optimizer.metrics_buffer[0]
        assert metrics.event_type == EventType.USER_INPUT.value
        assert metrics.success is True
        
    def test_metrics_buffer_limit(self, qt_app):
        """测试指标缓冲区限制"""
        config = OptimizationConfig(metrics_buffer_size=3)
        optimizer = ResponseTimeOptimizer(config)
        
        # 添加超过缓冲区大小的指标
        for i in range(5):
            optimizer._record_metrics(
                EventType.DATA_PROCESSING,
                time.time(),
                time.time() + 0.01,
                0.01,
                ResponsePriority.NORMAL
            )
            
        # 验证缓冲区大小限制
        assert len(optimizer.metrics_buffer) == 3
        
    def test_threshold_checking(self, qt_app):
        """测试阈值检查"""
        config = OptimizationConfig(critical_threshold=10.0)  # 10ms
        optimizer = ResponseTimeOptimizer(config)
        
        # 创建警告监听器
        warnings = []
        optimizer.warning_issued.connect(lambda msg: warnings.append(msg))
        
        # 记录超过阈值的指标
        optimizer._record_metrics(
            EventType.USER_INPUT,
            time.time(),
            time.time() + 0.02,  # 20ms
            0.02,
            ResponsePriority.CRITICAL
        )
        
        # 验证警告被发出
        assert len(warnings) > 0
        assert "超过阈值" in warnings[0]
        
    def test_optimization_strategy_application(self, qt_app):
        """测试优化策略应用"""
        optimizer = ResponseTimeOptimizer()
        
        # 创建优化监听器
        optimizations = []
        optimizer.optimization_applied.connect(lambda msg: optimizations.append(msg))
        
        # 创建超过阈值的指标
        metrics = ResponseMetrics(
            event_type=EventType.USER_INPUT.value,
            start_time=time.time(),
            end_time=time.time() + 0.6,  # 600ms
            duration=0.6,
            priority=ResponsePriority.CRITICAL
        )
        
        # 应用优化策略
        optimizer._apply_optimization_strategy(metrics)
        
        # 验证优化策略被应用
        assert len(optimizations) > 0
        
    def test_user_interaction_decorator(self, qt_app):
        """测试用户交互装饰器"""
        optimizer = ResponseTimeOptimizer()
        widget = QWidget()
        
        # 应用装饰器
        @optimizer.optimize_user_interaction(widget)
        def test_interaction():
            time.sleep(0.01)
            return "success"
            
        # 执行装饰器函数
        result = test_interaction()
        
        # 验证结果和指标记录
        assert result == "success"
        assert len(optimizer.metrics_buffer) > 0
        
    def test_event_handling_decorator(self, qt_app):
        """测试事件处理装饰器"""
        optimizer = ResponseTimeOptimizer()
        
        # 应用装饰器
        @optimizer.optimize_event_handling(EventType.UI_UPDATE, ResponsePriority.HIGH)
        def test_event_handler():
            return "handled"
            
        # 执行装饰器函数
        test_event_handler()
        
        # 验证事件被添加到处理队列
        assert not optimizer.event_processor.event_queue.empty()
        
    def test_background_task_decorator(self, qt_app):
        """测试后台任务装饰器"""
        optimizer = ResponseTimeOptimizer()
        
        # 应用装饰器
        @optimizer.defer_to_background("bg_test")
        def background_task():
            return "completed"
            
        # 执行装饰器函数
        task_id = background_task()
        
        # 验证任务被提交到后台
        assert task_id is not None
        
    def test_batch_ui_updates_decorator(self, qt_app):
        """测试批量UI更新装饰器"""
        optimizer = ResponseTimeOptimizer()
        
        # 创建更新计数器
        update_count = 0
        
        @optimizer.batch_ui_updates(batch_size=3, timeout_ms=100)
        def ui_update():
            nonlocal update_count
            update_count += 1
            
        # 执行多次更新
        for _ in range(5):
            ui_update()
            
        # 等待批量处理
        time.sleep(0.2)
        
        # 验证批量更新被执行
        assert update_count > 0
        
    def test_performance_report(self, qt_app):
        """测试性能报告"""
        optimizer = ResponseTimeOptimizer()
        
        # 添加一些测试指标
        for i in range(5):
            optimizer._record_metrics(
                EventType.USER_INPUT,
                time.time(),
                time.time() + 0.01 * (i + 1),
                0.01 * (i + 1),
                ResponsePriority.NORMAL,
                i < 4  # 最后一个失败
            )
            
        # 获取性能报告
        report = optimizer.get_performance_report()
        
        # 验证报告内容
        assert report['status'] == 'success'
        assert report['total_events'] == 5
        assert report['successful_events'] == 4
        assert report['failed_events'] == 1
        assert report['success_rate'] == 80.0
        assert 'avg_response_time' in report
        assert 'priority_stats' in report
        
    def test_performance_report_no_data(self, qt_app):
        """测试无数据时的性能报告"""
        optimizer = ResponseTimeOptimizer()
        
        report = optimizer.get_performance_report()
        
        assert report['status'] == 'no_data'
        
    def test_cleanup(self, qt_app):
        """测试清理资源"""
        optimizer = ResponseTimeOptimizer()
        
        # 添加一些数据
        optimizer._record_metrics(
            EventType.USER_INPUT,
            time.time(),
            time.time() + 0.01,
            0.01,
            ResponsePriority.NORMAL
        )
        
        # 清理资源
        optimizer.cleanup()
        
        # 验证资源被清理
        assert len(optimizer.metrics_buffer) == 0
        assert not optimizer.performance_timer.isActive()


class TestOptimizeResponseTimeDecorator:
    """响应时间优化装饰器测试"""
    
    def test_decorator_success(self, qt_app):
        """测试装饰器成功场景"""
        optimizer = ResponseTimeOptimizer()
        
        @optimize_response_time(optimizer, EventType.DATA_PROCESSING, ResponsePriority.HIGH)
        def test_function(value):
            return value * 2
            
        result = test_function(5)
        
        assert result == 10
        assert len(optimizer.metrics_buffer) == 1
        metrics = optimizer.metrics_buffer[0]
        assert metrics.success is True
        assert metrics.event_type == EventType.DATA_PROCESSING.value
        assert metrics.priority == ResponsePriority.HIGH
        
    def test_decorator_exception(self, qt_app):
        """测试装饰器异常场景"""
        optimizer = ResponseTimeOptimizer()
        
        @optimize_response_time(optimizer)
        def failing_function():
            raise ValueError("Test error")
            
        with pytest.raises(ValueError):
            failing_function()
            
        # 验证错误指标被记录
        assert len(optimizer.metrics_buffer) == 1
        metrics = optimizer.metrics_buffer[0]
        assert metrics.success is False
        assert "Test error" in metrics.error_message


class TestCreateResponseOptimizer:
    """创建响应时间优化器函数测试"""
    
    def test_create_with_default_config(self, qt_app):
        """测试使用默认配置创建"""
        optimizer = create_response_optimizer()
        
        assert isinstance(optimizer, ResponseTimeOptimizer)
        assert optimizer.config is not None
        
    def test_create_with_custom_config(self, qt_app):
        """测试使用自定义配置创建"""
        config = OptimizationConfig(critical_threshold=5.0)
        optimizer = create_response_optimizer(config)
        
        assert isinstance(optimizer, ResponseTimeOptimizer)
        assert optimizer.config.critical_threshold == 5.0


class TestIntegration:
    """集成测试"""
    
    def test_full_optimization_workflow(self, qt_app):
        """测试完整优化工作流"""
        config = OptimizationConfig(
            critical_threshold=10.0,
            max_workers=2,
            batch_size=2
        )
        optimizer = ResponseTimeOptimizer(config)
        
        # 创建监听器
        warnings = []
        optimizations = []
        optimizer.warning_issued.connect(lambda msg: warnings.append(msg))
        optimizer.optimization_applied.connect(lambda msg: optimizations.append(msg))
        
        # 模拟用户交互
        widget = QWidget()
        
        @optimizer.optimize_user_interaction(widget)
        def slow_interaction():
            time.sleep(0.02)  # 20ms，超过阈值
            return "done"
            
        # 执行交互
        result = slow_interaction()
        
        # 验证结果
        assert result == "done"
        assert len(optimizer.metrics_buffer) > 0
        
        # 可能触发警告和优化
        QTest.qWait(100)  # 等待异步处理
        
        # 验证系统状态
        report = optimizer.get_performance_report()
        assert report['status'] == 'success'
        
        # 清理
        optimizer.cleanup()
        
    def test_concurrent_operations(self, qt_app):
        """测试并发操作"""
        optimizer = ResponseTimeOptimizer()
        
        # 创建多个并发任务
        results = []
        
        @optimizer.defer_to_background()
        def concurrent_task(task_id):
            time.sleep(0.01)
            results.append(f"task_{task_id}")
            return f"completed_{task_id}"
            
        # 启动多个任务
        task_ids = []
        for i in range(3):
            task_id = concurrent_task(i)
            task_ids.append(task_id)
            
        # 等待任务完成
        time.sleep(0.5)
        
        # 验证任务执行
        assert len(results) <= 3  # 可能还有任务在执行
        
        # 清理
        optimizer.cleanup()
        
    def test_performance_monitoring(self, qt_app):
        """测试性能监控"""
        config = OptimizationConfig(performance_check_interval=0.1)  # 100ms
        optimizer = ResponseTimeOptimizer(config)
        
        # 创建指标监听器
        metrics_updates = []
        optimizer.metrics_updated.connect(lambda data: metrics_updates.append(data))
        
        # 添加一些指标
        for i in range(10):
            optimizer._record_metrics(
                EventType.USER_INPUT,
                time.time(),
                time.time() + 0.01,
                0.01,
                ResponsePriority.NORMAL
            )
            
        # 等待性能检查
        QTest.qWait(200)
        
        # 验证监控数据
        if metrics_updates:
            data = metrics_updates[-1]
            assert 'avg_response_time' in data
            assert 'success_rate' in data
            assert 'total_events' in data
            
        # 清理
        optimizer.cleanup()


if __name__ == '__main__':
    pytest.main([__file__, '-v']) 