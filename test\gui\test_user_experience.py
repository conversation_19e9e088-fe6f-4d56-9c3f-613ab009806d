"""
用户体验测试模块

该模块提供完整的用户体验(UX)测试解决方案，包括：
- 用户界面友好性测试：界面布局、颜色搭配、字体大小、图标清晰度
- 操作流程顺畅性验证：任务完成路径、步骤合理性、操作连贯性
- 错误处理用户体验：错误消息清晰度、恢复机制、用户指导
- 性能感知测试：响应时间感知、进度反馈、加载状态

技术特性：
- 基于心理学的用户体验评估
- 量化的可用性指标测试
- 真实用户场景模拟
- 错误恢复路径验证
- 性能感知阈值测试
- 无障碍访问支持验证
"""

import pytest
import time
import os
import sys
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QPushButton, QLineEdit,
    QLabel, QMessageBox, QProgressBar, QDialog, QVBoxLayout,
    QHBoxLayout, QTabWidget, QTextEdit, QComboBox, QTableWidget
)
from PyQt5.QtCore import QTimer, Qt, QSize, QRect
from PyQt5.QtTest import QTest
from PyQt5.QtGui import QFont, QColor, QPalette

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../src'))

from gui.main_window_refactored import MainWindow
from gui.windows import ChangeDetectionWindow, ReportGenerationWindow
from gui.dialogs import DataImportDialog, SettingsDialog, ProgressDialog
from core.application_service import ApplicationService


class UXMetrics:
    """用户体验指标"""
    
    def __init__(self):
        self.task_completion_time = 0.0
        self.error_count = 0
        self.click_count = 0
        self.navigation_steps = 0
        self.user_satisfaction_score = 0.0
        self.accessibility_score = 0.0
        self.visual_clarity_score = 0.0
        self.workflow_efficiency_score = 0.0
        
    def calculate_overall_score(self) -> float:
        """计算总体UX评分"""
        scores = [
            self.user_satisfaction_score,
            self.accessibility_score,
            self.visual_clarity_score,
            self.workflow_efficiency_score
        ]
        return sum(scores) / len(scores) if scores else 0.0
        
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'task_completion_time': self.task_completion_time,
            'error_count': self.error_count,
            'click_count': self.click_count,
            'navigation_steps': self.navigation_steps,
            'user_satisfaction_score': self.user_satisfaction_score,
            'accessibility_score': self.accessibility_score,
            'visual_clarity_score': self.visual_clarity_score,
            'workflow_efficiency_score': self.workflow_efficiency_score,
            'overall_score': self.calculate_overall_score()
        }


class UXTestFramework:
    """用户体验测试框架"""
    
    def __init__(self):
        self.metrics = UXMetrics()
        self.start_time = None
        self.setup_application()
        
    def setup_application(self):
        """设置Qt应用程序"""
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
            
    def start_task(self):
        """开始任务计时"""
        self.start_time = time.time()
        
    def end_task(self):
        """结束任务计时"""
        if self.start_time:
            self.metrics.task_completion_time = time.time() - self.start_time
            
    def record_click(self):
        """记录点击操作"""
        self.metrics.click_count += 1
        
    def record_navigation_step(self):
        """记录导航步骤"""
        self.metrics.navigation_steps += 1
        
    def record_error(self):
        """记录错误"""
        self.metrics.error_count += 1
        
    def evaluate_visual_clarity(self, widget: QWidget) -> float:
        """评估视觉清晰度"""
        score = 100.0
        
        # 检查字体大小
        font = widget.font()
        if font.pointSize() < 9:
            score -= 20  # 字体太小
        elif font.pointSize() > 14:
            score -= 10  # 字体过大
            
        # 检查颜色对比度
        palette = widget.palette()
        bg_color = palette.color(QPalette.Background)
        text_color = palette.color(QPalette.Text)
        
        # 简单的对比度检查
        if abs(bg_color.lightness() - text_color.lightness()) < 100:
            score -= 30  # 对比度不足
            
        # 检查组件大小
        size = widget.size()
        if size.width() < 100 or size.height() < 30:
            score -= 15  # 组件太小
            
        return max(0, score)
        
    def evaluate_accessibility(self, widget: QWidget) -> float:
        """评估无障碍访问性"""
        score = 100.0
        
        # 检查工具提示
        if not widget.toolTip():
            score -= 20
            
        # 检查快捷键
        if hasattr(widget, 'shortcut') and not widget.shortcut():
            score -= 15
            
        # 检查焦点策略
        if widget.focusPolicy() == Qt.NoFocus and isinstance(widget, (QPushButton, QLineEdit)):
            score -= 25
            
        # 检查标签关联
        if isinstance(widget, QLineEdit):
            # 查找关联的标签
            parent = widget.parent()
            if parent:
                labels = parent.findChildren(QLabel)
                has_label = any(label.buddy() == widget for label in labels)
                if not has_label:
                    score -= 20
                    
        return max(0, score)
        
    def evaluate_workflow_efficiency(self, steps: int, expected_steps: int) -> float:
        """评估工作流效率"""
        if expected_steps == 0:
            return 100.0
            
        efficiency = (expected_steps / max(steps, 1)) * 100
        return min(100.0, efficiency)
        
    def measure_response_time(self, action_func, threshold_ms: float = 100.0) -> dict:
        """测量响应时间"""
        start_time = time.time()
        action_func()
        response_time = (time.time() - start_time) * 1000  # 转换为毫秒
        
        # 评估用户感知
        if response_time <= 100:
            perception = "即时"
            score = 100
        elif response_time <= 300:
            perception = "快速"
            score = 85
        elif response_time <= 1000:
            perception = "可接受"
            score = 70
        elif response_time <= 3000:
            perception = "较慢"
            score = 50
        else:
            perception = "很慢"
            score = 20
            
        return {
            'response_time_ms': response_time,
            'perception': perception,
            'score': score,
            'meets_threshold': response_time <= threshold_ms
        }


@pytest.fixture
def qt_app():
    """Qt应用程序fixture"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app


@pytest.fixture
def mock_application_service():
    """Mock应用服务"""
    service = Mock(spec=ApplicationService)
    service.initialize.return_value = Mock(success=True, message="初始化成功")
    service.get_system_status.return_value = Mock(success=True, data={'status': 'ready'})
    service.import_excel_data.return_value = Mock(success=True, message="导入成功")
    service.detect_changes.return_value = Mock(success=True, data={'changes': []})
    service.generate_excel_report.return_value = Mock(success=True, message="报告生成成功")
    return service


@pytest.fixture
def ux_framework():
    """UX测试框架fixture"""
    return UXTestFramework()


class TestUIFriendliness:
    """用户界面友好性测试"""
    
    def test_main_window_visual_clarity(self, qt_app, mock_application_service, ux_framework):
        """测试主窗口视觉清晰度"""
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        # 评估视觉清晰度
        clarity_score = ux_framework.evaluate_visual_clarity(main_window)
        ux_framework.metrics.visual_clarity_score = clarity_score
        
        # 验证基本视觉要求
        assert clarity_score >= 60, f"视觉清晰度评分过低: {clarity_score}"
        
        # 检查窗口标题
        assert main_window.windowTitle(), "窗口标题不能为空"
        
        # 检查窗口大小合理性
        size = main_window.size()
        assert size.width() >= 800, "窗口宽度应至少800像素"
        assert size.height() >= 600, "窗口高度应至少600像素"
        
    def test_button_visual_design(self, qt_app, ux_framework):
        """测试按钮视觉设计"""
        # 创建测试按钮
        button = QPushButton("测试按钮")
        button.show()
        
        # 评估按钮视觉设计
        clarity_score = ux_framework.evaluate_visual_clarity(button)
        
        # 验证按钮设计
        assert clarity_score >= 70, f"按钮视觉设计评分过低: {clarity_score}"
        
        # 检查按钮大小
        size = button.size()
        assert size.width() >= 80, "按钮宽度应至少80像素"
        assert size.height() >= 30, "按钮高度应至少30像素"
        
        # 检查按钮文字
        assert button.text(), "按钮文字不能为空"
        
    def test_font_readability(self, qt_app):
        """测试字体可读性"""
        # 创建文本标签
        label = QLabel("这是一段测试文字，用于检查字体可读性。")
        label.show()
        
        # 检查字体设置
        font = label.font()
        assert font.pointSize() >= 9, f"字体大小过小: {font.pointSize()}pt"
        assert font.pointSize() <= 14, f"字体大小过大: {font.pointSize()}pt"
        
        # 检查字体族
        font_family = font.family()
        assert font_family, "字体族不能为空"
        
    def test_color_contrast(self, qt_app):
        """测试颜色对比度"""
        # 创建测试组件
        widget = QWidget()
        widget.setStyleSheet("background-color: white; color: black;")
        widget.show()
        
        # 检查颜色对比度
        palette = widget.palette()
        bg_color = palette.color(QPalette.Background)
        text_color = palette.color(QPalette.Text)
        
        # 计算亮度差异
        brightness_diff = abs(bg_color.lightness() - text_color.lightness())
        assert brightness_diff >= 100, f"颜色对比度不足: {brightness_diff}"
        
    def test_icon_clarity(self, qt_app):
        """测试图标清晰度"""
        # 创建带图标的按钮
        button = QPushButton("保存")
        button.setIconSize(QSize(16, 16))
        button.show()
        
        # 检查图标大小
        icon_size = button.iconSize()
        assert icon_size.width() >= 16, "图标宽度应至少16像素"
        assert icon_size.height() >= 16, "图标高度应至少16像素"
        
    def test_layout_consistency(self, qt_app, mock_application_service):
        """测试布局一致性"""
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        # 检查中央组件存在
        central_widget = main_window.centralWidget()
        assert central_widget is not None, "中央组件不能为空"
        
        # 检查菜单栏
        menu_bar = main_window.menuBar()
        assert menu_bar is not None, "菜单栏不能为空"
        assert menu_bar.isVisible(), "菜单栏应该可见"
        
        # 检查状态栏
        status_bar = main_window.statusBar()
        assert status_bar is not None, "状态栏不能为空"


class TestWorkflowSmoothness:
    """操作流程顺畅性测试"""
    
    def test_data_import_workflow_smoothness(self, qt_app, mock_application_service, ux_framework):
        """测试数据导入工作流顺畅性"""
        ux_framework.start_task()
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        ux_framework.record_navigation_step()
        
        # 模拟数据导入流程
        # 步骤1: 打开导入对话框
        import_dialog = DataImportDialog()
        import_dialog.show()
        ux_framework.record_click()
        ux_framework.record_navigation_step()
        
        # 步骤2: 检查对话框可用性
        assert import_dialog.isVisible(), "导入对话框应该可见"
        ux_framework.record_navigation_step()
        
        # 步骤3: 模拟文件选择
        QTest.qWait(100)  # 模拟用户思考时间
        ux_framework.record_click()
        ux_framework.record_navigation_step()
        
        ux_framework.end_task()
        
        # 评估工作流效率
        expected_steps = 3  # 理想的步骤数
        actual_steps = ux_framework.metrics.navigation_steps
        efficiency_score = ux_framework.evaluate_workflow_efficiency(actual_steps, expected_steps)
        ux_framework.metrics.workflow_efficiency_score = efficiency_score
        
        # 验证工作流效率
        assert efficiency_score >= 70, f"工作流效率过低: {efficiency_score}%"
        assert ux_framework.metrics.task_completion_time < 5.0, "任务完成时间过长"
        
    def test_change_detection_workflow(self, qt_app, mock_application_service, ux_framework):
        """测试异动检测工作流"""
        ux_framework.start_task()
        
        # 创建异动检测窗口
        window = ChangeDetectionWindow()
        window.application_service = mock_application_service
        window.show()
        ux_framework.record_navigation_step()
        
        # 模拟异动检测流程
        # 步骤1: 查看界面布局
        assert window.isVisible(), "异动检测窗口应该可见"
        ux_framework.record_navigation_step()
        
        # 步骤2: 查找操作按钮
        buttons = window.findChildren(QPushButton)
        assert len(buttons) > 0, "应该有操作按钮"
        ux_framework.record_navigation_step()
        
        # 步骤3: 模拟开始检测
        QTest.qWait(100)
        ux_framework.record_click()
        ux_framework.record_navigation_step()
        
        ux_framework.end_task()
        
        # 验证流程顺畅性
        assert ux_framework.metrics.navigation_steps <= 5, "导航步骤过多"
        assert ux_framework.metrics.task_completion_time < 3.0, "流程耗时过长"
        
    def test_report_generation_workflow(self, qt_app, mock_application_service, ux_framework):
        """测试报告生成工作流"""
        ux_framework.start_task()
        
        # 创建报告生成窗口
        window = ReportGenerationWindow()
        window.application_service = mock_application_service
        window.show()
        ux_framework.record_navigation_step()
        
        # 模拟报告生成流程
        # 步骤1: 检查界面组件
        assert window.centralWidget() is not None, "中央组件应该存在"
        ux_framework.record_navigation_step()
        
        # 步骤2: 查找生成按钮
        buttons = window.findChildren(QPushButton)
        generate_buttons = [btn for btn in buttons if "生成" in btn.text()]
        ux_framework.record_navigation_step()
        
        # 步骤3: 模拟报告生成
        if generate_buttons:
            ux_framework.record_click()
        ux_framework.record_navigation_step()
        
        ux_framework.end_task()
        
        # 验证工作流顺畅性
        expected_steps = 4
        actual_steps = ux_framework.metrics.navigation_steps
        efficiency_score = ux_framework.evaluate_workflow_efficiency(actual_steps, expected_steps)
        
        assert efficiency_score >= 75, f"报告生成流程效率过低: {efficiency_score}%"
        
    def test_settings_configuration_workflow(self, qt_app, ux_framework):
        """测试设置配置工作流"""
        ux_framework.start_task()
        
        # 创建设置对话框
        dialog = SettingsDialog()
        dialog.show()
        ux_framework.record_navigation_step()
        
        # 模拟设置配置流程
        # 步骤1: 检查选项卡结构
        tab_widget = dialog.findChild(QTabWidget)
        if tab_widget:
            assert tab_widget.count() > 0, "应该有配置选项卡"
            ux_framework.record_navigation_step()
            
            # 步骤2: 切换选项卡
            tab_widget.setCurrentIndex(1)
            ux_framework.record_click()
            ux_framework.record_navigation_step()
            
        # 步骤3: 查找配置项
        line_edits = dialog.findChildren(QLineEdit)
        if line_edits:
            ux_framework.record_navigation_step()
            
        # 步骤4: 保存设置
        buttons = dialog.findChildren(QPushButton)
        save_buttons = [btn for btn in buttons if "确定" in btn.text() or "应用" in btn.text()]
        if save_buttons:
            ux_framework.record_click()
        ux_framework.record_navigation_step()
        
        ux_framework.end_task()
        
        # 验证配置流程
        assert ux_framework.metrics.navigation_steps <= 6, "配置步骤过多"
        assert ux_framework.metrics.click_count <= 3, "点击次数过多"


class TestErrorHandlingUX:
    """错误处理用户体验测试"""
    
    def test_error_message_clarity(self, qt_app, ux_framework):
        """测试错误消息清晰度"""
        # 模拟错误消息显示
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Critical)
        error_msg.setWindowTitle("错误")
        error_msg.setText("文件导入失败：文件格式不正确")
        error_msg.setInformativeText("请选择Excel文件(.xlsx或.xls格式)")
        error_msg.show()
        
        # 验证错误消息要素
        assert error_msg.text(), "错误消息不能为空"
        assert len(error_msg.text()) > 5, "错误消息应该足够详细"
        assert error_msg.informativeText(), "应该提供解决建议"
        
        # 检查图标
        assert error_msg.icon() == QMessageBox.Critical, "应该使用适当的错误图标"
        
    def test_input_validation_feedback(self, qt_app, ux_framework):
        """测试输入验证反馈"""
        # 创建输入框
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("请输入数字")
        line_edit.show()
        
        # 模拟无效输入
        line_edit.setText("abc")
        
        # 验证占位符文本
        assert line_edit.placeholderText(), "应该有占位符提示"
        
        # 模拟验证失败样式
        line_edit.setStyleSheet("border: 2px solid red;")
        style = line_edit.styleSheet()
        assert "red" in style, "无效输入应该有视觉反馈"
        
    def test_progress_feedback(self, qt_app, ux_framework):
        """测试进度反馈"""
        # 创建进度对话框
        progress_dialog = ProgressDialog("正在处理数据...")
        progress_dialog.show()
        
        # 验证进度对话框要素
        assert progress_dialog.isVisible(), "进度对话框应该可见"
        
        # 查找进度条
        progress_bars = progress_dialog.findChildren(QProgressBar)
        assert len(progress_bars) > 0, "应该有进度条"
        
        # 验证进度条配置
        progress_bar = progress_bars[0]
        assert progress_bar.minimum() >= 0, "进度条最小值应该合理"
        assert progress_bar.maximum() > progress_bar.minimum(), "进度条最大值应该大于最小值"
        
    def test_recovery_mechanisms(self, qt_app, mock_application_service, ux_framework):
        """测试错误恢复机制"""
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        # 模拟错误恢复场景
        # 1. 检查重试机制
        ux_framework.record_error()
        
        # 2. 检查撤销功能
        # 查找可能的撤销按钮或菜单
        menu_bar = main_window.menuBar()
        if menu_bar:
            edit_menu = None
            for action in menu_bar.actions():
                if "编辑" in action.text():
                    edit_menu = action.menu()
                    break
                    
            if edit_menu:
                # 查找撤销操作
                for action in edit_menu.actions():
                    if "撤销" in action.text():
                        assert action.isEnabled() or True, "撤销功能应该可用或合理禁用"
                        
        # 3. 检查数据保护
        # 验证是否有自动保存或数据保护机制
        assert ux_framework.metrics.error_count >= 0, "错误计数应该正确"
        
    def test_help_system_accessibility(self, qt_app, mock_application_service):
        """测试帮助系统可访问性"""
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        # 检查帮助菜单
        menu_bar = main_window.menuBar()
        help_menu = None
        
        for action in menu_bar.actions():
            if "帮助" in action.text():
                help_menu = action.menu()
                break
                
        # 验证帮助系统
        if help_menu:
            assert help_menu.actions(), "帮助菜单应该有操作项"
        else:
            # 如果没有帮助菜单，检查是否有其他帮助机制
            # 如工具提示、状态栏提示等
            central_widget = main_window.centralWidget()
            if central_widget:
                # 检查工具提示
                widgets = central_widget.findChildren(QWidget)
                tooltip_count = sum(1 for w in widgets if w.toolTip())
                assert tooltip_count > 0, "应该有工具提示提供帮助信息"


class TestPerformancePerception:
    """性能感知测试"""
    
    def test_ui_response_time(self, qt_app, mock_application_service, ux_framework):
        """测试UI响应时间"""
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        # 测试按钮点击响应时间
        def click_action():
            # 模拟按钮点击
            QTest.qWait(10)  # 模拟处理时间
            
        response_data = ux_framework.measure_response_time(click_action, 100.0)
        
        # 验证响应时间
        assert response_data['meets_threshold'], f"响应时间超过阈值: {response_data['response_time_ms']}ms"
        assert response_data['score'] >= 70, f"响应性能评分过低: {response_data['score']}"
        
    def test_data_loading_feedback(self, qt_app, mock_application_service, ux_framework):
        """测试数据加载反馈"""
        # 创建数据导入对话框
        dialog = DataImportDialog()
        dialog.show()
        
        # 测试数据加载时的用户反馈
        def loading_action():
            # 模拟数据加载
            QTest.qWait(50)  # 模拟加载时间
            
        response_data = ux_framework.measure_response_time(loading_action, 200.0)
        
        # 验证加载反馈
        assert response_data['response_time_ms'] < 1000, "数据加载时间应该在可接受范围内"
        
        # 检查是否有加载指示器
        progress_bars = dialog.findChildren(QProgressBar)
        if response_data['response_time_ms'] > 100:
            # 如果加载时间较长，应该有进度指示
            assert len(progress_bars) > 0 or True, "长时间操作应该有进度指示"
            
    def test_large_data_handling(self, qt_app, ux_framework):
        """测试大数据处理性能感知"""
        # 创建表格组件
        table = QTableWidget(1000, 10)  # 大数据表格
        table.show()
        
        # 测试大数据渲染时间
        def render_action():
            # 模拟数据填充
            for i in range(min(100, table.rowCount())):  # 只测试前100行
                table.setItem(i, 0, table.__class__.itemClass()(f"数据{i}"))
                if i % 10 == 0:  # 每10行更新一次界面
                    QTest.qWait(1)
                    
        response_data = ux_framework.measure_response_time(render_action, 500.0)
        
        # 验证大数据处理性能
        assert response_data['response_time_ms'] < 2000, "大数据渲染时间应该在可接受范围内"
        
    def test_memory_usage_perception(self, qt_app, mock_application_service):
        """测试内存使用感知"""
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建多个窗口模拟内存使用
        windows = []
        for i in range(5):
            window = MainWindow()
            window.application_service = mock_application_service
            window.show()
            windows.append(window)
            QTest.qWait(10)
            
        # 检查内存增长
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = current_memory - initial_memory
        
        # 验证内存使用合理性
        assert memory_increase < 200, f"内存增长过多: {memory_increase}MB"
        
        # 清理窗口
        for window in windows:
            window.close()
            window.deleteLater()
            
    def test_animation_smoothness(self, qt_app):
        """测试动画流畅性"""
        # 创建带动画的组件
        widget = QWidget()
        widget.setGeometry(100, 100, 200, 100)
        widget.show()
        
        # 模拟动画
        def animate_action():
            # 模拟位置动画
            for i in range(10):
                new_x = 100 + i * 5
                widget.move(new_x, 100)
                QTest.qWait(16)  # 60FPS = 16ms per frame
                
        start_time = time.time()
        animate_action()
        animation_time = time.time() - start_time
        
        # 验证动画流畅性
        expected_time = 10 * 16 / 1000  # 10帧 * 16ms
        assert animation_time <= expected_time * 1.5, "动画应该保持流畅"


class TestAccessibilitySupport:
    """无障碍访问支持测试"""
    
    def test_keyboard_navigation(self, qt_app, mock_application_service, ux_framework):
        """测试键盘导航"""
        # 创建主窗口
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        # 评估无障碍访问性
        accessibility_score = ux_framework.evaluate_accessibility(main_window)
        ux_framework.metrics.accessibility_score = accessibility_score
        
        # 验证键盘导航
        assert accessibility_score >= 60, f"无障碍访问性评分过低: {accessibility_score}"
        
        # 检查焦点策略
        central_widget = main_window.centralWidget()
        if central_widget:
            focusable_widgets = []
            for widget in central_widget.findChildren(QWidget):
                if widget.focusPolicy() != Qt.NoFocus:
                    focusable_widgets.append(widget)
                    
            assert len(focusable_widgets) > 0, "应该有可获得焦点的组件"
            
    def test_screen_reader_support(self, qt_app):
        """测试屏幕阅读器支持"""
        # 创建标签和输入框
        label = QLabel("用户名:")
        line_edit = QLineEdit()
        label.setBuddy(line_edit)
        
        # 创建布局
        widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(label)
        layout.addWidget(line_edit)
        widget.setLayout(layout)
        widget.show()
        
        # 验证标签关联
        assert label.buddy() == line_edit, "标签应该关联到输入框"
        
        # 验证可访问性属性
        assert line_edit.accessibleName() or label.text(), "应该有可访问的名称"
        
    def test_high_contrast_support(self, qt_app):
        """测试高对比度支持"""
        # 创建组件
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: black;
                color: white;
            }
        """)
        widget.show()
        
        # 验证高对比度设置
        palette = widget.palette()
        bg_color = palette.color(QPalette.Background)
        text_color = palette.color(QPalette.Text)
        
        # 计算对比度
        brightness_diff = abs(bg_color.lightness() - text_color.lightness())
        assert brightness_diff >= 150, f"高对比度模式对比度不足: {brightness_diff}"
        
    def test_font_scaling_support(self, qt_app):
        """测试字体缩放支持"""
        # 创建文本组件
        label = QLabel("测试文字")
        label.show()
        
        # 测试字体缩放
        original_font = label.font()
        original_size = original_font.pointSize()
        
        # 模拟字体放大
        scaled_font = QFont(original_font)
        scaled_font.setPointSize(int(original_size * 1.5))
        label.setFont(scaled_font)
        
        # 验证字体缩放
        current_size = label.font().pointSize()
        assert current_size > original_size, "字体应该能够缩放"
        assert current_size <= 20, "缩放后字体大小应该合理"


class TestOverallUXAssessment:
    """整体UX评估测试"""
    
    def test_comprehensive_ux_score(self, qt_app, mock_application_service, ux_framework):
        """综合UX评分测试"""
        # 执行完整的用户体验测试
        ux_framework.start_task()
        
        # 1. 界面友好性测试
        main_window = MainWindow()
        main_window.application_service = mock_application_service
        main_window.show()
        
        visual_score = ux_framework.evaluate_visual_clarity(main_window)
        ux_framework.metrics.visual_clarity_score = visual_score
        ux_framework.record_navigation_step()
        
        # 2. 无障碍访问性测试
        accessibility_score = ux_framework.evaluate_accessibility(main_window)
        ux_framework.metrics.accessibility_score = accessibility_score
        ux_framework.record_navigation_step()
        
        # 3. 工作流效率测试
        # 模拟完整的用户任务
        import_dialog = DataImportDialog()
        import_dialog.show()
        ux_framework.record_click()
        ux_framework.record_navigation_step()
        
        QTest.qWait(100)  # 模拟用户操作时间
        ux_framework.record_navigation_step()
        
        # 4. 性能感知测试
        def ui_action():
            QTest.qWait(50)  # 模拟UI响应
            
        response_data = ux_framework.measure_response_time(ui_action)
        performance_score = response_data['score']
        
        ux_framework.end_task()
        
        # 计算工作流效率
        expected_steps = 4
        actual_steps = ux_framework.metrics.navigation_steps
        workflow_score = ux_framework.evaluate_workflow_efficiency(actual_steps, expected_steps)
        ux_framework.metrics.workflow_efficiency_score = workflow_score
        
        # 设置用户满意度评分（基于其他指标）
        ux_framework.metrics.user_satisfaction_score = (
            visual_score * 0.3 +
            accessibility_score * 0.2 +
            workflow_score * 0.3 +
            performance_score * 0.2
        )
        
        # 计算总体UX评分
        overall_score = ux_framework.metrics.calculate_overall_score()
        
        # 验证UX评分
        assert overall_score >= 70, f"整体UX评分过低: {overall_score}"
        assert ux_framework.metrics.visual_clarity_score >= 60, "视觉清晰度不足"
        assert ux_framework.metrics.accessibility_score >= 50, "无障碍访问性不足"
        assert ux_framework.metrics.workflow_efficiency_score >= 70, "工作流效率不足"
        
        # 生成UX报告
        ux_report = ux_framework.metrics.to_dict()
        assert ux_report['overall_score'] == overall_score, "UX报告数据一致性"
        
        # 验证任务完成时间
        assert ux_framework.metrics.task_completion_time < 10.0, "整体任务完成时间过长"
        
    def test_user_satisfaction_metrics(self, qt_app, ux_framework):
        """用户满意度指标测试"""
        # 模拟用户满意度评估
        metrics = ux_framework.metrics
        
        # 设置测试指标
        metrics.task_completion_time = 3.5  # 3.5秒完成任务
        metrics.error_count = 1  # 1个错误
        metrics.click_count = 5  # 5次点击
        metrics.navigation_steps = 4  # 4个导航步骤
        
        # 基于指标计算满意度
        # 时间因子 (越快越好)
        time_factor = max(0, 100 - metrics.task_completion_time * 10)
        
        # 错误因子 (越少越好)
        error_factor = max(0, 100 - metrics.error_count * 20)
        
        # 效率因子 (点击和步骤越少越好)
        efficiency_factor = max(0, 100 - (metrics.click_count + metrics.navigation_steps) * 5)
        
        # 计算综合满意度
        satisfaction_score = (time_factor + error_factor + efficiency_factor) / 3
        metrics.user_satisfaction_score = satisfaction_score
        
        # 验证满意度指标
        assert satisfaction_score >= 60, f"用户满意度过低: {satisfaction_score}"
        assert metrics.task_completion_time <= 5.0, "任务完成时间应该在可接受范围内"
        assert metrics.error_count <= 2, "错误数量应该控制在最低限度"


if __name__ == '__main__':
    pytest.main([__file__, '-v']) 