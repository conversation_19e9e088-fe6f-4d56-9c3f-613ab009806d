#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入智能功能性能测试

测试智能匹配算法的性能，包括缓存效果和响应时间。

创建时间: 2025-01-20
"""

import time
import random
import statistics
from typing import List, Dict, Any

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_import.import_defaults_manager import ImportDefaultsManager
from src.modules.data_import.smart_sheet_matcher import SmartSheetMatcher


def generate_test_data() -> Dict[str, List[str]]:
    """生成测试数据"""
    categories = [
        '全部在职人员', '离休人员', '退休人员', 'A岗职工', '临时工', 
        '实习生', '管理层', '教师', '科研人员', '医务人员', '技术人员'
    ]
    
    sheet_templates = [
        '{category}工资表', '{category}数据', '{category}信息', 
        '{category}统计', '{category}明细', '{category}汇总',
        '2024年{category}', '月度{category}', '{category}报表'
    ]
    
    test_data = {}
    
    for category in categories:
        # 为每个类别生成多种工作表名称
        sheets = []
        
        # 添加精确匹配的工作表
        sheets.append(category)
        
        # 添加模糊匹配的工作表
        for template in sheet_templates[:3]:
            sheets.append(template.format(category=category))
        
        # 添加一些干扰项
        other_categories = [c for c in categories if c != category]
        for i in range(2):
            other_category = random.choice(other_categories)
            template = random.choice(sheet_templates)
            sheets.append(template.format(category=other_category))
        
        # 添加完全无关的工作表
        irrelevant_sheets = ['销售数据', '财务报表', '库存信息', '客户资料', '产品目录']
        sheets.extend(random.sample(irrelevant_sheets, 2))
        
        test_data[category] = sheets
    
    return test_data


def run_performance_test():
    """运行性能测试"""
    print("🚀 数据导入智能功能性能测试")
    print("=" * 60)
    
    # 初始化
    defaults_manager = ImportDefaultsManager()
    matcher = SmartSheetMatcher(defaults_manager)
    
    # 生成测试数据
    print("📊 生成测试数据...")
    test_data = generate_test_data()
    print(f"✅ 生成了 {len(test_data)} 个类别的测试数据")
    
    # 测试1: 基本性能测试
    print("\n🔍 测试1: 基本匹配性能")
    basic_times = []
    
    for category, sheets in test_data.items():
        start_time = time.time()
        result = matcher.find_best_match(category, sheets)
        end_time = time.time()
        
        match_time = (end_time - start_time) * 1000  # 转换为毫秒
        basic_times.append(match_time)
        
        status = "✅ 匹配成功" if result else "❌ 无匹配"
        print(f"  {category}: {match_time:.2f}ms - {status}")
    
    print(f"\n📈 基本性能统计:")
    print(f"  平均时间: {statistics.mean(basic_times):.2f}ms")
    print(f"  最大时间: {max(basic_times):.2f}ms")
    print(f"  最小时间: {min(basic_times):.2f}ms")
    print(f"  标准差: {statistics.stdev(basic_times):.2f}ms")
    
    # 测试2: 缓存效果测试
    print("\n🗄️ 测试2: 缓存效果测试")
    
    # 清空缓存
    matcher.clear_cache()
    
    # 第一轮：无缓存
    print("  第一轮（无缓存）:")
    first_round_times = []
    
    for category, sheets in list(test_data.items())[:5]:  # 只测试前5个
        start_time = time.time()
        matcher.find_best_match(category, sheets)
        end_time = time.time()
        
        match_time = (end_time - start_time) * 1000
        first_round_times.append(match_time)
    
    # 第二轮：有缓存
    print("  第二轮（有缓存）:")
    second_round_times = []
    
    for category, sheets in list(test_data.items())[:5]:  # 相同的测试数据
        start_time = time.time()
        matcher.find_best_match(category, sheets)
        end_time = time.time()
        
        match_time = (end_time - start_time) * 1000
        second_round_times.append(match_time)
    
    avg_first = statistics.mean(first_round_times)
    avg_second = statistics.mean(second_round_times)
    speedup = avg_first / avg_second if avg_second > 0 else 0
    
    print(f"\n📊 缓存效果统计:")
    print(f"  无缓存平均时间: {avg_first:.2f}ms")
    print(f"  有缓存平均时间: {avg_second:.2f}ms")
    print(f"  性能提升: {speedup:.1f}x")
    
    # 测试3: 大量数据测试
    print("\n📈 测试3: 大量数据性能测试")
    
    # 生成大量工作表名称
    large_sheet_list = []
    for i in range(100):
        large_sheet_list.append(f"工作表_{i}")
        large_sheet_list.append(f"数据表_{i}")
        large_sheet_list.append(f"报表_{i}")
    
    # 添加一些匹配项
    large_sheet_list.extend(['全部在职人员', '在职人员工资表', '员工数据'])
    
    large_data_times = []
    for _ in range(10):  # 重复10次
        start_time = time.time()
        result = matcher.find_best_match('全部在职人员', large_sheet_list)
        end_time = time.time()
        
        match_time = (end_time - start_time) * 1000
        large_data_times.append(match_time)
    
    print(f"  工作表数量: {len(large_sheet_list)}")
    print(f"  平均匹配时间: {statistics.mean(large_data_times):.2f}ms")
    print(f"  最大匹配时间: {max(large_data_times):.2f}ms")
    
    # 测试4: 并发性能测试（模拟）
    print("\n⚡ 测试4: 并发性能模拟测试")
    
    concurrent_times = []
    categories_list = list(test_data.keys())
    
    # 模拟并发请求
    for round_num in range(5):
        round_times = []
        
        for _ in range(10):  # 每轮10个请求
            category = random.choice(categories_list)
            sheets = test_data[category]
            
            start_time = time.time()
            matcher.find_best_match(category, sheets)
            end_time = time.time()
            
            match_time = (end_time - start_time) * 1000
            round_times.append(match_time)
        
        avg_round_time = statistics.mean(round_times)
        concurrent_times.append(avg_round_time)
        print(f"  第{round_num + 1}轮平均时间: {avg_round_time:.2f}ms")
    
    # 获取性能统计
    print("\n📊 最终性能统计:")
    stats = matcher.get_performance_stats()
    
    print(f"  总匹配次数: {stats['total_matches']}")
    print(f"  缓存命中次数: {stats['cache_hits']}")
    print(f"  缓存命中率: {stats['cache_hit_rate']:.1%}")
    print(f"  平均匹配时间: {stats['avg_match_time_ms']:.2f}ms")
    print(f"  缓存大小: {stats['cache_size']}/{stats['cache_max_size']}")
    
    # 性能建议
    print("\n💡 性能建议:")
    
    if stats['cache_hit_rate'] < 0.3:
        print("  ⚠️ 缓存命中率较低，考虑增加缓存大小或调整缓存策略")
    else:
        print("  ✅ 缓存命中率良好")
    
    if stats['avg_match_time_ms'] > 50:
        print("  ⚠️ 平均匹配时间较长，考虑优化匹配算法")
    else:
        print("  ✅ 匹配性能良好")
    
    if avg_first > 10:
        print("  💡 建议启用预加载缓存以提升首次匹配性能")
    
    print("\n🎉 性能测试完成！")


def run_memory_test():
    """运行内存使用测试"""
    print("\n🧠 内存使用测试")
    print("-" * 40)
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始内存使用: {initial_memory:.2f} MB")
        
        # 创建大量匹配器实例
        matchers = []
        for i in range(100):
            matcher = SmartSheetMatcher()
            matchers.append(matcher)
            
            # 执行一些匹配操作
            test_sheets = [f"sheet_{j}" for j in range(50)]
            matcher.find_best_match("测试类别", test_sheets)
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f"峰值内存使用: {peak_memory:.2f} MB")
        print(f"内存增长: {memory_increase:.2f} MB")
        print(f"平均每个匹配器: {memory_increase / 100:.3f} MB")
        
        # 清理
        del matchers
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"清理后内存: {final_memory:.2f} MB")
        
    except ImportError:
        print("⚠️ 需要安装 psutil 库来进行内存测试")
        print("  pip install psutil")


if __name__ == '__main__':
    try:
        run_performance_test()
        run_memory_test()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
