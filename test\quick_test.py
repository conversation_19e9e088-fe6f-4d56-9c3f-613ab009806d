#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 立即显示蓝色渐变头部
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication

def main():
    """快速测试主函数"""
    print("快速界面测试 - 蓝色渐变头部")
    print("=" * 40)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        
        # 导入现代化主窗口
        from src.gui.modern_main_window import ModernMainWindow
        
        # 创建主窗口
        window = ModernMainWindow()
        
        # 显示窗口
        window.show()
        
        print("✓ 现代化界面已启动")
        print("现在您应该看到:")
        print("• 窗口顶部的蓝色渐变头部")
        print("• 左侧现代化导航栏")
        print("• 主内容区域的功能卡片")
        print("\n如果头部仍然没有蓝色渐变，请关闭程序并告诉我。")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 