"""
异动计算模块全面测试
专注于提升amount_calculator.py的测试覆盖率
目标：从56%提升至80%+
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
import pandas as pd
import numpy as np
from decimal import Decimal
import tempfile
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.modules.change_detection.amount_calculator import AmountCalculator
from src.core.config_manager import ConfigManager


class TestAmountCalculatorEdgeCases(unittest.TestCase):
    """异动计算器边界条件测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建配置管理器
        self.test_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.test_dir, 'test_config.json')
        self.config_manager = ConfigManager(self.config_file)
        
        # 创建异动计算器
        self.calculator = AmountCalculator(self.config_manager)
        
        # 准备测试异动记录
        self.sample_change_record = {
            'employee_id': '001',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资',
                'baseline_value': 5000.0,
                'current_value': 5200.0
            },
            'confidence': 0.95
        }
        
        self.allowance_change_record = {
            'employee_id': '002',
            'change_type': '津贴变动',
            'details': {
                'field': '岗位津贴',
                'baseline_value': 1000.0,
                'current_value': 1200.0
            },
            'confidence': 0.90
        }
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_calculate_basic_salary_changes(self):
        """测试基本工资变动计算"""
        result = self.calculator.calculate_change_amount(self.sample_change_record)
        
        # 验证计算结果
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 200.0)
        self.assertEqual(result['calculation_type'], '工资调整')
        self.assertTrue(result['is_increase'])
        self.assertEqual(result['baseline_amount'], 5000.0)
        self.assertEqual(result['current_amount'], 5200.0)
    
    def test_calculate_allowance_changes(self):
        """测试津贴变动计算"""
        result = self.calculator.calculate_change_amount(self.allowance_change_record)
        
        # 验证津贴计算结果
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 200.0)
        self.assertEqual(result['calculation_type'], '津贴变动')
        self.assertTrue(result['is_increase'])
        self.assertEqual(result['field'], '岗位津贴')
    
    def test_calculate_new_employee_amount(self):
        """测试新进人员金额计算"""
        new_employee_record = {
            'employee_id': '003',
            'change_type': '新进人员',
            'details': {
                'employee_data': {
                    '基本工资': 6000.0,
                    '岗位津贴': 1500.0,
                    '交通补贴': 300.0,
                    '其他': '非金额字段'
                }
            },
            'confidence': 0.95
        }
        
        result = self.calculator.calculate_change_amount(new_employee_record)
        
        # 验证新员工计算
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 7800.0)  # 6000+1500+300
        self.assertEqual(result['calculation_type'], '新进人员')
        self.assertTrue(result['is_new_employee'])
        self.assertIn('income_breakdown', result)
    
    def test_calculate_bonus_changes(self):
        """测试奖金变动计算"""
        bonus_record = {
            'employee_id': '004',
            'change_type': '工资调整',
            'details': {
                'field': '奖金',
                'baseline_value': 1000.0,
                'current_value': 1500.0
            },
            'confidence': 0.90
        }
        
        result = self.calculator.calculate_change_amount(bonus_record)
        
        # 验证奖金计算
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 500.0)
        self.assertEqual(result['field'], '奖金')
    
    def test_calculate_deduction_changes(self):
        """测试扣款计算"""
        deduction_record = {
            'employee_id': '005',
            'change_type': '扣款异动',
            'details': {
                'field': '迟到扣款',
                'deduction_amount': 500.0
            },
            'confidence': 0.85
        }
        
        result = self.calculator.calculate_change_amount(deduction_record)
        
        # 验证扣款计算
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], -500.0)  # 扣款为负数
        self.assertEqual(result['calculation_type'], '扣款异动')
        self.assertTrue(result['is_deduction'])
    
    def test_calculate_percentage_changes(self):
        """测试百分比变动计算"""
        # 测试变动比例计算
        result = self.calculator.calculate_change_amount(self.sample_change_record)
        
        # 验证变动比例
        expected_rate = (5200.0 - 5000.0) / 5000.0  # 4%
        self.assertAlmostEqual(result['change_rate'], expected_rate, places=4)
    
    def test_calculate_total_changes(self):
        """测试总变动计算"""
        # 执行多次计算并检查总计
        results = []
        test_records = [
            self.sample_change_record,
            self.allowance_change_record,
            {
                'employee_id': '006',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 8000.0,
                    'current_value': 8300.0
                },
                'confidence': 0.95
            }
        ]
        
        for record in test_records:
            result = self.calculator.calculate_change_amount(record)
            results.append(result['total_amount'])
        
        # 验证总计算
        total_changes = sum(results)
        self.assertEqual(total_changes, 700.0)  # 200+200+300
    
    def test_handle_missing_data(self):
        """测试缺失数据处理"""
        incomplete_record = {
            'employee_id': '007',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资'
                # 缺少baseline_value和current_value
            },
            'confidence': 0.50
        }
        
        result = self.calculator.calculate_change_amount(incomplete_record)
        
        # 验证缺失数据处理
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 0.0)
    
    def test_handle_zero_baseline(self):
        """测试基准值为零的处理"""
        zero_baseline_record = {
            'employee_id': '008',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资',
                'baseline_value': 0.0,
                'current_value': 5000.0
            },
            'confidence': 0.85
        }
        
        result = self.calculator.calculate_change_amount(zero_baseline_record)
        
        # 验证零基准值处理
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 5000.0)
        self.assertEqual(result['change_rate'], 1.0)  # 应该是100%增长
    
    def test_negative_values_handling(self):
        """测试负值处理"""
        negative_record = {
            'employee_id': '009',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资',
                'baseline_value': 5000.0,
                'current_value': 4500.0
            },
            'confidence': 0.90
        }
        
        result = self.calculator.calculate_change_amount(negative_record)
        
        # 验证负值处理
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], -500.0)
        self.assertFalse(result['is_increase'])
    
    def test_large_number_precision(self):
        """测试大数值精度"""
        large_number_record = {
            'employee_id': '010',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资',
                'baseline_value': 1000000.0,
                'current_value': 1000200.0
            },
            'confidence': 0.95
        }
        
        result = self.calculator.calculate_change_amount(large_number_record)
        
        # 验证大数值精度
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 200.0)
        self.assertAlmostEqual(result['change_rate'], 0.0002, places=6)
    
    def test_decimal_precision_calculation(self):
        """测试小数精度计算"""
        decimal_record = {
            'employee_id': '011',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资',
                'baseline_value': 5000.123,
                'current_value': 5200.456
            },
            'confidence': 0.95
        }
        
        result = self.calculator.calculate_change_amount(decimal_record)
        
        # 验证小数精度（默认保留2位小数）
        self.assertIsInstance(result, dict)
        self.assertAlmostEqual(result['total_amount'], 200.33, places=2)
    
    def test_multi_column_changes(self):
        """测试多列变动"""
        # 测试批量计算不同字段的变动
        multi_records = [
            {
                'employee_id': '012',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 5000.0,
                    'current_value': 5200.0
                },
                'confidence': 0.95
            },
            {
                'employee_id': '012',
                'change_type': '津贴变动',
                'details': {
                    'field': '津贴',
                    'baseline_value': 1000.0,
                    'current_value': 1100.0
                },
                'confidence': 0.90
            }
        ]
        
        results = self.calculator.batch_calculate_amounts(multi_records)
        
        # 验证多列变动
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]['total_amount'], 200.0)
        self.assertEqual(results[1]['total_amount'], 100.0)
    
    def test_group_based_calculation(self):
        """测试基于组的计算"""
        # 模拟按部门分组的计算
        department_records = [
            {
                'employee_id': '013',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 5000.0,
                    'current_value': 5200.0,
                    'department': '技术部'
                },
                'confidence': 0.95
            },
            {
                'employee_id': '014',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 6000.0,
                    'current_value': 6300.0,
                    'department': '技术部'
                },
                'confidence': 0.95
            }
        ]
        
        results = self.calculator.batch_calculate_amounts(department_records)
        
        # 验证分组计算
        self.assertEqual(len(results), 2)
        tech_total = sum(r['total_amount'] for r in results)
        self.assertEqual(tech_total, 500.0)  # 200+300
    
    def test_outlier_detection(self):
        """测试异常值检测"""
        # 创建包含异常值的记录
        outlier_record = {
            'employee_id': '015',
            'change_type': '工资调整',
            'details': {
                'field': '基本工资',
                'baseline_value': 5000.0,
                'current_value': 200000.0  # 异常大的变动（超过10万元阈值）
            },
            'confidence': 0.30  # 低置信度
        }
        
        result = self.calculator.calculate_change_amount(outlier_record)
        validation = self.calculator.validate_calculation_result(result)
        
        # 验证异常值检测
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_amount'], 195000.0)  # 200000-5000
        self.assertIn('validation_warnings', validation)
        self.assertGreater(len(validation['validation_warnings']), 0)
    
    def test_statistical_analysis(self):
        """测试统计分析功能"""
        # 执行多次计算
        for i in range(5):
            record = {
                'employee_id': f'{i:03d}',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 5000.0 + i * 100,
                    'current_value': 5200.0 + i * 100
                },
                'confidence': 0.95
            }
            self.calculator.calculate_change_amount(record)
        
        # 获取统计摘要
        summary = self.calculator.get_calculation_summary()
        
        # 验证统计分析
        self.assertIn('计算统计', summary)
        self.assertIn('平均金额', summary['计算统计'])
        self.assertIn('最大金额', summary['计算统计'])
        self.assertIn('最小金额', summary['计算统计'])
        # 验证计算次数为5（刚才的5次计算）
        self.assertEqual(summary['计算统计']['总计算次数'], 5)
    
    def test_custom_calculation_rules(self):
        """测试自定义计算规则"""
        # 验证计算器初始化了默认规则
        self.assertIsInstance(self.calculator.calculation_rules, dict)
        self.assertIn('工资调整', self.calculator.calculation_rules)
        self.assertIn('津贴变动', self.calculator.calculation_rules)
        self.assertIn('新进人员', self.calculator.calculation_rules)
    
    def test_calculation_performance(self):
        """测试计算性能"""
        import time
        
        # 创建大量测试记录
        large_batch = []
        for i in range(100):
            record = {
                'employee_id': f'{i:03d}',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 5000.0 + i,
                    'current_value': 5200.0 + i
                },
                'confidence': 0.95
            }
            large_batch.append(record)
        
        # 测试批量计算性能
        start_time = time.time()
        results = self.calculator.batch_calculate_amounts(large_batch)
        elapsed_time = time.time() - start_time
        
        # 验证性能和结果
        self.assertEqual(len(results), 100)
        self.assertLess(elapsed_time, 5.0)  # 应该在5秒内完成


class TestAmountCalculatorIntegration(unittest.TestCase):
    """异动计算器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建配置管理器
        self.test_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.test_dir, 'integration_config.json')
        self.config_manager = ConfigManager(self.config_file)
        
        # 创建异动计算器
        self.calculator = AmountCalculator(self.config_manager)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_complete_calculation_workflow(self):
        """测试完整计算工作流"""
        # 1. 执行多种类型的计算
        calculations = [
            {
                'employee_id': '001',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 5000.0,
                    'current_value': 5500.0
                },
                'confidence': 0.95
            },
            {
                'employee_id': '002',
                'change_type': '新进人员',
                'details': {
                    'employee_data': {
                        '基本工资': 6000.0,
                        '岗位津贴': 1000.0
                    }
                },
                'confidence': 0.90
            },
            {
                'employee_id': '003',
                'change_type': '扣款异动',
                'details': {
                    'field': '迟到扣款',
                    'deduction_amount': 200.0
                },
                'confidence': 0.85
            }
        ]
        
        # 2. 批量计算
        results = self.calculator.batch_calculate_amounts(calculations)
        
        # 3. 验证所有计算结果
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0]['total_amount'], 500.0)  # 工资调整
        self.assertEqual(results[1]['total_amount'], 7000.0)  # 新进人员
        self.assertEqual(results[2]['total_amount'], -200.0)  # 扣款
        
        # 4. 获取计算统计
        summary = self.calculator.get_calculation_summary()
        self.assertEqual(summary['计算统计']['总计算次数'], 3)
        self.assertEqual(summary['计算统计']['总金额'], 7300.0)  # 500+7000-200
        
        # 5. 验证每个结果
        for result in results:
            validation = self.calculator.validate_calculation_result(result)
            self.assertTrue(validation['is_valid'])
    
    def test_calculation_with_error_handling(self):
        """测试带错误处理的计算"""
        # 混合有效和无效记录
        mixed_records = [
            {
                'employee_id': '001',
                'change_type': '工资调整',
                'details': {
                    'field': '基本工资',
                    'baseline_value': 5000.0,
                    'current_value': 5200.0
                },
                'confidence': 0.95
            },
            {
                'employee_id': '002',
                'change_type': '无效类型',
                'details': {},
                'confidence': 0.50
            },
            {
                'employee_id': '003',
                'change_type': '津贴变动',
                'details': {
                    'field': '津贴',
                    'baseline_value': 1000.0,
                    'current_value': 1100.0
                },
                'confidence': 0.90
            }
        ]
        
        results = self.calculator.batch_calculate_amounts(mixed_records)
        
        # 验证混合结果处理
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0]['total_amount'], 200.0)  # 正常计算
        self.assertEqual(results[1]['total_amount'], 0.0)    # 其他异动
        self.assertEqual(results[2]['total_amount'], 100.0)  # 正常计算
        
        # 验证统计摘要能正确处理混合结果
        summary = self.calculator.get_calculation_summary()
        # 修正：由于这是独立的计算器实例，总计算次数应该等于3
        self.assertEqual(summary['计算统计']['总计算次数'], 3)


if __name__ == '__main__':
    unittest.main() 