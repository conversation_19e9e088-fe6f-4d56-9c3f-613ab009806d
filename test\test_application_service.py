"""
应用服务层测试

测试应用服务层的核心功能，确保GUI层和业务逻辑层之间的抽象层正常工作。

创建时间: 2025-01-22 (REFLECT Day 4)
目标: 将测试覆盖率从31%提升至80%
重点: ApplicationService作为新架构的核心，当前覆盖率0%
"""

import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.core.application_service import ApplicationService, ServiceResult


class TestApplicationService(unittest.TestCase):
    """应用服务层测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.app_service = ApplicationService()
        
        print("=" * 60)
        print("🏗️ 应用服务层测试开始")
        print("=" * 60)
    
    def test_service_result_creation(self):
        """测试ServiceResult数据类"""
        print("\n📋 测试ServiceResult数据类...")
        
        # 测试成功结果
        success_result = ServiceResult(
            success=True,
            message="操作成功",
            data={"key": "value"}
        )
        
        self.assertTrue(success_result.success)
        self.assertEqual(success_result.message, "操作成功")
        self.assertIsNotNone(success_result.data)
        self.assertIsNone(success_result.error)
        print("✅ 成功结果创建正确")
        
        # 测试失败结果
        test_error = Exception("测试错误")
        error_result = ServiceResult(
            success=False,
            message="操作失败",
            error=test_error
        )
        
        self.assertFalse(error_result.success)
        self.assertEqual(error_result.message, "操作失败")
        self.assertEqual(error_result.error, test_error)
        self.assertIsNone(error_result.data)
        print("✅ 失败结果创建正确")
        
        print("📋 ServiceResult数据类测试通过！")
    
    def test_initialization(self):
        """测试应用服务初始化"""
        print("\n🚀 测试应用服务初始化...")
        
        # 创建新的服务实例进行测试
        service = ApplicationService()
        
        # 初始状态验证
        self.assertFalse(service._initialized)
        self.assertIsNone(service._config_manager)
        self.assertIsNone(service._excel_importer)
        print("✅ 初始状态正确")
        
        # 模拟成功初始化
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(), 
            ReportManager=Mock()
        ):
            result = service.initialize()
            
            self.assertTrue(result.success)
            self.assertTrue(service._initialized)
            self.assertEqual(result.message, "应用服务初始化成功")
            print("✅ 服务初始化成功")
        
        print("🚀 应用服务初始化测试通过！")
    
    def test_initialization_failure(self):
        """测试应用服务初始化失败情况"""
        print("\n❌ 测试应用服务初始化失败...")
        
        service = ApplicationService()
        
        # 模拟初始化失败
        with patch('src.core.application_service.ConfigManager', side_effect=Exception("配置管理器初始化失败")):
            result = service.initialize()
            
            self.assertFalse(result.success)
            self.assertFalse(service._initialized)
            self.assertIn("配置管理器初始化失败", result.message)
            self.assertIsInstance(result.error, Exception)
            print("✅ 初始化失败处理正确")
        
        print("❌ 应用服务初始化失败测试通过！")
    
    def test_ensure_initialized_check(self):
        """测试初始化检查机制"""
        print("\n🔒 测试初始化检查机制...")
        
        service = ApplicationService()
        
        # 未初始化时调用方法应该抛出异常
        with self.assertRaises(RuntimeError) as context:
            service._ensure_initialized()
        
        self.assertIn("应用服务未初始化", str(context.exception))
        print("✅ 未初始化检查正确")
        
        # 初始化后应该正常
        service._initialized = True
        try:
            service._ensure_initialized()
            print("✅ 已初始化检查正确")
        except RuntimeError:
            self.fail("已初始化时不应抛出异常")
        
        print("🔒 初始化检查机制测试通过！")
    
    @patch('os.path.exists')
    def test_import_excel_data(self, mock_exists):
        """测试Excel数据导入"""
        print("\n📊 测试Excel数据导入...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._excel_importer = Mock()
        
        # 测试文件不存在
        mock_exists.return_value = False
        result = self.app_service.import_excel_data("nonexistent.xlsx")
        
        self.assertFalse(result.success)
        self.assertIn("文件不存在", result.message)
        print("✅ 文件不存在处理正确")
        
        # 测试成功导入
        mock_exists.return_value = True
        mock_import_result = {"rows": 100, "columns": ["工号", "姓名", "工资"]}
        self.app_service._excel_importer.import_file.return_value = mock_import_result
        
        result = self.app_service.import_excel_data("test.xlsx")
        
        self.assertTrue(result.success)
        self.assertIn("Excel数据导入成功", result.message)
        self.assertEqual(result.data, mock_import_result)
        print("✅ Excel导入成功")
        
        # 测试导入异常
        self.app_service._excel_importer.import_file.side_effect = Exception("导入失败")
        result = self.app_service.import_excel_data("test.xlsx")
        
        self.assertFalse(result.success)
        self.assertIn("数据导入失败", result.message)
        print("✅ 导入异常处理正确")
        
        print("📊 Excel数据导入测试通过！")
    
    @patch('os.path.exists')
    def test_import_baseline_data(self, mock_exists):
        """测试基准数据导入"""
        print("\n📋 测试基准数据导入...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._baseline_manager = Mock()
        
        # 测试成功导入
        mock_exists.return_value = True
        mock_baseline_result = {"records": 50, "period": "2024-01"}
        self.app_service._baseline_manager.load_baseline.return_value = mock_baseline_result
        
        result = self.app_service.import_baseline_data("baseline.xlsx")
        
        self.assertTrue(result.success)
        self.assertIn("基准数据导入成功", result.message)
        self.assertEqual(result.data, mock_baseline_result)
        self.app_service._baseline_manager.load_baseline.assert_called_once_with("baseline.xlsx")
        print("✅ 基准数据导入成功")
        
        print("📋 基准数据导入测试通过！")
    
    def test_validate_data(self):
        """测试数据验证"""
        print("\n🔍 测试数据验证...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._data_validator = Mock()
        
        # 测试验证通过
        mock_validation_result = Mock()
        mock_validation_result.is_valid = True
        self.app_service._data_validator.validate_all.return_value = mock_validation_result
        
        result = self.app_service.validate_data()
        
        self.assertTrue(result.success)
        self.assertEqual(result.message, "数据验证通过")
        self.assertEqual(result.data, mock_validation_result)
        print("✅ 数据验证通过")
        
        # 测试验证失败
        mock_validation_result.is_valid = False
        mock_validation_result.error_summary = "发现3个数据错误"
        
        result = self.app_service.validate_data()
        
        self.assertFalse(result.success)
        self.assertIn("数据验证发现问题", result.message)
        self.assertIn("发现3个数据错误", result.message)
        print("✅ 数据验证失败处理正确")
        
        print("🔍 数据验证测试通过！")
    
    def test_detect_changes(self):
        """测试异动检测"""
        print("\n🔍 测试异动检测...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._change_detector = Mock()
        
        # 测试检测成功
        mock_detection_result = Mock()
        mock_detection_result.changes = [
            {"employee_id": "001", "change_type": "工资调整"},
            {"employee_id": "002", "change_type": "新进人员"}
        ]
        self.app_service._change_detector.detect_all_changes.return_value = mock_detection_result
        
        result = self.app_service.detect_changes()
        
        self.assertTrue(result.success)
        self.assertIn("发现 2 个异动", result.message)
        self.assertEqual(result.data, mock_detection_result)
        print("✅ 异动检测成功")
        
        # 测试无异动
        mock_detection_result.changes = []
        result = self.app_service.detect_changes()
        
        self.assertTrue(result.success)
        self.assertIn("发现 0 个异动", result.message)
        print("✅ 无异动情况处理正确")
        
        print("🔍 异动检测测试通过！")
    
    def test_generate_word_report(self):
        """测试Word报告生成"""
        print("\n📄 测试Word报告生成...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._change_detector = Mock()
        self.app_service._report_manager = Mock()
        
        # 测试有数据的报告生成
        mock_detection_result = Mock()
        mock_detection_result.changes = [{"employee_id": "001", "change_type": "工资调整"}]
        self.app_service._change_detector.get_latest_results.return_value = mock_detection_result
        
        mock_report_path = "reports/salary_changes_report.docx"
        self.app_service._report_manager.generate_word_report.return_value = mock_report_path
        
        result = self.app_service.generate_word_report()
        
        self.assertTrue(result.success)
        self.assertIn("Word报告生成成功", result.message)
        self.assertEqual(result.data["report_path"], mock_report_path)
        print("✅ Word报告生成成功")
        
        # 测试无数据情况
        mock_detection_result.changes = None
        result = self.app_service.generate_word_report()
        
        self.assertFalse(result.success)
        self.assertIn("没有可生成报告的异动数据", result.message)
        print("✅ 无数据情况处理正确")
        
        print("📄 Word报告生成测试通过！")
    
    def test_generate_excel_report(self):
        """测试Excel报告生成"""
        print("\n📊 测试Excel报告生成...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._change_detector = Mock()
        self.app_service._report_manager = Mock()
        
        # 测试Excel报告生成
        mock_detection_result = Mock()
        mock_detection_result.changes = [
            {"employee_id": "001", "change_type": "工资调整"},
            {"employee_id": "002", "change_type": "津贴变更"}
        ]
        self.app_service._change_detector.get_latest_results.return_value = mock_detection_result
        
        mock_report_path = "reports/salary_changes_data.xlsx"
        self.app_service._report_manager.generate_excel_report.return_value = mock_report_path
        
        result = self.app_service.generate_excel_report("custom_path.xlsx")
        
        self.assertTrue(result.success)
        self.assertIn("Excel报告生成成功", result.message)
        self.assertEqual(result.data["report_path"], mock_report_path)
        self.app_service._report_manager.generate_excel_report.assert_called_once_with(
            mock_detection_result, "custom_path.xlsx"
        )
        print("✅ Excel报告生成成功")
        
        print("📊 Excel报告生成测试通过！")
    
    def test_user_preferences_management(self):
        """测试用户偏好设置管理"""
        print("\n⚙️ 测试用户偏好设置管理...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._user_preferences = Mock()
        
        # 测试获取用户偏好设置
        mock_preferences = {
            "theme": "dark",
            "language": "zh_CN",
            "auto_save": True
        }
        self.app_service._user_preferences.get_all_preferences.return_value = mock_preferences
        
        result = self.app_service.get_user_preferences()
        
        self.assertTrue(result.success)
        self.assertEqual(result.data, mock_preferences)
        print("✅ 获取用户偏好设置成功")
        
        # 测试保存用户偏好设置
        new_preferences = {
            "theme": "light",
            "auto_backup": False
        }
        
        result = self.app_service.save_user_preferences(new_preferences)
        
        self.assertTrue(result.success)
        self.assertIn("用户偏好设置保存成功", result.message)
        
        # 验证set_preference被正确调用
        expected_calls = [
            unittest.mock.call("theme", "light"),
            unittest.mock.call("auto_backup", False)
        ]
        self.app_service._user_preferences.set_preference.assert_has_calls(expected_calls, any_order=True)
        print("✅ 保存用户偏好设置成功")
        
        print("⚙️ 用户偏好设置管理测试通过！")
    
    def test_system_config_management(self):
        """测试系统配置管理"""
        print("\n🔧 测试系统配置管理...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._config_manager = Mock()
        
        # 测试获取系统配置
        mock_config = {
            "database_path": "data/salary_data.db",
            "template_path": "templates/",
            "backup_enabled": True
        }
        self.app_service._config_manager.get_all_config.return_value = mock_config
        
        result = self.app_service.get_system_config()
        
        self.assertTrue(result.success)
        self.assertEqual(result.data, mock_config)
        self.assertIn("获取系统配置成功", result.message)
        print("✅ 获取系统配置成功")
        
        print("🔧 系统配置管理测试通过！")
    
    def test_system_status(self):
        """测试系统状态获取"""
        print("\n📊 测试系统状态获取...")
        
        # 设置服务为已初始化状态
        self.app_service._initialized = True
        self.app_service._config_manager = Mock()
        self.app_service._excel_importer = Mock()
        self.app_service._data_validator = Mock()
        self.app_service._change_detector = Mock()
        self.app_service._report_manager = Mock()
        
        result = self.app_service.get_system_status()
        
        self.assertTrue(result.success)
        
        status = result.data
        self.assertTrue(status["initialized"])
        self.assertTrue(status["config_loaded"])
        self.assertTrue(status["modules_ready"])
        print("✅ 系统状态获取成功")
        
        print("📊 系统状态获取测试通过！")
    
    def test_resource_cleanup(self):
        """测试资源清理"""
        print("\n🧹 测试资源清理...")
        
        # 设置已初始化的服务
        self.app_service._initialized = True
        self.app_service._change_detector = Mock()
        self.app_service._baseline_manager = Mock()
        self.app_service._excel_importer = Mock()
        self.app_service._report_manager = Mock()
        
        # 执行清理
        self.app_service.cleanup()
        
        # 验证清理调用
        self.app_service._change_detector.cleanup.assert_called_once()
        self.app_service._baseline_manager.cleanup.assert_called_once()
        self.app_service._excel_importer.cleanup.assert_called_once()
        self.app_service._report_manager.cleanup.assert_called_once()
        
        # 验证初始化状态重置
        self.assertFalse(self.app_service._initialized)
        print("✅ 资源清理成功")
        
        print("🧹 资源清理测试通过！")
    
    def test_integration_workflow(self):
        """测试集成工作流程"""
        print("\n🔄 测试集成工作流程...")
        
        # 创建新的服务实例
        service = ApplicationService()
        
        # 步骤1: 初始化服务
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            init_result = service.initialize()
            self.assertTrue(init_result.success)
            print("✅ 步骤1: 服务初始化完成")
        
        # 步骤2: 导入数据
        with patch('os.path.exists', return_value=True):
            import_result = service.import_excel_data("test.xlsx")
            self.assertTrue(import_result.success)
            print("✅ 步骤2: 数据导入完成")
        
        # 步骤3: 异动检测
        mock_detection_result = Mock()
        mock_detection_result.changes = [{"employee_id": "001", "change_type": "工资调整"}]
        service._change_detector.detect_all_changes.return_value = mock_detection_result
        
        detect_result = service.detect_changes()
        self.assertTrue(detect_result.success)
        print("✅ 步骤3: 异动检测完成")
        
        # 步骤4: 生成报告
        service._change_detector.get_latest_results.return_value = mock_detection_result
        service._report_manager.generate_word_report.return_value = "report.docx"
        
        report_result = service.generate_word_report()
        self.assertTrue(report_result.success)
        print("✅ 步骤4: 报告生成完成")
        
        # 步骤5: 资源清理
        service.cleanup()
        self.assertFalse(service._initialized)
        print("✅ 步骤5: 资源清理完成")
        
        print("\n📊 集成工作流程结果统计:")
        print(f"   • 总步骤数: 5")
        print(f"   • 成功步骤: 5")
        print(f"   • 成功率: 100%")
        
        print("🔄 集成工作流程测试通过！")
    
    def tearDown(self):
        """测试清理"""
        # 确保服务资源被清理
        if hasattr(self.app_service, 'cleanup'):
            self.app_service.cleanup()
        
        print("\n" + "=" * 60)
        print("🎉 应用服务层测试全部完成！")
        print("=" * 60)


def run_tests():
    """运行测试"""
    # 设置测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestApplicationService)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1) 