"""
异动识别模块测试

测试异动识别模块的4个核心组件：
1. BaselineManager - 基准数据管理器
2. ChangeDetector - 异动识别器  
3. AmountCalculator - 金额计算器
4. ReasonAnalyzer - 原因分析器

创建时间: 2025-06-15 (CREATIVE阶段 Day 3)
"""

import sys
import os
import unittest
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.core.config_manager import ConfigManager
from src.modules.change_detection import (
    BaselineManager, 
    ChangeDetector, 
    AmountCalculator, 
    ReasonAnalyzer
)


class TestChangeDetectionModule(unittest.TestCase):
    """异动识别模块测试类"""
    
    def setUp(self):
        """测试初始化"""
        # 初始化配置管理器
        config_file = project_root / "test_config.json"
        self.config_manager = ConfigManager(config_file=str(config_file))
        
        # 初始化各组件
        self.baseline_manager = BaselineManager(self.config_manager)
        self.change_detector = ChangeDetector(self.config_manager, self.baseline_manager)
        self.amount_calculator = AmountCalculator(self.config_manager)
        self.reason_analyzer = ReasonAnalyzer(self.config_manager)
        
        # 准备测试数据
        self.sample_baseline_data = pd.DataFrame({
            '工号': ['001', '002', '003'],
            '姓名': ['张三', '李四', '王五'],
            '基本工资': [5000, 6000, 5500],
            '岗位津贴': [1000, 1200, 1100],
            '部门': ['人事部', '财务部', '技术部']
        })
        
        self.sample_current_data = pd.DataFrame({
            '工号': ['001', '002', '004'],  # 004是新员工
            '姓名': ['张三', '李四', '赵六'],
            '基本工资': [5200, 6000, 4500],  # 张三涨工资
            '岗位津贴': [1000, 1500, 800],  # 李四津贴增加
            '部门': ['人事部', '财务部', '技术部']
        })
        
        print("=" * 60)
        print("🔍 异动识别模块测试开始")
        print("=" * 60)
    
    def test_baseline_manager(self):
        """测试基准数据管理器"""
        print("\n📊 测试基准数据管理器...")
        
        # 测试创建基准数据
        success = self.baseline_manager.create_baseline_from_data(
            self.sample_baseline_data, 
            "2024-01",
            save_as_baseline=False  # 测试中不保存
        )
        self.assertTrue(success, "基准数据创建失败")
        print(f"✅ 基准数据创建成功，包含 {len(self.baseline_manager.baseline_data)} 条记录")
        
        # 测试获取个人基准数据
        baseline_record = self.baseline_manager.get_baseline_for_person("001")
        self.assertIsNotNone(baseline_record, "未找到员工001的基准数据")
        self.assertEqual(baseline_record['姓名'], '张三', "基准数据内容不正确")
        print(f"✅ 成功获取员工001的基准数据: {baseline_record['姓名']}")
        
        # 测试统计信息
        stats = self.baseline_manager.get_baseline_statistics()
        self.assertIn('总记录数', stats, "统计信息格式不正确")
        print(f"✅ 基准数据统计: 总记录数 {stats['总记录数']}")
        
        print("📊 基准数据管理器测试通过！")
    
    def test_change_detector(self):
        """测试异动识别器"""
        print("\n🔍 测试异动识别器...")
        
        # 先创建基准数据
        self.baseline_manager.create_baseline_from_data(
            self.sample_baseline_data, 
            "2024-01",
            save_as_baseline=False
        )
        
        # 测试异动检测
        changes = self.change_detector.detect_changes(
            self.sample_current_data,
            "2024-02"
        )
        
        self.assertIsInstance(changes, list, "异动检测结果应为列表")
        self.assertGreater(len(changes), 0, "应该检测到异动")
        print(f"✅ 检测到 {len(changes)} 项异动")
        
        # 验证检测到的异动类型
        change_types = [change['change_type'] for change in changes]
        self.assertIn('新进人员', change_types, "应该检测到新进人员")
        self.assertIn('离职人员', change_types, "应该检测到离职人员")
        print(f"✅ 检测到的异动类型: {set(change_types)}")
        
        # 测试异动摘要
        summary = self.change_detector.get_change_summary()
        self.assertIn('总异动数', summary, "摘要格式不正确")
        print(f"✅ 异动摘要: 总异动数 {summary['总异动数']}")
        
        print("🔍 异动识别器测试通过！")
    
    def test_amount_calculator(self):
        """测试金额计算器"""
        print("\n💰 测试金额计算器...")
        
        # 创建测试异动记录
        test_change = {
            'employee_id': '001',
            'name': '张三',
            'change_type': '工资调整',
            'period': '2024-02',
            'confidence': 0.95,
            'details': {
                'field': '基本工资',
                'baseline_value': 5000,
                'current_value': 5200,
                'change_amount': 200
            }
        }
        
        # 测试金额计算
        result = self.amount_calculator.calculate_change_amount(test_change)
        
        self.assertIn('total_amount', result, "计算结果应包含总金额")
        self.assertEqual(result['total_amount'], 200.0, "金额计算不正确")
        print(f"✅ 工资调整金额计算: {result['total_amount']:.2f}元")
        
        # 测试批量计算
        change_records = [test_change]
        batch_results = self.amount_calculator.batch_calculate_amounts(change_records)
        
        self.assertEqual(len(batch_results), 1, "批量计算结果数量不正确")
        self.assertEqual(batch_results[0]['total_amount'], 200.0, "批量计算结果不正确")
        print(f"✅ 批量计算完成，处理 {len(batch_results)} 条记录")
        
        # 测试计算摘要
        summary = self.amount_calculator.get_calculation_summary()
        self.assertIn('计算统计', summary, "计算摘要格式不正确")
        print(f"✅ 计算摘要: 总计算次数 {summary['计算统计']['总计算次数']}")
        
        print("💰 金额计算器测试通过！")
    
    def test_reason_analyzer(self):
        """测试原因分析器"""
        print("\n📝 测试原因分析器...")
        
        # 创建测试异动记录
        test_change = {
            'employee_id': '001',
            'name': '张三',
            'change_type': '工资调整',
            'period': '2024-02',
            'confidence': 0.95,
            'detected_time': datetime.now(),
            'details': {
                'field': '基本工资',
                'description': '工资标准调整',
                'baseline_value': 5000,
                'current_value': 5200
            }
        }
        
        # 创建测试计算结果
        test_calculation = {
            'calculation_type': '工资调整',
            'total_amount': 200.0,
            'is_increase': True
        }
        
        # 测试原因分析
        analysis_result = self.reason_analyzer.analyze_change_reason(
            test_change, 
            test_calculation
        )
        
        self.assertIn('reason_inference', analysis_result, "分析结果应包含原因推断")
        self.assertIn('recommendations', analysis_result, "分析结果应包含建议")
        self.assertIn('confidence_score', analysis_result, "分析结果应包含置信度")
        
        primary_reason = analysis_result['reason_inference']['primary_reason']
        print(f"✅ 原因分析完成，推断原因: {primary_reason}")
        print(f"✅ 分析置信度: {analysis_result['confidence_score']:.3f}")
        print(f"✅ 处理建议数量: {len(analysis_result['recommendations'])}")
        
        # 测试批量分析
        change_records = [test_change]
        calculation_results = [test_calculation]
        batch_analysis = self.reason_analyzer.batch_analyze_reasons(
            change_records, 
            calculation_results
        )
        
        self.assertEqual(len(batch_analysis), 1, "批量分析结果数量不正确")
        print(f"✅ 批量分析完成，处理 {len(batch_analysis)} 条记录")
        
        # 测试分析摘要
        summary = self.reason_analyzer.get_analysis_summary()
        self.assertIn('总分析次数', summary, "分析摘要格式不正确")
        print(f"✅ 分析摘要: 总分析次数 {summary['总分析次数']}")
        
        print("📝 原因分析器测试通过！")
    
    def test_integration_workflow(self):
        """测试集成工作流程"""
        print("\n🔄 测试集成工作流程...")
        
        # 步骤1: 创建基准数据
        baseline_success = self.baseline_manager.create_baseline_from_data(
            self.sample_baseline_data, 
            "2024-01",
            save_as_baseline=False
        )
        self.assertTrue(baseline_success, "基准数据创建失败")
        print("✅ 步骤1: 基准数据创建完成")
        
        # 步骤2: 检测异动
        changes = self.change_detector.detect_changes(
            self.sample_current_data,
            "2024-02"
        )
        self.assertGreater(len(changes), 0, "应该检测到异动")
        print(f"✅ 步骤2: 异动检测完成，发现 {len(changes)} 项异动")
        
        # 步骤3: 计算金额
        calculation_results = []
        for change in changes:
            result = self.amount_calculator.calculate_change_amount(change)
            calculation_results.append(result)
        
        self.assertEqual(len(calculation_results), len(changes), "计算结果数量不匹配")
        print(f"✅ 步骤3: 金额计算完成，处理 {len(calculation_results)} 项")
        
        # 步骤4: 原因分析
        analysis_results = self.reason_analyzer.batch_analyze_reasons(
            changes, 
            calculation_results
        )
        
        self.assertEqual(len(analysis_results), len(changes), "分析结果数量不匹配")
        print(f"✅ 步骤4: 原因分析完成，分析 {len(analysis_results)} 项")
        
        # 统计最终结果
        total_amount = sum(calc.get('total_amount', 0) for calc in calculation_results)
        high_confidence_count = sum(1 for analysis in analysis_results if analysis.get('confidence_score', 0) >= 0.7)
        
        print("\n📊 集成工作流程结果统计:")
        print(f"   • 总异动数: {len(changes)}")
        print(f"   • 总金额: {total_amount:.2f}元")
        print(f"   • 高置信度分析: {high_confidence_count}/{len(analysis_results)}")
        
        print("🔄 集成工作流程测试通过！")
    
    def tearDown(self):
        """测试清理"""
        print("\n" + "=" * 60)
        print("🎉 异动识别模块测试全部完成！")
        print("=" * 60)


def run_tests():
    """运行测试"""
    # 设置测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestChangeDetectionModule)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1) 