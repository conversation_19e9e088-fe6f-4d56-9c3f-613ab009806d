"""
配置管理模块增强测试
专注于提升config_manager.py的测试覆盖率
目标：从51%提升至80%+
"""
import unittest
from unittest.mock import Mock, patch, mock_open
import sys
import os
import tempfile
import json
import shutil
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.core.config_manager import ConfigManager


class TestConfigManagerEdgeCases(unittest.TestCase):
    """配置管理器边界条件测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.test_dir, 'test_config.json')
        self.config_manager = ConfigManager(self.config_file)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_config_file_creation_on_missing(self):
        """测试配置文件不存在时自动创建"""
        non_existent_file = os.path.join(self.test_dir, 'new_config.json')
        manager = ConfigManager(non_existent_file)
        
        # 验证文件被创建
        self.assertTrue(os.path.exists(non_existent_file))
        
        # 验证默认配置
        config = manager.get_config()
        self.assertIsNotNone(config)
        self.assertEqual(config.version, "0.1.0")
    
    def test_config_file_corrupted_handling(self):
        """测试损坏配置文件的处理"""
        # 创建损坏的JSON文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write('{"invalid": json content}')
        
        # 验证错误处理 - 应该使用默认配置
        manager = ConfigManager(self.config_file)
        config = manager.get_config()
        
        # 应该回退到默认配置
        self.assertEqual(config.version, "0.1.0")
    
    def test_nested_config_operations(self):
        """测试嵌套配置操作"""
        # 设置嵌套配置
        self.config_manager.update_config(
            debug_mode=True,
            log_level="DEBUG"
        )
        
        # 验证配置被更新
        config = self.config_manager.get_config()
        self.assertTrue(config.debug_mode)
        self.assertEqual(config.log_level, "DEBUG")
    
    def test_config_validation_edge_cases(self):
        """测试配置验证边界情况"""
        # 测试有效配置
        validation = self.config_manager.validate_config()
        self.assertTrue(validation['valid'])
        
        # 测试修改配置后的验证
        self.config_manager.update_config(debug_mode=True)
        validation = self.config_manager.validate_config()
        self.assertTrue(validation['valid'])
    
    def test_config_backup_and_restore(self):
        """测试配置保存功能"""
        # 更新配置
        self.config_manager.update_config(
            debug_mode=True,
            log_level="DEBUG"
        )
        
        # 验证配置已保存
        new_manager = ConfigManager(self.config_file)
        config = new_manager.get_config()
        self.assertTrue(config.debug_mode)
        self.assertEqual(config.log_level, "DEBUG")
    
    def test_environment_variable_loading(self):
        """测试环境变量加载功能"""
        # 设置环境变量
        with patch.dict(os.environ, {'SALARY_SYSTEM_DEBUG': 'true', 'SALARY_SYSTEM_LOG_LEVEL': 'DEBUG'}):
            manager = ConfigManager(self.config_file)
            config = manager.get_config()
            
            # 验证环境变量被应用
            self.assertIsNotNone(config)
    
    def test_config_section_access(self):
        """测试配置部分访问"""
        # 获取数据库配置部分
        database_config = self.config_manager.get_section('database')
        self.assertIsNotNone(database_config)
        self.assertEqual(database_config.db_name, 'salary_system.db')
        
        # 获取UI配置部分
        ui_config = self.config_manager.get_section('ui')
        self.assertIsNotNone(ui_config)
        self.assertEqual(ui_config.window_width, 1200)
        
        # 测试不存在的配置部分
        with self.assertRaises(AttributeError):
            self.config_manager.get_section('nonexistent')
    
    def test_path_operations(self):
        """测试路径操作功能"""
        # 测试数据库路径
        db_path = self.config_manager.get_database_path()
        self.assertIsNotNone(db_path)
        self.assertTrue(str(db_path).endswith('.db'))
        
        # 测试模板路径
        template_path = self.config_manager.get_template_path()
        self.assertIsNotNone(template_path)
        
        # 测试输出路径
        output_path = self.config_manager.get_output_path()
        self.assertIsNotNone(output_path)
        
        # 测试带文件名的路径
        template_file = self.config_manager.get_template_path('test.docx')
        self.assertTrue(str(template_file).endswith('test.docx'))
    
    def test_config_update_with_invalid_values(self):
        """测试使用无效值更新配置"""
        # 测试无效的配置项名称
        original_config = self.config_manager.get_config()
        
        # 更新不存在的配置项（应该记录警告但不会失败）
        self.config_manager.update_config(invalid_config_item="test")
        
        # 验证原有配置仍然有效
        current_config = self.config_manager.get_config()
        self.assertEqual(original_config.version, current_config.version)


class TestConfigManagerPerformance(unittest.TestCase):
    """配置管理器性能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.test_dir, 'perf_config.json')
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_repeated_config_loading(self):
        """测试重复配置加载性能"""
        import time
        
        manager = ConfigManager(self.config_file)
        
        # 测试多次加载配置的性能
        start_time = time.time()
        for _ in range(10):
            config = manager.get_config()
            self.assertIsNotNone(config)
        
        elapsed = time.time() - start_time
        # 配置加载应该很快（小于1秒）
        self.assertLess(elapsed, 1.0)
    
    def test_config_update_performance(self):
        """测试配置更新性能"""
        import time
        
        manager = ConfigManager(self.config_file)
        
        # 测试多次更新配置的性能
        start_time = time.time()
        for i in range(5):
            manager.update_config(debug_mode=(i % 2 == 0))
        
        elapsed = time.time() - start_time
        # 配置更新应该在合理时间内完成（小于3秒）
        self.assertLess(elapsed, 3.0)


class TestConfigManagerIntegration(unittest.TestCase):
    """配置管理器集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.test_dir, 'integration_config.json')
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_complete_config_lifecycle(self):
        """测试完整配置生命周期"""
        # 1. 创建配置管理器
        manager = ConfigManager(self.config_file)
        
        # 2. 验证初始配置
        config = manager.get_config()
        self.assertEqual(config.version, "0.1.0")
        self.assertFalse(config.debug_mode)
        
        # 3. 更新配置
        manager.update_config(
            debug_mode=True,
            log_level="DEBUG"
        )
        
        # 4. 验证更新
        updated_config = manager.get_config()
        self.assertTrue(updated_config.debug_mode)
        self.assertEqual(updated_config.log_level, "DEBUG")
        
        # 5. 重新加载验证持久化
        new_manager = ConfigManager(self.config_file)
        reloaded_config = new_manager.get_config()
        self.assertTrue(reloaded_config.debug_mode)
        self.assertEqual(reloaded_config.log_level, "DEBUG")
        
        # 6. 验证配置有效性
        validation = new_manager.validate_config()
        self.assertTrue(validation['valid'])
        self.assertEqual(len(validation['errors']), 0)
    
    def test_directory_creation_and_management(self):
        """测试目录创建和管理"""
        manager = ConfigManager(self.config_file)
        
        # 获取各种路径
        db_path = manager.get_database_path()
        template_path = manager.get_template_path()
        output_path = manager.get_output_path()
        
        # 验证路径都是有效的Path对象
        from pathlib import Path
        self.assertIsInstance(db_path, Path)
        self.assertIsInstance(template_path, Path)
        self.assertIsInstance(output_path, Path)
        
        # 验证路径结构合理
        self.assertTrue(str(db_path).endswith('.db'))
        self.assertTrue('template' in str(template_path).lower())
        self.assertTrue('output' in str(output_path).lower())


if __name__ == '__main__':
    unittest.main() 