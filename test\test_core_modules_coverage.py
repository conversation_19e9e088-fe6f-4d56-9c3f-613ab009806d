#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心模块测试覆盖率提升

本模块专门补充测试覆盖率较低的核心业务模块，
目标是将整体测试覆盖率从46%提升至80%以上。

重点覆盖模块:
1. report_generation模块 (当前15% → 目标70%)
2. system_config模块 (当前45% → 目标70%) 
3. change_detection模块补充 (当前65% → 目标80%)
4. data_import模块补充 (当前64% → 目标80%)
"""

import unittest
import tempfile
import shutil
import os
import sys
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock, call
from typing import Dict, List, Any
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger


class TestReportGenerationCoverage(unittest.TestCase):
    """报告生成模块测试覆盖率提升"""
    
    def setUp(self):
        """测试前置准备"""
        self.logger = setup_logger(f"{__name__}.{self.__class__.__name__}")
        self.temp_dir = tempfile.mkdtemp(prefix="coverage_test_")
        
    def tearDown(self):
        """测试清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_template_engine_operations(self):
        """测试模板引擎操作"""
        from src.modules.report_generation.template_engine import TemplateEngine
        
        engine = TemplateEngine()
        
        # 测试占位符提取
        test_text = "你好 {name}，今年是 {{year}} 年，请查看 [month] 月的报告。"
        placeholders = engine.extract_placeholders(test_text)
        self.assertIsInstance(placeholders, list)
        
        # 测试占位符替换
        data = {'name': '张三', 'year': '2025', 'month': '5'}
        result = engine.replace_placeholders(test_text, data)
        self.assertIsInstance(result, str)
        self.assertIn("张三", result)
        
        # 测试模板列表
        templates = engine.list_templates()
        self.assertIsInstance(templates, dict)
    
    def test_excel_generator_operations(self):
        """测试Excel生成器操作"""
        from src.modules.report_generation.excel_generator import ExcelGenerator
        from src.modules.report_generation.template_engine import TemplateEngine
        
        with patch('src.modules.report_generation.excel_generator.EXCEL_AVAILABLE', True):
            template_engine = Mock(spec=TemplateEngine)
            template_engine.get_template_info.return_value = {'type': 'excel', 'name': 'test'}
            template_engine.get_template_path.return_value = "test_template.xlsx"
            template_engine.validate_template_data.return_value = {'valid': True, 'missing_fields': []}
            
            generator = ExcelGenerator(template_engine)
            
            # 测试预览功能
            preview_path = generator.preview_report("test_template", {"test": "data"})
            # 由于无实际模板文件，预期返回None
            self.assertIsNone(preview_path)
    
    def test_preview_manager_operations(self):
        """测试预览管理器操作"""
        from src.modules.report_generation.preview_manager import PreviewManager
        
        manager = PreviewManager()
        
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试内容")
        
        # 测试预览文档
        result = manager.preview_document(test_file, 'system')
        self.assertIsInstance(result, bool)


class TestSystemConfigCoverage(unittest.TestCase):
    """系统配置模块测试覆盖率提升"""
    
    def setUp(self):
        """测试前置准备"""
        self.logger = setup_logger(f"{__name__}.{self.__class__.__name__}")
        self.temp_dir = tempfile.mkdtemp(prefix="config_test_")
        
    def tearDown(self):
        """测试清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_user_preferences_operations(self):
        """测试用户偏好设置操作"""
        from src.modules.system_config.user_preferences import UserPreferences
        from src.modules.system_config.config_manager import ConfigManager
        
        config_manager = Mock(spec=ConfigManager)
        prefs = UserPreferences(config_manager)
        
        # 测试设置获取
        prefs.set_preference("language", "zh-CN")
        language = prefs.get_preference("language")
        self.assertEqual(language, "zh-CN")
        
        # 测试默认值
        default_theme = prefs.get_preference("theme", "default")
        self.assertEqual(default_theme, "default")
        
        # 测试批量操作通过单独调用
        batch_prefs = {
            "auto_save": True,
            "backup_interval": 300,
            "theme": "dark"
        }
        
        for key, value in batch_prefs.items():
            prefs.set_preference(key, value, save_immediately=False)
        
        for key, value in batch_prefs.items():
            self.assertEqual(prefs.get_preference(key), value)
    
    def test_template_manager_operations(self):
        """测试模板管理器操作"""
        from src.modules.system_config.template_manager import TemplateManager
        from src.modules.system_config.config_manager import ConfigManager
        
        config_manager = Mock(spec=ConfigManager)
        manager = TemplateManager(config_manager)
        
        # 测试模板列表获取
        templates = manager.list_templates()
        self.assertIsInstance(templates, list)
        
        # 测试模板验证
        with patch('os.path.exists', return_value=True):
            is_valid = manager.validate_template_file("test_template.docx")
            self.assertIsInstance(is_valid, bool)
    
    def test_system_maintenance_operations(self):
        """测试系统维护操作"""
        from src.modules.system_config.system_maintenance import SystemMaintenance
        from src.modules.system_config.config_manager import ConfigManager
        
        config_manager = Mock(spec=ConfigManager)
        maintenance = SystemMaintenance(config_manager)
        
        # 测试清理操作 - 返回dict而不是bool
        result = maintenance.cleanup_temp_files()
        self.assertIsInstance(result, dict)
        self.assertIn('deleted_files', result)
        
        # 测试系统信息收集
        system_info = maintenance.collect_system_info()
        self.assertIsInstance(system_info, dict)
    
    def test_config_manager_operations(self):
        """测试配置管理器操作"""
        from src.modules.system_config.config_manager import ConfigManager
        
        # 创建临时配置文件
        config_file = os.path.join(self.temp_dir, "test_config.json")
        manager = ConfigManager(config_file)
        
        # 测试配置加载
        config = manager.load_config()
        self.assertIsInstance(config, dict)
        
        # 测试配置值设置和获取
        manager.set_config_value("test.key", "test_value")
        value = manager.get_config_value("test.key")
        self.assertEqual(value, "test_value")
        
        # 测试配置保存
        result = manager.save_config(config)
        self.assertIsInstance(result, bool)


class TestChangeDetectionCoverage(unittest.TestCase):
    """异动检测模块测试覆盖率提升"""
    
    def setUp(self):
        """测试前置准备"""
        self.logger = setup_logger(f"{__name__}.{self.__class__.__name__}")
        from src.core.config_manager import ConfigManager
        self.config_manager = ConfigManager()
        
    def test_baseline_manager_operations(self):
        """测试基准管理器操作"""
        from src.modules.change_detection.baseline_manager import BaselineManager
        
        manager = BaselineManager(self.config_manager)
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '工号': ['001', '002'],
            '姓名': ['张三', '李四'],
            '基本工资': [5000, 6000]
        })
        
        # 测试基准数据创建
        result = manager.create_baseline_from_data(test_data, "2025-01", save_as_baseline=False)
        self.assertIsInstance(result, bool)
        
        # 测试基准数据获取 - 修正为实际存在的方法
        baseline = manager.get_baseline_for_person("001")
        if baseline is not None:
            self.assertIsInstance(baseline, dict)
            self.assertIn('工号', baseline)
    
    def test_amount_calculator_operations(self):
        """测试金额计算器操作"""
        from src.modules.change_detection.amount_calculator import AmountCalculator
        
        calculator = AmountCalculator(self.config_manager)
        
        # 测试工资调整计算场景
        change_record = {
            "employee_id": "001",
            "change_type": "工资调整",
            "details": {
                "field": "基本工资",
                "baseline_value": 5000,
                "current_value": 5500
            }
        }
        
        result = calculator.calculate_change_amount(change_record)
        self.assertIsInstance(result, dict)
        self.assertIn("total_amount", result)
        
        # 测试批量计算
        batch_results = calculator.batch_calculate_amounts([change_record])
        self.assertEqual(len(batch_results), 1)
        
        # 测试计算摘要
        summary = calculator.get_calculation_summary()
        self.assertIsInstance(summary, dict)
    
    def test_reason_analyzer_operations(self):
        """测试原因分析器操作"""
        from src.modules.change_detection.reason_analyzer import ReasonAnalyzer
        
        analyzer = ReasonAnalyzer(self.config_manager)
        
        # 测试异动原因分析
        change_record = {
            "employee_id": "001",
            "change_type": "工资调整",
            "details": {
                "field": "基本工资",
                "baseline_value": 5000,
                "current_value": 5500
            }
        }
        
        calculation_result = {
            "total_amount": 500,
            "change_rate": 0.1
        }
        
        reason = analyzer.analyze_change_reason(change_record, calculation_result)
        self.assertIsInstance(reason, dict)
        
        # 测试批量分析
        batch_results = analyzer.batch_analyze_reasons([change_record], [calculation_result])
        self.assertEqual(len(batch_results), 1)


class TestDataImportCoverage(unittest.TestCase):
    """数据导入模块测试覆盖率提升"""
    
    def setUp(self):
        """测试前置准备"""
        self.logger = setup_logger(f"{__name__}.{self.__class__.__name__}")
        self.temp_dir = tempfile.mkdtemp(prefix="import_test_")
        
    def tearDown(self):
        """测试清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_csv_importer_operations(self):
        """测试CSV导入器操作"""
        from src.modules.data_import.csv_importer import CSVImporter
        
        importer = CSVImporter()
        
        # 创建测试CSV文件
        csv_content = "工号,姓名,工资\n001,张三,5000\n002,李四,6000"
        csv_file = os.path.join(self.temp_dir, "test.csv")
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write(csv_content)
        
        # 测试导入
        result = importer.import_data(csv_file)
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
    
    def test_field_mapper_operations(self):
        """测试字段映射器操作"""
        from src.modules.data_import.field_mapper import FieldMapper
        
        mapper = FieldMapper()
        
        # 创建测试数据
        test_df = pd.DataFrame({
            "工号": ["001", "002"],
            "员工姓名": ["张三", "李四"], 
            "基本工资": [5000, 6000]
        })
        
        # 测试字段映射
        mapped_df = mapper.map_fields(test_df)
        self.assertIsInstance(mapped_df, pd.DataFrame)
        self.assertEqual(len(mapped_df.columns), 3)
    
    def test_excel_importer_operations(self):
        """测试Excel导入器操作"""
        from src.modules.data_import.excel_importer import ExcelImporter
        
        importer = ExcelImporter()
        
        # 创建测试Excel文件
        test_data = pd.DataFrame({
            "工号": ["001", "002", "003"],
            "工资": [5000, 6000, 5500],
            "入职日期": ["2020-01-01", "2019-05-15", "2021-03-10"]
        })
        
        excel_file = os.path.join(self.temp_dir, "test.xlsx")
        test_data.to_excel(excel_file, index=False, engine='openpyxl')
        
        # 测试文件验证
        file_info = importer.validate_file(excel_file)
        self.assertTrue(file_info['is_valid'])
        
        # 测试工作表名称获取
        sheet_names = importer.get_sheet_names(excel_file)
        self.assertIsInstance(sheet_names, list)
        self.assertGreater(len(sheet_names), 0)
        
        # 测试数据预览
        preview_info = importer.preview_data(excel_file, max_rows=5)
        self.assertIsInstance(preview_info, dict)
        self.assertIn('columns', preview_info)


class TestUtilsCoverage(unittest.TestCase):
    """工具模块测试覆盖率提升"""
    
    def test_log_config_operations(self):
        """测试日志配置操作"""
        from src.utils.log_config import setup_logger, get_module_logger
        
        # 测试不同级别的日志配置 - 修正参数名
        logger_info = setup_logger("test_info", log_level="INFO")
        logger_debug = setup_logger("test_debug", log_level="DEBUG")
        logger_error = setup_logger("test_error", log_level="ERROR")
        
        self.assertIsNotNone(logger_info)
        self.assertIsNotNone(logger_debug)
        self.assertIsNotNone(logger_error)
        
        # 测试模块日志器
        module_logger = get_module_logger(__name__)
        self.assertIsNotNone(module_logger)
        
        # 测试日志输出
        logger_info.info("测试信息日志")
        logger_debug.debug("测试调试日志")
        logger_error.error("测试错误日志")


# 测试套件执行器
def run_coverage_improvement_tests():
    """运行测试覆盖率提升测试"""
    print("\n" + "="*80)
    print("📊 测试覆盖率提升专项测试开始")
    print("="*80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestReportGenerationCoverage,
        TestSystemConfigCoverage, 
        TestChangeDetectionCoverage,
        TestDataImportCoverage,
        TestUtilsCoverage
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试总结
    print("\n" + "="*80)
    print("📈 测试覆盖率提升结果")
    print("="*80)
    print(f"新增测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors))/result.testsRun*100):.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    # 运行测试覆盖率提升测试
    success = run_coverage_improvement_tests()
    
    if success:
        print("\n🎉 测试覆盖率提升测试通过！")
        exit(0)
    else:
        print("\n⚠️ 部分测试失败，需要修复。")
        exit(1) 