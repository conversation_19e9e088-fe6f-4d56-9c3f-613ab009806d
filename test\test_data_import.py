"""
数据导入模块测试

测试功能:
- Excel文件导入器的各种功能
- 数据验证器的验证规则和错误处理
- 集成测试
"""

import os
import sys
import pandas as pd
import tempfile
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator, ValidationRule, FieldType
from src.core.config_manager import ConfigManager
from src.utils.log_config import setup_logger

def create_test_excel_file() -> str:
    """创建测试用的Excel文件"""
    # 创建测试数据
    test_data = {
        '工号': ['EMP001', 'EMP002', 'EMP003', 'INVALID', ''],
        '姓名': ['张三', '李四', '王五', '赵六', ''],
        '身份证号': ['110101199001011234', '110101199002021234', 'INVALID_ID', '110101199003031234', ''],
        '基本工资': [8000.50, 9500.00, 'INVALID', 10000.00, ''],
        '手机号': ['13912345678', 'INVALID_PHONE', '13923456789', '13934567890', ''],
        '入职日期': ['2020-01-15', '2021/03/20', 'INVALID_DATE', '2022-05-10', '']
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df.to_excel(tmp_file.name, index=False, engine='openpyxl')
        return tmp_file.name

def test_excel_importer():
    """测试Excel导入器"""
    logger = setup_logger(__name__)
    logger.info("开始测试Excel导入器...")
    
    try:
        # 创建配置
        config = {
            'max_file_size_mb': 100,
            'chunk_size': 1000,
            'supported_formats': ['.xlsx', '.xls'],
            'encoding_priority': ['utf-8', 'gbk', 'gb2312']
        }
        
        # 初始化导入器
        importer = ExcelImporter(config)
        
        # 创建测试文件
        test_file = create_test_excel_file()
        logger.info(f"创建测试文件: {test_file}")
        
        # 测试文件验证
        file_info = importer.validate_file(test_file)
        logger.info(f"文件验证结果: {file_info}")
        assert file_info['is_valid'], "文件验证失败"
        
        # 测试Excel版本检测
        version = importer.detect_excel_version(test_file)
        logger.info(f"Excel版本: {version}")
        assert version == 'xlsx', f"版本检测错误: {version}"
        
        # 测试工作表名称获取
        sheet_names = importer.get_sheet_names(test_file)
        logger.info(f"工作表名称: {sheet_names}")
        assert len(sheet_names) > 0, "未检测到工作表"
        
        # 测试数据预览
        preview = importer.preview_data(test_file, max_rows=3)
        logger.info(f"数据预览: {preview['column_count']}列, {preview['preview_rows']}行")
        assert preview['column_count'] == 6, f"列数不正确: {preview['column_count']}"
        
        # 测试数据导入
        progress_values = []
        def progress_callback(progress):
            progress_values.append(progress)
            
        df = importer.import_data(test_file, progress_callback=progress_callback)
        logger.info(f"导入数据: {len(df)}行 x {len(df.columns)}列")
        assert not df.empty, "导入数据为空"
        assert len(progress_values) > 0, "进度回调未执行"
        
        # 测试模板导出
        template_path = tempfile.mktemp(suffix='.xlsx')
        columns = ['工号', '姓名', '身份证号', '基本工资']
        success = importer.export_template(template_path, columns)
        assert success, "模板导出失败"
        
        # 清理临时文件
        os.unlink(test_file)
        os.unlink(template_path)
        
        logger.info("Excel导入器测试通过!")
        
    except Exception as e:
        logger.error(f"Excel导入器测试失败: {e}")
        assert False, f"Excel导入器测试失败: {e}"

def test_data_validator():
    """测试数据验证器"""
    logger = setup_logger(__name__)
    logger.info("开始测试数据验证器...")
    
    try:
        # 创建测试数据
        test_data = {
            '工号': ['EMP001', 'EMP002', 'INVALID_ID', '', 'EMP005'],
            '姓名': ['张三', '李四', '王五', '', '赵六'],
            '身份证号': ['110101199001011234', '110101199002021234', 'INVALID', '', '110101199005051234'],
            '基本工资': [8000.50, 9500.00, 'INVALID', '', -1000],
            '手机号': ['13912345678', 'INVALID', '13923456789', '', '13945678901'],
            '入职日期': ['2020-01-15', '2021/03/20', 'INVALID', '', '2022-05-10']
        }
        
        df = pd.DataFrame(test_data)
        
        # 创建验证规则
        validation_rules = [
            ValidationRule(
                field_name='工号',
                field_type=FieldType.EMPLOYEE_ID,
                required=True
            ),
            ValidationRule(
                field_name='姓名',
                field_type=FieldType.STRING,
                required=True,
                min_length=2,
                max_length=10
            ),
            ValidationRule(
                field_name='身份证号',
                field_type=FieldType.ID_CARD,
                required=True
            ),
            ValidationRule(
                field_name='基本工资',
                field_type=FieldType.FLOAT,
                required=True,
                min_value=0,
                max_value=100000
            ),
            ValidationRule(
                field_name='手机号',
                field_type=FieldType.PHONE,
                required=False
            ),
            ValidationRule(
                field_name='入职日期',
                field_type=FieldType.DATE,
                required=False
            )
        ]
        
        # 初始化验证器
        validator = DataValidator(validation_rules)
        
        # 执行验证
        validation_report = validator.validate_dataframe(df)
        
        logger.info(f"验证结果: {validation_report['total_errors']}个错误")
        logger.info(f"验证通过: {validation_report['validation_passed']}")
        logger.info(f"错误统计: {validation_report['error_counts']}")
        
        # 验证应该发现错误
        assert validation_report['total_errors'] > 0, "应该检测到验证错误"
        assert not validation_report['validation_passed'], "验证不应该通过"
        
        # 检查特定错误类型
        error_codes = [result['error_code'] for result in validation_report['results']]
        assert 'MISSING_VALUE' in error_codes, "应该检测到缺失值错误"
        assert 'TYPE_MISMATCH' in error_codes, "应该检测到类型不匹配错误"
        
        # 测试错误报告导出
        report_path = tempfile.mktemp(suffix='.xlsx')
        success = validator.export_error_report(report_path)
        assert success, "错误报告导出失败"
        
        # 验证报告文件是否存在
        assert os.path.exists(report_path), "错误报告文件未创建"
        
        # 清理文件
        os.unlink(report_path)
        
        logger.info("数据验证器测试通过!")
        
    except Exception as e:
        logger.error(f"数据验证器测试失败: {e}")
        assert False, f"数据验证器测试失败: {e}"

def test_integration():
    """集成测试：Excel导入 + 数据验证"""
    logger = setup_logger(__name__)
    logger.info("开始集成测试...")
    
    try:
        # 配置管理器
        config_manager = ConfigManager()
        import_config = config_manager.get_section('import_settings')
        
        # Excel导入器
        importer = ExcelImporter(import_config)
        
        # 创建测试文件
        test_file = create_test_excel_file()
        
        # 导入数据
        df = importer.import_data(test_file)
        
        # 数据验证器
        validator = DataValidator()  # 使用默认规则
        
        # 验证导入的数据
        validation_report = validator.validate_dataframe(df)
        
        logger.info("=== 集成测试结果 ===")
        logger.info(f"导入数据: {len(df)}行 x {len(df.columns)}列")
        logger.info(f"验证错误: {validation_report['total_errors']}个")
        logger.info(f"错误率: {validation_report['summary']['error_rate']:.2%}")
        
        if validation_report['total_errors'] > 0:
            logger.info("主要问题字段:")
            for field, count in validation_report['summary']['most_problematic_fields']:
                logger.info(f"  {field}: {count}个错误")
                
            logger.info("主要错误类型:")
            for error_type, count in validation_report['summary']['most_common_errors']:
                logger.info(f"  {error_type}: {count}次")
        
        # 清理临时文件
        os.unlink(test_file)
        
        logger.info("集成测试完成!")
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        assert False, f"集成测试失败: {e}"

def main():
    """主测试函数"""
    logger = setup_logger(__name__)
    logger.info("=== 数据导入模块测试开始 ===")
    
    # 确保输出目录存在
    output_dir = project_root / 'temp'
    output_dir.mkdir(exist_ok=True)
    
    tests = [
        ("Excel导入器测试", test_excel_importer),
        ("数据验证器测试", test_data_validator),
        ("集成测试", test_integration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过!")
        return True
    else:
        logger.error(f"⚠️  {total_tests - passed_tests}个测试失败")
        return False

if __name__ == "__main__":
    main() 