#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据导入对话框更新测试程序

测试内容：
1. 验证"工资表文件"已修改为"Excel文件"
2. 验证"设为基准数据"复选框已移除
3. 验证新增的"数据库设置"区域功能
4. 验证表名选择和动态创建功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def create_test_excel_file():
    """创建测试用Excel文件"""
    try:
        import pandas as pd
        from openpyxl import Workbook
        
        # 测试数据
        test_data = {
            '工号': ['001', '002', '003', '004', '005'],
            '姓名': ['张三', '李四', '王五', '赵六', '钱七'],
            '基本工资': [5000, 5500, 6000, 5800, 5200],
            '岗位工资': [2000, 2200, 2500, 2300, 2100],
            '津贴补贴': [800, 900, 1000, 850, 750],
            '应发合计': [7800, 8600, 9500, 8950, 8050],
            '实发工资': [7020, 7740, 8550, 8055, 7245]
        }
        
        # 创建DataFrame
        df = pd.DataFrame(test_data)
        
        # 创建测试文件路径
        test_file = project_root / "temp" / "test_salary_data_updated.xlsx"
        test_file.parent.mkdir(exist_ok=True)
        
        # 创建工作簿
        wb = Workbook()
        
        # 创建多个工作表
        ws1 = wb.active
        ws1.title = "2024年6月工资表"
        
        # 写入表头
        for col_idx, header in enumerate(df.columns, 1):
            ws1.cell(row=1, column=col_idx, value=header)
        
        # 写入数据
        for row_idx, row_data in enumerate(df.values, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws1.cell(row=row_idx, column=col_idx, value=value)
        
        # 创建第二个工作表
        ws2 = wb.create_sheet("全部在职人员工资表")
        for col_idx, header in enumerate(df.columns, 1):
            ws2.cell(row=1, column=col_idx, value=header)
        
        for row_idx, row_data in enumerate(df.values, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws2.cell(row=row_idx, column=col_idx, value=value)
        
        # 保存文件
        wb.save(str(test_file))
        
        return test_file
        
    except Exception as e:
        print(f"创建测试Excel文件失败: {e}")
        return None

def test_data_import_dialog_updated():
    """测试更新后的DataImportDialog功能"""
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.dialogs import DataImportDialog
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("============================================================")
        print("数据导入对话框更新测试")
        print("============================================================")
        
        # 创建测试文件
        print("\n1. 创建测试Excel文件...")
        test_file = create_test_excel_file()
        if test_file:
            print(f"   ✅ 测试Excel文件创建成功: {test_file}")
        else:
            print("   ❌ 测试Excel文件创建失败")
            return False
        
        # 创建DataImportDialog
        print("\n2. 创建DataImportDialog...")
        dialog = DataImportDialog()
        print("   ✅ 对话框创建成功")
        
        # 验证界面元素
        print("\n3. 验证界面元素...")
        
        # 检查文件选择标签是否已改为"Excel文件"
        file_group = None
        for child in dialog.findChildren(dialog.__class__.__bases__[0]):
            if hasattr(child, 'title') and child.title() == "文件选择":
                file_group = child
                break
        
        if file_group:
            print("   ✅ 找到文件选择组")
        else:
            print("   ❌ 未找到文件选择组")
        
        # 检查是否存在salary_file_edit（现在应该对应Excel文件）
        if hasattr(dialog, 'salary_file_edit'):
            print("   ✅ Excel文件输入框存在")
        else:
            print("   ❌ Excel文件输入框不存在")
        
        # 检查"设为基准数据"复选框是否已移除
        if hasattr(dialog, 'set_as_baseline_check'):
            print("   ❌ '设为基准数据'复选框仍然存在（应该已移除）")
        else:
            print("   ✅ '设为基准数据'复选框已成功移除")
        
        # 检查数据库设置相关组件
        database_components = [
            'target_table_combo',
            'table_creation_group',
            'create_table_with_sheet_name',
            'create_table_with_custom_name',
            'custom_table_name_edit',
            'no_table_creation'
        ]
        
        missing_components = []
        for component in database_components:
            if hasattr(dialog, component):
                print(f"   ✅ {component} 存在")
            else:
                print(f"   ❌ {component} 不存在")
                missing_components.append(component)
        
        if not missing_components:
            print("   ✅ 所有数据库设置组件都已正确添加")
        else:
            print(f"   ❌ 缺少组件: {missing_components}")
        
        # 测试文件加载
        print("\n4. 测试文件加载...")
        dialog.salary_file_edit.setText(str(test_file))
        dialog._load_sheet_names(str(test_file))
        
        # 获取工作表列表
        sheet_names = [dialog.sheet_combo.itemText(i) for i in range(dialog.sheet_combo.count())]
        print(f"   ✅ 工作表: {sheet_names}")
        
        # 测试表名选择功能
        print("\n5. 测试数据库设置功能...")
        
        # 测试现有表选择
        existing_tables = [dialog.target_table_combo.itemText(i) for i in range(dialog.target_table_combo.count())]
        print(f"   ✅ 现有表列表: {existing_tables}")
        
        # 测试表创建选项
        print(f"   ✅ 使用工作表名创建表: {dialog.create_table_with_sheet_name.isChecked()}")
        print(f"   ✅ 使用自定义表名: {dialog.create_table_with_custom_name.isChecked()}")
        print(f"   ✅ 仅预览模式: {dialog.no_table_creation.isChecked()}")
        
        # 测试获取目标表名功能
        target_table = dialog._get_target_table_name()
        print(f"   ✅ 当前目标表名: '{target_table}'")
        
        # 测试表创建选项切换
        print("\n6. 测试表创建选项切换...")
        
        # 测试自定义表名选项
        dialog.create_table_with_custom_name.setChecked(True)
        dialog._on_table_creation_option_changed()
        print(f"   ✅ 自定义表名输入框启用: {dialog.custom_table_name_edit.isEnabled()}")
        
        # 测试工作表名选项
        dialog.create_table_with_sheet_name.setChecked(True)
        dialog._on_table_creation_option_changed()
        print(f"   ✅ 使用工作表名选项: {dialog.create_table_with_sheet_name.isChecked()}")
        print(f"   ✅ 自定义表名输入框禁用: {not dialog.custom_table_name_edit.isEnabled()}")
        
        # 测试预览按钮状态
        print(f"\n7. 预览功能状态:")
        print(f"   ✅ 预览按钮启用: {dialog.preview_btn.isEnabled()}")
        print(f"   ✅ 字段映射按钮启用: {dialog.mapping_btn.isEnabled()}")
        
        print("\n============================================================")
        print("数据导入对话框更新测试完成")
        print("============================================================")
        
        # 显示对话框进行手动验证（可选）
        user_input = input("\n是否显示对话框进行手动验证？(y/n): ")
        if user_input.lower() == 'y':
            print("显示对话框，请手动验证界面...")
            dialog.show()
            app.exec_()
        
        dialog.close()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_data_import_dialog_updated()
    
    if success:
        print("\n🎉 数据导入对话框更新测试成功完成！")
        print("\n✅ 主要更新验证:")
        print("   - '工资表文件' → 'Excel文件'")
        print("   - 移除 '设为基准数据' 复选框")
        print("   - 新增 '数据库设置' 区域")
        print("   - 新增 表名选择和动态创建功能")
    else:
        print("\n❌ 数据导入对话框更新测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 