#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入修复测试脚本（更新版）

用于验证修复后的DataImportDialog功能是否正常工作
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_excel_file():
    """创建测试Excel文件"""
    # 创建测试数据
    test_data = {
        '工号': ['E001', 'E002', 'E003', 'E004', 'E005'],
        '姓名': ['张三', '李四', '王五', '赵六', '钱七'],
        '部门': ['技术部', '人事部', '财务部', '技术部', '市场部'],
        '基本工资': [8000, 7500, 9000, 8500, 7000],
        '津贴': [1000, 800, 1200, 1100, 900],
        '入职日期': ['2020-01-01', '2019-06-15', '2021-03-10', '2020-08-20', '2022-02-14']
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存到Excel文件
    test_file = project_root / 'temp' / 'test_salary_data.xlsx'
    test_file.parent.mkdir(exist_ok=True)
    
    with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='全部在职人员工资表', index=False)
        df.iloc[:3].to_excel(writer, sheet_name='部分数据', index=False)  # 只保存前3行到第二个sheet
    
    return test_file

def test_data_import_dialog():
    """测试DataImportDialog功能"""
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.dialogs import DataImportDialog
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("============================================================")
        print("DataImportDialog 修复测试")
        print("============================================================")
        
        # 创建测试文件
        print("创建测试Excel文件...")
        test_file = create_test_excel_file()
        print(f"测试Excel文件创建成功: {test_file}")
        
        # 创建DataImportDialog
        print("\n创建DataImportDialog...")
        dialog = DataImportDialog()
        
        # 设置文件路径
        dialog.salary_file_edit.setText(str(test_file))
        
        # 触发加载工作表名称
        print("加载工作表名称...")
        dialog._load_sheet_names(str(test_file))
        
        # 获取工作表列表
        sheet_names = [dialog.sheet_combo.itemText(i) for i in range(dialog.sheet_combo.count())]
        print(f"工作表: {sheet_names}")
        
        # 测试预览按钮是否可用
        print(f"预览按钮是否可用: {dialog.preview_btn.isEnabled()}")
        print(f"字段映射按钮是否可用: {dialog.mapping_btn.isEnabled()}")
        
        # 选择第一个工作表
        if sheet_names:
            dialog.sheet_combo.setCurrentText(sheet_names[0])
            print(f"选择工作表: {sheet_names[0]}")
            
            # 测试数据预览功能
            print("\n测试数据预览功能...")
            dialog._preview_data()
            
            # 检查预览表格
            row_count = dialog.preview_table.rowCount()
            col_count = dialog.preview_table.columnCount()
            print(f"预览表格: {row_count} 行 x {col_count} 列")
            
            # 测试字段映射功能
            print("\n测试字段映射功能...")
            dialog._setup_field_mapping()
            
            # 检查字段映射表格
            mapping_rows = dialog.mapping_table.rowCount()
            print(f"字段映射表格: {mapping_rows} 行")
            
            # 测试导入功能（不显示对话框）
            print("\n测试导入功能...")
            try:
                # 模拟导入过程（但不显示对话框）
                from src.modules.data_import.excel_importer import ExcelImporter
                from src.modules.data_import.data_validator import DataValidator
                
                excel_importer = ExcelImporter()
                data_validator = DataValidator()
                
                # 验证文件
                file_info = excel_importer.validate_file(str(test_file))
                print(f"文件验证结果: {file_info['is_valid']}")
                
                # 导入数据
                df = excel_importer.import_data(
                    file_path=str(test_file),
                    sheet_name=sheet_names[0],
                    start_row=0,
                    max_rows=None
                )
                
                print(f"导入数据: {len(df)} 行 x {len(df.columns)} 列")
                print(f"列名: {list(df.columns)}")
                
                # 数据验证
                validation_result = data_validator.validate_dataframe(df)
                print(f"验证结果类型: {type(validation_result)}")
                print(f"总错误数: {validation_result.get('total_errors', 0)}")
                
                print("✓ 所有功能测试通过！")
                
            except Exception as e:
                print(f"✗ 导入功能测试失败: {e}")
                return False
        
        print("\n============================================================")
        print("✓ DataImportDialog 修复测试完成！")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_import_dialog()
    sys.exit(0 if success else 1) 