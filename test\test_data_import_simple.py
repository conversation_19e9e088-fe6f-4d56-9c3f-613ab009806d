"""
简单的数据导入模块测试
"""

import pandas as pd
import tempfile
import os
from pathlib import Path

# 导入模块
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator, ValidationRule, FieldType
from src.utils.log_config import setup_logger

def test_basic_functionality():
    """测试基本功能"""
    logger = setup_logger("test_data_import")
    logger.info("开始基本功能测试...")
    
    try:
        # 1. 创建测试数据
        test_data = {
            '工号': ['EMP001', 'EMP002', 'EMP003'],
            '姓名': ['张三', '李四', '王五'],
            '基本工资': [8000, 9000, 10000]
        }
        df = pd.DataFrame(test_data)
        
        # 2. 创建临时Excel文件
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            df.to_excel(tmp_file.name, index=False, engine='openpyxl')
            test_file = tmp_file.name
        
        logger.info(f"创建测试文件: {test_file}")
        
        # 3. 测试Excel导入器
        importer = ExcelImporter()
        
        # 文件验证
        file_info = importer.validate_file(test_file)
        logger.info(f"文件验证: {file_info['is_valid']}")
        
        # 数据导入
        imported_df = importer.import_data(test_file)
        logger.info(f"导入数据: {len(imported_df)}行 x {len(imported_df.columns)}列")
        
        # 4. 测试数据验证器
        validator = DataValidator()
        
        # 添加验证规则
        validator.add_validation_rule(ValidationRule(
            field_name='工号',
            field_type=FieldType.EMPLOYEE_ID,
            required=True
        ))
        
        # 执行验证
        validation_report = validator.validate_dataframe(imported_df)
        logger.info(f"验证结果: {validation_report['total_errors']}个错误")
        
        # 5. 清理临时文件
        os.unlink(test_file)
        
        logger.info("✅ 基本功能测试通过!")
        
    except Exception as e:
        logger.error(f"❌ 基本功能测试失败: {e}")
        assert False, f"基本功能测试失败: {e}"

if __name__ == "__main__":
    logger = setup_logger("test_main")
    logger.info("=== 数据导入模块简单测试 ===")
    
    if test_basic_functionality():
        logger.info("🎉 测试成功!")
    else:
        logger.error("💥 测试失败!") 