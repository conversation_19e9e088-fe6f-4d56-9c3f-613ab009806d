"""
分页字段映射丢失问题修复验证测试

测试目标：
1. 验证新的字段映射配置格式
2. 验证分页时字段映射的一致性
3. 验证表头双击编辑功能
4. 验证智能字段匹配算法
5. 验证以数据库字段名为键生成映射
6. 验证Excel列名到数据库字段名的转换

创建时间: 2025-01-27 (更新)
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator
from src.modules.data_import.config_sync_manager import ConfigSyncManager


class TestFieldMappingFix(unittest.TestCase):
    """字段映射修复功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = AutoFieldMappingGenerator()
        self.test_excel_columns = [
            "工号", "姓名", "部门名称", "2025年岗位工资", 
            "2025年薪级工资", "津贴", "2025年奖励性绩效预发", 
            "应发工资", "2025公积金"
        ]
        self.test_table_name = "salary_data_2025_03_test"
    
    def test_excel_column_to_db_field_conversion(self):
        """测试Excel列名到数据库字段名的转换"""
        print("\n=== 测试Excel列名到数据库字段名转换 ===")
        
        test_cases = [
            ("工号", "employee_id"),
            ("姓名", "employee_name"),
            ("部门名称", "department"),
            ("2025年岗位工资", "position_salary"),
            ("2025年薪级工资", "grade_salary"),
            ("津贴", "allowance"),
            ("应发工资", "total_salary"),
            ("五险一金", "social_insurance")
        ]
        
        for excel_col, expected_db_field in test_cases:
            with self.subTest(excel_col=excel_col):
                result = self.generator._excel_column_to_db_field(excel_col)
                print(f"  {excel_col} -> {result} (期望: {expected_db_field})")
                self.assertEqual(result, expected_db_field, 
                               f"Excel列名 '{excel_col}' 应转换为 '{expected_db_field}'，实际得到 '{result}'")
    
    def test_generate_mapping_with_db_keys(self):
        """测试生成的映射是否以数据库字段名为键"""
        print("\n=== 测试映射生成（数据库字段名为键） ===")
        
        mapping = self.generator.generate_mapping(self.test_excel_columns, self.test_table_name)
        
        print(f"生成的映射配置:")
        for db_field, display_name in mapping.items():
            print(f"  {db_field} -> {display_name}")
        
        # 验证映射不为空
        self.assertGreater(len(mapping), 0, "应该生成至少一个字段映射")
        
        # 验证所有键都是数据库字段名格式（小写+下划线）
        for db_field in mapping.keys():
            self.assertRegex(db_field, r'^[a-z_]+$', 
                           f"映射键 '{db_field}' 应该是数据库字段名格式（小写+下划线）")
        
        # 验证特定的映射
        expected_mappings = {
            "employee_id": "工号",
            "employee_name": "姓名",
            "department": "部门名称",
            "position_salary": "2025年岗位工资",
            "grade_salary": "2025年薪级工资"
        }
        
        for db_field, expected_display in expected_mappings.items():
            if db_field in mapping:
                print(f"  验证: {db_field} -> {mapping[db_field]} (期望: {expected_display})")
                # 注意：显示名可能经过处理，所以这里检查是否包含关键词
                self.assertIn("工号", mapping.get("employee_id", ""), "employee_id应映射到包含'工号'的显示名")
                self.assertIn("姓名", mapping.get("employee_name", ""), "employee_name应映射到包含'姓名'的显示名")
    
    def test_mapping_validation(self):
        """测试映射键验证功能"""
        print("\n=== 测试映射键验证 ===")
        
        # 模拟的字段映射（包含有效和无效的键）
        test_mapping = {
            "employee_id": "工号",
            "employee_name": "姓名",
            "invalid_field": "无效字段",  # 这个字段不存在于数据库中
            "department": "部门"
        }
        
        # 模拟的实际数据库字段
        actual_db_fields = [
            "employee_id", "employee_name", "department", 
            "position_salary", "grade_salary", "total_salary"
        ]
        
        validated_mapping = self.generator.validate_mapping_keys(test_mapping, actual_db_fields)
        
        print(f"原始映射: {test_mapping}")
        print(f"实际DB字段: {actual_db_fields}")
        print(f"验证后映射: {validated_mapping}")
        
        # 验证无效字段被移除
        self.assertNotIn("invalid_field", validated_mapping, "无效字段应该被移除")
        
        # 验证有效字段被保留
        self.assertIn("employee_id", validated_mapping, "有效字段应该被保留")
        self.assertIn("employee_name", validated_mapping, "有效字段应该被保留")
        
        # 验证缺失字段被补充
        self.assertIn("position_salary", validated_mapping, "缺失的数据库字段应该被补充")
        self.assertIn("grade_salary", validated_mapping, "缺失的数据库字段应该被补充")
        
        # 验证所有实际数据库字段都有映射
        for db_field in actual_db_fields:
            self.assertIn(db_field, validated_mapping, f"数据库字段 '{db_field}' 应该有对应的映射")
    
    def test_default_display_name_generation(self):
        """测试默认显示名生成"""
        print("\n=== 测试默认显示名生成 ===")
        
        test_cases = [
            ("employee_id", "工号"),
            ("employee_name", "姓名"),
            ("department", "部门"),
            ("unknown_field", "Unknown Field"),  # 未知字段应转换为友好格式
            ("中文字段", "中文字段")  # 中文字段应保持不变
        ]
        
        for db_field, expected_pattern in test_cases:
            with self.subTest(db_field=db_field):
                result = self.generator._generate_default_display_name(db_field)
                print(f"  {db_field} -> {result}")
                
                if db_field in ["employee_id", "employee_name", "department"]:
                    # 对于已知字段，应该返回准确的中文名
                    self.assertEqual(result, expected_pattern)
                elif db_field == "中文字段":
                    # 中文字段应保持不变
                    self.assertEqual(result, expected_pattern)
                else:
                    # 未知字段应该被转换为某种友好格式
                    self.assertIsInstance(result, str)
                    self.assertGreater(len(result), 0)


class TestDataImportDialogIntegration(unittest.TestCase):
    """数据导入对话框集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 这里我们只测试逻辑，不涉及GUI
        pass
    
    def test_field_mapping_save_logic(self):
        """测试字段映射保存逻辑（模拟）"""
        print("\n=== 测试字段映射保存逻辑 ===")
        
        # 模拟Excel列名和用户映射
        excel_columns = ["工号", "姓名", "部门名称", "2025年岗位工资"]
        user_field_mapping = {
            "工号": "工号",
            "姓名": "姓名", 
            "部门名称": "部门",
            "2025年岗位工资": "岗位工资"
        }
        table_name = "test_table"
        
        # 模拟AutoFieldMappingGenerator
        generator = AutoFieldMappingGenerator()
        
        # 1. 生成DB字段映射
        db_field_mapping = generator.generate_mapping(excel_columns, table_name)
        print(f"自动生成的DB字段映射: {db_field_mapping}")
        
        # 2. 转换用户映射为DB字段名为键的格式
        validated_mapping = {}
        for excel_col, display_name in user_field_mapping.items():
            db_field_name = generator._excel_column_to_db_field(excel_col)
            validated_mapping[db_field_name] = display_name
        
        print(f"转换后的用户映射: {validated_mapping}")
        
        # 3. 合并映射
        final_mapping = {}
        final_mapping.update(db_field_mapping)
        final_mapping.update(validated_mapping)
        
        print(f"最终映射: {final_mapping}")
        
        # 验证最终映射的格式
        for db_field, display_name in final_mapping.items():
            self.assertRegex(db_field, r'^[a-z_]+$', 
                           f"最终映射的键 '{db_field}' 应该是数据库字段名格式")
            self.assertIsInstance(display_name, str, "显示名应该是字符串")
            self.assertGreater(len(display_name), 0, "显示名不应为空")


def run_tests():
    """运行所有测试"""
    print("开始运行字段映射修复功能测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(unittest.makeSuite(TestFieldMappingFix))
    suite.addTest(unittest.makeSuite(TestDataImportDialogIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    print(f"测试完成！")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

    def test_new_intelligent_field_mapping(self):
        """测试新的智能字段映射生成算法"""
        print("\n=== 测试新的智能字段映射生成算法 ===")

        excel_headers = ["工号", "姓名", "部门名称", "基本工资", "应发工资"]
        db_fields = ["employee_id", "employee_name", "department", "basic_salary", "total_salary"]

        # 使用新的智能映射方法
        mapping = self.generator.create_initial_field_mapping(excel_headers, db_fields)

        print(f"生成的映射: {mapping}")

        # 验证映射结果
        self.assertIsInstance(mapping, dict)
        self.assertEqual(len(mapping), len(db_fields))

        # 验证关键字段映射
        self.assertEqual(mapping.get("employee_id"), "工号")
        self.assertEqual(mapping.get("employee_name"), "姓名")
        self.assertEqual(mapping.get("department"), "部门名称")
        self.assertEqual(mapping.get("basic_salary"), "基本工资")
        self.assertEqual(mapping.get("total_salary"), "应发工资")

        print("✅ 新的智能字段映射生成测试通过")

    def test_pagination_field_mapping_consistency(self):
        """测试分页时字段映射的一致性"""
        print("\n=== 测试分页字段映射一致性 ===")

        import pandas as pd
        import tempfile

        # 创建临时配置管理器
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_config_path = f.name

        try:
            config_sync = ConfigSyncManager(config_path=temp_config_path)

            # 模拟数据库返回的DataFrame（使用数据库字段名）
            db_data = pd.DataFrame({
                "employee_id": ["001", "002", "003"],
                "employee_name": ["张三", "李四", "王五"],
                "department": ["技术部", "财务部", "人事部"],
                "basic_salary": [8000, 7000, 6000],
                "total_salary": [10000, 8500, 7500]
            })

            # 设置字段映射
            field_mapping = {
                "employee_id": "工号",
                "employee_name": "员工姓名",
                "department": "所属部门",
                "basic_salary": "基本工资",
                "total_salary": "应发合计"
            }

            mapping_data = {
                "field_mappings": field_mapping,
                "metadata": {"source": "test"}
            }

            table_name = "test_table"
            config_sync.save_complete_mapping(table_name, mapping_data)

            # 模拟修复后的字段映射应用逻辑
            def apply_field_mapping_to_dataframe(df, table_name):
                field_mapping = config_sync.load_mapping(table_name)

                if not field_mapping:
                    return df

                # 创建列名重命名映射：数据库字段名 → 用户显示名
                column_rename_map = {}

                for db_field, display_name in field_mapping.items():
                    if db_field in df.columns and display_name:
                        column_rename_map[db_field] = display_name

                # 应用重命名
                if column_rename_map:
                    df_renamed = df.rename(columns=column_rename_map)
                    return df_renamed

                return df

            # 应用字段映射
            mapped_df = apply_field_mapping_to_dataframe(db_data, table_name)

            # 验证映射结果
            expected_columns = ["工号", "员工姓名", "所属部门", "基本工资", "应发合计"]
            actual_columns = list(mapped_df.columns)

            print(f"原始列名: {list(db_data.columns)}")
            print(f"映射后列名: {actual_columns}")
            print(f"期望列名: {expected_columns}")

            self.assertEqual(actual_columns, expected_columns)

            # 验证数据内容不变
            self.assertEqual(len(mapped_df), 3)
            self.assertEqual(mapped_df["工号"].tolist(), ["001", "002", "003"])
            self.assertEqual(mapped_df["员工姓名"].tolist(), ["张三", "李四", "王五"])

            print("✅ 分页字段映射一致性测试通过")

        finally:
            # 清理临时文件
            import os
            try:
                os.unlink(temp_config_path)
            except:
                pass


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
