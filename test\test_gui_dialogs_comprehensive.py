"""
GUI对话框模块综合测试

测试覆盖：
1. DataImportDialog - 数据导入对话框
2. SettingsDialog - 系统设置对话框  
3. ProgressDialog - 进度显示对话框
4. AboutDialog - 关于系统对话框

测试策略：模拟GUI组件，验证初始化、界面创建、信号处理等核心功能
"""

import pytest
import sys
import os
from unittest.mock import MagicMock, patch, mock_open
from PyQt5.QtWidgets import QApplication, QDialog, QLineEdit, QComboBox, QSpinBox, QCheckBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from gui.dialogs import DataImportDialog, SettingsDialog, ProgressDialog, AboutDialog


@pytest.fixture(scope="module")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app
    # 不要在这里quit()，因为其他测试可能还需要


class TestDataImportDialog:
    """数据导入对话框测试"""
    
    def test_data_import_dialog_initialization(self, qapp):
        """测试数据导入对话框初始化"""
        dialog = DataImportDialog()
        
        # 验证基本属性
        assert dialog.windowTitle() == "数据导入"
        assert dialog.minimumSize().width() == 600
        assert dialog.minimumSize().height() == 500
        assert hasattr(dialog, 'imported_data')
        assert dialog.imported_data == {}
        
        # 验证信号
        assert hasattr(dialog, 'data_imported')
        
        dialog.close()
    
    def test_import_tabs_creation(self, qapp):
        """测试导入选项卡创建"""
        dialog = DataImportDialog()
        
        # 验证选项卡存在
        assert hasattr(dialog, 'tab_widget')
        assert dialog.tab_widget.count() == 3
        
        # 验证选项卡标题
        tab_titles = []
        for i in range(dialog.tab_widget.count()):
            tab_titles.append(dialog.tab_widget.tabText(i))
        
        assert "Excel导入" in tab_titles
        assert "字段映射" in tab_titles
        assert "数据预览" in tab_titles
        
        dialog.close()
    
    def test_excel_import_tab_components(self, qapp):
        """测试Excel导入选项卡组件"""
        dialog = DataImportDialog()
        
        # 验证文件选择组件
        assert hasattr(dialog, 'salary_file_edit')
        assert hasattr(dialog, 'baseline_file_edit')
        assert isinstance(dialog.salary_file_edit, QLineEdit)
        assert isinstance(dialog.baseline_file_edit, QLineEdit)
        assert dialog.salary_file_edit.isReadOnly()
        assert dialog.baseline_file_edit.isReadOnly()
        
        # 验证导入选项组件
        assert hasattr(dialog, 'sheet_combo')
        assert hasattr(dialog, 'start_row_spin')
        assert hasattr(dialog, 'header_check')
        assert hasattr(dialog, 'skip_empty_check')
        
        assert isinstance(dialog.sheet_combo, QComboBox)
        assert isinstance(dialog.start_row_spin, QSpinBox)
        assert isinstance(dialog.header_check, QCheckBox)
        assert isinstance(dialog.skip_empty_check, QCheckBox)
        
        # 验证默认值
        assert dialog.start_row_spin.value() == 2
        assert dialog.header_check.isChecked() == True
        assert dialog.skip_empty_check.isChecked() == True
        
        dialog.close()
    
    def test_field_mapping_tab_components(self, qapp):
        """测试字段映射选项卡组件"""
        dialog = DataImportDialog()
        
        # 验证映射表格
        assert hasattr(dialog, 'mapping_table')
        assert dialog.mapping_table.columnCount() == 3
        
        # 验证表头
        headers = []
        for i in range(dialog.mapping_table.columnCount()):
            headers.append(dialog.mapping_table.horizontalHeaderItem(i).text())
        
        assert "系统字段" in headers
        assert "Excel列" in headers
        assert "数据预览" in headers
        
        dialog.close()
    
    def test_data_preview_tab_components(self, qapp):
        """测试数据预览选项卡组件"""
        dialog = DataImportDialog()
        
        # 验证预览组件
        assert hasattr(dialog, 'preview_table')
        assert hasattr(dialog, 'stats_label')
        assert dialog.stats_label.text() == "数据统计: 总行数: 0, 有效行数: 0"
        
        dialog.close()
    
    @patch('gui.dialogs.QFileDialog.getOpenFileName')
    def test_browse_file_functionality(self, mock_file_dialog, qapp):
        """测试文件浏览功能"""
        mock_file_dialog.return_value = ("/test/path/file.xlsx", "Excel文件 (*.xlsx *.xls)")
        
        dialog = DataImportDialog()
        
        # 测试文件选择
        dialog._browse_file(dialog.salary_file_edit, "Excel文件 (*.xlsx *.xls)")
        
        assert dialog.salary_file_edit.text() == "/test/path/file.xlsx"
        
        dialog.close()
    
    @patch('gui.dialogs.setup_logger')
    def test_auto_map_fields(self, mock_logger, qapp):
        """测试自动字段映射"""
        dialog = DataImportDialog()
        
        # 调用自动映射功能
        dialog._auto_map_fields()
        
        # 验证方法被调用（通过没有异常验证基本功能）
        assert hasattr(dialog, 'mapping_table')
        
        dialog.close()
    
    @patch('gui.dialogs.setup_logger')
    def test_start_import(self, mock_logger, qapp):
        """测试开始导入功能"""
        dialog = DataImportDialog()
        
        # 设置测试数据
        dialog.salary_file_edit.setText("/test/salary.xlsx")
        dialog.baseline_file_edit.setText("/test/baseline.xlsx")
        
        # 调用导入功能
        dialog._start_import()
        
        # 验证方法执行（基本功能测试）
        assert dialog.salary_file_edit.text() == "/test/salary.xlsx"
        
        dialog.close()


class TestSettingsDialog:
    """系统设置对话框测试"""
    
    def test_settings_dialog_initialization(self, qapp):
        """测试设置对话框初始化"""
        dialog = SettingsDialog()
        
        # 验证基本属性
        assert dialog.windowTitle() == "系统设置"
        assert dialog.minimumSize().width() == 500
        assert dialog.minimumSize().height() == 400
        assert hasattr(dialog, 'settings_changed')
        
        dialog.close()
    
    def test_settings_tabs_creation(self, qapp):
        """测试设置选项卡创建"""
        dialog = SettingsDialog()
        
        # 验证选项卡存在
        assert hasattr(dialog, 'tab_widget')
        assert dialog.tab_widget.count() == 3
        
        # 验证选项卡标题
        tab_titles = []
        for i in range(dialog.tab_widget.count()):
            tab_titles.append(dialog.tab_widget.tabText(i))
        
        assert "常规" in tab_titles
        assert "数据" in tab_titles
        assert "界面" in tab_titles
        
        dialog.close()
    
    def test_general_tab_components(self, qapp):
        """测试常规设置选项卡组件"""
        dialog = SettingsDialog()
        
        # 验证常规设置组件（根据实际实现修正）
        assert hasattr(dialog, 'auto_save_check')
        assert hasattr(dialog, 'save_interval_spin')
        assert hasattr(dialog, 'backup_check')
        
        # 验证默认值
        assert dialog.save_interval_spin.value() == 5
        assert dialog.save_interval_spin.suffix() == " 分钟"
        
        dialog.close()
    
    def test_data_tab_components(self, qapp):
        """测试数据设置选项卡组件"""
        dialog = SettingsDialog()
        
        # 验证数据设置组件
        assert hasattr(dialog, 'encoding_combo')
        assert hasattr(dialog, 'error_handling_combo')
        
        assert isinstance(dialog.encoding_combo, QComboBox)
        assert isinstance(dialog.error_handling_combo, QComboBox)
        
        dialog.close()
    
    def test_ui_tab_components(self, qapp):
        """测试界面设置选项卡组件"""
        dialog = SettingsDialog()
        
        # 验证界面设置组件
        assert hasattr(dialog, 'theme_combo')
        assert hasattr(dialog, 'font_size_spin')
        assert hasattr(dialog, 'toolbar_check')
        assert hasattr(dialog, 'statusbar_check')
        
        # 验证默认值
        assert dialog.font_size_spin.value() == 10
        
        dialog.close()
    
    @patch('gui.dialogs.QFileDialog.getExistingDirectory')
    def test_browse_db_path(self, mock_file_dialog, qapp):
        """测试数据库路径浏览"""
        mock_file_dialog.return_value = "/test/db/path"
        
        dialog = SettingsDialog()
        
        # 测试路径选择
        dialog._browse_db_path()
        
        assert dialog.db_path_edit.text() == "/test/db/path"
        
        dialog.close()
    
    @patch('gui.dialogs.QMessageBox.information')
    def test_apply_settings(self, mock_message_box, qapp):
        """测试应用设置"""
        dialog = SettingsDialog()
        
        # 测试应用设置
        dialog._apply_settings()
        
        # 验证设置已标记为改变
        assert dialog.settings_changed == True
        
        # 验证消息框被调用
        mock_message_box.assert_called_once()
        
        dialog.close()


class TestProgressDialog:
    """进度对话框测试"""
    
    def test_progress_dialog_initialization(self, qapp):
        """测试进度对话框初始化"""
        dialog = ProgressDialog("测试进度")
        
        # 验证基本属性
        assert dialog.windowTitle() == "测试进度"
        assert dialog.minimumSize().width() == 300
        assert dialog.minimumSize().height() == 120
        assert dialog.canceled == False
        
        # 验证组件
        assert hasattr(dialog, 'progress_bar')
        assert hasattr(dialog, 'status_label')
        assert hasattr(dialog, 'title_label')
        
        dialog.close()
    
    def test_progress_dialog_default_title(self, qapp):
        """测试进度对话框默认标题"""
        dialog = ProgressDialog()
        
        assert dialog.windowTitle() == "处理中..."
        assert dialog.title_label.text() == "处理中..."
        
        dialog.close()
    
    def test_update_progress(self, qapp):
        """测试进度更新"""
        dialog = ProgressDialog()
        
        # 测试进度更新
        dialog.update_progress(50, "正在处理...")
        
        assert dialog.progress_bar.value() == 50
        assert dialog.status_label.text() == "正在处理..."
        
        # 测试无状态文本的进度更新
        dialog.update_progress(75)
        
        assert dialog.progress_bar.value() == 75
        assert dialog.status_label.text() == "进度: 75%"
        
        dialog.close()
    
    def test_cancel_functionality(self, qapp):
        """测试取消功能"""
        dialog = ProgressDialog()
        
        # 初始状态
        assert dialog.wasCanceled() == False
        
        # 模拟取消操作
        dialog._cancel()
        
        # 验证取消状态
        assert dialog.wasCanceled() == True
        assert dialog.canceled == True


class TestAboutDialog:
    """关于对话框测试"""
    
    def test_about_dialog_initialization(self, qapp):
        """测试关于对话框初始化"""
        dialog = AboutDialog()
        
        # 验证基本属性
        assert dialog.windowTitle() == "关于系统"
        assert dialog.size().width() == 400
        assert dialog.size().height() == 300
        
        dialog.close()
    
    def test_about_dialog_content(self, qapp):
        """测试关于对话框内容"""
        dialog = AboutDialog()
        
        # 验证对话框内容通过查找子组件
        # 这里主要验证对话框能正常创建和显示
        assert dialog.isModal() == False  # 默认非模态
        
        dialog.close()


class TestDialogsIntegration:
    """对话框集成测试"""
    
    def test_all_dialogs_creation(self, qapp):
        """测试所有对话框都能正常创建"""
        dialogs = [
            DataImportDialog(),
            SettingsDialog(),
            ProgressDialog(),
            AboutDialog()
        ]
        
        # 验证所有对话框都能正常创建
        for dialog in dialogs:
            assert isinstance(dialog, QDialog)
            assert dialog.windowTitle() != ""
            dialog.close()
    
    def test_dialog_parent_child_relationship(self, qapp):
        """测试对话框父子关系"""
        parent_dialog = AboutDialog()
        child_dialog = ProgressDialog(parent=parent_dialog)
        
        # 验证父子关系
        assert child_dialog.parent() == parent_dialog
        
        parent_dialog.close()
        child_dialog.close()
    
    @patch('gui.dialogs.setup_logger')
    def test_dialogs_logging(self, mock_logger, qapp):
        """测试对话框日志记录"""
        dialog = DataImportDialog()
        
        # 验证日志设置被调用
        mock_logger.assert_called()
        
        dialog.close()


if __name__ == "__main__":
    # 创建QApplication用于测试
    import sys
    app = QApplication(sys.argv)
    
    # 运行测试
    pytest.main([__file__, "-v"]) 