"""
GUI主窗口模块综合测试

测试覆盖：
1. MainWindow - 主窗口类的初始化、界面创建、菜单管理等核心功能
2. 配置管理和模块初始化
3. 信号和槽机制
4. 用户偏好设置和主题应用

测试策略：模拟GUI组件和依赖模块，验证主窗口的核心功能
"""

import pytest
import sys
import os
from unittest.mock import MagicMock, patch, PropertyMock
from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget, QStatusBar, QMenuBar, QToolBar
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.fixture(scope="module")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app
    # 不要在这里quit()，因为其他测试可能还需要


@pytest.fixture
def mock_dependencies():
    """模拟主窗口的依赖项"""
    with patch.multiple(
        'gui.main_window',
        ConfigManager=MagicMock(),
        UserPreferences=MagicMock(),
        ExcelImporter=MagicMock(),
        DataValidator=MagicMock(),
        BaselineManager=MagicMock(),
        ChangeDetector=MagicMock(),
        ReportManager=MagicMock(),
        setup_logger=MagicMock(return_value=MagicMock())
    ) as mocks:
        yield mocks


class TestMainWindowInitialization:
    """主窗口初始化测试"""
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_main_window_initialization_success(self, mock_report_manager, mock_change_detector,
                                              mock_baseline_manager, mock_data_validator,
                                              mock_excel_importer, mock_user_preferences,
                                              mock_config_manager, mock_setup_logger, qapp):
        """测试主窗口成功初始化"""
        # 配置模拟对象
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        mock_config = MagicMock()
        mock_config_manager.return_value = mock_config
        
        mock_prefs = MagicMock()
        mock_user_preferences.return_value = mock_prefs
        
        # 导入并创建主窗口
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证基本属性
        assert isinstance(window, QMainWindow)
        assert window.windowTitle() == "月度工资异动处理系统 v1.0"
        assert window.minimumSize().width() == 1200
        assert window.minimumSize().height() == 800
        
        # 验证模块初始化
        mock_setup_logger.assert_called()
        mock_config_manager.assert_called_once()
        mock_user_preferences.assert_called_once()
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.QMessageBox.critical')
    @patch('sys.exit')
    def test_main_window_initialization_config_failure(self, mock_exit, mock_message_box,
                                                      mock_config_manager, mock_setup_logger, qapp):
        """测试配置管理器初始化失败"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        # 模拟配置管理器初始化失败
        mock_config_manager.side_effect = Exception("配置初始化失败")
        
        # 导入主窗口类
        from gui.main_window import MainWindow
        
        # 创建窗口应该触发错误处理
        MainWindow()
        
        # 验证错误处理
        mock_message_box.assert_called_once()
        mock_exit.assert_called_once_with(1)


class TestMainWindowUI:
    """主窗口UI组件测试"""
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_central_widget_creation(self, mock_report_manager, mock_change_detector,
                                   mock_baseline_manager, mock_data_validator,
                                   mock_excel_importer, mock_user_preferences,
                                   mock_config_manager, mock_setup_logger, qapp):
        """测试中央部件创建"""
        # 配置模拟对象
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证中央部件存在
        assert window.centralWidget() is not None
        
        # 验证选项卡存在
        assert hasattr(window, 'tab_widget')
        assert isinstance(window.tab_widget, QTabWidget)
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_menu_bar_creation(self, mock_report_manager, mock_change_detector,
                             mock_baseline_manager, mock_data_validator,
                             mock_excel_importer, mock_user_preferences,
                             mock_config_manager, mock_setup_logger, qapp):
        """测试菜单栏创建"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证菜单栏存在
        menu_bar = window.menuBar()
        assert isinstance(menu_bar, QMenuBar)
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_status_bar_creation(self, mock_report_manager, mock_change_detector,
                                mock_baseline_manager, mock_data_validator,
                                mock_excel_importer, mock_user_preferences,
                                mock_config_manager, mock_setup_logger, qapp):
        """测试状态栏创建"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证状态栏存在
        status_bar = window.statusBar()
        assert isinstance(status_bar, QStatusBar)
        
        window.close()


class TestMainWindowFunctionality:
    """主窗口功能测试"""
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_show_status_message(self, mock_report_manager, mock_change_detector,
                                mock_baseline_manager, mock_data_validator,
                                mock_excel_importer, mock_user_preferences,
                                mock_config_manager, mock_setup_logger, qapp):
        """测试状态消息显示"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 测试状态消息显示
        test_message = "测试状态消息"
        window.show_status_message(test_message)
        
        # 验证状态栏消息
        status_bar = window.statusBar()
        assert test_message in status_bar.currentMessage()
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_update_progress(self, mock_report_manager, mock_change_detector,
                           mock_baseline_manager, mock_data_validator,
                           mock_excel_importer, mock_user_preferences,
                           mock_config_manager, mock_setup_logger, qapp):
        """测试进度更新"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 测试进度更新
        window.update_progress(50)
        
        # 验证进度条存在（通过检查是否有进度条属性）
        assert hasattr(window, 'progress_bar') or window.statusBar() is not None
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    @patch('gui.main_window.DataImportDialog')
    def test_show_import_dialog(self, mock_import_dialog, mock_report_manager, mock_change_detector,
                              mock_baseline_manager, mock_data_validator,
                              mock_excel_importer, mock_user_preferences,
                              mock_config_manager, mock_setup_logger, qapp):
        """测试显示导入对话框"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        # 配置对话框模拟
        mock_dialog = MagicMock()
        mock_dialog.exec_.return_value = 1  # QDialog.Accepted
        mock_import_dialog.return_value = mock_dialog
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 测试显示导入对话框
        window.show_import_dialog()
        
        # 验证对话框被创建和显示
        mock_import_dialog.assert_called_once()
        mock_dialog.exec_.assert_called_once()
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    @patch('gui.main_window.AboutDialog')
    def test_show_about_dialog(self, mock_about_dialog, mock_report_manager, mock_change_detector,
                             mock_baseline_manager, mock_data_validator,
                             mock_excel_importer, mock_user_preferences,
                             mock_config_manager, mock_setup_logger, qapp):
        """测试显示关于对话框"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        # 配置对话框模拟
        mock_dialog = MagicMock()
        mock_about_dialog.return_value = mock_dialog
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 测试显示关于对话框
        window.show_about_dialog()
        
        # 验证对话框被创建和显示
        mock_about_dialog.assert_called_once()
        mock_dialog.exec_.assert_called_once()
        
        window.close()


class TestMainWindowSignals:
    """主窗口信号测试"""
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_signal_definitions(self, mock_report_manager, mock_change_detector,
                              mock_baseline_manager, mock_data_validator,
                              mock_excel_importer, mock_user_preferences,
                              mock_config_manager, mock_setup_logger, qapp):
        """测试信号定义"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证信号存在
        assert hasattr(window, 'status_message')
        assert hasattr(window, 'progress_update')
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_signal_emission(self, mock_report_manager, mock_change_detector,
                           mock_baseline_manager, mock_data_validator,
                           mock_excel_importer, mock_user_preferences,
                           mock_config_manager, mock_setup_logger, qapp):
        """测试信号发射"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 测试信号连接
        status_received = []
        progress_received = []
        
        window.status_message.connect(lambda msg: status_received.append(msg))
        window.progress_update.connect(lambda val: progress_received.append(val))
        
        # 发射信号
        window.status_message.emit("测试状态")
        window.progress_update.emit(75)
        
        # 验证信号接收
        assert "测试状态" in status_received
        assert 75 in progress_received
        
        window.close()


class TestMainWindowConfiguration:
    """主窗口配置测试"""
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_user_preferences_application(self, mock_report_manager, mock_change_detector,
                                        mock_baseline_manager, mock_data_validator,
                                        mock_excel_importer, mock_user_preferences,
                                        mock_config_manager, mock_setup_logger, qapp):
        """测试用户偏好设置应用"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        # 配置用户偏好
        mock_prefs = MagicMock()
        mock_prefs.get_preference.return_value = "深色"
        mock_user_preferences.return_value = mock_prefs
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证偏好设置被调用
        assert window.user_preferences is not None
        
        window.close()
    
    @patch('gui.main_window.setup_logger')
    @patch('gui.main_window.ConfigManager')
    @patch('gui.main_window.UserPreferences')
    @patch('gui.main_window.ExcelImporter')
    @patch('gui.main_window.DataValidator')
    @patch('gui.main_window.BaselineManager')
    @patch('gui.main_window.ChangeDetector')
    @patch('gui.main_window.ReportManager')
    def test_module_integration(self, mock_report_manager, mock_change_detector,
                              mock_baseline_manager, mock_data_validator,
                              mock_excel_importer, mock_user_preferences,
                              mock_config_manager, mock_setup_logger, qapp):
        """测试模块集成"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.main_window import MainWindow
        window = MainWindow()
        
        # 验证所有模块都已初始化
        assert hasattr(window, 'config_manager')
        assert hasattr(window, 'user_preferences')
        assert hasattr(window, 'excel_importer')
        assert hasattr(window, 'data_validator')
        assert hasattr(window, 'baseline_manager')
        assert hasattr(window, 'change_detector')
        assert hasattr(window, 'report_manager')
        
        window.close()


if __name__ == "__main__":
    # 创建QApplication用于测试
    import sys
    app = QApplication(sys.argv)
    
    # 运行测试
    pytest.main([__file__, "-v"]) 