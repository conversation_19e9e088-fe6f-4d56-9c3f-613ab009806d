"""
GUI组件模块综合测试

测试覆盖：
1. DataTableWidget - 数据表格组件
2. ChangeDetectionWidget - 异动检测组件
3. ReportPreviewWidget - 报告预览组件
4. 各种自定义控件和辅助组件
5. 组件的交互功能和信号处理

测试策略：模拟GUI组件操作，验证组件的功能和响应
"""

import pytest
import sys
import os
from unittest.mock import MagicMock, patch, PropertyMock
from PyQt5.QtWidgets import QApplication, QTableWidget, QWidget, QHeaderView
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.fixture(scope="module")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app
    # 不要在这里quit()，因为其他测试可能还需要


class TestDataTableWidget:
    """数据表格组件测试"""
    
    def test_data_table_widget_initialization(self, qapp):
        """测试数据表格组件初始化"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 验证基本属性
        assert isinstance(widget, QTableWidget)
        assert widget.rowCount() == 0
        assert widget.columnCount() == 0
        
        widget.close()
    
    def test_data_table_widget_with_headers(self, qapp):
        """测试带表头的数据表格组件"""
        from gui.widgets import DataTableWidget
        
        headers = ["列1", "列2", "列3"]
        widget = DataTableWidget(headers=headers)
        
        # 验证表头设置
        assert widget.columnCount() == len(headers)
        for i, header in enumerate(headers):
            assert widget.horizontalHeaderItem(i).text() == header
        
        widget.close()
    
    def test_data_table_widget_data_loading(self, qapp):
        """测试数据表格数据加载"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试数据加载 (使用实际方法set_data)
        test_data = [
            {"列1": "值1", "列2": "值2", "列3": "值3"},
            {"列1": "值4", "列2": "值5", "列3": "值6"}
        ]
        
        widget.set_data(test_data)
        
        # 验证数据加载
        assert widget.rowCount() == len(test_data)
        assert widget.columnCount() == len(test_data[0])
        
        widget.close()
    
    def test_data_table_widget_search_functionality(self, qapp):
        """测试数据表格搜索功能"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 加载测试数据
        test_data = [
            {"姓名": "张三", "职位": "工程师", "薪资": "10000"},
            {"姓名": "李四", "职位": "设计师", "薪资": "8000"},
            {"姓名": "王五", "职位": "经理", "薪资": "15000"}
        ]
        widget.set_data(test_data)
        
        # 测试筛选功能 (使用实际方法filter_data)
        widget.filter_data("姓名", "张三")
        # 验证筛选结果（基本功能测试）
        assert widget.rowCount() >= 0
        
        widget.close()
    
    def test_data_table_widget_sorting(self, qapp):
        """测试数据表格排序功能"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试排序功能
        test_data = [
            {"列1": "C", "列2": "3"},
            {"列1": "A", "列2": "1"},
            {"列1": "B", "列2": "2"}
        ]
        widget.set_data(test_data)
        
        # 启用排序
        widget.setSortingEnabled(True)
        
        # 验证排序功能可用
        assert widget.isSortingEnabled() == True
        
        widget.close()
    
    def test_data_table_widget_selection(self, qapp):
        """测试数据表格选择功能"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试数据
        test_data = [{"列1": "值1", "列2": "值2"}, {"列1": "值3", "列2": "值4"}]
        widget.set_data(test_data)
        
        # 测试选择功能
        widget.selectRow(0)
        selected_rows = widget.selectionModel().selectedRows()
        assert len(selected_rows) > 0
        
        # 测试获取选中数据 (使用实际方法get_selected_data)
        selected_data = widget.get_selected_data()
        assert selected_data is not None
        
        widget.close()
    
    def test_data_table_widget_context_menu(self, qapp):
        """测试数据表格右键菜单"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 加载测试数据
        test_data = [{"列1": "值1", "列2": "值2"}]
        widget.set_data(test_data)
        
        # 测试右键菜单设置
        assert widget.contextMenuPolicy() == Qt.CustomContextMenu
        
        widget.close()
    
    def test_data_table_widget_empty_data(self, qapp):
        """测试数据表格空数据处理"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试空数据
        widget.set_data([])
        assert widget.rowCount() == 0
        
        # 测试None数据
        widget.set_data(None if hasattr(widget, 'set_data') else [])
        
        widget.close()
    
    def test_data_table_widget_refresh(self, qapp):
        """测试数据表格刷新功能"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 加载初始数据
        test_data = [{"列1": "值1", "列2": "值2"}]
        widget.set_data(test_data)
        
        # 测试刷新功能
        if hasattr(widget, 'refresh_data'):
            widget.refresh_data()
        
        widget.close()


class TestChangeDetectionWidget:
    """异动检测组件测试"""
    
    def test_change_detection_widget_initialization(self, qapp):
        """测试异动检测组件初始化"""
        from gui.widgets import ChangeDetectionWidget
        
        widget = ChangeDetectionWidget()
        
        # 验证基本属性
        assert isinstance(widget, QWidget)
        assert widget is not None
        
        widget.close()
    
    def test_change_detection_widget_parameters(self, qapp):
        """测试异动检测参数设置"""
        from gui.widgets import ChangeDetectionWidget
        
        widget = ChangeDetectionWidget()
        
        # 测试参数获取
        if hasattr(widget, 'get_detection_parameters'):
            params = widget.get_detection_parameters()
            assert isinstance(params, dict)
        
        widget.close()
    
    def test_change_detection_widget_start_detection(self, qapp):
        """测试启动异动检测"""
        from gui.widgets import ChangeDetectionWidget
        
        widget = ChangeDetectionWidget()
        
        # 测试启动检测
        if hasattr(widget, 'start_detection'):
            widget.start_detection()
        
        widget.close()
    
    def test_change_detection_widget_results(self, qapp):
        """测试异动检测结果处理"""
        from gui.widgets import ChangeDetectionWidget
        
        widget = ChangeDetectionWidget()
        
        # 测试结果设置
        test_results = [
            {"员工": "张三", "异动类型": "薪资调整", "金额": 1000},
            {"员工": "李四", "异动类型": "职位变动", "金额": 0}
        ]
        
        if hasattr(widget, 'set_detection_results'):
            widget.set_detection_results(test_results)
        
        widget.close()
    
    def test_change_detection_widget_reset(self, qapp):
        """测试异动检测重置"""
        from gui.widgets import ChangeDetectionWidget
        
        widget = ChangeDetectionWidget()
        
        # 测试重置功能
        if hasattr(widget, 'reset_parameters'):
            widget.reset_parameters()
        
        widget.close()
    
    def test_change_detection_widget_signals(self, qapp):
        """测试异动检测组件信号"""
        from gui.widgets import ChangeDetectionWidget
        
        widget = ChangeDetectionWidget()
        
        # 测试信号连接
        signal_received = []
        
        if hasattr(widget, 'detection_started'):
            widget.detection_started.connect(lambda params: signal_received.append("started"))
        
        if hasattr(widget, 'detection_completed'):
            widget.detection_completed.connect(lambda results: signal_received.append("completed"))
        
        widget.close()


class TestReportPreviewWidget:
    """报告预览组件测试"""
    
    def test_report_preview_widget_initialization(self, qapp):
        """测试报告预览组件初始化"""
        from gui.widgets import ReportPreviewWidget
        
        widget = ReportPreviewWidget()
        
        # 验证基本属性
        assert isinstance(widget, QWidget)
        assert widget is not None
        
        widget.close()
    
    def test_report_preview_widget_content(self, qapp):
        """测试报告预览内容设置"""
        from gui.widgets import ReportPreviewWidget
        
        widget = ReportPreviewWidget()
        
        # 测试内容设置
        test_content = "<h1>测试报告</h1><p>这是测试内容</p>"
        
        if hasattr(widget, 'set_content'):
            widget.set_content(test_content)
        
        # 测试内容获取
        if hasattr(widget, 'get_content'):
            content = widget.get_content()
            assert isinstance(content, str)
        
        widget.close()
    
    def test_report_preview_widget_modes(self, qapp):
        """测试报告预览模式切换"""
        from gui.widgets import ReportPreviewWidget
        
        widget = ReportPreviewWidget()
        
        # 测试HTML预览模式
        if hasattr(widget, 'set_content'):
            widget.set_content("<h1>HTML测试</h1>", "HTML预览")
        
        widget.close()
    
    def test_report_preview_widget_refresh(self, qapp):
        """测试报告预览刷新"""
        from gui.widgets import ReportPreviewWidget
        
        widget = ReportPreviewWidget()
        
        # 测试刷新功能
        if hasattr(widget, 'refresh_preview'):
            widget.refresh_preview()
        
        widget.close()
    
    def test_report_preview_widget_clear(self, qapp):
        """测试报告预览清空"""
        from gui.widgets import ReportPreviewWidget
        
        widget = ReportPreviewWidget()
        
        # 设置内容然后清空
        if hasattr(widget, 'set_content'):
            widget.set_content("测试内容")
        
        if hasattr(widget, 'clear_content'):
            widget.clear_content()
        
        widget.close()
    
    def test_report_preview_widget_signals(self, qapp):
        """测试报告预览组件信号"""
        from gui.widgets import ReportPreviewWidget
        
        widget = ReportPreviewWidget()
        
        # 测试信号连接
        signal_received = []
        
        if hasattr(widget, 'content_changed'):
            widget.content_changed.connect(lambda content: signal_received.append("content_changed"))
        
        widget.close()


class TestCustomWidgets:
    """自定义组件测试"""
    
    def test_custom_widgets_availability(self, qapp):
        """测试自定义组件可用性"""
        from gui import widgets
        
        # 验证主要组件类存在
        assert hasattr(widgets, 'DataTableWidget')
        assert hasattr(widgets, 'ChangeDetectionWidget')
        assert hasattr(widgets, 'ReportPreviewWidget')
        
        # 测试基本实例化
        widget = widgets.DataTableWidget()
        assert isinstance(widget, QTableWidget)
        widget.close()
    
    @patch('gui.widgets.setup_logger')
    def test_widget_logging(self, mock_logger, qapp):
        """测试组件日志功能"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 验证日志设置（如果组件使用日志）
        if hasattr(widget, 'logger'):
            assert widget.logger is not None
        
        widget.close()
    
    def test_widget_styling(self, qapp):
        """测试组件样式设置"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试样式应用
        if hasattr(widget, '_apply_style'):
            widget._apply_style()
        
        # 验证样式设置不会导致错误
        assert widget.isVisible() or not widget.isVisible()  # 基本验证
        
        widget.close()


class TestWidgetInteractions:
    """组件交互测试"""
    
    def test_widget_signals(self, qapp):
        """测试组件信号"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试信号连接
        signal_received = []
        
        if hasattr(widget, 'data_changed'):
            widget.data_changed.connect(lambda: signal_received.append("data_changed"))
        
        # 添加测试数据触发信号
        test_data = [{"列1": "测试1", "列2": "测试2"}]
        widget.set_data(test_data)
        
        widget.close()
    
    def test_widget_context_menu(self, qapp):
        """测试组件右键菜单"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试右键菜单
        if hasattr(widget, 'contextMenuEvent'):
            # 模拟右键菜单事件
            pass
        
        widget.close()
    
    def test_widget_keyboard_shortcuts(self, qapp):
        """测试组件键盘快捷键"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 添加测试数据
        test_data = [{"列1": "行1列1", "列2": "行1列2"}, {"列1": "行2列1", "列2": "行2列2"}]
        widget.set_data(test_data)
        
        # 测试键盘事件处理
        if hasattr(widget, 'keyPressEvent'):
            # 基本键盘事件处理验证
            pass
        
        widget.close()


class TestWidgetDataOperations:
    """组件数据操作测试"""
    
    def test_data_export(self, qapp):
        """测试数据导出"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 加载测试数据
        test_data = [
            {"姓名": "张三", "职位": "工程师", "薪资": "10000"},
            {"姓名": "李四", "职位": "设计师", "薪资": "8000"}
        ]
        widget.set_data(test_data)
        
        # 测试数据导出功能 (验证基本功能)
        assert widget.rowCount() == len(test_data)
        
        widget.close()
    
    def test_data_filtering(self, qapp):
        """测试数据过滤"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 加载测试数据
        test_data = [
            {"职位": "工程师", "薪资": "10000"},
            {"职位": "设计师", "薪资": "8000"},
            {"职位": "经理", "薪资": "15000"}
        ]
        widget.set_data(test_data)
        
        # 测试过滤功能
        widget.filter_data("职位", "工程师")
        
        widget.close()
    
    def test_data_validation(self, qapp):
        """测试数据验证"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试有效数据
        valid_data = [{"列1": "有效1", "列2": "有效2"}, {"列1": "有效3", "列2": "有效4"}]
        widget.set_data(valid_data)
        
        # 验证数据设置成功
        assert widget.rowCount() == len(valid_data)
        
        widget.close()


class TestWidgetPerformance:
    """组件性能测试"""
    
    def test_large_data_loading(self, qapp):
        """测试大数据加载性能"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 创建大数据集
        large_data = []
        for i in range(1000):
            large_data.append({"列1": f"数据{i}", "列2": f"值{i}", "列3": f"内容{i}"})
        
        # 测试大数据加载
        import time
        start_time = time.time()
        widget.set_data(large_data)
        load_time = time.time() - start_time
        
        # 验证加载完成且性能可接受（5秒以内）
        assert widget.rowCount() == len(large_data)
        assert load_time < 5.0
        
        widget.close()
    
    def test_widget_memory_usage(self, qapp):
        """测试组件内存使用"""
        from gui.widgets import DataTableWidget
        
        import gc
        
        # 创建多个组件实例
        widgets = []
        for i in range(10):
            widget = DataTableWidget()
            test_data = [{"列{}".format(j): f"数据{j}" for j in range(10)} for _ in range(100)]
            widget.set_data(test_data)
            widgets.append(widget)
        
        # 清理组件
        for widget in widgets:
            widget.close()
        
        # 强制垃圾回收
        gc.collect()
        
        # 基本内存管理验证
        assert len(widgets) == 10


class TestWidgetIntegration:
    """组件集成测试"""
    
    def test_multiple_widgets_interaction(self, qapp):
        """测试多个组件交互"""
        from gui.widgets import DataTableWidget
        
        # 创建多个组件
        widget1 = DataTableWidget()
        widget2 = DataTableWidget()
        
        # 在组件间共享数据
        test_data = [{"列1": "共享1", "列2": "共享2"}, {"列1": "共享3", "列2": "共享4"}]
        widget1.set_data(test_data)
        
        # 基本功能验证
        assert widget1.rowCount() == len(test_data)
        
        widget1.close()
        widget2.close()
    
    def test_widget_state_persistence(self, qapp):
        """测试组件状态持久化"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 设置组件状态
        test_data = [{"列1": "状态1", "列2": "状态2"}]
        widget.set_data(test_data)
        
        # 验证状态设置
        assert widget.rowCount() == len(test_data)
        
        widget.close()
    
    def test_widget_theme_adaptation(self, qapp):
        """测试组件主题适应"""
        from gui.widgets import DataTableWidget
        
        widget = DataTableWidget()
        
        # 测试主题切换
        if hasattr(widget, 'apply_theme'):
            widget.apply_theme("dark")
            widget.apply_theme("light")
        
        # 验证主题应用不会导致错误
        assert widget is not None
        
        widget.close()


if __name__ == "__main__":
    # 创建QApplication用于测试
    import sys
    app = QApplication(sys.argv)
    
    # 运行测试
    pytest.main([__file__, "-v"]) 