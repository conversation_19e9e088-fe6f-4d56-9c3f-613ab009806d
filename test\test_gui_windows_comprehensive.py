"""
GUI窗口模块综合测试

测试覆盖：
1. ChangeDetectionWindow - 异动检测窗口
2. ReportGenerationWindow - 报告生成窗口
3. 窗口的初始化、UI组件、功能操作
4. 窗口间交互和数据传递

测试策略：使用Mock模拟依赖，专注测试GUI窗口逻辑
"""

import pytest
import sys
import os
from unittest.mock import MagicMock, patch, PropertyMock
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


@pytest.fixture(scope="module")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app


class TestChangeDetectionWindow:
    """异动检测窗口测试"""
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_initialization(self, mock_change_detector, mock_baseline_manager,
                                                  mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口初始化"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 验证窗口基本属性
        assert isinstance(window, QMainWindow)
        assert window.windowTitle() != ""
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_ui_components(self, mock_change_detector, mock_baseline_manager,
                                                 mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口UI组件"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 验证主要UI组件存在
        assert window.centralWidget() is not None
        assert window.menuBar() is not None
        assert window.statusBar() is not None
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_data_import(self, mock_change_detector, mock_baseline_manager,
                                                mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口数据导入功能"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试数据导入方法
        if hasattr(window, 'refresh_data'):
            window.refresh_data()
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_detection_process(self, mock_change_detector, mock_baseline_manager,
                                                     mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口检测过程"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        # Mock检测器返回检测结果
        mock_change_detector.return_value.detect_changes.return_value = [
            {"工号": "001", "异动类型": "薪资调整", "原值": 5000, "新值": 5500, "差额": 500}
        ]
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试异动检测方法
        if hasattr(window, 'start_detection'):
            window.start_detection()
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_results_display(self, mock_change_detector, mock_baseline_manager,
                                                   mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口结果显示"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试结果显示方法
        test_results = [
            {"工号": "001", "异动类型": "薪资调整", "金额": 500},
            {"工号": "002", "异动类型": "职位变动", "金额": 0}
        ]
        
        if hasattr(window, '_populate_results'):
            window._populate_results(test_results)
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_export_functionality(self, mock_change_detector, mock_baseline_manager,
                                                         mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口导出功能"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 设置一些测试结果
        window.detection_results = {"changes": [{"工号": "001", "异动类型": "薪资调整"}]}
        
        # 测试导出功能
        if hasattr(window, 'export_results'):
            # 模拟用户选择文件对话框
            with patch('PyQt5.QtWidgets.QFileDialog.getSaveFileName') as mock_dialog:
                mock_dialog.return_value = ("test_export.xlsx", "Excel Files (*.xlsx)")
                window.export_results()
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_change_detection_window_settings(self, mock_change_detector, mock_baseline_manager,
                                            mock_config_manager, mock_setup_logger, qapp):
        """测试异动检测窗口设置功能"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试设置相关功能
        if hasattr(window, 'detection_type_combo'):
            window.detection_type_combo.setCurrentText("工资增加")
            assert window.detection_type_combo.currentText() == "工资增加"
        
        if hasattr(window, 'threshold_spin'):
            window.threshold_spin.setValue(500)
            assert window.threshold_spin.value() == 500
        
        window.close()


class TestReportGenerationWindow:
    """报告生成窗口测试"""
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_initialization(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口初始化"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 验证窗口基本属性
        assert isinstance(window, QMainWindow)
        assert window.windowTitle() != ""
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_ui_components(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口UI组件"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 验证主要UI组件存在
        assert window.centralWidget() is not None
        assert window.menuBar() is not None
        assert window.statusBar() is not None
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_template_selection(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口模板选择"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 测试模板选择功能
        if hasattr(window, 'template_combo'):
            available_templates = window.template_combo.count()
            assert available_templates >= 0
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_data_loading(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口数据加载"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 测试数据源选择
        if hasattr(window, 'data_source_combo'):
            window.data_source_combo.addItem("测试数据源")
            assert window.data_source_combo.count() > 0
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_report_preview(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口报告预览"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 测试预览功能
        if hasattr(window, 'preview_report'):
            window.preview_report()
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_report_generation(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口报告生成"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 测试报告生成功能
        if hasattr(window, 'generate_report'):
            with patch('PyQt5.QtWidgets.QFileDialog.getSaveFileName') as mock_dialog:
                mock_dialog.return_value = ("test_report.pdf", "PDF Files (*.pdf)")
                window.generate_report()
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.modules.report_generation.ReportManager')
    def test_report_generation_window_format_options(self, mock_report_manager, mock_setup_logger, qapp):
        """测试报告生成窗口格式选项"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ReportGenerationWindow
        
        window = ReportGenerationWindow()
        
        # 测试格式选项
        if hasattr(window, 'format_combo'):
            window.format_combo.addItem("PDF")
            window.format_combo.addItem("Excel")
            assert window.format_combo.count() >= 2
        
        window.close()


class TestWindowInteractions:
    """窗口交互测试"""
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    @patch('src.modules.report_generation.ReportManager')
    def test_window_communication(self, mock_report_manager, mock_change_detector, mock_baseline_manager,
                                mock_config_manager, mock_setup_logger, qapp):
        """测试窗口间通信"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow, ReportGenerationWindow
        
        detection_window = ChangeDetectionWindow()
        report_window = ReportGenerationWindow()
        
        # 测试信号连接
        if hasattr(detection_window, 'detection_completed') and hasattr(report_window, 'report_generated'):
            # 验证信号存在
            assert detection_window.detection_completed is not None
            assert report_window.report_generated is not None
        
        detection_window.close()
        report_window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_window_state_management(self, mock_change_detector, mock_baseline_manager,
                                   mock_config_manager, mock_setup_logger, qapp):
        """测试窗口状态管理"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试窗口状态保存和恢复
        original_size = window.size()
        window.resize(800, 600)
        new_size = window.size()
        
        assert new_size != original_size
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_window_error_handling(self, mock_change_detector, mock_baseline_manager,
                                 mock_config_manager, mock_setup_logger, qapp):
        """测试窗口错误处理"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        # 模拟依赖注入失败
        mock_change_detector.side_effect = Exception("模拟错误")
        
        from gui.windows import ChangeDetectionWindow
        
        try:
            window = ChangeDetectionWindow()
            window.close()
        except Exception:
            # 预期会有异常，但应该能优雅处理
            pass


class TestWindowConfiguration:
    """窗口配置测试"""
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_window_settings_persistence(self, mock_change_detector, mock_baseline_manager,
                                        mock_config_manager, mock_setup_logger, qapp):
        """测试窗口设置持久化"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试设置保存
        if hasattr(window, 'threshold_spin'):
            test_value = 1000
            window.threshold_spin.setValue(test_value)
            assert window.threshold_spin.value() == test_value
        
        window.close()
    
    @patch('src.utils.log_config.setup_logger')
    @patch('src.core.config_manager.ConfigManager')
    @patch('src.modules.change_detection.BaselineManager')
    @patch('src.modules.change_detection.ChangeDetector')
    def test_window_theme_support(self, mock_change_detector, mock_baseline_manager,
                                mock_config_manager, mock_setup_logger, qapp):
        """测试窗口主题支持"""
        mock_logger = MagicMock()
        mock_setup_logger.return_value = mock_logger
        
        from gui.windows import ChangeDetectionWindow
        
        window = ChangeDetectionWindow()
        
        # 测试样式应用
        original_style = window.styleSheet()
        test_style = "QMainWindow { background-color: #f0f0f0; }"
        window.setStyleSheet(test_style)
        
        assert window.styleSheet() == test_style
        
        window.close()


if __name__ == "__main__":
    # 创建QApplication用于测试
    import sys
    app = QApplication(sys.argv)
    
    # 运行测试
    pytest.main([__file__, "-v"]) 