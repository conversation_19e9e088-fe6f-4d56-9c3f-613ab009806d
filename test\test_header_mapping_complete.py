#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工资表表头映射与自定义显示功能完整测试脚本

测试内容：
1. 自动字段映射生成器功能
2. 数据导入时字段映射保存
3. 表格组件字段映射应用
4. 用户表头偏好管理
5. 按需加载字段功能
6. 表头自定义对话框
7. 主界面集成功能

创建时间: 2025-06-25
"""

import sys
import os
import tempfile
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
import json

# 添加src目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))
sys.path.insert(0, str(project_root))

from src.utils.log_config import setup_logger
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator


class HeaderMappingTester:
    """表头映射功能测试器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.test_results = {}
        
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        # 初始化组件
        self.table_manager = DynamicTableManager(self.temp_db.name)
        self.config_sync = ConfigSyncManager()
        self.auto_mapping_generator = AutoFieldMappingGenerator()
        self.multi_sheet_importer = MultiSheetImporter(self.table_manager)
        
        self.logger.info("表头映射功能测试器初始化完成")
    
    def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("开始运行表头映射功能完整测试")
        
        try:
            # 1. 测试自动字段映射生成器
            self.test_auto_field_mapping_generator()
            
            # 2. 测试配置同步管理器
            self.test_config_sync_manager()
            
            # 3. 测试用户表头偏好管理
            self.test_user_header_preferences()
            
            # 4. 测试数据导入时字段映射
            self.test_import_with_field_mapping()
            
            # 5. 测试按需加载字段功能
            self.test_selective_field_loading()
            
            # 6. 生成测试报告
            self.generate_test_report()
            
        except Exception as e:
            self.logger.error(f"测试运行失败: {e}")
            self.test_results['overall'] = {'status': 'FAILED', 'error': str(e)}
        
        finally:
            self.cleanup()
    
    def test_auto_field_mapping_generator(self):
        """测试自动字段映射生成器"""
        self.logger.info("测试自动字段映射生成器...")
        
        try:
            # 测试数据
            excel_columns = ['工号', '姓名', '部门名称', '2025年岗位工资', '2025年薪级工资', 
                           '津贴', '2025年奖励性绩效预发', '应发工资', '2025公积金']
            table_name = "salary_data_2025_05_test"
            
            # 生成映射
            mapping = self.auto_mapping_generator.generate_mapping(excel_columns, table_name)
            
            # 验证映射结果
            assert len(mapping) == len(excel_columns), "映射数量不匹配"
            assert '工号' in mapping, "工号字段映射缺失"
            assert '姓名' in mapping, "姓名字段映射缺失"
            
            # 评估映射质量
            quality = self.auto_mapping_generator.assess_mapping_quality(mapping)
            assert quality['coverage_rate'] > 0.5, "映射覆盖率过低"
            
            self.test_results['auto_mapping'] = {
                'status': 'PASSED',
                'mapping_count': len(mapping),
                'coverage_rate': quality['coverage_rate'],
                'sample_mapping': dict(list(mapping.items())[:3])
            }
            
            self.logger.info("✅ 自动字段映射生成器测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 自动字段映射生成器测试失败: {e}")
            self.test_results['auto_mapping'] = {'status': 'FAILED', 'error': str(e)}
    
    def test_config_sync_manager(self):
        """测试配置同步管理器"""
        self.logger.info("测试配置同步管理器...")
        
        try:
            table_name = "test_table"
            test_mapping = {
                '工号': '工号',
                '姓名': '姓名',
                '部门': '部门名称',
                'basic_salary': '基本工资'
            }
            
            # 测试保存映射
            success = self.config_sync.save_mapping(table_name, test_mapping)
            assert success, "保存映射失败"
            
            # 测试加载映射
            loaded_mapping = self.config_sync.load_mapping(table_name)
            assert loaded_mapping == test_mapping, "加载的映射与保存的不一致"
            
            # 测试更新单个字段映射
            success = self.config_sync.update_mapping(table_name, 'new_field', '新字段')
            assert success, "更新单个字段映射失败"
            
            # 验证更新结果
            updated_mapping = self.config_sync.load_mapping(table_name)
            assert 'new_field' in updated_mapping, "新字段映射未保存"
            assert updated_mapping['new_field'] == '新字段', "新字段映射值不正确"
            
            self.test_results['config_sync'] = {
                'status': 'PASSED',
                'save_success': True,
                'load_success': True,
                'update_success': True
            }
            
            self.logger.info("✅ 配置同步管理器测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 配置同步管理器测试失败: {e}")
            self.test_results['config_sync'] = {'status': 'FAILED', 'error': str(e)}
    
    def test_user_header_preferences(self):
        """测试用户表头偏好管理"""
        self.logger.info("测试用户表头偏好管理...")
        
        try:
            table_name = "test_preference_table"
            selected_fields = ['工号', '姓名', '基本工资', '应发合计']
            
            # 测试保存用户偏好
            success = self.config_sync.save_user_header_preference(table_name, selected_fields)
            assert success, "保存用户表头偏好失败"
            
            # 测试获取用户偏好
            loaded_fields = self.config_sync.get_user_header_preference(table_name)
            assert loaded_fields == selected_fields, "加载的用户偏好与保存的不一致"
            
            # 测试获取所有用户偏好
            all_preferences = self.config_sync.get_all_user_header_preferences()
            assert table_name in all_preferences, "用户偏好未包含在全部偏好中"
            
            # 测试删除用户偏好
            success = self.config_sync.remove_user_header_preference(table_name)
            assert success, "删除用户表头偏好失败"
            
            # 验证删除结果
            deleted_fields = self.config_sync.get_user_header_preference(table_name)
            assert deleted_fields is None, "用户偏好未被正确删除"
            
            self.test_results['user_preferences'] = {
                'status': 'PASSED',
                'save_success': True,
                'load_success': True,
                'delete_success': True,
                'selected_fields_count': len(selected_fields)
            }
            
            self.logger.info("✅ 用户表头偏好管理测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 用户表头偏好管理测试失败: {e}")
            self.test_results['user_preferences'] = {'status': 'FAILED', 'error': str(e)}
    
    def test_import_with_field_mapping(self):
        """测试数据导入时字段映射"""
        self.logger.info("测试数据导入时字段映射...")
        
        try:
            # 创建测试Excel数据
            test_data = {
                '工号': ['001', '002', '003'],
                '姓名': ['张三', '李四', '王五'],
                '部门名称': ['技术部', '财务部', '人事部'],
                '2025年岗位工资': [5000, 4500, 4000],
                '应发工资': [6000, 5500, 5000]
            }
            
            df = pd.DataFrame(test_data)
            
            # 创建临时Excel文件
            temp_excel = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
            temp_excel.close()
            
            with pd.ExcelWriter(temp_excel.name, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='全部在职人员', index=False)
            
            # 测试导入
            result = self.multi_sheet_importer.import_excel_file(
                temp_excel.name, year=2025, month=5
            )
            
            assert result['success'], f"导入失败: {result.get('error', '未知错误')}"
            
            # 验证字段映射是否保存
            table_name = "salary_data_2025_05_active_employees"
            mapping = self.config_sync.load_mapping(table_name)
            assert mapping is not None, "字段映射未保存"
            assert len(mapping) > 0, "字段映射为空"
            
            # 清理临时文件
            os.unlink(temp_excel.name)
            
            self.test_results['import_mapping'] = {
                'status': 'PASSED',
                'import_success': True,
                'mapping_saved': True,
                'mapping_count': len(mapping),
                'table_name': table_name
            }
            
            self.logger.info("✅ 数据导入时字段映射测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 数据导入时字段映射测试失败: {e}")
            self.test_results['import_mapping'] = {'status': 'FAILED', 'error': str(e)}
    
    def test_selective_field_loading(self):
        """测试按需加载字段功能"""
        self.logger.info("测试按需加载字段功能...")
        
        try:
            # 使用之前导入的表进行测试
            table_name = "salary_data_2025_05_active_employees"
            
            # 检查表是否存在
            if not self.table_manager.table_exists(table_name):
                self.logger.warning("测试表不存在，跳过按需加载测试")
                self.test_results['selective_loading'] = {'status': 'SKIPPED', 'reason': '测试表不存在'}
                return
            
            # 获取所有字段
            all_columns = self.table_manager.get_table_columns(table_name)
            all_field_names = [col['name'] for col in all_columns]
            
            # 测试加载所有字段
            df_all, total_count = self.table_manager.get_dataframe_with_selected_fields(table_name)
            assert df_all is not None, "加载所有字段失败"
            assert len(df_all.columns) == len(all_field_names), "加载的字段数量不正确"
            
            # 测试加载部分字段
            selected_fields = all_field_names[:3]  # 选择前3个字段
            df_partial, _ = self.table_manager.get_dataframe_with_selected_fields(
                table_name, selected_fields
            )
            assert df_partial is not None, "加载部分字段失败"
            assert len(df_partial.columns) == len(selected_fields), "加载的部分字段数量不正确"
            
            # 测试分页加载
            df_page, _ = self.table_manager.get_dataframe_with_selected_fields(
                table_name, selected_fields, page=1, page_size=2
            )
            assert df_page is not None, "分页加载失败"
            assert len(df_page) <= 2, "分页大小不正确"
            
            self.test_results['selective_loading'] = {
                'status': 'PASSED',
                'all_fields_count': len(all_field_names),
                'partial_fields_count': len(selected_fields),
                'pagination_success': True
            }
            
            self.logger.info("✅ 按需加载字段功能测试通过")
            
        except Exception as e:
            self.logger.error(f"❌ 按需加载字段功能测试失败: {e}")
            self.test_results['selective_loading'] = {'status': 'FAILED', 'error': str(e)}
    
    def generate_test_report(self):
        """生成测试报告"""
        self.logger.info("生成测试报告...")
        
        try:
            # 统计测试结果
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results.values() 
                             if result.get('status') == 'PASSED')
            failed_tests = sum(1 for result in self.test_results.values() 
                             if result.get('status') == 'FAILED')
            skipped_tests = sum(1 for result in self.test_results.values() 
                              if result.get('status') == 'SKIPPED')
            
            # 生成报告
            report = {
                'test_summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'skipped_tests': skipped_tests,
                    'success_rate': f"{(passed_tests / total_tests * 100):.1f}%" if total_tests > 0 else "0%"
                },
                'test_details': self.test_results,
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
            # 保存报告到文件
            report_file = project_root / "test" / "header_mapping_test_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            # 打印报告摘要
            self.logger.info("=" * 60)
            self.logger.info("表头映射功能测试报告")
            self.logger.info("=" * 60)
            self.logger.info(f"总测试数: {total_tests}")
            self.logger.info(f"通过测试: {passed_tests}")
            self.logger.info(f"失败测试: {failed_tests}")
            self.logger.info(f"跳过测试: {skipped_tests}")
            self.logger.info(f"成功率: {report['test_summary']['success_rate']}")
            self.logger.info("=" * 60)
            
            for test_name, result in self.test_results.items():
                status_icon = "✅" if result['status'] == 'PASSED' else "❌" if result['status'] == 'FAILED' else "⏭️"
                self.logger.info(f"{status_icon} {test_name}: {result['status']}")
                if result['status'] == 'FAILED':
                    self.logger.error(f"   错误: {result.get('error', '未知错误')}")
            
            self.logger.info("=" * 60)
            self.logger.info(f"详细报告已保存到: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成测试报告失败: {e}")
    
    def cleanup(self):
        """清理测试资源"""
        try:
            # 删除临时数据库
            if os.path.exists(self.temp_db.name):
                os.unlink(self.temp_db.name)
            
            self.logger.info("测试资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理测试资源失败: {e}")


def main():
    """主函数"""
    print("开始运行工资表表头映射与自定义显示功能完整测试...")
    
    tester = HeaderMappingTester()
    tester.run_all_tests()
    
    print("测试完成！")


if __name__ == "__main__":
    main()
