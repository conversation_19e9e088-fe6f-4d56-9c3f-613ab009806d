#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入默认设置功能测试

测试导入默认设置管理器和智能工作表匹配功能的正确性。

创建时间: 2025-01-20
"""

import unittest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_import.import_defaults_manager import ImportDefaultsManager
from src.modules.data_import.smart_sheet_matcher import SmartSheetMatcher, MatchResult


class TestImportDefaultsManager(unittest.TestCase):
    """测试导入默认设置管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = ImportDefaultsManager(config_dir=self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_load_default_settings(self):
        """测试加载默认设置"""
        settings = self.manager.load_settings()
        
        # 验证默认设置
        self.assertEqual(settings['start_row'], 1)
        self.assertEqual(settings['import_mode'], 'multi_sheet')
        self.assertEqual(settings['import_strategy'], 'separate_tables')  # 新增验证
        self.assertEqual(settings['table_template'], 'salary_data')  # 新增验证
        self.assertTrue(settings['auto_match_sheet'])
        self.assertTrue(settings['include_header'])
        self.assertTrue(settings['skip_empty_rows'])
    
    def test_save_and_load_custom_settings(self):
        """测试保存和加载自定义设置"""
        custom_settings = {
            'start_row': 2,
            'import_mode': 'single_sheet',
            'auto_match_sheet': False
        }
        
        # 保存设置
        success = self.manager.save_settings(custom_settings)
        self.assertTrue(success)
        
        # 重新加载设置
        loaded_settings = self.manager.load_settings()
        
        # 验证自定义设置已生效
        self.assertEqual(loaded_settings['start_row'], 2)
        self.assertEqual(loaded_settings['import_mode'], 'single_sheet')
        self.assertFalse(loaded_settings['auto_match_sheet'])
        
        # 验证其他默认设置仍然存在
        self.assertTrue(loaded_settings['include_header'])
    
    def test_category_sheet_mapping(self):
        """测试人员类别与工作表映射"""
        mapping = self.manager.get_category_sheet_mapping()
        
        # 验证映射包含预期的类别
        self.assertIn('全部在职人员', mapping)
        self.assertIn('离休人员', mapping)
        self.assertIn('退休人员', mapping)
        
        # 验证映射值是列表
        self.assertIsInstance(mapping['全部在职人员'], list)
        self.assertIn('在职人员', mapping['全部在职人员'])
    
    def test_find_matching_sheet(self):
        """测试查找匹配工作表"""
        sheet_names = ['全部在职人员', '离休人员表', '退休职工', '临时工数据']
        
        # 测试精确匹配
        result = self.manager.find_matching_sheet('全部在职人员', sheet_names)
        self.assertEqual(result, '全部在职人员')
        
        # 测试模糊匹配
        result = self.manager.find_matching_sheet('离休人员', sheet_names)
        self.assertEqual(result, '离休人员表')
        
        # 测试无匹配
        result = self.manager.find_matching_sheet('不存在的类别', sheet_names)
        self.assertIsNone(result)
    
    def test_smart_defaults_for_category(self):
        """测试根据类别获取智能默认设置"""
        # 测试离退休人员
        settings = self.manager.get_smart_defaults_for_category('离休人员')
        self.assertEqual(settings['import_mode'], 'single_sheet')
        
        # 测试临时工
        settings = self.manager.get_smart_defaults_for_category('临时工')
        self.assertEqual(settings['import_mode'], 'single_sheet')
        self.assertEqual(settings['create_table_mode'], 'custom_name')
        
        # 测试普通类别
        settings = self.manager.get_smart_defaults_for_category('全部在职人员')
        self.assertEqual(settings['import_mode'], 'multi_sheet')
        self.assertEqual(settings['import_strategy'], 'separate_tables')  # 新增验证
        self.assertEqual(settings['table_template'], 'salary_data')  # 新增验证


class TestSmartSheetMatcher(unittest.TestCase):
    """测试智能工作表匹配器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.defaults_manager = ImportDefaultsManager(config_dir=self.temp_dir)
        self.matcher = SmartSheetMatcher(self.defaults_manager)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_exact_match(self):
        """测试精确匹配"""
        sheet_names = ['全部在职人员', '离休人员', '退休人员']
        
        result = self.matcher.find_best_match('全部在职人员', sheet_names)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.sheet_name, '全部在职人员')
        self.assertEqual(result.match_type, 'exact')
        self.assertEqual(result.score, 1.0)
    
    def test_fuzzy_match(self):
        """测试模糊匹配"""
        sheet_names = ['在职人员表', '离休干部', '退休职工数据']
        
        result = self.matcher.find_best_match('全部在职人员', sheet_names)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.sheet_name, '在职人员表')
        self.assertEqual(result.match_type, 'fuzzy')
        self.assertGreater(result.score, 0.3)
    
    def test_semantic_match(self):
        """测试语义匹配"""
        sheet_names = ['现职人员', '离岗人员', '退岗职工']
        
        result = self.matcher.find_best_match('全部在职人员', sheet_names)
        
        # 语义匹配可能找到结果
        if result:
            self.assertEqual(result.match_type, 'semantic')
            self.assertGreater(result.score, 0.0)
    
    def test_no_match(self):
        """测试无匹配情况"""
        sheet_names = ['销售数据', '财务报表', '库存信息']
        
        result = self.matcher.find_best_match('全部在职人员', sheet_names)
        
        self.assertIsNone(result)
    
    def test_user_preference_learning(self):
        """测试用户偏好学习"""
        sheet_names = ['工资表A', '工资表B', '工资表C']
        
        # 记录用户选择
        self.matcher.record_user_choice('全部在职人员', '工资表B')
        self.matcher.record_user_choice('全部在职人员', '工资表B')
        
        # 再次匹配应该优先选择用户偏好
        result = self.matcher.find_best_match('全部在职人员', sheet_names)
        
        if result and result.match_type == 'preference':
            self.assertEqual(result.sheet_name, '工资表B')
    
    def test_get_match_suggestions(self):
        """测试获取匹配建议"""
        sheet_names = ['全部在职人员', '在职人员表', '现职员工', '销售数据']
        
        suggestions = self.matcher.get_match_suggestions('全部在职人员', sheet_names, top_n=3)
        
        self.assertIsInstance(suggestions, list)
        self.assertLessEqual(len(suggestions), 3)
        
        if suggestions:
            # 验证建议按得分排序
            for i in range(1, len(suggestions)):
                self.assertGreaterEqual(suggestions[i-1].score, suggestions[i].score)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        # 1. 创建管理器
        defaults_manager = ImportDefaultsManager(config_dir=self.temp_dir)
        matcher = SmartSheetMatcher(defaults_manager)
        
        # 2. 模拟用户选择人员类别
        category = '全部在职人员'
        
        # 3. 获取智能默认设置
        settings = defaults_manager.get_smart_defaults_for_category(category)
        self.assertEqual(settings['import_mode'], 'multi_sheet')
        
        # 4. 模拟Excel文件工作表
        sheet_names = ['在职人员工资表', '离休人员', '退休人员', '临时工']
        
        # 5. 执行智能匹配
        match_result = matcher.find_best_match(category, sheet_names)
        
        # 6. 验证匹配结果
        self.assertIsNotNone(match_result)
        self.assertEqual(match_result.sheet_name, '在职人员工资表')
        
        # 7. 记录用户选择
        matcher.record_user_choice(category, match_result.sheet_name)
        
        # 8. 验证学习效果
        self.assertIn(category, matcher.user_preferences)


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestImportDefaultsManager))
    test_suite.addTest(unittest.makeSuite(TestSmartSheetMatcher))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        for test, traceback in result.failures:
            print(f"\n失败: {test}")
            print(traceback)
        
        for test, traceback in result.errors:
            print(f"\n错误: {test}")
            print(traceback)
