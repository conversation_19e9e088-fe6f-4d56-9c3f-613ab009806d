#!/usr/bin/env python3
"""
测试数据导入对话框修复

验证以下功能：
1. 导入窗口在导入完成后自动关闭
2. 主界面能正确显示导入的数据
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import Qt

# 导入项目模块
try:
    from src.gui.dialogs import DataImportDialog
    from src.gui.widgets import DataTableWidget
    from src.utils.log_config import setup_logger
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("导入对话框修复测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        self.import_btn = QPushButton("测试数据导入")
        self.import_btn.clicked.connect(self.test_import_dialog)
        layout.addWidget(self.import_btn)
        
        # 数据表格
        self.data_table = DataTableWidget()
        layout.addWidget(self.data_table)
        
        # 状态标签
        from PyQt5.QtWidgets import QLabel
        self.status_label = QLabel("点击按钮测试数据导入功能")
        layout.addWidget(self.status_label)
        
    def test_import_dialog(self):
        """测试导入对话框"""
        try:
            self.status_label.setText("正在测试导入对话框...")
            
            dialog = DataImportDialog(self)
            
            # 检查对话框是否有必要的方法
            if not hasattr(dialog, 'get_imported_data'):
                self.status_label.setText("错误：导入对话框缺少 get_imported_data 方法")
                return
                
            if not hasattr(dialog, 'get_selected_file'):
                self.status_label.setText("错误：导入对话框缺少 get_selected_file 方法")
                return
            
            # 模拟对话框接受状态
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                # 获取导入的数据
                imported_data = dialog.get_imported_data()
                
                if imported_data and imported_data.get('data'):
                    # 显示导入的数据
                    self.data_table.set_data(
                        imported_data['data'], 
                        imported_data['headers']
                    )
                    self.status_label.setText(f"测试成功：导入了 {len(imported_data['data'])} 行数据")
                else:
                    self.status_label.setText("测试结果：没有导入数据，但对话框正常关闭")
            else:
                self.status_label.setText("测试结果：用户取消了导入")
                
        except Exception as e:
            self.logger.error(f"测试失败: {e}")
            self.status_label.setText(f"测试失败: {e}")
    
    def show_status_message(self, message: str):
        """显示状态消息"""
        self.status_label.setText(message)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 创建测试窗口
        window = TestMainWindow()
        window.show()
        
        print("=" * 50)
        print("数据导入对话框修复测试")
        print("=" * 50)
        print("测试项目：")
        print("1. 导入对话框在完成导入后自动关闭")
        print("2. 主界面能正确获取并显示导入数据")
        print("3. 验证必要方法的存在性")
        print("")
        print("请点击'测试数据导入'按钮开始测试")
        print("=" * 50)
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        return 1


if __name__ == "__main__":
    main() 