"""
测试改进后的数据导入和异动检测功能 - 阶段1验证

这个测试程序验证：
1. 简化的数据导入界面（移除基准数据文件选择）
2. 数据期间管理功能
3. 新的异动检测对话框
4. 基准数据设置选项
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel
from PyQt5.QtCore import Qt

# 导入改进后的组件
from src.gui.dialogs import DataImportDialog
from src.gui.change_detection_dialog import ChangeDetectionDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("改进功能测试 - 阶段1")
        self.setMinimumSize(800, 600)
        
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("改进功能测试 - 阶段1")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)
        
        # 说明
        description = QLabel("""
这个测试程序验证以下改进：

✅ 数据导入界面优化：
   - 移除了混淆的"基准数据文件"选择
   - 添加了数据期间管理功能
   - 增加了"设为基准数据"选项
   - 简化了导入流程

✅ 异动检测功能重构：
   - 创建了独立的异动检测对话框
   - 实现了基准期间选择器
   - 从数据库加载基准数据进行对比
   - 提供丰富的检测配置选项

点击下面的按钮测试各项功能：
        """)
        description.setWordWrap(True)
        description.setStyleSheet("background-color: #f0f0f0; padding: 15px; border-radius: 5px;")
        layout.addWidget(description)
        
        # 测试按钮
        button_layout = QVBoxLayout()
        
        # 数据导入测试
        import_btn = QPushButton("测试改进后的数据导入功能")
        import_btn.setMinimumHeight(40)
        import_btn.clicked.connect(self.test_data_import)
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(import_btn)
        
        # 异动检测测试
        detection_btn = QPushButton("测试新的异动检测功能")
        detection_btn.setMinimumHeight(40)
        detection_btn.clicked.connect(self.test_change_detection)
        detection_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(detection_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        # 测试结果显示区域
        self.result_label = QLabel("点击按钮开始测试...")
        self.result_label.setStyleSheet("background-color: #e8f5e8; padding: 10px; border-radius: 5px;")
        self.result_label.setWordWrap(True)
        layout.addWidget(self.result_label)
        
    def test_data_import(self):
        """测试数据导入功能"""
        try:
            self.result_label.setText("正在打开数据导入对话框...")
            
            dialog = DataImportDialog(self)
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                imported_data = dialog.get_imported_data()
                if imported_data:
                    result_text = f"""
✅ 数据导入测试成功！

导入信息:
• 数据期间: {imported_data.get('data_period', '未设置')}
• 数据描述: {imported_data.get('data_description', '未设置')}
• 设为基准数据: {'是' if imported_data.get('set_as_baseline', False) else '否'}
• 导入行数: {len(imported_data.get('data', []))}
• 导入时间: {imported_data.get('import_time', '未知')}

✨ 改进效果:
• 界面更简洁，不再有混淆的基准数据文件选择
• 增加了数据期间管理，便于历史数据组织
• 基准数据设置集成到导入流程中
                    """
                else:
                    result_text = "✅ 数据导入对话框显示成功，但未完成导入操作"
            else:
                result_text = "ℹ️ 用户取消了数据导入操作"
            
            self.result_label.setText(result_text.strip())
            
        except Exception as e:
            error_text = f"❌ 数据导入测试失败: {e}"
            self.result_label.setText(error_text)
            print(f"数据导入测试错误: {e}")
    
    def test_change_detection(self):
        """测试异动检测功能"""
        try:
            self.result_label.setText("正在打开异动检测对话框...")
            
            dialog = ChangeDetectionDialog(self)
            
            # 连接检测完成信号
            dialog.detection_completed.connect(self.on_detection_completed)
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                detection_result = dialog.get_detection_result()
                if detection_result:
                    summary = detection_result['detection_summary']
                    result_text = f"""
✅ 异动检测测试成功！

检测结果:
• 检测期间: {detection_result['baseline_period']} → {detection_result['current_period']}
• 发现异动: {summary['total_changes']} 项
• 新进人员: {summary['new_employees']} 人
• 离职人员: {summary['resignations']} 人
• 工资调整: {summary['salary_adjustments']} 人
• 津贴变动: {summary['allowance_changes']} 人
• 净变动金额: {summary['net_change']:,.2f} 元

✨ 改进效果:
• 独立的异动检测界面，功能更专注
• 基准期间从数据库选择，无需手动提供文件
• 丰富的检测配置选项
• 详细的检测结果展示和分析
                    """
                else:
                    result_text = "✅ 异动检测对话框显示成功，但未完成检测操作"
            else:
                result_text = "ℹ️ 用户取消了异动检测操作"
            
            self.result_label.setText(result_text.strip())
            
        except Exception as e:
            error_text = f"❌ 异动检测测试失败: {e}"
            self.result_label.setText(error_text)
            print(f"异动检测测试错误: {e}")
    
    def on_detection_completed(self, result):
        """处理异动检测完成"""
        try:
            summary = result['detection_summary']
            completion_text = f"""
🎉 异动检测完成回调触发！

自动处理结果:
• 总异动数: {summary['total_changes']} 项
• 净变动: {summary['net_change']:,.2f} 元
• 检测时间: {result['detection_time']}

这证明了信号连接正常工作！
            """
            
            # 更新结果显示（追加信息）
            current_text = self.result_label.text()
            self.result_label.setText(current_text + "\n" + completion_text.strip())
            
        except Exception as e:
            print(f"处理检测完成回调失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("改进功能测试")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("工资异动处理系统")
    
    # 创建并显示主窗口
    window = TestMainWindow()
    window.show()
    
    # 显示测试说明
    print("="*60)
    print("改进功能测试程序 - 阶段1")
    print("="*60)
    print("\n测试内容:")
    print("1. 数据导入界面优化")
    print("   - 移除基准数据文件选择")
    print("   - 添加数据期间管理") 
    print("   - 增加基准数据设置选项")
    print("\n2. 异动检测功能重构")
    print("   - 独立的检测对话框")
    print("   - 基准期间选择器")
    print("   - 丰富的配置选项")
    print("   - 详细的结果展示")
    print("\n请在界面中点击按钮进行测试")
    print("="*60)
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main()) 