"""
大数据处理优化模块测试

测试大数据处理优化的各个组件功能。
"""

import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# 添加项目路径
sys.path.append('.')

from src.gui.large_data_optimizer import (
    DataChunk, PerformanceMetrics, DataLoader, MemoryManager,
    VirtualizedTableWidget, PerformanceMonitor, LargeDataOptimizer
)


class TestDataChunk(unittest.TestCase):
    """测试数据块"""
    
    def test_data_chunk_creation(self):
        """测试数据块创建"""
        data = [{"name": "test", "value": 123}]
        chunk = DataChunk(0, 1000, data)
        
        self.assertEqual(chunk.start_index, 0)
        self.assertEqual(chunk.end_index, 1000)
        self.assertEqual(chunk.data, data)
        self.assertFalse(chunk.is_loaded)
        self.assertEqual(chunk.last_accessed, 0.0)
        self.assertEqual(chunk.memory_size, 0)


class TestPerformanceMetrics(unittest.TestCase):
    """测试性能指标"""
    
    def test_performance_metrics_creation(self):
        """测试性能指标创建"""
        metrics = PerformanceMetrics()
        
        self.assertEqual(metrics.memory_usage, 0.0)
        self.assertEqual(metrics.cpu_usage, 0.0)
        self.assertEqual(metrics.render_time, 0.0)
        self.assertEqual(metrics.data_load_time, 0.0)
        self.assertEqual(metrics.visible_rows, 0)
        self.assertEqual(metrics.total_rows, 0)
        self.assertEqual(metrics.cache_hit_rate, 0.0)


class TestDataLoader(unittest.TestCase):
    """测试数据加载器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.data_loader = DataLoader()
    
    def test_set_data_source(self):
        """测试设置数据源"""
        test_data = [{"name": f"test{i}", "value": i} for i in range(100)]
        self.data_loader.set_data_source(test_data)
        
        self.assertEqual(self.data_loader.data_source, test_data)
    
    def test_set_chunk_size(self):
        """测试设置块大小"""
        self.data_loader.set_chunk_size(500)
        self.assertEqual(self.data_loader.chunk_size, 500)
        
        # 测试边界值
        self.data_loader.set_chunk_size(50)  # 小于最小值
        self.assertEqual(self.data_loader.chunk_size, 100)
        
        self.data_loader.set_chunk_size(20000)  # 大于最大值
        self.assertEqual(self.data_loader.chunk_size, 10000)
    
    def test_load_chunk_data(self):
        """测试加载数据块"""
        test_data = [{"name": f"test{i}", "value": i} for i in range(100)]
        self.data_loader.set_data_source(test_data)
        
        # 测试加载前50行数据
        chunk_data = self.data_loader._load_chunk_data(0, 50)
        
        self.assertEqual(len(chunk_data), 50)
        self.assertEqual(chunk_data[0]["name"], "test0")
        self.assertEqual(chunk_data[49]["name"], "test49")


class TestMemoryManager(unittest.TestCase):
    """测试内存管理器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.memory_manager = MemoryManager()
    
    def test_set_max_memory(self):
        """测试设置最大内存"""
        self.memory_manager.set_max_memory(1000)
        self.assertEqual(self.memory_manager.max_memory_mb, 1000)
        
        # 测试最小值限制
        self.memory_manager.set_max_memory(50)
        self.assertEqual(self.memory_manager.max_memory_mb, 100)
    
    def test_add_chunk_to_cache(self):
        """测试添加数据块到缓存"""
        data = [{"name": f"test{i}", "value": i} for i in range(10)]
        chunk = DataChunk(0, 10, data, is_loaded=True)
        
        self.memory_manager.add_chunk_to_cache(0, chunk)
        
        self.assertIn(0, self.memory_manager.cache_chunks)
        self.assertIn(0, self.memory_manager.chunk_access_times)
        self.assertGreater(chunk.memory_size, 0)
        self.assertGreater(chunk.last_accessed, 0)
    
    def test_get_chunk_from_cache(self):
        """测试从缓存获取数据块"""
        data = [{"name": f"test{i}", "value": i} for i in range(10)]
        chunk = DataChunk(0, 10, data, is_loaded=True)
        
        self.memory_manager.add_chunk_to_cache(0, chunk)
        retrieved_chunk = self.memory_manager.get_chunk_from_cache(0)
        
        self.assertIsNotNone(retrieved_chunk)
        self.assertEqual(retrieved_chunk.data, data)
        
        # 测试不存在的数据块
        non_existent_chunk = self.memory_manager.get_chunk_from_cache(999)
        self.assertIsNone(non_existent_chunk)


class TestVirtualizedTableWidget(unittest.TestCase):
    """测试虚拟化表格控件"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        headers = ["姓名", "工号", "部门", "工资"]
        self.table = VirtualizedTableWidget(headers=headers)
    
    def test_set_total_rows(self):
        """测试设置总行数"""
        self.table.set_total_rows(10000)
        
        self.assertEqual(self.table.total_rows, 10000)
        # 虚拟化表格的实际行数应该是可见行数+缓冲区大小
        expected_rows = min(10000, self.table.visible_row_count + self.table.buffer_size)
        self.assertEqual(self.table.rowCount(), expected_rows)
    
    def test_set_data_chunk(self):
        """测试设置数据块"""
        test_data = [
            {"姓名": f"员工{i}", "工号": f"E{i:05d}", "部门": f"部门{i%3}", "工资": 5000 + i*10}
            for i in range(100)
        ]
        
        self.table.set_data_chunk(0, test_data)
        
        self.assertIn(0, self.table.data_chunks)
        self.assertEqual(len(self.table.data_chunks[0]), 100)
    
    def test_performance_metrics(self):
        """测试性能指标"""
        metrics = self.table.get_performance_metrics()
        
        self.assertIsInstance(metrics, PerformanceMetrics)
        self.assertGreaterEqual(metrics.render_time, 0)


class TestPerformanceMonitor(unittest.TestCase):
    """测试性能监控器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.monitor = PerformanceMonitor()
    
    def test_monitor_creation(self):
        """测试监控器创建"""
        self.assertIsNotNone(self.monitor.metrics)
        self.assertIsInstance(self.monitor.metrics, PerformanceMetrics)
    
    def test_update_metrics(self):
        """测试更新性能指标"""
        test_metrics = PerformanceMetrics()
        test_metrics.memory_usage = 100.0
        test_metrics.cpu_usage = 50.0
        test_metrics.total_rows = 10000
        test_metrics.visible_rows = 50
        
        self.monitor.update_metrics(test_metrics)
        
        self.assertEqual(self.monitor.metrics.memory_usage, 100.0)
        self.assertEqual(self.monitor.metrics.cpu_usage, 50.0)
        self.assertEqual(self.monitor.metrics.total_rows, 10000)
        self.assertEqual(self.monitor.metrics.visible_rows, 50)


class TestLargeDataOptimizer(unittest.TestCase):
    """测试大数据处理优化器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.optimizer = LargeDataOptimizer()
    
    def test_optimizer_creation(self):
        """测试优化器创建"""
        self.assertIsNotNone(self.optimizer.data_loader)
        self.assertIsNotNone(self.optimizer.memory_manager)
        self.assertIsNotNone(self.optimizer.virtualized_table)
        self.assertIsNotNone(self.optimizer.performance_monitor)
        
        self.assertFalse(self.optimizer.is_loading)
        self.assertEqual(self.optimizer.total_rows, 0)
        self.assertEqual(len(self.optimizer.loaded_chunks), 0)
    
    def test_set_data_source(self):
        """测试设置数据源"""
        test_data = [{"name": f"test{i}", "value": i} for i in range(1000)]
        
        self.optimizer.set_data_source(test_data)
        
        self.assertEqual(self.optimizer.data_loader.data_source, test_data)
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        metrics = self.optimizer.get_performance_metrics()
        
        self.assertIsInstance(metrics, PerformanceMetrics)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
    
    def test_large_data_processing_workflow(self):
        """测试大数据处理工作流"""
        # 创建测试数据
        test_data = [
            {"姓名": f"员工{i}", "工号": f"E{i:05d}", "部门": f"部门{i%10}", "工资": 5000 + i*10}
            for i in range(1000)  # 1000条测试数据
        ]
        
        # 创建优化器
        optimizer = LargeDataOptimizer()
        
        # 设置数据源
        optimizer.set_data_source(test_data)
        
        # 验证数据源设置
        self.assertEqual(optimizer.data_loader.data_source, test_data)
        
        # 验证初始状态
        self.assertFalse(optimizer.is_loading)
        self.assertEqual(optimizer.total_rows, 0)
        
        # 验证组件存在
        self.assertIsNotNone(optimizer.virtualized_table)
        self.assertIsNotNone(optimizer.performance_monitor)
        self.assertIsNotNone(optimizer.memory_manager)
    
    def test_memory_management_workflow(self):
        """测试内存管理工作流"""
        memory_manager = MemoryManager()
        
        # 创建多个数据块
        chunks = []
        for i in range(5):
            data = [{"name": f"test{j}", "value": j} for j in range(100)]
            chunk = DataChunk(i*100, (i+1)*100, data, is_loaded=True)
            chunks.append(chunk)
            memory_manager.add_chunk_to_cache(i, chunk)
        
        # 验证缓存
        self.assertEqual(len(memory_manager.cache_chunks), 5)
        
        # 测试获取缓存
        for i in range(5):
            cached_chunk = memory_manager.get_chunk_from_cache(i)
            self.assertIsNotNone(cached_chunk)
            self.assertEqual(len(cached_chunk.data), 100)
    
    def test_virtualized_table_workflow(self):
        """测试虚拟化表格工作流"""
        headers = ["姓名", "工号", "部门", "工资"]
        table = VirtualizedTableWidget(headers=headers)
        
        # 设置总行数
        table.set_total_rows(10000)
        self.assertEqual(table.total_rows, 10000)
        
        # 添加数据块
        test_data = [
            {"姓名": f"员工{i}", "工号": f"E{i:05d}", "部门": f"部门{i%3}", "工资": 5000 + i*10}
            for i in range(1000)
        ]
        table.set_data_chunk(0, test_data)
        
        # 验证数据块
        self.assertIn(0, table.data_chunks)
        self.assertEqual(len(table.data_chunks[0]), 1000)
        
        # 获取性能指标
        metrics = table.get_performance_metrics()
        self.assertIsInstance(metrics, PerformanceMetrics)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDataChunk,
        TestPerformanceMetrics,
        TestDataLoader,
        TestMemoryManager,
        TestVirtualizedTableWidget,
        TestPerformanceMonitor,
        TestLargeDataOptimizer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1) 