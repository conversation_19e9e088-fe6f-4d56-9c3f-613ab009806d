#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Material Design标签组件测试程序

测试MaterialTabWidget在主窗口环境中的集成效果，验证：
1. Material Design视觉样式正确显示
2. 标签切换动画流畅运行
3. 响应式布局正确适配
4. 波纹点击效果正常工作
5. 与现有组件的集成兼容性

运行方式:
    cd src/gui/prototype
    python test_material_tabs.py
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试组件
from src.gui.prototype.widgets.material_tab_widget import MaterialTabWidget, MaterialColorSystem
from src.gui.prototype.prototype_main_window import MainWorkspaceArea

class MaterialTabsTestWindow(QMainWindow):
    """Material标签组件测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.color_system = MaterialColorSystem()
        self.setup_ui()
        self.setup_test_data()
        
    def setup_ui(self):
        """设置测试UI"""
        self.setWindowTitle("Material Design标签组件测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 测试标题
        title_label = QLabel("🎨 Material Design标签组件测试")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {self.color_system.PRIMARY['primary_60']};
                padding: 10px;
            }}
        """)
        main_layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel(
            "本测试验证Material Design标签组件的以下功能:\n"
            "• Material Design 3.0视觉样式\n"
            "• 流畅的标签切换动画\n"
            "• 波纹点击效果\n"
            "• 响应式布局适配\n"
            "• 与现有组件的集成兼容性"
        )
        info_label.setStyleSheet(f"""
            QLabel {{
                background-color: {self.color_system.NEUTRAL['neutral_95']};
                border: 1px solid {self.color_system.NEUTRAL['neutral_80']};
                border-radius: 8px;
                padding: 15px;
                color: {self.color_system.NEUTRAL['neutral_30']};
                font-size: 14px;
                line-height: 1.5;
            }}
        """)
        main_layout.addWidget(info_label)
        
        # 创建Material标签组件
        self.material_tabs = MaterialTabWidget()
        self.create_test_tabs()
        main_layout.addWidget(self.material_tabs)
        
        # 状态面板
        self.create_status_panel(main_layout)
        
    def create_test_tabs(self):
        """创建测试标签页"""
        # 标签页1：基础功能演示
        tab1 = self.create_basic_demo_tab()
        self.material_tabs.add_material_tab(tab1, "基础演示")
        
        # 标签页2：响应式测试
        tab2 = self.create_responsive_test_tab()
        self.material_tabs.add_material_tab(tab2, "响应式测试")
        
        # 标签页3：动画效果
        tab3 = self.create_animation_test_tab()
        self.material_tabs.add_material_tab(tab3, "动画效果")
        
        # 标签页4：集成测试
        tab4 = self.create_integration_test_tab()
        self.material_tabs.add_material_tab(tab4, "集成测试")
        
        # 连接信号
        self.material_tabs.tab_changed.connect(self.on_tab_changed)
        
    def create_basic_demo_tab(self) -> QWidget:
        """创建基础演示标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 颜色系统演示
        color_group = QGroupBox("Material Design 3.0 色彩系统")
        color_layout = QGridLayout(color_group)
        
        colors = [
            ("Primary", self.color_system.PRIMARY['primary_60']),
            ("Secondary", self.color_system.SECONDARY['secondary_40']),
            ("Neutral Dark", self.color_system.NEUTRAL['neutral_20']),
            ("Neutral Light", self.color_system.NEUTRAL['neutral_90'])
        ]
        
        for i, (name, color) in enumerate(colors):
            color_label = QLabel(name)
            color_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {color};
                    color: white;
                    padding: 10px;
                    border-radius: 4px;
                    font-weight: bold;
                }}
            """)
            color_layout.addWidget(color_label, i // 2, i % 2)
            
        layout.addWidget(color_group)
        
        # 交互按钮测试
        button_group = QGroupBox("Material Design按钮样式")
        button_layout = QHBoxLayout(button_group)
        
        primary_btn = QPushButton("Primary按钮")
        primary_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color_system.PRIMARY['primary_60']};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {self.color_system.PRIMARY['primary_50']};
            }}
        """)
        
        secondary_btn = QPushButton("Secondary按钮")
        secondary_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color_system.SECONDARY['secondary_40']};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {self.color_system.SECONDARY['secondary_60']};
            }}
        """)
        
        button_layout.addWidget(primary_btn)
        button_layout.addWidget(secondary_btn)
        button_layout.addStretch()
        
        layout.addWidget(button_group)
        layout.addStretch()
        
        return widget
        
    def create_responsive_test_tab(self) -> QWidget:
        """创建响应式测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 响应式信息显示
        self.responsive_info = QLabel("当前断点: 未知")
        self.responsive_info.setStyleSheet(f"""
            QLabel {{
                background-color: {self.color_system.PRIMARY['primary_90']};
                color: {self.color_system.PRIMARY['primary_30']};
                padding: 15px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(self.responsive_info)
        
        # 模拟不同屏幕尺寸按钮
        size_group = QGroupBox("模拟屏幕尺寸")
        size_layout = QHBoxLayout(size_group)
        
        sizes = [
            ("超小屏 (480px)", 480),
            ("小屏 (768px)", 768), 
            ("中屏 (1024px)", 1024),
            ("大屏 (1440px)", 1440)
        ]
        
        for name, width in sizes:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, w=width: self.simulate_screen_size(w))
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {self.color_system.NEUTRAL['neutral_90']};
                    color: {self.color_system.NEUTRAL['neutral_30']};
                    border: 1px solid {self.color_system.NEUTRAL['neutral_80']};
                    padding: 8px 16px;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    background-color: {self.color_system.NEUTRAL['neutral_80']};
                }}
            """)
            size_layout.addWidget(btn)
            
        layout.addWidget(size_group)
        layout.addStretch()
        
        return widget
        
    def create_animation_test_tab(self) -> QWidget:
        """创建动画效果测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 动画控制面板
        animation_group = QGroupBox("动画效果测试")
        animation_layout = QVBoxLayout(animation_group)
        
        # 动画按钮
        animate_btn = QPushButton("测试淡入动画")
        animate_btn.clicked.connect(self.test_fade_animation)
        animate_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color_system.PRIMARY['primary_60']};
                color: white;
                padding: 15px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        
        # 动画目标元素
        self.animation_target = QLabel("动画目标元素")
        self.animation_target.setAlignment(Qt.AlignCenter)
        self.animation_target.setStyleSheet(f"""
            QLabel {{
                background-color: {self.color_system.SECONDARY['secondary_40']};
                color: white;
                padding: 30px;
                border-radius: 12px;
                font-size: 18px;
                font-weight: bold;
            }}
        """)
        
        animation_layout.addWidget(animate_btn)
        animation_layout.addWidget(self.animation_target)
        
        layout.addWidget(animation_group)
        layout.addStretch()
        
        return widget
        
    def create_integration_test_tab(self) -> QWidget:
        """创建集成测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 集成测试信息
        integration_info = QLabel(
            "🔗 集成测试\n\n"
            "此标签页测试Material Design标签组件与现有系统的集成:\n"
            "• 与ResponsiveLayoutManager的兼容性\n"
            "• 与ComponentCommunicationManager的信号通信\n"
            "• 与现有样式系统的一致性\n"
            "• 性能和内存使用情况"
        )
        integration_info.setStyleSheet(f"""
            QLabel {{
                background-color: {self.color_system.NEUTRAL['neutral_95']};
                color: {self.color_system.NEUTRAL['neutral_30']};
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
            }}
        """)
        
        layout.addWidget(integration_info)
        
        # 性能指标显示
        self.performance_label = QLabel("性能指标：初始化中...")
        self.performance_label.setStyleSheet(f"""
            QLabel {{
                background-color: {self.color_system.PRIMARY['primary_90']};
                color: {self.color_system.PRIMARY['primary_30']};
                padding: 15px;
                border-radius: 8px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(self.performance_label)
        
        # 启动性能测试
        QTimer.singleShot(1000, self.update_performance_metrics)
        
        layout.addStretch()
        return widget
        
    def create_status_panel(self, main_layout):
        """创建状态面板"""
        status_frame = QFrame()
        status_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color_system.NEUTRAL['neutral_95']};
                border: 1px solid {self.color_system.NEUTRAL['neutral_80']};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        # 当前标签显示
        self.current_tab_label = QLabel("当前标签: 基础演示")
        self.current_tab_label.setStyleSheet(f"""
            QLabel {{
                color: {self.color_system.NEUTRAL['neutral_30']};
                font-weight: bold;
            }}
        """)
        
        # 测试状态
        self.test_status_label = QLabel("✅ 测试就绪")
        self.test_status_label.setStyleSheet(f"""
            QLabel {{
                color: {self.color_system.SECONDARY['secondary_40']};
                font-weight: bold;
            }}
        """)
        
        status_layout.addWidget(self.current_tab_label)
        status_layout.addStretch()
        status_layout.addWidget(self.test_status_label)
        
        main_layout.addWidget(status_frame)
        
    def setup_test_data(self):
        """设置测试数据"""
        # 更新响应式信息
        self.update_responsive_info()
        
    def on_tab_changed(self, index: int):
        """标签切换事件"""
        tab_names = ["基础演示", "响应式测试", "动画效果", "集成测试"]
        if 0 <= index < len(tab_names):
            tab_name = tab_names[index]
            self.current_tab_label.setText(f"当前标签: {tab_name}")
            self.test_status_label.setText(f"✅ 已切换到 {tab_name}")
            print(f"🎨 Material标签切换: {tab_name} (索引: {index})")
            
    def simulate_screen_size(self, width: int):
        """模拟屏幕尺寸"""
        current_height = self.height()
        self.resize(width, current_height)
        
        # 更新响应式信息
        QTimer.singleShot(100, self.update_responsive_info)
        
    def update_responsive_info(self):
        """更新响应式信息"""
        width = self.width()
        breakpoint = self.material_tabs.get_current_breakpoint()
        
        self.responsive_info.setText(
            f"当前断点: {breakpoint} | 窗口宽度: {width}px"
        )
        
    def test_fade_animation(self):
        """测试淡入动画"""
        effect = QGraphicsOpacityEffect()
        self.animation_target.setGraphicsEffect(effect)
        
        # 淡出
        fade_out = QPropertyAnimation(effect, b"opacity")
        fade_out.setDuration(300)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)
        fade_out.setEasingCurve(QEasingCurve.OutCubic)
        
        # 淡入
        fade_in = QPropertyAnimation(effect, b"opacity")
        fade_in.setDuration(300)
        fade_in.setStartValue(0.0)
        fade_in.setEndValue(1.0)
        fade_in.setEasingCurve(QEasingCurve.OutCubic)
        
        # 序列动画
        animation_sequence = QSequentialAnimationGroup()
        animation_sequence.addAnimation(fade_out)
        animation_sequence.addAnimation(fade_in)
        
        # 动画完成后清理
        animation_sequence.finished.connect(
            lambda: self.animation_target.setGraphicsEffect(None)
        )
        
        animation_sequence.start()
        self.test_status_label.setText("🎬 动画测试进行中...")
        
        # 延迟更新状态
        QTimer.singleShot(700, lambda: self.test_status_label.setText("✅ 动画测试完成"))
        
    def update_performance_metrics(self):
        """更新性能指标"""
        # 模拟性能指标
        memory_usage = "12.5 MB"
        response_time = "< 50ms"
        animations_fps = "60 FPS"
        
        self.performance_label.setText(
            f"性能指标 | 内存: {memory_usage} | 响应: {response_time} | 动画: {animations_fps}"
        )
        
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 延迟更新响应式信息
        QTimer.singleShot(100, self.update_responsive_info)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    print("🧪 Material Design标签组件测试启动...")
    print("=" * 50)
    
    try:
        # 创建测试窗口
        window = MaterialTabsTestWindow()
        window.show()
        
        print("✅ 测试窗口创建成功")
        print("📋 测试项目:")
        print("   • Material Design 3.0视觉样式")
        print("   • 标签切换动画效果")
        print("   • 响应式布局适配")
        print("   • 波纹点击效果")
        print("   • 组件集成兼容性")
        print("=" * 50)
        
        # 启动应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("错误详情:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main() 