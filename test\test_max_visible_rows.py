#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试max_visible_rows功能
"""

import sys
import os

# 添加路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 获取项目根目录
widgets_path = os.path.join(project_root, 'src', 'gui', 'prototype', 'widgets')
sys.path.insert(0, widgets_path)

from PyQt5.QtWidgets import QApplication

try:
    from virtualized_expandable_table import VirtualizedExpandableTable, create_sample_data
except ImportError as e:
    print(f"导入错误: {e}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"widgets路径: {widgets_path}")
    print(f"widgets路径是否存在: {os.path.exists(widgets_path)}")
    sys.exit(1)

def test_max_visible_rows():
    """测试max_visible_rows功能"""
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    print('=== 测试 max_visible_rows 功能 ===')
    
    # 测试1: 默认初始化
    table1 = VirtualizedExpandableTable()
    print(f'默认初始化 - max_visible_rows: {table1.get_max_visible_rows()}')
    
    # 测试2: 自定义初始化
    table2 = VirtualizedExpandableTable(max_visible_rows=500)
    print(f'自定义初始化 - max_visible_rows: {table2.get_max_visible_rows()}')
    
    # 测试3: 设置方法
    success = table2.set_max_visible_rows(200)
    print(f'设置为200 - 成功: {success}, 当前值: {table2.get_max_visible_rows()}')
    
    # 测试4: 无效值
    success = table2.set_max_visible_rows(-1)
    print(f'设置为-1 - 成功: {success}, 当前值: {table2.get_max_visible_rows()}')
    
    # 测试5: 自动调整功能
    success = table2.auto_adjust_max_visible_rows(2000)
    print(f'自动调整(2000行数据) - 成功: {success}, 调整后值: {table2.get_max_visible_rows()}')
    
    success = table2.auto_adjust_max_visible_rows(50)
    print(f'自动调整(50行数据) - 成功: {success}, 调整后值: {table2.get_max_visible_rows()}')
    
    # 测试6: 数据设置时的自动调整
    data, headers = create_sample_data(1500)
    table2.set_data(data, headers, auto_adjust_visible_rows=True)
    print(f'设置1500行数据后自动调整 - max_visible_rows: {table2.get_max_visible_rows()}')
    
    # 测试7: 性能统计
    stats = table2.get_performance_stats()
    print(f'性能统计 - max_visible_rows: {stats["max_visible_rows"]}, total_rows: {stats["total_rows"]}')
    
    print('=== 测试完成 ===')
    
    # 不启动事件循环，只是测试功能
    return True

if __name__ == "__main__":
    test_max_visible_rows() 