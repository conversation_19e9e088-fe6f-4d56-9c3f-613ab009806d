#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化界面测试脚本
用于验证现代化界面是否正确显示
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def test_modern_main_window():
    """测试现代化主窗口"""
    try:
        from src.gui.modern_main_window import ModernMainWindow
        
        app = QApplication(sys.argv)
        
        # 设置应用程序基本信息
        app.setApplicationName("现代化界面测试")
        app.setStyle('Fusion')
        
        # 创建并显示现代化主窗口
        window = ModernMainWindow()
        window.show()
        
        # 输出确认信息
        print("✓ 现代化主窗口创建成功")
        print(f"✓ 窗口标题: {window.windowTitle()}")
        print(f"✓ 窗口大小: {window.size().width()} x {window.size().height()}")
        
        # 检查样式表是否应用
        stylesheet = window.styleSheet()
        if stylesheet and len(stylesheet) > 1000:
            print(f"✓ 现代化样式已应用，样式长度: {len(stylesheet)} 字符")
        else:
            print("⚠ 现代化样式未正确应用")
        
        # 运行应用程序
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_style_availability():
    """测试样式文件可用性"""
    try:
        from src.gui.modern_style import MaterialDesignPalette, ModernStyleSheet
        
        print("✓ MaterialDesignPalette 导入成功")
        print(f"  - 主色调: {MaterialDesignPalette.PRIMARY['main']}")
        print(f"  - 背景色: {MaterialDesignPalette.BACKGROUND['default']}")
        
        print("✓ ModernStyleSheet 导入成功")
        main_style = ModernStyleSheet.get_main_window_style()
        print(f"  - 主窗口样式长度: {len(main_style)} 字符")
        
        return True
        
    except ImportError as e:
        print(f"❌ 样式模块导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 样式测试错误: {e}")
        return False

def main():
    """主测试函数"""
    print("现代化界面测试开始...")
    print("=" * 50)
    
    # 测试样式文件
    print("1. 测试样式文件可用性...")
    style_ok = test_style_availability()
    print()
    
    if not style_ok:
        print("❌ 样式文件测试失败，无法继续")
        return 1
    
    # 测试主窗口
    print("2. 测试现代化主窗口...")
    window_ok = test_modern_main_window()
    
    if window_ok:
        print("\n✓ 现代化界面测试完成")
        return 0
    else:
        print("\n❌ 现代化界面测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 