#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多Sheet导入默认设置测试

测试多Sheet导入配置的默认设置是否正确应用。

创建时间: 2025-01-20
"""

import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.modules.data_import.import_defaults_manager import ImportDefaultsManager


class TestMultiSheetDefaults(unittest.TestCase):
    """测试多Sheet导入默认设置"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = ImportDefaultsManager(config_dir=self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_settings(self):
        """测试基本默认设置"""
        settings = self.manager.load_settings()
        
        # 验证新的默认设置
        self.assertEqual(settings['import_strategy'], 'separate_tables', 
                        "导入策略应该默认为'分离到多表'")
        self.assertEqual(settings['table_template'], 'salary_data',
                        "表模板应该默认为'工资数据表'")
        self.assertEqual(settings['import_mode'], 'multi_sheet',
                        "导入模式应该默认为'多Sheet模式'")
    
    def test_general_category_defaults(self):
        """测试一般人员类别的默认设置"""
        categories = ['全部在职人员', 'A岗职工', '管理层', '教师', '科研人员', '医务人员', '技术人员']
        
        for category in categories:
            with self.subTest(category=category):
                settings = self.manager.get_smart_defaults_for_category(category)
                
                self.assertEqual(settings['import_mode'], 'multi_sheet',
                               f"{category} 应该使用多Sheet模式")
                self.assertEqual(settings['import_strategy'], 'separate_tables',
                               f"{category} 应该使用分离到多表策略")
                self.assertEqual(settings['table_template'], 'salary_data',
                               f"{category} 应该使用工资数据表模板")
    
    def test_special_category_defaults(self):
        """测试特殊人员类别的默认设置"""
        # 测试离退休人员
        retirement_categories = ['离休人员', '退休人员']
        for category in retirement_categories:
            with self.subTest(category=category):
                settings = self.manager.get_smart_defaults_for_category(category)
                
                self.assertEqual(settings['import_mode'], 'single_sheet',
                               f"{category} 应该使用单Sheet模式")
                self.assertEqual(settings['import_strategy'], 'merge_to_single_table',
                               f"{category} 应该使用合并到单表策略")
        
        # 测试临时人员
        temp_categories = ['临时工', '实习生']
        for category in temp_categories:
            with self.subTest(category=category):
                settings = self.manager.get_smart_defaults_for_category(category)
                
                self.assertEqual(settings['import_mode'], 'single_sheet',
                               f"{category} 应该使用单Sheet模式")
                self.assertEqual(settings['create_table_mode'], 'custom_name',
                               f"{category} 应该使用自定义表名模式")
                self.assertEqual(settings['import_strategy'], 'merge_to_single_table',
                               f"{category} 应该使用合并到单表策略")
    
    def test_settings_consistency(self):
        """测试设置的一致性"""
        # 测试多Sheet模式的类别都有正确的策略设置
        multi_sheet_categories = ['全部在职人员', 'A岗职工', '管理层']
        
        for category in multi_sheet_categories:
            settings = self.manager.get_smart_defaults_for_category(category)
            
            # 多Sheet模式应该配合分离到多表策略
            if settings['import_mode'] == 'multi_sheet':
                self.assertEqual(settings['import_strategy'], 'separate_tables',
                               f"多Sheet模式应该配合分离到多表策略，类别: {category}")
                self.assertEqual(settings['table_template'], 'salary_data',
                               f"多Sheet模式应该使用工资数据表模板，类别: {category}")
    
    def test_custom_settings_override(self):
        """测试自定义设置覆盖默认值"""
        # 保存自定义设置
        custom_settings = {
            'import_strategy': 'merge_to_single_table',
            'table_template': 'salary_changes'
        }
        
        success = self.manager.save_settings(custom_settings)
        self.assertTrue(success, "自定义设置应该保存成功")
        
        # 重新加载设置
        loaded_settings = self.manager.load_settings()
        
        # 验证自定义设置生效
        self.assertEqual(loaded_settings['import_strategy'], 'merge_to_single_table',
                        "自定义导入策略应该覆盖默认值")
        self.assertEqual(loaded_settings['table_template'], 'salary_changes',
                        "自定义表模板应该覆盖默认值")
        
        # 验证其他默认设置仍然存在
        self.assertEqual(loaded_settings['import_mode'], 'multi_sheet',
                        "其他默认设置应该保持不变")
    
    def test_backwards_compatibility(self):
        """测试向后兼容性"""
        # 模拟旧版本的配置文件（没有新的设置项）
        old_settings = {
            'start_row': 2,
            'import_mode': 'single_sheet',
            'auto_match_sheet': False
        }
        
        # 保存旧设置
        self.manager.save_settings(old_settings)
        
        # 重新加载，应该合并默认值
        loaded_settings = self.manager.load_settings()
        
        # 验证旧设置保持
        self.assertEqual(loaded_settings['start_row'], 2)
        self.assertEqual(loaded_settings['import_mode'], 'single_sheet')
        self.assertFalse(loaded_settings['auto_match_sheet'])
        
        # 验证新的默认值被添加
        self.assertEqual(loaded_settings['import_strategy'], 'separate_tables',
                        "新的默认导入策略应该被添加")
        self.assertEqual(loaded_settings['table_template'], 'salary_data',
                        "新的默认表模板应该被添加")


def run_demo():
    """运行演示"""
    print("🚀 多Sheet导入默认设置演示")
    print("=" * 50)
    
    manager = ImportDefaultsManager()
    
    print("\n📋 基本默认设置:")
    basic_settings = manager.load_settings()
    print(f"  导入模式: {basic_settings['import_mode']}")
    print(f"  导入策略: {basic_settings['import_strategy']}")
    print(f"  表模板: {basic_settings['table_template']}")
    
    print("\n👥 不同人员类别的智能设置:")
    
    categories = [
        '全部在职人员', '离休人员', '退休人员', 
        'A岗职工', '临时工', '实习生'
    ]
    
    for category in categories:
        settings = manager.get_smart_defaults_for_category(category)
        print(f"\n  {category}:")
        print(f"    导入模式: {settings['import_mode']}")
        print(f"    导入策略: {settings['import_strategy']}")
        print(f"    表模板: {settings.get('table_template', '默认')}")
        if 'create_table_mode' in settings:
            print(f"    表创建模式: {settings['create_table_mode']}")
    
    print("\n✅ 演示完成！")


if __name__ == '__main__':
    print("选择运行模式:")
    print("1. 运行测试")
    print("2. 运行演示")
    print("3. 运行全部")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == '1' or choice == '3':
        print("\n🧪 运行测试...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    
    if choice == '2' or choice == '3':
        print("\n🎬 运行演示...")
        run_demo()
    
    if choice not in ['1', '2', '3']:
        print("无效选择，运行演示...")
        run_demo()
