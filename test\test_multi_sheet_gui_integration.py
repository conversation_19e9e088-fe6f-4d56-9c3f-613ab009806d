"""
多Sheet Excel导入GUI集成测试

测试新集成的多Sheet导入功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from src.gui.dialogs import DataImportDialog
from src.utils.log_config import setup_logger

def test_multi_sheet_dialog():
    """测试多Sheet导入对话框"""
    
    logger = setup_logger(__name__)
    
    app = QApplication(sys.argv)
    
    # 创建数据导入对话框
    dialog = DataImportDialog()
    
    print("=== 多Sheet Excel导入GUI集成测试 ===")
    print()
    print("界面集成检查:")
    
    # 检查多Sheet相关组件
    checks = [
        ("MultiSheetImporter", hasattr(dialog, 'multi_sheet_importer')),
        ("多Sheet模式标记", hasattr(dialog, 'is_multi_sheet_mode')),
        ("单Sheet模式按钮", hasattr(dialog, 'single_sheet_mode')),
        ("多Sheet模式按钮", hasattr(dialog, 'multi_sheet_mode')),
        ("导入策略选择", hasattr(dialog, 'import_strategy_combo')),
        ("表模板选择", hasattr(dialog, 'table_template_combo')),
        ("Sheet配置按钮", hasattr(dialog, 'config_sheets_btn')),
        ("配置导出按钮", hasattr(dialog, 'export_config_btn')),
        ("策略说明标签", hasattr(dialog, 'strategy_description'))
    ]
    
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
    
    print()
    print("方法集成检查:")
    
    method_checks = [
        ("_on_import_mode_changed", hasattr(dialog, '_on_import_mode_changed')),
        ("_on_strategy_changed", hasattr(dialog, '_on_strategy_changed')),
        ("_update_strategy_description", hasattr(dialog, '_update_strategy_description')),
        ("_configure_sheet_mappings", hasattr(dialog, '_configure_sheet_mappings')),
        ("_export_sheet_config", hasattr(dialog, '_export_sheet_config')),
        ("_execute_multi_sheet_import", hasattr(dialog, '_execute_multi_sheet_import')),
        ("_execute_single_sheet_import", hasattr(dialog, '_execute_single_sheet_import')),
        ("_show_import_result", hasattr(dialog, '_show_import_result'))
    ]
    
    for method_name, result in method_checks:
        status = "✅" if result else "❌"
        print(f"  {status} {method_name}")
    
    print()
    
    # 测试模式切换
    print("功能测试:")
    
    try:
        # 测试切换到多Sheet模式
        dialog.multi_sheet_mode.setChecked(True)
        print("  ✅ 多Sheet模式切换正常")
        
        # 测试策略变化
        dialog.import_strategy_combo.setCurrentIndex(1)  # 切换到分离策略
        print("  ✅ 导入策略切换正常")
        
        # 测试策略说明更新
        if dialog.strategy_description.text():
            print("  ✅ 策略说明显示正常")
        else:
            print("  ❌ 策略说明显示异常")
            
    except Exception as e:
        print(f"  ❌ 功能测试失败: {e}")
    
    print()
    print("=== 测试完成 ===")
    print()
    print("使用说明:")
    print("1. 在数据导入对话框中选择'多Sheet模式'")
    print("2. 选择导入策略：合并到单表 或 分离到多表")
    print("3. 选择Excel文件后可配置Sheet映射")
    print("4. 支持动态字段扩展和自动类型推断")
    print()
    
    # 显示对话框进行手工测试
    if len(sys.argv) > 1 and sys.argv[1] == "--show":
        print("显示对话框进行手工测试...")
        dialog.show()
        return app.exec_()
    
    return 0

if __name__ == "__main__":
    sys.exit(test_multi_sheet_dialog()) 