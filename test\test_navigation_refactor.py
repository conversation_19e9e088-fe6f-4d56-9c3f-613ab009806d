"""
导航重构功能测试

测试导航重构后的功能完整性：
1. 菜单栏功能测试
2. 简化后的导航面板测试
3. 功能可访问性验证
"""

import sys
import os
import unittest
import tempfile
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.gui.main_window_refactored import MainWindow
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager


class TestNavigationRefactor(unittest.TestCase):
    """导航重构功能测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def setUp(self):
        """每个测试方法的初始化"""
        # 模拟应用服务
        with patch('src.gui.main_window_refactored.ApplicationService') as mock_service:
            mock_service.return_value.initialize.return_value.success = True
            mock_service.return_value.initialize.return_value.message = "初始化成功"
            
            self.main_window = MainWindow()
            self.main_window.app_service = mock_service.return_value
    
    def tearDown(self):
        """测试后清理"""
        self.main_window.close()
    
    def test_menu_bar_structure(self):
        """测试菜单栏结构"""
        # 获取菜单栏
        menu_bar = self.main_window.menuBar()
        
        # 检查主要菜单是否存在
        menu_titles = [action.text() for action in menu_bar.actions()]
        expected_menus = ['文件(&F)', '数据(&D)', '异动(&C)', '报告(&R)', '设置(&S)', '帮助(&H)']
        
        for expected in expected_menus:
            self.assertIn(expected, menu_titles, f"菜单 '{expected}' 不存在")
    
    def test_file_menu_actions(self):
        """测试文件菜单的动作"""
        # 获取文件菜单
        menu_bar = self.main_window.menuBar()
        file_menu = None
        for action in menu_bar.actions():
            if action.text() == '文件(&F)':
                file_menu = action.menu()
                break
        
        self.assertIsNotNone(file_menu, "文件菜单不存在")
        
        # 检查文件菜单项
        file_actions = [action.text() for action in file_menu.actions() if action.text()]
        expected_actions = ['导入数据(&I)', '导出数据(&E)', '导入历史(&H)', '退出(&X)']
        
        for expected in expected_actions:
            self.assertIn(expected, file_actions, f"文件菜单项 '{expected}' 不存在")
    
    def test_data_menu_actions(self):
        """测试数据菜单的动作"""
        menu_bar = self.main_window.menuBar()
        data_menu = None
        for action in menu_bar.actions():
            if action.text() == '数据(&D)':
                data_menu = action.menu()
                break
        
        self.assertIsNotNone(data_menu, "数据菜单不存在")
        
        # 检查数据菜单项
        data_actions = [action.text() for action in data_menu.actions() if action.text()]
        expected_actions = ['基准数据管理(&B)', '数据验证(&V)', '数据备份(&K)', '系统日志(&L)']
        
        for expected in expected_actions:
            self.assertIn(expected, data_actions, f"数据菜单项 '{expected}' 不存在")
    
    def test_change_menu_actions(self):
        """测试异动菜单的动作"""
        menu_bar = self.main_window.menuBar()
        change_menu = None
        for action in menu_bar.actions():
            if action.text() == '异动(&C)':
                change_menu = action.menu()
                break
        
        self.assertIsNotNone(change_menu, "异动菜单不存在")
        
        # 检查异动菜单项
        change_actions = [action.text() for action in change_menu.actions() if action.text()]
        expected_actions = ['异动检测(&D)', '新增人员(&N)', '离职人员(&L)', '工资调整(&S)', '检测结果(&R)']
        
        for expected in expected_actions:
            self.assertIn(expected, change_actions, f"异动菜单项 '{expected}' 不存在")
    
    def test_report_menu_actions(self):
        """测试报告菜单的动作"""
        menu_bar = self.main_window.menuBar()
        report_menu = None
        for action in menu_bar.actions():
            if action.text() == '报告(&R)':
                report_menu = action.menu()
                break
        
        self.assertIsNotNone(report_menu, "报告菜单不存在")
        
        # 检查报告菜单项
        report_actions = [action.text() for action in report_menu.actions() if action.text()]
        expected_actions = ['生成Word报告(&W)', '生成Excel分析表(&E)', '自定义报告(&C)', '报告模板管理(&T)', '报告管理窗口(&M)']
        
        for expected in expected_actions:
            self.assertIn(expected, report_actions, f"报告菜单项 '{expected}' 不存在")
    
    def test_settings_menu_actions(self):
        """测试设置菜单的动作"""
        menu_bar = self.main_window.menuBar()
        settings_menu = None
        for action in menu_bar.actions():
            if action.text() == '设置(&S)':
                settings_menu = action.menu()
                break
        
        self.assertIsNotNone(settings_menu, "设置菜单不存在")
        
        # 检查设置菜单项
        settings_actions = [action.text() for action in settings_menu.actions() if action.text()]
        expected_actions = ['用户设置(&U)', '系统设置(&S)', '偏好设置(&P)']
        
        for expected in expected_actions:
            self.assertIn(expected, settings_actions, f"设置菜单项 '{expected}' 不存在")
    
    def test_toolbar_actions(self):
        """测试工具栏动作"""
        # 获取工具栏
        toolbar = None
        for child in self.main_window.children():
            if hasattr(child, 'actions'):
                toolbar = child
                break
        
        self.assertIsNotNone(toolbar, "工具栏不存在")
        
        # 检查工具栏按钮
        toolbar_actions = [action.text() for action in toolbar.actions() if action.text()]
        expected_actions = ['导入数据', '异动检测', '生成报告', '数据验证', '基准管理', '查看结果', 'Excel报告', '系统设置']
        
        for expected in expected_actions:
            self.assertIn(expected, toolbar_actions, f"工具栏按钮 '{expected}' 不存在")
    
    def test_shortcut_keys(self):
        """测试快捷键设置"""
        # 创建快捷键映射
        expected_shortcuts = {
            'Ctrl+I': '导入数据(&I)',
            'Ctrl+E': '导出数据(&E)',
            'Ctrl+Q': '退出(&X)',
            'Ctrl+Shift+V': '数据验证(&V)',
            'F5': '异动检测(&D)',
            'F6': '生成Word报告(&W)',
            'F7': '生成Excel分析表(&E)',
            'F8': '报告管理窗口(&M)',
            'Ctrl+R': '检测结果(&R)',
            'Ctrl+,': '偏好设置(&P)',
            'F1': '用户手册(&M)'
        }
        
        # 获取所有动作
        all_actions = []
        menu_bar = self.main_window.menuBar()
        for menu_action in menu_bar.actions():
            if menu_action.menu():
                all_actions.extend(menu_action.menu().actions())
        
        # 检查快捷键
        action_shortcuts = {}
        for action in all_actions:
            if action.shortcut().toString():
                action_shortcuts[action.shortcut().toString()] = action.text()
        
        for shortcut, action_text in expected_shortcuts.items():
            self.assertIn(shortcut, action_shortcuts, f"快捷键 '{shortcut}' 未设置")
            self.assertEqual(action_shortcuts[shortcut], action_text, 
                           f"快捷键 '{shortcut}' 对应的动作不正确")


class TestNavigationPanel(unittest.TestCase):
    """导航面板测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def setUp(self):
        """每个测试方法的初始化"""
        # 模拟依赖
        self.mock_table_manager = Mock(spec=DynamicTableManager)
        self.mock_comm_manager = Mock(spec=ComponentCommunicationManager)
        
        # 模拟导航数据
        self.mock_table_manager.get_navigation_tree_data.return_value = {
            2025: {
                5: [
                    {'display_name': '全部在职人员', 'icon': '👥'},
                    {'display_name': '离休人员', 'icon': '🏖️'},
                    {'display_name': '退休人员', 'icon': '🏠'}
                ]
            }
        }
        
        self.navigation_panel = EnhancedNavigationPanel(
            self.mock_table_manager, 
            self.mock_comm_manager
        )
    
    def tearDown(self):
        """测试后清理"""
        self.navigation_panel.close()
    
    def test_navigation_panel_structure(self):
        """测试导航面板结构"""
        # 检查主要导航项
        tree_widget = self.navigation_panel.tree_widget
        
        # 获取根级项目
        root_items = []
        for i in range(tree_widget.topLevelItemCount()):
            item = tree_widget.topLevelItem(i)
            root_items.append(item.text(0))
        
        # 验证只包含数据导航，不包含功能导航
        expected_data_navigation = ['📊 工资表', '📈 异动人员表']
        unexpected_function_navigation = ['📁 数据导入', '🔍 异动检测', '📋 报告生成', '⚙️ 系统管理']
        
        for expected in expected_data_navigation:
            self.assertIn(expected, root_items, f"数据导航项 '{expected}' 不存在")
        
        for unexpected in unexpected_function_navigation:
            self.assertNotIn(unexpected, root_items, f"功能导航项 '{unexpected}' 不应存在")
    
    def test_salary_table_navigation(self):
        """测试工资表导航结构"""
        tree_widget = self.navigation_panel.tree_widget
        
        # 查找工资表项目
        salary_item = None
        for i in range(tree_widget.topLevelItemCount()):
            item = tree_widget.topLevelItem(i)
            if '工资表' in item.text(0):
                salary_item = item
                break
        
        self.assertIsNotNone(salary_item, "工资表导航项不存在")
        
        # 检查年份子项
        year_items = []
        for i in range(salary_item.childCount()):
            child = salary_item.child(i)
            year_items.append(child.text(0))
        
        expected_years = ['📅 2025年', '📅 2024年']
        for expected in expected_years:
            self.assertIn(expected, year_items, f"年份导航项 '{expected}' 不存在")
    
    def test_change_table_navigation(self):
        """测试异动人员表导航结构"""
        tree_widget = self.navigation_panel.tree_widget
        
        # 查找异动人员表项目
        change_item = None
        for i in range(tree_widget.topLevelItemCount()):
            item = tree_widget.topLevelItem(i)
            if '异动人员表' in item.text(0):
                change_item = item
                break
        
        self.assertIsNotNone(change_item, "异动人员表导航项不存在")
        
        # 检查年份子项
        year_items = []
        for i in range(change_item.childCount()):
            child = change_item.child(i)
            year_items.append(child.text(0))
        
        # 至少应该有2025年的数据
        self.assertIn('📅 2025年', year_items, "2025年异动数据导航项不存在")
    
    def test_functional_navigation_removed(self):
        """测试功能性导航项已被移除"""
        tree_widget = self.navigation_panel.tree_widget
        
        # 获取所有导航项文本
        all_items = []
        
        def collect_items(parent_item, items_list):
            if parent_item is None:
                # 遍历根级项目
                for i in range(tree_widget.topLevelItemCount()):
                    item = tree_widget.topLevelItem(i)
                    items_list.append(item.text(0))
                    collect_items(item, items_list)
            else:
                # 遍历子项目
                for i in range(parent_item.childCount()):
                    child = parent_item.child(i)
                    items_list.append(child.text(0))
                    collect_items(child, items_list)
        
        collect_items(None, all_items)
        
        # 验证功能性导航项不存在
        functional_items = [
            '数据导入', 'Excel文件导入', '数据验证', '导入历史',
            '异动检测', '基准数据管理', '异动分析', '检测结果',
            '报告生成', 'Word报告', 'Excel分析表', '自定义报告', '报告模板',
            '系统管理', '用户设置', '数据备份', '系统日志'
        ]
        
        for func_item in functional_items:
            for nav_item in all_items:
                self.assertNotIn(func_item, nav_item, 
                               f"功能导航项 '{func_item}' 不应该存在于导航面板中")


class TestFunctionalAccessibility(unittest.TestCase):
    """功能可访问性测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def setUp(self):
        """每个测试方法的初始化"""
        # 模拟应用服务
        with patch('src.gui.main_window_refactored.ApplicationService') as mock_service:
            mock_service.return_value.initialize.return_value.success = True
            self.main_window = MainWindow()
            self.main_window.app_service = mock_service.return_value
    
    def tearDown(self):
        """测试后清理"""
        self.main_window.close()
    
    def test_all_functions_accessible_via_menu(self):
        """测试所有功能都可以通过菜单访问"""
        # 定义应该可访问的功能
        expected_functions = [
            # 数据导入相关
            'show_import_dialog', 'show_import_history', 'import_baseline_data', 
            'validate_imported_data', 'export_change_data',
            
            # 异动检测相关
            'start_change_detection', 'show_new_staff_changes', 'show_leaving_staff_changes',
            'show_salary_adjustments', 'show_detection_window',
            
            # 报告生成相关
            'generate_word_report', 'generate_excel_report', 'generate_custom_report',
            'manage_templates', 'show_report_window',
            
            # 系统设置相关
            'show_user_settings', 'show_settings_dialog', 'show_preferences_dialog',
            'backup_data', 'show_system_log',
            
            # 帮助相关
            'show_user_manual', 'show_about_dialog'
        ]
        
        # 检查每个功能是否存在于主窗口类中
        for func_name in expected_functions:
            self.assertTrue(hasattr(self.main_window, func_name), 
                          f"功能方法 '{func_name}' 不存在")
            self.assertTrue(callable(getattr(self.main_window, func_name)), 
                          f"'{func_name}' 不是可调用的方法")
    
    @patch('src.gui.main_window_refactored.QMessageBox')
    def test_menu_action_triggers_functions(self, mock_msgbox):
        """测试菜单动作能正确触发对应功能"""
        # 模拟应用服务方法
        self.main_window.app_service.get_import_history = Mock()
        self.main_window.app_service.get_import_history.return_value.success = False
        self.main_window.app_service.get_import_history.return_value.data = None
        
        # 测试导入历史功能
        self.main_window.show_import_history()
        
        # 验证应用服务被调用
        self.main_window.app_service.get_import_history.assert_called_once()
        
        # 验证消息框被调用（因为没有历史数据）
        mock_msgbox.information.assert_called()


def run_navigation_refactor_tests():
    """运行导航重构测试套件"""
    print("开始导航重构功能测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_suite.addTest(unittest.makeSuite(TestNavigationRefactor))
    test_suite.addTest(unittest.makeSuite(TestNavigationPanel))
    test_suite.addTest(unittest.makeSuite(TestFunctionalAccessibility))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    print(f"\n{'='*60}")
    print("导航重构测试结果摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n失败的测试:")
        for test, error in result.failures:
            print(f"- {test}: {error.split('\n')[-2]}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, error in result.errors:
            print(f"- {test}: {error.split('\n')[-2]}")
    
    print(f"{'='*60}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    # 运行测试
    success = run_navigation_refactor_tests()
    sys.exit(0 if success else 1) 