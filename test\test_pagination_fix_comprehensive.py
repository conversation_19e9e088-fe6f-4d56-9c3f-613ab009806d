#!/usr/bin/env python3
"""
分页功能修复综合测试

根据.specstory文档的分析，全面测试修复后的分页功能：
1. 中文表头和英文表头的分页一致性
2. 字段映射的正确应用
3. 表级字段偏好的正确处理
4. 分页前后字段数量和类型的一致性
"""

import sys
import os
from pathlib import Path
import unittest
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.system_config.config_manager import ConfigManager


class TestPaginationFixComprehensive(unittest.TestCase):
    """分页功能修复综合测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.config_manager = ConfigManager()
        cls.db_manager = DatabaseManager(config_manager=cls.config_manager)
        cls.config_sync = ConfigSyncManager()
        cls.table_manager = DynamicTableManager(
            db_manager=cls.db_manager, 
            config_manager=cls.config_manager
        )
        
        # 测试表名
        cls.test_tables = {
            "salary_data_2026_01_a_grade_employees": "英文表头表（无映射）",
            "salary_data_2026_04_a_grade_employees": "中文表头表（有映射）"
        }
        
        print("=" * 80)
        print("分页功能修复综合测试")
        print("=" * 80)
    
    def test_01_table_existence(self):
        """测试1：验证测试表存在性"""
        print("\n🔍 测试1：验证测试表存在性")
        print("-" * 50)
        
        for table_name, description in self.test_tables.items():
            with self.subTest(table=table_name):
                exists = self.db_manager.table_exists(table_name)
                print(f"  {description}: {'✅ 存在' if exists else '❌ 不存在'}")
                self.assertTrue(exists, f"测试表 {table_name} 不存在")
    
    def test_02_field_mapping_configuration(self):
        """测试2：验证字段映射配置"""
        print("\n🔍 测试2：验证字段映射配置")
        print("-" * 50)
        
        for table_name, description in self.test_tables.items():
            if not self.db_manager.table_exists(table_name):
                continue
                
            with self.subTest(table=table_name):
                field_mapping = self.config_sync.get_mapping(table_name)
                has_mapping = field_mapping is not None and len(field_mapping) > 0
                
                print(f"  {description}:")
                print(f"    字段映射: {'✅ 有' if has_mapping else '❌ 无'}")
                
                if has_mapping:
                    print(f"    映射数量: {len(field_mapping)}")
                    # 验证映射的有效性
                    self.assertIsInstance(field_mapping, dict)
                    self.assertGreater(len(field_mapping), 0)
    
    def test_03_table_field_preference(self):
        """测试3：验证表级字段偏好"""
        print("\n🔍 测试3：验证表级字段偏好")
        print("-" * 50)
        
        for table_name, description in self.test_tables.items():
            if not self.db_manager.table_exists(table_name):
                continue
                
            with self.subTest(table=table_name):
                table_preference = self.config_sync.get_table_field_preference(table_name)
                has_preference = table_preference is not None and len(table_preference) > 0
                
                print(f"  {description}:")
                print(f"    表级偏好: {'✅ 有' if has_preference else '❌ 无'}")
                
                if has_preference:
                    print(f"    偏好字段数: {len(table_preference)}")
                    print(f"    偏好字段: {table_preference[:3]}...")
                    # 验证偏好的有效性
                    self.assertIsInstance(table_preference, list)
                    self.assertGreater(len(table_preference), 0)
    
    def test_04_pagination_field_consistency(self):
        """测试4：验证分页字段一致性（核心测试）"""
        print("\n🔍 测试4：验证分页字段一致性（核心测试）")
        print("-" * 50)
        
        for table_name, description in self.test_tables.items():
            if not self.db_manager.table_exists(table_name):
                continue
                
            with self.subTest(table=table_name):
                print(f"  {description}:")
                
                # 获取多页数据
                df_page1, total_count = self.table_manager.get_dataframe_paginated(
                    table_name, 1, 20
                )
                df_page2, _ = self.table_manager.get_dataframe_paginated(
                    table_name, 2, 20
                )
                df_page3, _ = self.table_manager.get_dataframe_paginated(
                    table_name, 3, 20
                )
                
                # 验证数据加载成功
                self.assertIsNotNone(df_page1, "第1页数据加载失败")
                self.assertIsNotNone(df_page2, "第2页数据加载失败")
                self.assertIsNotNone(df_page3, "第3页数据加载失败")
                
                # 验证字段一致性
                page1_fields = list(df_page1.columns)
                page2_fields = list(df_page2.columns)
                page3_fields = list(df_page3.columns)
                
                print(f"    第1页字段数: {len(page1_fields)}")
                print(f"    第2页字段数: {len(page2_fields)}")
                print(f"    第3页字段数: {len(page3_fields)}")
                
                # 核心断言：字段必须完全一致
                self.assertEqual(page1_fields, page2_fields, 
                    f"第1页和第2页字段不一致\n第1页: {page1_fields}\n第2页: {page2_fields}")
                self.assertEqual(page1_fields, page3_fields, 
                    f"第1页和第3页字段不一致\n第1页: {page1_fields}\n第3页: {page3_fields}")
                
                print(f"    ✅ 字段一致性验证通过")
                print(f"    总记录数: {total_count}")
    
    def test_05_field_mapping_application(self):
        """测试5：验证字段映射应用"""
        print("\n🔍 测试5：验证字段映射应用")
        print("-" * 50)
        
        # 重点测试有映射的表
        table_name = "salary_data_2026_04_a_grade_employees"
        if not self.db_manager.table_exists(table_name):
            self.skipTest(f"测试表 {table_name} 不存在")
        
        # 获取原始数据
        df_raw, _ = self.table_manager.get_dataframe_paginated(table_name, 1, 10)
        self.assertIsNotNone(df_raw, "原始数据加载失败")
        
        # 获取字段映射
        field_mapping = self.config_sync.get_mapping(table_name)
        self.assertIsNotNone(field_mapping, "字段映射不存在")
        
        print(f"  原始字段数: {len(df_raw.columns)}")
        print(f"  映射规则数: {len(field_mapping)}")
        
        # 模拟应用字段映射
        mapped_columns = []
        for col in df_raw.columns:
            if col in field_mapping:
                mapped_columns.append(field_mapping[col])
            else:
                mapped_columns.append(col)
        
        print(f"  映射后字段示例: {mapped_columns[:5]}")
        
        # 验证映射应用的正确性
        self.assertEqual(len(mapped_columns), len(df_raw.columns), "映射后字段数量不匹配")
        
        # 验证中文字段名存在
        chinese_fields = [col for col in mapped_columns if any('\u4e00' <= char <= '\u9fff' for char in col)]
        self.assertGreater(len(chinese_fields), 0, "映射后应该包含中文字段名")
        
        print(f"  ✅ 字段映射应用验证通过，包含 {len(chinese_fields)} 个中文字段")
    
    def test_06_table_preference_filtering(self):
        """测试6：验证表级偏好过滤"""
        print("\n🔍 测试6：验证表级偏好过滤")
        print("-" * 50)
        
        # 重点测试有偏好设置的表
        table_name = "salary_data_2026_04_a_grade_employees"
        if not self.db_manager.table_exists(table_name):
            self.skipTest(f"测试表 {table_name} 不存在")
        
        # 获取原始数据
        df_raw, _ = self.table_manager.get_dataframe_paginated(table_name, 1, 10)
        self.assertIsNotNone(df_raw, "原始数据加载失败")
        
        # 获取表级偏好
        table_preference = self.config_sync.get_table_field_preference(table_name)
        
        if table_preference:
            print(f"  原始字段数: {len(df_raw.columns)}")
            print(f"  偏好字段数: {len(table_preference)}")
            
            # 模拟应用偏好过滤
            available_fields = [field for field in table_preference if field in df_raw.columns]
            
            if available_fields:
                df_filtered = df_raw[available_fields]
                print(f"  过滤后字段数: {len(df_filtered.columns)}")
                print(f"  过滤后字段: {list(df_filtered.columns)}")
                
                # 验证过滤的正确性
                self.assertEqual(len(df_filtered.columns), len(available_fields), "过滤后字段数量不匹配")
                self.assertTrue(all(field in df_raw.columns for field in df_filtered.columns), 
                    "过滤后的字段必须都存在于原始数据中")
                
                print(f"  ✅ 表级偏好过滤验证通过")
            else:
                print(f"  ⚠️ 偏好字段与实际字段不匹配")
        else:
            print(f"  ℹ️ 该表无表级偏好设置")
    
    def test_07_end_to_end_pagination_workflow(self):
        """测试7：端到端分页工作流程"""
        print("\n🔍 测试7：端到端分页工作流程")
        print("-" * 50)
        
        for table_name, description in self.test_tables.items():
            if not self.db_manager.table_exists(table_name):
                continue
                
            with self.subTest(table=table_name):
                print(f"  {description}:")
                
                # 步骤1：数据加载（模拟PaginationWorker）
                df_page1, total_count = self.table_manager.get_dataframe_paginated(
                    table_name, 1, 25
                )
                df_page2, _ = self.table_manager.get_dataframe_paginated(
                    table_name, 2, 25
                )
                
                self.assertIsNotNone(df_page1, "第1页数据加载失败")
                self.assertIsNotNone(df_page2, "第2页数据加载失败")
                
                # 步骤2：字段映射应用（模拟显示层）
                field_mapping = self.config_sync.get_mapping(table_name)
                if field_mapping:
                    # 应用映射到两页数据
                    df_page1_mapped = self._apply_field_mapping(df_page1, field_mapping)
                    df_page2_mapped = self._apply_field_mapping(df_page2, field_mapping)
                else:
                    df_page1_mapped = df_page1
                    df_page2_mapped = df_page2
                
                # 步骤3：表级偏好过滤（模拟显示层）
                table_preference = self.config_sync.get_table_field_preference(table_name)
                if table_preference:
                    available_fields = [field for field in table_preference 
                                      if field in df_page1_mapped.columns]
                    if available_fields:
                        df_page1_final = df_page1_mapped[available_fields]
                        df_page2_final = df_page2_mapped[available_fields]
                    else:
                        df_page1_final = df_page1_mapped
                        df_page2_final = df_page2_mapped
                else:
                    df_page1_final = df_page1_mapped
                    df_page2_final = df_page2_mapped
                
                # 步骤4：最终验证
                final_page1_fields = list(df_page1_final.columns)
                final_page2_fields = list(df_page2_final.columns)
                
                print(f"    最终第1页字段数: {len(final_page1_fields)}")
                print(f"    最终第2页字段数: {len(final_page2_fields)}")
                
                # 核心断言：最终显示的字段必须一致
                self.assertEqual(final_page1_fields, final_page2_fields, 
                    f"最终显示字段不一致\n第1页: {final_page1_fields}\n第2页: {final_page2_fields}")
                
                print(f"    ✅ 端到端工作流程验证通过")
    
    def _apply_field_mapping(self, df: pd.DataFrame, field_mapping: dict) -> pd.DataFrame:
        """应用字段映射"""
        if not field_mapping:
            return df
        
        column_rename_map = {}
        for col in df.columns:
            if col in field_mapping:
                column_rename_map[col] = field_mapping[col]
        
        if column_rename_map:
            return df.rename(columns=column_rename_map)
        return df


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
