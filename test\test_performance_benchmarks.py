"""
性能基准测试

建立系统性能基准测试机制，监控关键性能指标，确保系统性能满足要求。

创建时间: 2025-01-22 (REFLECT Day 4)
目标: 建立性能基准测试体系 (R3-002)
重点: 内存使用、处理速度、响应时间等关键指标
"""

import sys
import time
import psutil
import unittest
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
from unittest.mock import Mock, patch

# 添加src目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.core.application_service import ApplicationService
from src.modules.change_detection import ChangeDetector, BaselineManager
from src.modules.data_import import ExcelImporter, DataValidator
from src.core.config_manager import ConfigManager


class PerformanceBenchmark:
    """性能基准测试工具类"""
    
    def __init__(self):
        """初始化性能基准测试"""
        self.process = psutil.Process()
        self.start_time = None
        self.start_memory = None
        self.benchmarks = {}
    
    def start_benchmark(self, test_name: str):
        """开始性能基准测试"""
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        print(f"🚀 开始性能测试: {test_name}")
    
    def end_benchmark(self, test_name: str) -> Dict[str, Any]:
        """结束性能基准测试"""
        end_time = time.time()
        end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        duration = end_time - self.start_time
        memory_used = end_memory - self.start_memory
        
        result = {
            "test_name": test_name,
            "duration_seconds": round(duration, 3),
            "memory_used_mb": round(memory_used, 2),
            "peak_memory_mb": round(end_memory, 2),
            "timestamp": datetime.now().isoformat()
        }
        
        self.benchmarks[test_name] = result
        
        print(f"⏱️ 测试完成: {test_name}")
        print(f"   • 执行时间: {duration:.3f}秒")
        print(f"   • 内存使用: {memory_used:.2f}MB")
        print(f"   • 峰值内存: {end_memory:.2f}MB")
        
        return result
    
    def get_benchmark_summary(self) -> Dict[str, Any]:
        """获取性能基准测试总结"""
        if not self.benchmarks:
            return {
                "total_tests": 0, 
                "total_duration": 0.0,
                "total_memory_used": 0.0,
                "peak_memory": 0.0,
                "average_duration": 0.0,
                "average_memory": 0.0,
                "summary": "无测试数据"
            }
        
        total_duration = sum(b["duration_seconds"] for b in self.benchmarks.values())
        total_memory = sum(b["memory_used_mb"] for b in self.benchmarks.values())
        max_memory = max(b["peak_memory_mb"] for b in self.benchmarks.values())
        
        return {
            "total_tests": len(self.benchmarks),
            "total_duration": round(total_duration, 3),
            "total_memory_used": round(total_memory, 2),
            "peak_memory": round(max_memory, 2),
            "average_duration": round(total_duration / len(self.benchmarks), 3),
            "average_memory": round(total_memory / len(self.benchmarks), 2)
        }


class TestPerformanceBenchmarks(unittest.TestCase):
    """性能基准测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.benchmark = PerformanceBenchmark()
        
        # 创建测试数据
        self.create_test_data()
        
        print("=" * 60)
        print("🏎️ 性能基准测试开始")
        print("=" * 60)
    
    def create_test_data(self):
        """创建性能测试数据"""
        # 小数据集 (100行)
        self.small_dataset = pd.DataFrame({
            '工号': [f'00{i:03d}' for i in range(1, 101)],
            '姓名': [f'员工{i}' for i in range(1, 101)],
            '基本工资': [5000 + i*100 for i in range(100)],
            '岗位津贴': [1000 + i*10 for i in range(100)],
            '部门': [f'部门{i%10}' for i in range(100)]
        })
        
        # 中等数据集 (1000行)
        self.medium_dataset = pd.DataFrame({
            '工号': [f'{i:06d}' for i in range(1, 1001)],
            '姓名': [f'员工{i}' for i in range(1, 1001)],
            '基本工资': [5000 + i*10 for i in range(1000)],
            '岗位津贴': [1000 + i for i in range(1000)],
            '部门': [f'部门{i%20}' for i in range(1000)]
        })
        
        # 大数据集 (10000行)
        self.large_dataset = pd.DataFrame({
            '工号': [f'{i:07d}' for i in range(1, 10001)],
            '姓名': [f'员工{i}' for i in range(1, 10001)],
            '基本工资': [5000 + (i%5000) for i in range(10000)],
            '岗位津贴': [1000 + (i%1000) for i in range(10000)],
            '部门': [f'部门{i%50}' for i in range(10000)]
        })
        
        print("✅ 测试数据创建完成")
        print(f"   • 小数据集: {len(self.small_dataset)} 行")
        print(f"   • 中等数据集: {len(self.medium_dataset)} 行")
        print(f"   • 大数据集: {len(self.large_dataset)} 行")
    
    def test_application_service_initialization_performance(self):
        """测试应用服务初始化性能"""
        print("\n🚀 测试应用服务初始化性能...")
        
        self.benchmark.start_benchmark("应用服务初始化")
        
        # 模拟应用服务初始化
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            app_service = ApplicationService()
            result = app_service.initialize()
            self.assertTrue(result.success)
        
        benchmark_result = self.benchmark.end_benchmark("应用服务初始化")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 2.0, "初始化时间应小于2秒")
        self.assertLess(benchmark_result["memory_used_mb"], 50.0, "初始化内存使用应小于50MB")
        
        print("🚀 应用服务初始化性能测试通过！")
    
    def test_data_import_performance_small(self):
        """测试小数据集导入性能"""
        print("\n📊 测试小数据集导入性能...")
        
        self.benchmark.start_benchmark("小数据集导入")
        
        # 模拟数据导入过程
        with patch('pandas.read_excel', return_value=self.small_dataset):
            # 这里模拟实际的数据导入逻辑
            time.sleep(0.1)  # 模拟IO操作
            processed_data = self.small_dataset.copy()
            
            # 模拟数据处理
            processed_data['总工资'] = processed_data['基本工资'] + processed_data['岗位津贴']
        
        benchmark_result = self.benchmark.end_benchmark("小数据集导入")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 1.0, "小数据集导入应小于1秒")
        self.assertLess(benchmark_result["memory_used_mb"], 20.0, "小数据集导入内存使用应小于20MB")
        
        print("📊 小数据集导入性能测试通过！")
    
    def test_data_import_performance_medium(self):
        """测试中等数据集导入性能"""
        print("\n📊 测试中等数据集导入性能...")
        
        self.benchmark.start_benchmark("中等数据集导入")
        
        # 模拟中等数据集导入
        with patch('pandas.read_excel', return_value=self.medium_dataset):
            time.sleep(0.3)  # 模拟较长IO操作
            processed_data = self.medium_dataset.copy()
            
            # 模拟复杂数据处理
            processed_data['总工资'] = processed_data['基本工资'] + processed_data['岗位津贴']
            processed_data['工资等级'] = processed_data['总工资'].apply(
                lambda x: '高' if x > 8000 else '中' if x > 6000 else '低'
            )
        
        benchmark_result = self.benchmark.end_benchmark("中等数据集导入")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 3.0, "中等数据集导入应小于3秒")
        self.assertLess(benchmark_result["memory_used_mb"], 100.0, "中等数据集导入内存使用应小于100MB")
        
        print("📊 中等数据集导入性能测试通过！")
    
    def test_data_import_performance_large(self):
        """测试大数据集导入性能"""
        print("\n📊 测试大数据集导入性能...")
        
        self.benchmark.start_benchmark("大数据集导入")
        
        # 模拟大数据集导入
        with patch('pandas.read_excel', return_value=self.large_dataset):
            time.sleep(1.0)  # 模拟长时间IO操作
            processed_data = self.large_dataset.copy()
            
            # 模拟复杂数据处理和聚合
            processed_data['总工资'] = processed_data['基本工资'] + processed_data['岗位津贴']
            dept_summary = processed_data.groupby('部门').agg({
                '总工资': ['mean', 'sum', 'count']
            })
        
        benchmark_result = self.benchmark.end_benchmark("大数据集导入")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 10.0, "大数据集导入应小于10秒")
        self.assertLess(benchmark_result["memory_used_mb"], 500.0, "大数据集导入内存使用应小于500MB")
        
        print("📊 大数据集导入性能测试通过！")
    
    def test_change_detection_performance(self):
        """测试异动检测性能"""
        print("\n🔍 测试异动检测性能...")
        
        self.benchmark.start_benchmark("异动检测")
        
        # 模拟异动检测过程
        baseline_data = self.medium_dataset.copy()
        current_data = self.medium_dataset.copy()
        
        # 模拟数据变更
        current_data.loc[0:50, '基本工资'] += 500  # 模拟工资调整
        current_data = current_data.iloc[10:]  # 模拟人员离职
        
        # 添加新员工
        new_employees = pd.DataFrame({
            '工号': ['999901', '999902', '999903'],
            '姓名': ['新员工1', '新员工2', '新员工3'],
            '基本工资': [5500, 5600, 5700],
            '岗位津贴': [1100, 1200, 1300],
            '部门': ['新部门', '新部门', '新部门']
        })
        current_data = pd.concat([current_data, new_employees], ignore_index=True)
        
        # 模拟异动检测算法
        changes = []
        
        # 检测工资变更
        for index, row in current_data.iterrows():
            工号 = row['工号']
            if 工号 in baseline_data['工号'].values:
                baseline_row = baseline_data[baseline_data['工号'] == 工号].iloc[0]
                if row['基本工资'] != baseline_row['基本工资']:
                    changes.append({
                        'employee_id': 工号,
                        'change_type': '工资调整',
                        'old_value': baseline_row['基本工资'],
                        'new_value': row['基本工资']
                    })
        
        # 检测新进人员
        for index, row in current_data.iterrows():
            if row['工号'] not in baseline_data['工号'].values:
                changes.append({
                    'employee_id': row['工号'],
                    'change_type': '新进人员',
                    'salary': row['基本工资']
                })
        
        benchmark_result = self.benchmark.end_benchmark("异动检测")
        
        # 验证检测结果
        self.assertGreater(len(changes), 0, "应该检测到异动")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 5.0, "异动检测应小于5秒")
        self.assertLess(benchmark_result["memory_used_mb"], 200.0, "异动检测内存使用应小于200MB")
        
        print(f"✅ 检测到 {len(changes)} 个异动")
        print("🔍 异动检测性能测试通过！")
    
    def test_report_generation_performance(self):
        """测试报告生成性能"""
        print("\n📄 测试报告生成性能...")
        
        self.benchmark.start_benchmark("报告生成")
        
        # 模拟报告数据准备
        report_data = {
            'changes': [
                {'employee_id': '001', 'change_type': '工资调整', 'amount': 500},
                {'employee_id': '002', 'change_type': '新进人员', 'amount': 5500},
                {'employee_id': '003', 'change_type': '津贴变更', 'amount': 200}
            ],
            'summary': {
                'total_changes': 3,
                'salary_adjustments': 1,
                'new_employees': 1,
                'allowance_changes': 1
            }
        }
        
        # 模拟Word报告生成
        time.sleep(0.5)  # 模拟文档处理时间
        
        # 模拟Excel报告生成
        df_report = pd.DataFrame(report_data['changes'])
        time.sleep(0.2)  # 模拟Excel写入时间
        
        benchmark_result = self.benchmark.end_benchmark("报告生成")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 3.0, "报告生成应小于3秒")
        self.assertLess(benchmark_result["memory_used_mb"], 100.0, "报告生成内存使用应小于100MB")
        
        print("📄 报告生成性能测试通过！")
    
    def test_concurrent_operations_performance(self):
        """测试并发操作性能"""
        print("\n🔄 测试并发操作性能...")
        
        self.benchmark.start_benchmark("并发操作")
        
        # 模拟并发操作场景
        operations = []
        
        # 操作1: 数据导入
        start_time = time.time()
        processed_data = self.small_dataset.copy()
        processed_data['处理时间'] = datetime.now()
        operations.append(("数据导入", time.time() - start_time))
        
        # 操作2: 异动检测
        start_time = time.time()
        changes = []
        for i in range(10):
            changes.append({
                'employee_id': f'00{i}',
                'change_type': '模拟异动',
                'timestamp': datetime.now()
            })
        operations.append(("异动检测", time.time() - start_time))
        
        # 操作3: 数据验证
        start_time = time.time()
        validation_results = {
            'valid_records': len(processed_data),
            'invalid_records': 0,
            'validation_time': datetime.now()
        }
        operations.append(("数据验证", time.time() - start_time))
        
        benchmark_result = self.benchmark.end_benchmark("并发操作")
        
        # 性能基准验证
        self.assertLess(benchmark_result["duration_seconds"], 2.0, "并发操作应小于2秒")
        
        print(f"✅ 完成 {len(operations)} 个并发操作")
        for op_name, op_time in operations:
            print(f"   • {op_name}: {op_time:.3f}秒")
        
        print("🔄 并发操作性能测试通过！")
    
    def test_memory_usage_patterns(self):
        """测试内存使用模式"""
        print("\n🧠 测试内存使用模式...")
        
        self.benchmark.start_benchmark("内存使用模式")
        
        memory_snapshots = []
        
        # 初始内存
        initial_memory = self.benchmark.process.memory_info().rss / 1024 / 1024
        memory_snapshots.append(("初始状态", initial_memory))
        
        # 加载小数据集
        data1 = self.small_dataset.copy()
        current_memory = self.benchmark.process.memory_info().rss / 1024 / 1024
        memory_snapshots.append(("小数据集", current_memory))
        
        # 加载中等数据集
        data2 = self.medium_dataset.copy()
        current_memory = self.benchmark.process.memory_info().rss / 1024 / 1024
        memory_snapshots.append(("中等数据集", current_memory))
        
        # 数据处理
        processed = pd.concat([data1, data2], ignore_index=True)
        processed['总工资'] = processed['基本工资'] + processed['岗位津贴']
        current_memory = self.benchmark.process.memory_info().rss / 1024 / 1024
        memory_snapshots.append(("数据处理后", current_memory))
        
        # 清理数据
        del data1, data2, processed
        current_memory = self.benchmark.process.memory_info().rss / 1024 / 1024
        memory_snapshots.append(("清理后", current_memory))
        
        benchmark_result = self.benchmark.end_benchmark("内存使用模式")
        
        # 分析内存使用模式
        max_memory = max(snapshot[1] for snapshot in memory_snapshots)
        memory_growth = max_memory - initial_memory
        
        print("📊 内存使用快照:")
        for stage, memory in memory_snapshots:
            print(f"   • {stage}: {memory:.2f}MB")
        
        print(f"💾 最大内存增长: {memory_growth:.2f}MB")
        
        # 性能基准验证
        self.assertLess(memory_growth, 1000.0, "内存增长应小于1GB")
        
        print("🧠 内存使用模式测试通过！")
    
    def test_performance_regression_check(self):
        """性能回归检查"""
        print("\n📈 性能回归检查...")
        
        # 定义性能基准线 (期望性能指标)
        performance_baselines = {
            "max_initialization_time": 2.0,  # 秒
            "max_small_data_import_time": 1.0,  # 秒
            "max_change_detection_time": 5.0,  # 秒
            "max_report_generation_time": 3.0,  # 秒
            "max_memory_usage": 500.0,  # MB
        }
        
        # 获取当前测试的性能数据
        current_performance = self.benchmark.get_benchmark_summary()
        
        regression_results = []
        
        # 检查是否有性能回归
        for test_name, result in self.benchmark.benchmarks.items():
            duration = result["duration_seconds"]
            memory = result["memory_used_mb"]
            
            # 根据测试类型检查基准线
            if "初始化" in test_name:
                if duration > performance_baselines["max_initialization_time"]:
                    regression_results.append(f"初始化时间回归: {duration:.3f}s > {performance_baselines['max_initialization_time']}s")
            
            elif "小数据集" in test_name:
                if duration > performance_baselines["max_small_data_import_time"]:
                    regression_results.append(f"小数据集导入时间回归: {duration:.3f}s > {performance_baselines['max_small_data_import_time']}s")
            
            elif "异动检测" in test_name:
                if duration > performance_baselines["max_change_detection_time"]:
                    regression_results.append(f"异动检测时间回归: {duration:.3f}s > {performance_baselines['max_change_detection_time']}s")
            
            elif "报告生成" in test_name:
                if duration > performance_baselines["max_report_generation_time"]:
                    regression_results.append(f"报告生成时间回归: {duration:.3f}s > {performance_baselines['max_report_generation_time']}s")
            
            # 检查内存使用
            if memory > performance_baselines["max_memory_usage"]:
                regression_results.append(f"{test_name}内存使用回归: {memory:.2f}MB > {performance_baselines['max_memory_usage']}MB")
        
        # 输出回归检查结果
        if regression_results:
            print("⚠️ 发现性能回归:")
            for regression in regression_results:
                print(f"   • {regression}")
        else:
            print("✅ 未发现性能回归")
        
        # 输出性能总结
        print("\n📊 性能测试总结:")
        print(f"   • 总测试数: {current_performance['total_tests']}")
        print(f"   • 总执行时间: {current_performance['total_duration']}秒")
        print(f"   • 总内存使用: {current_performance['total_memory_used']}MB")
        print(f"   • 平均执行时间: {current_performance['average_duration']}秒")
        print(f"   • 平均内存使用: {current_performance['average_memory']}MB")
        
        # 断言：确保没有严重的性能回归
        serious_regressions = [r for r in regression_results if "时间回归" in r]
        self.assertEqual(len(serious_regressions), 0, f"发现严重性能回归: {serious_regressions}")
        
        print("📈 性能回归检查完成！")
    
    def tearDown(self):
        """测试清理"""
        # 输出最终性能报告
        summary = self.benchmark.get_benchmark_summary()
        
        print("\n" + "=" * 60)
        print("🏁 性能基准测试全部完成！")
        print("=" * 60)
        print("📊 最终性能报告:")
        print(f"   • 测试总数: {summary['total_tests']}")
        print(f"   • 总执行时间: {summary['total_duration']}秒")
        print(f"   • 总内存使用: {summary['total_memory_used']}MB")
        print(f"   • 峰值内存: {summary['peak_memory']}MB")
        print(f"   • 平均性能: {summary['average_duration']}秒/{summary['average_memory']}MB")
        print("=" * 60)


def run_performance_tests():
    """运行性能测试"""
    # 设置测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPerformanceBenchmarks)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_performance_tests()
    sys.exit(0 if success else 1) 