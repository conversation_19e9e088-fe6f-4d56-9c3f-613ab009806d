"""
PyQt5重构高保真原型 - Phase 2测试程序

测试智能展开算法、智能防抖搜索和增强导航面板的功能。
验证智能算法的性能、准确性和用户体验优化效果。

测试内容:
1. SmartTreeExpansion智能展开算法测试
2. SmartSearchDebounce智能防抖搜索测试
3. EnhancedNavigationPanel集成测试
4. 智能算法性能基准测试
5. 用户体验模拟测试
"""

import sys
import time
import random
from typing import Dict, List, Any
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
    QLabel, QPushButton, QTextEdit, QGroupBox, QProgressBar, QFrame
)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import logging

from src.utils.log_config import setup_logger
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel
from src.gui.prototype.widgets.smart_tree_expansion import SmartTreeExpansion
from src.gui.prototype.widgets.smart_search_debounce import SmartSearchDebounce


class PerformanceBenchmark(QThread):
    """
    性能基准测试线程
    
    测试智能算法的性能指标和响应时间。
    """
    
    benchmark_completed = pyqtSignal(dict)  # (results)
    progress_updated = pyqtSignal(int, str)  # (percentage, status)
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__ + ".PerformanceBenchmark")
        
    def run(self):
        """
        执行性能基准测试
        """
        results = {}
        
        # 1. 智能展开算法性能测试
        self.progress_updated.emit(10, "测试智能展开算法性能...")
        expansion_results = self._test_expansion_performance()
        results['expansion'] = expansion_results
        
        # 2. 智能搜索算法性能测试
        self.progress_updated.emit(40, "测试智能搜索算法性能...")
        search_results = self._test_search_performance()
        results['search'] = search_results
        
        # 3. 预测准确性测试
        self.progress_updated.emit(70, "测试智能预测准确性...")
        prediction_results = self._test_prediction_accuracy()
        results['prediction'] = prediction_results
        
        # 4. 内存使用测试
        self.progress_updated.emit(90, "测试内存使用情况...")
        memory_results = self._test_memory_usage()
        results['memory'] = memory_results
        
        self.progress_updated.emit(100, "基准测试完成")
        self.benchmark_completed.emit(results)
        
        self.logger.info("性能基准测试完成")
    
    def _test_expansion_performance(self) -> Dict[str, Any]:
        """
        测试智能展开算法性能
        
        Returns:
            性能测试结果
        """
        expansion = SmartTreeExpansion()
        
        # 测试访问记录性能
        start_time = time.time()
        for i in range(1000):
            path = f"path_{i % 10}"
            expansion.record_access(path)
        record_time = time.time() - start_time
        
        # 测试预测性能
        start_time = time.time()
        for i in range(100):
            predictions = expansion.predict_expansion_paths()
        predict_time = time.time() - start_time
        
        return {
            'record_access_time': record_time,
            'record_access_rate': 1000 / record_time,
            'predict_time': predict_time,
            'predict_rate': 100 / predict_time,
            'total_paths': len(expansion.path_weights)
        }
    
    def _test_search_performance(self) -> Dict[str, Any]:
        """
        测试智能搜索算法性能
        
        Returns:
            性能测试结果
        """
        search = SmartSearchDebounce()
        
        # 测试搜索延迟计算性能
        queries = ["测试", "search", "智能算法", "PyQt5", "performance"]
        
        start_time = time.time()
        for query in queries * 100:
            delay = search._calculate_smart_delay(query)
        delay_calc_time = time.time() - start_time
        
        # 测试输入模式分析性能
        start_time = time.time()
        for i, query in enumerate(queries * 200):
            search.pattern_analyzer.record_input(query, time.time() + i * 0.1)
        pattern_time = time.time() - start_time
        
        return {
            'delay_calculation_time': delay_calc_time,
            'delay_calculation_rate': 500 / delay_calc_time,
            'pattern_analysis_time': pattern_time,
            'pattern_analysis_rate': 1000 / pattern_time,
            'cache_hit_rate': search.query_cache.get_hit_rate()
        }
    
    def _test_prediction_accuracy(self) -> Dict[str, Any]:
        """
        测试智能预测准确性
        
        Returns:
            预测准确性测试结果
        """
        expansion = SmartTreeExpansion()
        
        # 模拟用户访问模式
        user_patterns = [
            ["数据导入", "数据导入 > Excel文件导入", "数据导入 > 数据验证"],
            ["异动检测", "异动检测 > 异动分析", "异动检测 > 异动分析 > 新增人员"],
            ["报告生成", "报告生成 > Word报告"],
            ["系统管理", "系统管理 > 用户设置"]
        ]
        
        # 训练模式
        for _ in range(10):  # 重复训练
            for pattern in user_patterns:
                for path in pattern:
                    expansion.record_access(path)
                    time.sleep(0.01)  # 模拟时间间隔
        
        # 测试预测准确性
        correct_predictions = 0
        total_predictions = 0
        
        for pattern in user_patterns:
            for i in range(len(pattern) - 1):
                current_path = pattern[i]
                expected_next = pattern[i + 1]
                
                predictions = expansion.predict_expansion_paths()
                predicted_paths = [path for path, _ in predictions]
                
                if expected_next in predicted_paths:
                    correct_predictions += 1
                total_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        return {
            'prediction_accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'learned_patterns': len(expansion.prediction_model.sequence_patterns)
        }
    
    def _test_memory_usage(self) -> Dict[str, Any]:
        """
        测试内存使用情况
        
        Returns:
            内存使用测试结果
        """
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量对象测试内存增长
        expansion = SmartTreeExpansion()
        search = SmartSearchDebounce()
        
        # 添加大量数据
        for i in range(5000):
            expansion.record_access(f"path_{i}")
            search.pattern_analyzer.record_input(f"query_{i}")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        return {
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'memory_increase_mb': memory_increase,
            'memory_per_item_kb': (memory_increase * 1024) / 5000
        }


class UserExperienceSimulator(QThread):
    """
    用户体验模拟器
    
    模拟真实用户操作，测试智能算法的用户体验优化效果。
    """
    
    simulation_completed = pyqtSignal(dict)  # (results)
    interaction_logged = pyqtSignal(str)  # (interaction_log)
    
    def __init__(self, navigation_panel: EnhancedNavigationPanel):
        super().__init__()
        self.navigation_panel = navigation_panel
        self.logger = setup_logger(__name__ + ".UserExperienceSimulator")
        
    def run(self):
        """
        执行用户体验模拟
        """
        results = {}
        
        # 模拟新用户场景
        self.interaction_logged.emit("🚀 开始新用户体验模拟...")
        new_user_results = self._simulate_new_user()
        results['new_user'] = new_user_results
        
        # 模拟熟练用户场景
        self.interaction_logged.emit("👨‍💼 开始熟练用户体验模拟...")
        expert_user_results = self._simulate_expert_user()
        results['expert_user'] = expert_user_results
        
        # 模拟搜索行为
        self.interaction_logged.emit("🔍 开始搜索行为模拟...")
        search_results = self._simulate_search_behavior()
        results['search_behavior'] = search_results
        
        self.simulation_completed.emit(results)
        self.logger.info("用户体验模拟完成")
    
    def _simulate_new_user(self) -> Dict[str, Any]:
        """
        模拟新用户行为
        
        Returns:
            新用户体验测试结果
        """
        interactions = []
        
        # 新用户通常会探索性地浏览
        exploration_paths = [
            "数据导入",
            "异动检测",
            "报告生成",
            "系统管理"
        ]
        
        for path in exploration_paths:
            start_time = time.time()
            self.navigation_panel.navigate_to_path(path)
            response_time = time.time() - start_time
            
            interactions.append({
                'action': 'navigate',
                'path': path,
                'response_time': response_time
            })
            
            self.interaction_logged.emit(f"新用户导航到: {path}")
            time.sleep(0.5)  # 模拟用户思考时间
        
        return {
            'total_interactions': len(interactions),
            'avg_response_time': sum(i['response_time'] for i in interactions) / len(interactions),
            'exploration_completed': True
        }
    
    def _simulate_expert_user(self) -> Dict[str, Any]:
        """
        模拟熟练用户行为
        
        Returns:
            熟练用户体验测试结果
        """
        interactions = []
        
        # 熟练用户有固定的工作流程
        workflow_paths = [
            "数据导入 > Excel文件导入",
            "数据导入 > 数据验证",
            "异动检测 > 异动分析",
            "异动检测 > 异动分析 > 新增人员",
            "异动检测 > 异动分析 > 工资调整",
            "报告生成 > Word报告"
        ]
        
        # 重复工作流程多次，测试学习效果
        for cycle in range(3):
            self.interaction_logged.emit(f"熟练用户工作流程 - 第{cycle + 1}轮")
            
            for path in workflow_paths:
                start_time = time.time()
                self.navigation_panel.navigate_to_path(path)
                response_time = time.time() - start_time
                
                interactions.append({
                    'action': 'workflow_navigate',
                    'path': path,
                    'cycle': cycle,
                    'response_time': response_time
                })
                
                time.sleep(0.2)  # 熟练用户操作更快
        
        # 分析学习效果
        first_cycle_times = [i['response_time'] for i in interactions if i['cycle'] == 0]
        last_cycle_times = [i['response_time'] for i in interactions if i['cycle'] == 2]
        
        improvement = (sum(first_cycle_times) - sum(last_cycle_times)) / sum(first_cycle_times) if first_cycle_times else 0
        
        return {
            'total_interactions': len(interactions),
            'workflow_cycles': 3,
            'performance_improvement': improvement,
            'avg_first_cycle_time': sum(first_cycle_times) / len(first_cycle_times) if first_cycle_times else 0,
            'avg_last_cycle_time': sum(last_cycle_times) / len(last_cycle_times) if last_cycle_times else 0
        }
    
    def _simulate_search_behavior(self) -> Dict[str, Any]:
        """
        模拟搜索行为
        
        Returns:
            搜索行为测试结果
        """
        search_queries = [
            "数据",      # 简短查询
            "导入",      # 中等查询
            "Excel文件", # 具体查询
            "异动检测分析", # 复杂查询
            "报告生成模板", # 长查询
            "用户设置",   # 准确查询
            "workflo",   # 错误拼写
            "管理系统"    # 模糊查询
        ]
        
        search_results = []
        
        for query in search_queries:
            start_time = time.time()
            
            # 模拟逐字输入
            for i in range(1, len(query) + 1):
                partial_query = query[:i]
                self.navigation_panel.search_edit.setText(partial_query)
                time.sleep(0.1)  # 模拟打字间隔
            
            search_time = time.time() - start_time
            
            search_results.append({
                'query': query,
                'length': len(query),
                'search_time': search_time,
                'query_type': self._categorize_query(query)
            })
            
            self.interaction_logged.emit(f"搜索查询: '{query}' ({search_time:.2f}s)")
            time.sleep(0.5)  # 查看结果时间
            
            # 清空搜索
            self.navigation_panel.clear_search()
        
        return {
            'total_searches': len(search_results),
            'avg_search_time': sum(r['search_time'] for r in search_results) / len(search_results),
            'query_types': {t: len([r for r in search_results if r['query_type'] == t]) 
                          for t in ['short', 'medium', 'long', 'complex']},
            'search_efficiency': len([r for r in search_results if r['search_time'] < 2.0]) / len(search_results)
        }
    
    def _categorize_query(self, query: str) -> str:
        """
        分类查询类型
        
        Args:
            query: 查询字符串
            
        Returns:
            查询类型
        """
        if len(query) <= 2:
            return 'short'
        elif len(query) <= 4:
            return 'medium'
        elif len(query) <= 8:
            return 'long'
        else:
            return 'complex'


class Phase2TestWindow(QMainWindow):
    """
    Phase 2测试主窗口
    
    展示智能算法的测试结果和用户体验模拟。
    """
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        
        # 测试组件
        self.benchmark_thread = None
        self.simulation_thread = None
        
        # 设置界面
        self._setup_ui()
        self._setup_connections()
        
        self.logger.info("Phase 2测试窗口初始化完成")
    
    def _setup_ui(self):
        """
        设置用户界面
        """
        self.setWindowTitle("PyQt5重构高保真原型 - Phase 2 智能算法测试")
        self.setGeometry(100, 100, 1400, 900)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 左侧：增强导航面板
        self.navigation_panel = EnhancedNavigationPanel()
        self.navigation_panel.setFixedWidth(350)
        
        # 右侧：测试控制和结果显示
        test_widget = QWidget()
        test_layout = QVBoxLayout(test_widget)
        test_layout.setContentsMargins(0, 0, 0, 0)
        test_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🧪 Phase 2 智能算法测试")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2196F3; padding: 10px;")
        
        # 测试控制区域
        control_group = QGroupBox("测试控制")
        control_layout = QHBoxLayout(control_group)
        
        self.benchmark_btn = QPushButton("🚀 性能基准测试")
        self.benchmark_btn.setStyleSheet("""
            QPushButton {
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        
        self.simulation_btn = QPushButton("🎭 用户体验模拟")
        self.simulation_btn.setStyleSheet("""
            QPushButton {
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        
        control_layout.addWidget(self.benchmark_btn)
        control_layout.addWidget(self.simulation_btn)
        control_layout.addStretch()
        
        # 进度指示器
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_label = QLabel("")
        self.progress_label.setVisible(False)
        
        # 结果显示区域
        results_group = QGroupBox("测试结果")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
            }
        """)
        
        results_layout.addWidget(self.results_text)
        
        # 布局安排
        test_layout.addWidget(title_label)
        test_layout.addWidget(control_group)
        test_layout.addWidget(self.progress_bar)
        test_layout.addWidget(self.progress_label)
        test_layout.addWidget(results_group)
        
        main_layout.addWidget(self.navigation_panel)
        main_layout.addWidget(test_widget)
        
        # 初始化结果显示
        self._show_initial_info()
    
    def _setup_connections(self):
        """
        设置信号连接
        """
        self.benchmark_btn.clicked.connect(self._start_benchmark)
        self.simulation_btn.clicked.connect(self._start_simulation)
        
        # 导航面板事件
        self.navigation_panel.navigation_changed.connect(self._on_navigation_changed)
        self.navigation_panel.search_performed.connect(self._on_search_performed)
        self.navigation_panel.expansion_predicted.connect(self._on_expansion_predicted)
    
    def _show_initial_info(self):
        """
        显示初始信息
        """
        info_text = """
🧪 PyQt5重构高保真原型 - Phase 2 智能算法测试

📋 测试内容:
┌─ 性能基准测试
│  ├─ 智能展开算法性能 (访问记录、预测性能)
│  ├─ 智能防抖搜索性能 (延迟计算、模式分析)
│  ├─ 预测准确性验证 (模拟用户模式学习)
│  └─ 内存使用优化测试
│
└─ 用户体验模拟
   ├─ 新用户探索行为模拟
   ├─ 熟练用户工作流程模拟
   └─ 搜索行为模式分析

🎯 测试目标:
• 验证智能算法的性能表现 (<100ms响应时间)
• 确认用户体验优化效果 (>70%预测准确率)
• 测试内存使用效率 (<2MB增长)
• 评估学习能力和适应性

📊 当前导航面板状态:
• 导航项数量: 等待统计...
• 智能预测: 已启用
• 搜索缓存: 已启用
• 状态持久化: 已启用

👆 点击上方按钮开始测试，或在左侧导航面板中进行交互体验
        """
        
        self.results_text.setPlainText(info_text)
        
        # 延迟更新统计信息
        QTimer.singleShot(1000, self._update_initial_stats)
    
    def _update_initial_stats(self):
        """
        更新初始统计信息
        """
        stats = self.navigation_panel.get_navigation_statistics()
        
        updated_text = self.results_text.toPlainText()
        updated_text = updated_text.replace(
            "• 导航项数量: 等待统计...",
            f"• 导航项数量: {stats['tree_stats']['total_items']}"
        )
        
        self.results_text.setPlainText(updated_text)
    
    def _start_benchmark(self):
        """
        开始性能基准测试
        """
        if self.benchmark_thread and self.benchmark_thread.isRunning():
            return
        
        self.benchmark_btn.setEnabled(False)
        self.simulation_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.results_text.append("\n🚀 开始性能基准测试...\n")
        
        self.benchmark_thread = PerformanceBenchmark()
        self.benchmark_thread.benchmark_completed.connect(self._on_benchmark_completed)
        self.benchmark_thread.progress_updated.connect(self._on_progress_updated)
        self.benchmark_thread.start()
    
    def _start_simulation(self):
        """
        开始用户体验模拟
        """
        if self.simulation_thread and self.simulation_thread.isRunning():
            return
        
        self.benchmark_btn.setEnabled(False)
        self.simulation_btn.setEnabled(False)
        
        self.results_text.append("\n🎭 开始用户体验模拟...\n")
        
        self.simulation_thread = UserExperienceSimulator(self.navigation_panel)
        self.simulation_thread.simulation_completed.connect(self._on_simulation_completed)
        self.simulation_thread.interaction_logged.connect(self._on_interaction_logged)
        self.simulation_thread.start()
    
    def _on_benchmark_completed(self, results: Dict[str, Any]):
        """
        处理基准测试完成
        
        Args:
            results: 测试结果
        """
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        
        # 格式化结果
        result_text = """
📊 性能基准测试结果:

🔧 智能展开算法性能:
  • 访问记录性能: {:.1f} 次/秒
  • 预测计算性能: {:.1f} 次/秒  
  • 跟踪路径数量: {} 个
  
🔍 智能搜索算法性能:
  • 延迟计算性能: {:.1f} 次/秒
  • 模式分析性能: {:.1f} 次/秒
  • 缓存命中率: {:.1%}
  
🎯 智能预测准确性:
  • 预测准确率: {:.1%}
  • 正确预测: {} / {} 次
  • 学习到的模式: {} 个
  
💾 内存使用效率:
  • 初始内存: {:.1f} MB
  • 最终内存: {:.1f} MB  
  • 内存增长: {:.1f} MB
  • 每项平均: {:.2f} KB

✅ 测试评估:
""".format(
            results['expansion']['record_access_rate'],
            results['expansion']['predict_rate'],
            results['expansion']['total_paths'],
            results['search']['delay_calculation_rate'],
            results['search']['pattern_analysis_rate'],
            results['search']['cache_hit_rate'],
            results['prediction']['prediction_accuracy'],
            results['prediction']['correct_predictions'],
            results['prediction']['total_predictions'],
            results['prediction']['learned_patterns'],
            results['memory']['initial_memory_mb'],
            results['memory']['final_memory_mb'],
            results['memory']['memory_increase_mb'],
            results['memory']['memory_per_item_kb']
        )
        
        # 添加评估结论
        if results['expansion']['record_access_rate'] > 1000:
            result_text += "  ✅ 展开算法性能: 优秀\n"
        else:
            result_text += "  ⚠️ 展开算法性能: 需要优化\n"
            
        if results['prediction']['prediction_accuracy'] > 0.7:
            result_text += "  ✅ 预测准确性: 优秀\n"
        else:
            result_text += "  ⚠️ 预测准确性: 需要优化\n"
            
        if results['memory']['memory_increase_mb'] < 5.0:
            result_text += "  ✅ 内存使用: 优秀\n"
        else:
            result_text += "  ⚠️ 内存使用: 需要优化\n"
        
        self.results_text.append(result_text)
        
        self.benchmark_btn.setEnabled(True)
        self.simulation_btn.setEnabled(True)
        
        self.logger.info("性能基准测试完成")
    
    def _on_simulation_completed(self, results: Dict[str, Any]):
        """
        处理用户体验模拟完成
        
        Args:
            results: 模拟结果
        """
        result_text = """
🎭 用户体验模拟结果:

👶 新用户体验:
  • 探索交互次数: {} 次
  • 平均响应时间: {:.3f} 秒
  • 探索完成度: {}

👨‍💼 熟练用户体验:
  • 工作流程轮次: {} 轮
  • 总交互次数: {} 次
  • 性能改善率: {:.1%}
  • 首轮平均时间: {:.3f} 秒
  • 末轮平均时间: {:.3f} 秒

🔍 搜索行为分析:
  • 搜索查询总数: {} 次
  • 平均搜索时间: {:.2f} 秒
  • 搜索效率: {:.1%}
  • 查询类型分布:
    - 简短查询: {} 次
    - 中等查询: {} 次  
    - 长查询: {} 次
    - 复杂查询: {} 次

✅ 体验评估:
""".format(
            results['new_user']['total_interactions'],
            results['new_user']['avg_response_time'],
            "✅ 完成" if results['new_user']['exploration_completed'] else "❌ 未完成",
            results['expert_user']['workflow_cycles'],
            results['expert_user']['total_interactions'],
            results['expert_user']['performance_improvement'],
            results['expert_user']['avg_first_cycle_time'],
            results['expert_user']['avg_last_cycle_time'],
            results['search_behavior']['total_searches'],
            results['search_behavior']['avg_search_time'],
            results['search_behavior']['search_efficiency'],
            results['search_behavior']['query_types']['short'],
            results['search_behavior']['query_types']['medium'],
            results['search_behavior']['query_types']['long'],
            results['search_behavior']['query_types']['complex']
        )
        
        # 添加体验评估
        if results['new_user']['avg_response_time'] < 0.1:
            result_text += "  ✅ 新用户响应: 优秀\n"
        else:
            result_text += "  ⚠️ 新用户响应: 需要优化\n"
            
        if results['expert_user']['performance_improvement'] > 0.1:
            result_text += "  ✅ 学习效果: 优秀\n"
        else:
            result_text += "  ⚠️ 学习效果: 需要优化\n"
            
        if results['search_behavior']['search_efficiency'] > 0.8:
            result_text += "  ✅ 搜索效率: 优秀\n"
        else:
            result_text += "  ⚠️ 搜索效率: 需要优化\n"
        
        self.results_text.append(result_text)
        
        self.benchmark_btn.setEnabled(True)
        self.simulation_btn.setEnabled(True)
        
        self.logger.info("用户体验模拟完成")
    
    def _on_progress_updated(self, percentage: int, status: str):
        """
        处理进度更新
        
        Args:
            percentage: 进度百分比
            status: 状态信息
        """
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(status)
    
    def _on_interaction_logged(self, log_message: str):
        """
        处理交互日志
        
        Args:
            log_message: 日志消息
        """
        self.results_text.append(log_message)
    
    def _on_navigation_changed(self, path: str, context: dict):
        """
        处理导航变化
        
        Args:
            path: 导航路径
            context: 上下文信息
        """
        self.results_text.append(f"🧭 导航: {path}")
    
    def _on_search_performed(self, query: str, context: dict):
        """
        处理搜索执行
        
        Args:
            query: 搜索查询
            context: 搜索上下文
        """
        complexity = context.get('query_complexity', 0)
        delay = context.get('delay_used', 0)
        self.results_text.append(f"🔍 搜索: '{query}' (复杂度: {complexity:.2f}, 延迟: {delay:.0f}ms)")
    
    def _on_expansion_predicted(self, predicted_paths: List[str]):
        """
        处理展开预测
        
        Args:
            predicted_paths: 预测的路径
        """
        if predicted_paths:
            self.results_text.append(f"🤖 智能预测: {len(predicted_paths)} 个路径")


class Phase2TestRunner:
    """
    Phase 2测试运行器
    
    提供命令行和GUI两种测试方式。
    """
    
    def __init__(self):
        self.logger = setup_logger(__name__)
    
    def run_gui_test(self):
        """
        运行GUI测试
        """
        app = QApplication(sys.argv)
        
        # 设置应用程序样式
        app.setStyle('Fusion')
        
        window = Phase2TestWindow()
        window.show()
        
        self.logger.info("Phase 2 GUI测试启动")
        
        try:
            sys.exit(app.exec_())
        except Exception as e:
            self.logger.error(f"GUI测试异常: {e}")
    
    def run_console_test(self):
        """
        运行控制台测试
        """
        print("🧪 PyQt5重构高保真原型 - Phase 2 控制台测试")
        print("=" * 60)
        
        # 基本功能测试
        print("\n1. 智能展开算法测试...")
        expansion = SmartTreeExpansion()
        
        # 模拟访问
        test_paths = ["数据导入", "异动检测", "报告生成"]
        for path in test_paths:
            expansion.record_access(path)
        
        predictions = expansion.predict_expansion_paths()
        print(f"   ✅ 预测结果: {len(predictions)} 个路径")
        
        print("\n2. 智能搜索算法测试...")
        search = SmartSearchDebounce()
        
        # 测试搜索延迟计算
        test_queries = ["测试", "智能搜索", "PyQt5"]
        for query in test_queries:
            delay = search._calculate_smart_delay(query)
            print(f"   查询: '{query}' -> 延迟: {delay}ms")
        
        print("\n3. 集成测试...")
        app = QApplication([])
        
        navigation = EnhancedNavigationPanel()
        stats = navigation.get_navigation_statistics()
        
        print(f"   ✅ 导航项: {stats['tree_stats']['total_items']}")
        print(f"   ✅ 智能组件: 已初始化")
        
        print("\n✅ Phase 2控制台测试完成")
        self.logger.info("Phase 2控制台测试完成")


if __name__ == "__main__":
    # 根据命令行参数选择测试方式
    runner = Phase2TestRunner()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--console":
        runner.run_console_test()
    else:
        runner.run_gui_test() 