#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt5重构高保真原型 - Phase 2 增强导航面板验证程序

验证Phase 2实现的增强导航面板功能，包括：
1. EnhancedNavigationPanel集成测试
2. SmartTreeExpansion智能展开算法验证
3. SmartSearchDebounce智能防抖搜索验证
4. 导航状态持久化测试
5. 与主工作区域联动测试
6. 响应式布局适配测试

测试目标：
- 验证所有智能算法正常工作
- 确保用户体验流畅
- 验证性能表现符合预期
"""

import sys
import os
import time
from typing import Dict, Any, List
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QFrame
from PyQt5.QtCore import QTimer, pyqtSignal, QSize
from PyQt5.QtGui import QFont

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.utils.log_config import setup_logger
from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.gui.prototype.widgets.enhanced_navigation_panel import EnhancedNavigationPanel


class NavigationTestReporter(QWidget):
    """
    导航测试报告器
    
    显示测试结果和统计信息。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.test_results = []
        self.setup_ui()
        self.logger = setup_logger(__name__ + ".NavigationTestReporter")
    
    def setup_ui(self):
        """
        设置测试报告界面
        """
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🧪 Phase 2 导航面板测试报告")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2196F3; margin-bottom: 10px;")
        
        # 测试结果显示区域
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                font-family: Consolas, Monaco, monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        
        # 统计信息区域
        self.stats_frame = QFrame()
        self.stats_frame.setStyleSheet("""
            QFrame {
                background-color: #e3f2fd;
                border: 1px solid #2196F3;
                border-radius: 6px;
                padding: 15px;
            }
        """)
        
        stats_layout = QHBoxLayout(self.stats_frame)
        
        self.total_tests_label = QLabel("总测试: 0")
        self.passed_tests_label = QLabel("通过: 0")
        self.failed_tests_label = QLabel("失败: 0")
        self.performance_label = QLabel("性能: N/A")
        
        for label in [self.total_tests_label, self.passed_tests_label, self.failed_tests_label, self.performance_label]:
            label.setStyleSheet("font-weight: bold; color: #1976D2;")
            stats_layout.addWidget(label)
        
        # 布局安排
        layout.addWidget(title_label)
        layout.addWidget(self.stats_frame)
        layout.addWidget(self.results_text)
    
    def add_test_result(self, test_name: str, passed: bool, details: str = "", performance_ms: float = None):
        """
        添加测试结果
        
        Args:
            test_name: 测试名称
            passed: 是否通过
            details: 详细信息
            performance_ms: 性能数据（毫秒）
        """
        status = "✅ PASS" if passed else "❌ FAIL"
        perf_info = f" ({performance_ms:.1f}ms)" if performance_ms else ""
        
        result_text = f"[{time.strftime('%H:%M:%S')}] {status} {test_name}{perf_info}"
        if details:
            result_text += f"\n    📝 {details}"
        
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'details': details,
            'performance': performance_ms,
            'timestamp': time.time()
        })
        
        # 更新显示
        self.results_text.append(result_text)
        self.update_statistics()
        
        self.logger.info(f"测试结果: {test_name} - {'通过' if passed else '失败'}")
    
    def update_statistics(self):
        """
        更新统计信息
        """
        total = len(self.test_results)
        passed = sum(1 for r in self.test_results if r['passed'])
        failed = total - passed
        
        # 计算平均性能
        perf_results = [r['performance'] for r in self.test_results if r['performance'] is not None]
        avg_perf = sum(perf_results) / len(perf_results) if perf_results else None
        
        self.total_tests_label.setText(f"总测试: {total}")
        self.passed_tests_label.setText(f"通过: {passed}")
        self.failed_tests_label.setText(f"失败: {failed}")
        
        if avg_perf:
            self.performance_label.setText(f"平均性能: {avg_perf:.1f}ms")
        else:
            self.performance_label.setText("性能: N/A")
    
    def get_test_summary(self) -> Dict[str, Any]:
        """
        获取测试总结
        
        Returns:
            测试总结信息
        """
        total = len(self.test_results)
        passed = sum(1 for r in self.test_results if r['passed'])
        failed = total - passed
        
        perf_results = [r['performance'] for r in self.test_results if r['performance'] is not None]
        
        return {
            'total_tests': total,
            'passed_tests': passed,
            'failed_tests': failed,
            'success_rate': passed / total if total > 0 else 0,
            'avg_performance': sum(perf_results) / len(perf_results) if perf_results else None,
            'test_results': self.test_results
        }


class Phase2NavigationTester(QWidget):
    """
    Phase 2 导航面板测试器
    
    执行全面的导航面板功能测试。
    """
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        self.navigation_panel = None
        self.main_window = None
        self.test_reporter = NavigationTestReporter()
        
        self.setup_ui()
        self.setup_test_environment()
        
        # 启动测试
        QTimer.singleShot(1000, self.start_automated_tests)
        
        self.logger.info("Phase 2 导航面板测试器初始化完成")
    
    def setup_ui(self):
        """
        设置测试界面
        """
        self.setWindowTitle("Phase 2 增强导航面板验证程序")
        self.resize(1200, 800)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 左侧：主窗口预览
        self.main_window = PrototypeMainWindow()
        self.main_window.setParent(self)
        self.main_window.resize(800, 600)
        
        # 右侧：测试报告
        self.test_reporter.setFixedWidth(400)
        
        layout.addWidget(self.main_window)
        layout.addWidget(self.test_reporter)
    
    def setup_test_environment(self):
        """
        设置测试环境
        """
        # 获取导航面板引用
        self.navigation_panel = self.main_window.navigation_panel
        
        # 连接测试信号
        self.navigation_panel.navigation_changed.connect(self.on_navigation_changed_test)
        self.navigation_panel.search_performed.connect(self.on_search_performed_test)
        self.navigation_panel.expansion_predicted.connect(self.on_expansion_predicted_test)
    
    def start_automated_tests(self):
        """
        开始自动化测试
        """
        self.test_reporter.add_test_result("测试环境初始化", True, "主窗口和导航面板创建成功")
        
        # 执行各项测试
        QTimer.singleShot(500, self.test_navigation_panel_import)
        QTimer.singleShot(1000, self.test_smart_tree_expansion)
        QTimer.singleShot(2000, self.test_smart_search_debounce)
        QTimer.singleShot(3000, self.test_navigation_persistence)
        QTimer.singleShot(4000, self.test_responsive_behavior)
        QTimer.singleShot(5000, self.test_performance_metrics)
        QTimer.singleShot(6000, self.show_final_report)
    
    def test_navigation_panel_import(self):
        """
        测试导航面板导入和创建
        """
        start_time = time.time()
        
        try:
            # 检查导航面板是否正确创建
            panel_created = self.navigation_panel is not None
            enhanced_panel = hasattr(self.navigation_panel, 'smart_search')
            tree_widget = hasattr(self.navigation_panel, 'tree_widget')
            
            success = panel_created and enhanced_panel and tree_widget
            performance = (time.time() - start_time) * 1000
            
            details = f"面板创建: {panel_created}, 智能搜索: {enhanced_panel}, 树形控件: {tree_widget}"
            self.test_reporter.add_test_result(
                "导航面板导入测试", success, details, performance
            )
            
        except Exception as e:
            self.test_reporter.add_test_result(
                "导航面板导入测试", False, f"异常: {str(e)}"
            )
    
    def test_smart_tree_expansion(self):
        """
        测试智能树形展开功能
        """
        start_time = time.time()
        
        try:
            # 测试智能展开算法
            smart_expansion = self.navigation_panel.tree_widget.smart_expansion
            
            # 模拟路径访问
            test_paths = [
                "数据导入 > Excel文件导入",
                "异动检测 > 异动分析 > 新增人员",
                "报告生成 > Word报告"
            ]
            
            for path in test_paths:
                smart_expansion.record_access(path, {'test': True})
            
            # 测试预测功能
            predictions = smart_expansion.predict_expansion_paths()
            has_predictions = len(predictions) > 0
            
            performance = (time.time() - start_time) * 1000
            
            details = f"记录路径: {len(test_paths)}, 生成预测: {len(predictions)}"
            self.test_reporter.add_test_result(
                "智能树形展开测试", has_predictions, details, performance
            )
            
        except Exception as e:
            self.test_reporter.add_test_result(
                "智能树形展开测试", False, f"异常: {str(e)}"
            )
    
    def test_smart_search_debounce(self):
        """
        测试智能防抖搜索功能
        """
        start_time = time.time()
        
        try:
            # 测试搜索功能
            search_edit = self.navigation_panel.search_edit
            smart_search = self.navigation_panel.smart_search
            
            # 模拟快速输入
            test_queries = ["数", "数据", "数据导", "数据导入"]
            
            for query in test_queries:
                search_edit.setText(query)
                QApplication.processEvents()
                time.sleep(0.05)  # 模拟快速输入
            
            # 检查防抖是否工作
            stats = smart_search.get_statistics()
            has_stats = 'total_queries' in stats
            
            performance = (time.time() - start_time) * 1000
            
            details = f"搜索查询: {len(test_queries)}, 统计数据: {has_stats}"
            self.test_reporter.add_test_result(
                "智能防抖搜索测试", has_stats, details, performance
            )
            
        except Exception as e:
            self.test_reporter.add_test_result(
                "智能防抖搜索测试", False, f"异常: {str(e)}"
            )
    
    def test_navigation_persistence(self):
        """
        测试导航状态持久化
        """
        start_time = time.time()
        
        try:
            # 测试状态管理器
            state_manager = self.navigation_panel.state_manager
            
            # 模拟状态保存
            test_path = "测试路径"
            state_manager.save_expanded_state(test_path, True)
            state_manager.save_selected_state(test_path)
            state_manager.add_search_history("测试搜索")
            
            # 检查状态是否保存
            expanded_paths = state_manager.get_expanded_paths()
            selected_path = state_manager.get_selected_path()
            search_history = state_manager.get_search_history()
            
            success = (test_path in expanded_paths and 
                      selected_path == test_path and 
                      "测试搜索" in search_history)
            
            performance = (time.time() - start_time) * 1000
            
            details = f"展开状态: {len(expanded_paths)}, 选择状态: {bool(selected_path)}, 搜索历史: {len(search_history)}"
            self.test_reporter.add_test_result(
                "导航状态持久化测试", success, details, performance
            )
            
        except Exception as e:
            self.test_reporter.add_test_result(
                "导航状态持久化测试", False, f"异常: {str(e)}"
            )
    
    def test_responsive_behavior(self):
        """
        测试响应式行为
        """
        start_time = time.time()
        
        try:
            # 测试响应式处理
            responsive_method = getattr(self.navigation_panel, 'handle_responsive_change', None)
            has_responsive = responsive_method is not None
            
            if has_responsive:
                # 模拟响应式变化
                test_config = {
                    'sidebar_width': 320,
                    'sidebar_mode': 'fixed',
                    'font_scale': 1.2
                }
                responsive_method('lg', test_config)
            
            performance = (time.time() - start_time) * 1000
            
            details = f"响应式方法: {has_responsive}"
            self.test_reporter.add_test_result(
                "响应式行为测试", has_responsive, details, performance
            )
            
        except Exception as e:
            self.test_reporter.add_test_result(
                "响应式行为测试", False, f"异常: {str(e)}"
            )
    
    def test_performance_metrics(self):
        """
        测试性能指标
        """
        start_time = time.time()
        
        try:
            # 获取导航统计信息
            stats = self.navigation_panel.get_navigation_statistics()
            
            has_tree_stats = 'tree_stats' in stats
            has_search_stats = 'search_stats' in stats
            has_state_stats = 'state_stats' in stats
            
            success = has_tree_stats and has_search_stats and has_state_stats
            performance = (time.time() - start_time) * 1000
            
            details = f"树形统计: {has_tree_stats}, 搜索统计: {has_search_stats}, 状态统计: {has_state_stats}"
            self.test_reporter.add_test_result(
                "性能指标测试", success, details, performance
            )
            
        except Exception as e:
            self.test_reporter.add_test_result(
                "性能指标测试", False, f"异常: {str(e)}"
            )
    
    def show_final_report(self):
        """
        显示最终测试报告
        """
        summary = self.test_reporter.get_test_summary()
        
        report_text = f"""
        
🎯 Phase 2 测试总结
=====================================
总测试数量: {summary['total_tests']}
通过测试: {summary['passed_tests']}
失败测试: {summary['failed_tests']}
成功率: {summary['success_rate']:.1%}
平均性能: {summary['avg_performance']:.1f}ms (如果有性能数据)

📊 测试质量评估:
{self.get_quality_assessment(summary)}

🚀 Phase 2 状态: {'✅ 准备进入 Phase 3' if summary['success_rate'] >= 0.8 else '❌ 需要修复问题'}
        """
        
        self.test_reporter.results_text.append(report_text)
        self.logger.info(f"Phase 2 测试完成 - 成功率: {summary['success_rate']:.1%}")
    
    def get_quality_assessment(self, summary: Dict[str, Any]) -> str:
        """
        获取质量评估
        
        Args:
            summary: 测试总结
            
        Returns:
            质量评估文本
        """
        success_rate = summary['success_rate']
        
        if success_rate >= 0.9:
            return "🟢 优秀 - 所有核心功能正常"
        elif success_rate >= 0.8:
            return "🟡 良好 - 大部分功能正常"
        elif success_rate >= 0.6:
            return "🟠 一般 - 存在一些问题"
        else:
            return "🔴 需要改进 - 存在重要问题"
    
    # 测试信号处理
    def on_navigation_changed_test(self, path: str, context: Dict[str, Any]):
        """测试导航变化信号"""
        self.logger.debug(f"导航测试 - 路径变化: {path}")
    
    def on_search_performed_test(self, query: str, context: Dict[str, Any]):
        """测试搜索执行信号"""
        self.logger.debug(f"搜索测试 - 查询: {query}")
    
    def on_expansion_predicted_test(self, predicted_paths: List[str]):
        """测试展开预测信号"""
        self.logger.debug(f"预测测试 - 路径: {len(predicted_paths)}")


def main():
    """
    主函数
    """
    app = QApplication(sys.argv)
    
    # 创建测试器
    tester = Phase2NavigationTester()
    tester.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 