"""
Phase 3 增强搜索测试程序

测试EnhancedTableSearch组件的功能：
- 智能防抖搜索
- 表格内容搜索和高亮
- 搜索历史管理
- 实时搜索状态

作者: PyQt5重构项目组
创建时间: 2025-06-19 16:30
"""

import sys
import time
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem, QTextEdit,
    QSplitter, QGroupBox
)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont

from src.utils.log_config import setup_logger
from src.gui.prototype.widgets.enhanced_table_search import EnhancedTableSearch


class Phase3SearchTest(QMainWindow):
    """Phase 3 增强搜索测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        
        self.setWindowTitle("Phase 3 - 增强表格搜索测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建测试数据
        self.create_test_data()
        
        # 初始化UI
        self.setup_ui()
        
        # 设置连接
        self.setup_connections()
        
        self.logger.info("Phase 3 增强搜索测试程序初始化完成")
    
    def create_test_data(self):
        """创建测试数据"""
        self.test_data = [
            ["张三", "001", "技术部", "高级工程师", "12000", "2023-01-15"],
            ["李四", "002", "财务部", "会计师", "8000", "2023-02-20"],
            ["王五", "003", "技术部", "架构师", "15000", "2023-01-10"],
            ["赵六", "004", "人事部", "HR专员", "6000", "2023-03-05"],
            ["钱七", "005", "技术部", "前端工程师", "10000", "2023-01-25"],
            ["孙八", "006", "市场部", "市场经理", "11000", "2023-02-15"],
            ["周九", "007", "技术部", "后端工程师", "11500", "2023-01-20"],
            ["吴十", "008", "财务部", "财务经理", "13000", "2023-02-10"],
            ["郑一", "009", "人事部", "人事经理", "12500", "2023-03-01"],
            ["王二", "010", "技术部", "测试工程师", "9000", "2023-01-30"],
            ["李三", "011", "市场部", "销售代表", "7500", "2023-02-25"],
            ["张四", "012", "技术部", "DevOps工程师", "13500", "2023-01-12"],
            ["陈五", "013", "财务部", "出纳", "5500", "2023-03-08"],
            ["刘六", "014", "人事部", "培训专员", "7000", "2023-02-28"],
            ["杨七", "015", "技术部", "UI设计师", "9500", "2023-01-18"],
            ["徐八", "016", "市场部", "品牌经理", "10500", "2023-02-12"],
            ["马九", "017", "技术部", "数据分析师", "11800", "2023-01-22"],
            ["朱十", "018", "财务部", "税务专员", "8500", "2023-03-03"],
            ["胡一", "019", "人事部", "薪酬专员", "7800", "2023-02-18"],
            ["林二", "020", "技术部", "运维工程师", "10800", "2023-01-28"],
        ]
        
        self.column_headers = ["姓名", "工号", "部门", "职位", "工资", "入职时间"]
    
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("Phase 3 - 增强表格搜索组件测试")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2196F3;
                padding: 10px;
                background-color: #F5F5F5;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：表格区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 表格组
        table_group = QGroupBox("员工信息表格")
        table_layout = QVBoxLayout(table_group)
        
        # 创建表格
        self.table = QTableWidget(len(self.test_data), len(self.column_headers))
        self.table.setHorizontalHeaderLabels(self.column_headers)
        
        # 填充测试数据
        for i, row_data in enumerate(self.test_data):
            for j, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                self.table.setItem(i, j, item)
        
        # 调整列宽
        self.table.resizeColumnsToContents()
        
        # 创建增强搜索组件
        self.search_widget = EnhancedTableSearch(self.table)
        
        table_layout.addWidget(self.search_widget)
        table_layout.addWidget(self.table)
        left_layout.addWidget(table_group)
        
        # 右侧：测试控制和日志区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 测试控制组
        control_group = QGroupBox("测试控制")
        control_layout = QVBoxLayout(control_group)
        
        # 预设搜索按钮
        self.preset_searches = [
            ("搜索'技术部'", "技术部"),
            ("搜索'工程师'", "工程师"),
            ("搜索'张'", "张"),
            ("搜索工资'>10000'", "1[0-9][0-9][0-9][0-9]"),
            ("清空搜索", ""),
        ]
        
        for button_text, search_query in self.preset_searches:
            btn = QPushButton(button_text)
            btn.clicked.connect(lambda checked, q=search_query: self.preset_search(q))
            control_layout.addWidget(btn)
        
        # 统计按钮
        stats_btn = QPushButton("显示搜索统计")
        stats_btn.clicked.connect(self.show_search_stats)
        control_layout.addWidget(stats_btn)
        
        # 清空历史按钮
        clear_btn = QPushButton("清空搜索历史")
        clear_btn.clicked.connect(self.clear_search_history)
        control_layout.addWidget(clear_btn)
        
        right_layout.addWidget(control_group)
        
        # 日志显示组
        log_group = QGroupBox("搜索日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMaximumHeight(300)
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #00FF00;
                font-family: 'Consolas', monospace;
                font-size: 10px;
            }
        """)
        
        log_layout.addWidget(self.log_display)
        right_layout.addWidget(log_group)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([800, 400])
        
        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("准备就绪 - 请开始测试搜索功能")
    
    def setup_connections(self):
        """设置信号连接"""
        # 连接搜索组件的信号
        self.search_widget.search_performed.connect(self.on_search_performed)
        self.search_widget.search_cleared.connect(self.on_search_cleared)
    
    @pyqtSlot(str)
    def preset_search(self, query: str):
        """执行预设搜索"""
        if query:
            self.search_widget.search_input.setText(query)
            self.log_message(f"执行预设搜索: '{query}'")
        else:
            self.search_widget.clear_search()
            self.log_message("清空搜索")
    
    @pyqtSlot()
    def show_search_stats(self):
        """显示搜索统计"""
        stats = self.search_widget.get_search_stats()
        
        stats_text = f"""
搜索统计信息:
- 当前结果数: {stats['total_results']}
- 当前结果索引: {stats['current_index']}
- 搜索历史数量: {stats['search_history_count']}
        """
        
        # 添加防抖统计
        debounce_stats = stats.get('debounce_stats', {})
        if debounce_stats:
            stats_text += f"""
防抖算法统计:
- 总查询次数: {debounce_stats.get('total_queries', 0)}
- 缓存命中次数: {debounce_stats.get('cached_queries', 0)}
- 取消查询次数: {debounce_stats.get('cancelled_queries', 0)}
- 平均延迟: {debounce_stats.get('avg_delay', 0):.1f}ms
        """
        
        self.log_message(stats_text)
    
    @pyqtSlot()
    def clear_search_history(self):
        """清空搜索历史"""
        self.search_widget.history_manager.clear_history()
        self.search_widget._update_completer()
        self.log_message("搜索历史已清空")
    
    @pyqtSlot(str, list)
    def on_search_performed(self, query: str, results: list):
        """搜索执行完成处理"""
        timestamp = time.strftime("%H:%M:%S")
        message = f"[{timestamp}] 搜索完成: '{query}' -> 找到 {len(results)} 个结果"
        
        if results:
            # 显示前3个结果的详情
            details = []
            for i, result in enumerate(results[:3]):
                details.append(f"  结果{i+1}: 行{result.row}, 列{result.column}, '{result.match_text}'")
            message += "\n" + "\n".join(details)
            if len(results) > 3:
                message += f"\n  ... 还有 {len(results) - 3} 个结果"
        
        self.log_message(message)
        self.status_bar.showMessage(f"搜索完成: {len(results)} 个结果")
    
    @pyqtSlot()
    def on_search_cleared(self):
        """搜索清除处理"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_message(f"[{timestamp}] 搜索已清除")
        self.status_bar.showMessage("搜索已清除")
    
    def log_message(self, message: str):
        """记录日志消息"""
        self.log_display.append(message)
        # 自动滚动到底部
        cursor = self.log_display.textCursor()
        cursor.movePosition(cursor.End)
        self.log_display.setTextCursor(cursor)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建并显示测试窗口
    window = Phase3SearchTest()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main()) 