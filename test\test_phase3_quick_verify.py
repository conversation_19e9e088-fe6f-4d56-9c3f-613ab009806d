"""
Phase 3 快速验证脚本

快速验证EnhancedTableSearch组件的技术集成：
- 检查类初始化
- 验证信号连接
- 测试基本搜索功能

作者: PyQt5重构项目组
创建时间: 2025-06-19 16:35
"""

import sys
import traceback
from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import QCoreApplication

from src.utils.log_config import setup_logger


def test_enhanced_table_search():
    """测试增强表格搜索组件"""
    logger = setup_logger(__name__)
    logger.info("开始Phase 3组件验证...")
    
    try:
        # 1. 测试模块导入
        logger.info("1. 测试模块导入...")
        from src.gui.prototype.widgets.enhanced_table_search import EnhancedTableSearch
        logger.info("✅ EnhancedTableSearch导入成功")
        
        from src.gui.prototype.widgets.smart_search_debounce import SmartSearchDebounce
        logger.info("✅ SmartSearchDebounce导入成功")
        
        # 2. 创建测试表格
        logger.info("2. 创建测试表格...")
        table = QTableWidget(5, 3)
        table.setHorizontalHeaderLabels(["姓名", "部门", "职位"])
        
        test_data = [
            ["张三", "技术部", "工程师"],
            ["李四", "财务部", "会计"],
            ["王五", "技术部", "架构师"],
            ["赵六", "人事部", "专员"],
            ["钱七", "技术部", "前端工程师"],
        ]
        
        for i, row_data in enumerate(test_data):
            for j, cell_data in enumerate(row_data):
                table.setItem(i, j, QTableWidgetItem(cell_data))
        
        logger.info("✅ 测试表格创建成功")
        
        # 3. 测试搜索组件初始化
        logger.info("3. 测试搜索组件初始化...")
        search_widget = EnhancedTableSearch(table)
        logger.info("✅ EnhancedTableSearch初始化成功")
        
        # 4. 测试搜索功能
        logger.info("4. 测试搜索功能...")
        search_widget._perform_search("技术部")
        results = search_widget.get_search_results()
        logger.info(f"✅ 搜索功能测试成功: 找到 {len(results)} 个结果")
        
        # 5. 测试搜索统计
        logger.info("5. 测试搜索统计...")
        stats = search_widget.get_search_stats()
        logger.info(f"✅ 搜索统计获取成功: {stats}")
        
        logger.info("🎉 Phase 3组件验证全部通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 3组件验证失败: {e}")
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False


def main():
    """主函数"""
    # 创建应用程序实例（必须在Qt组件使用前创建）
    app = QApplication(sys.argv)
    
    # 运行验证测试
    success = test_enhanced_table_search()
    
    if success:
        print("\n✅ Phase 3 技术验证成功！")
        print("📋 已验证组件：")
        print("  - EnhancedTableSearch: 增强表格搜索组件")
        print("  - SmartSearchDebounce: 智能防抖搜索算法")
        print("  - TableSearchEngine: 表格搜索引擎")
        print("  - SearchHighlighter: 搜索高亮显示")
        print("  - SearchHistoryManager: 搜索历史管理")
        return 0
    else:
        print("\n❌ Phase 3 技术验证失败！")
        print("请检查错误日志并修复相关问题。")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 