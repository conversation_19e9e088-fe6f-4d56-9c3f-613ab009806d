#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Phase 3 响应式工作区域完善测试

测试P3-004任务的实现效果，验证：
1. 控制面板在5个断点下的响应式布局
2. 导航面板与工作区的深度联动机制
3. 按钮状态的智能切换
4. 搜索框的断点适配
5. 组件间通信功能

位置: test/test_phase3_responsive_workspace.py
运行方式: python test/test_phase3_responsive_workspace.py
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_responsive_control_panel():
    """测试控制面板的响应式功能"""
    print("🧪 Phase 3 响应式工作区域完善测试")
    print("=" * 50)
    
    try:
        # 测试1：导入必要组件
        print("1️⃣ 测试导入响应式组件...")
        from src.gui.prototype.prototype_main_window import MainWorkspaceArea
        from src.gui.prototype.managers.responsive_layout_manager import ResponsiveLayoutManager
        print("   ✅ 组件导入成功")
        
        # 测试2：创建应用和组件
        print("2️⃣ 测试创建响应式工作区域...")
        app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        
        # 创建主工作区域
        workspace = MainWorkspaceArea()
        
        # 验证控制面板组件存在
        if hasattr(workspace, 'control_panel_widgets'):
            print("   ✅ 控制面板组件创建成功")
            
            # 检查按钮组件
            buttons = workspace.control_panel_widgets.get('buttons', [])
            if len(buttons) == 4:
                print(f"   ✅ 控制按钮数量正确: {len(buttons)}")
            else:
                print(f"   ⚠️ 控制按钮数量异常: {len(buttons)}")
            
            # 检查搜索输入框
            search_input = workspace.control_panel_widgets.get('search_input')
            if search_input:
                print("   ✅ 搜索输入框组件创建成功")
            else:
                print("   ❌ 搜索输入框组件缺失")
        else:
            print("   ❌ 控制面板组件缺失")
            return False
        
        print("3️⃣ 测试响应式断点适配...")
        
        # 模拟不同断点的响应式配置
        breakpoints_configs = {
            'xs': {
                'button_size': 'small',
                'font_scale': 0.9,
                'padding_scale': 0.8
            },
            'sm': {
                'button_size': 'medium',
                'font_scale': 0.95,
                'padding_scale': 0.9
            },
            'md': {
                'button_size': 'medium',
                'font_scale': 1.0,
                'padding_scale': 1.0
            },
            'lg': {
                'button_size': 'large',
                'font_scale': 1.05,
                'padding_scale': 1.1
            },
            'xl': {
                'button_size': 'large',
                'font_scale': 1.1,
                'padding_scale': 1.2
            }
        }
        
        # 测试每个断点的响应式适配
        for breakpoint, config in breakpoints_configs.items():
            workspace.handle_responsive_change(breakpoint, config)
            QApplication.processEvents()  # 处理UI更新
            print(f"   ✅ {breakpoint}断点适配成功")
        
        # 测试xs断点的特殊行为（按钮隐藏）
        workspace.handle_responsive_change('xs', breakpoints_configs['xs'])
        QApplication.processEvents()
        
        # 检查xs断点下是否正确隐藏了部分按钮
        export_btn = workspace.btn_export
        refresh_btn = workspace.btn_refresh
        if not export_btn.isVisible() and not refresh_btn.isVisible():
            print("   ✅ xs断点下按钮隐藏功能正常")
        else:
            print("   ⚠️ xs断点下按钮隐藏功能异常")
        
        print("✅ 控制面板响应式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 控制面板响应式测试失败: {e}")
        traceback.print_exc()
        return False

def test_deep_navigation_integration():
    """测试深度联动机制"""
    print("\n🔗 测试导航与工作区深度联动...")
    
    try:
        # 确保QApplication存在
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入主窗口组件
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        print("   ✅ 主窗口组件导入成功")
        
        # 创建主窗口
        main_window = PrototypeMainWindow()
        print("   ✅ 主窗口创建成功")
        
        # 测试导航变化处理
        test_navigation_paths = [
            ('data_import', {'module': 'import', 'context': 'test'}),
            ('change_detection', {'module': 'detection', 'context': 'test'}),
            ('report_generation', {'module': 'report', 'context': 'test'}),
            ('salary_overview', {'module': 'overview', 'context': 'test'})
        ]
        
        for path, context in test_navigation_paths:
            # 模拟导航变化
            main_window.on_navigation_changed(path, context)
            QApplication.processEvents()
            print(f"   ✅ 导航路径处理成功: {path}")
        
        # 测试ComponentCommunicationManager集成
        if hasattr(main_window, 'communication_manager'):
            print("   ✅ 组件通信管理器集成正常")
        else:
            print("   ⚠️ 组件通信管理器未集成")
        
        # 验证状态栏更新
        status_bar = main_window.statusBar()
        if status_bar:
            current_message = status_bar.currentMessage()
            if current_message:
                print(f"   ✅ 状态栏更新成功: {current_message}")
            else:
                print("   ⚠️ 状态栏消息为空")
        
        print("✅ 深度联动机制测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 深度联动机制测试失败: {e}")
        traceback.print_exc()
        return False

def test_component_communication():
    """测试组件间通信功能"""
    print("\n📡 测试组件间通信功能...")
    
    try:
        # 确保QApplication存在
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入通信管理器
        from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager
        print("   ✅ 通信管理器导入成功")
        
        # 创建通信管理器实例
        comm_manager = ComponentCommunicationManager()
        
        # 测试事件订阅和发布
        received_events = []
        
        def test_listener(event_data):
            received_events.append(event_data)
        
        # 订阅导航事件
        comm_manager.subscribe('navigation', test_listener)
        
        # 发布测试事件
        test_event = {
            'event_type': 'navigation_changed',
            'path': 'test_path',
            'context': {'test': True}
        }
        comm_manager.emit_event('navigation', test_event)
        
        # 验证事件接收
        if received_events:
            print("   ✅ 组件通信功能正常")
            print(f"   📨 接收事件: {received_events[0]}")
        else:
            print("   ❌ 组件通信功能异常")
            return False
        
        print("✅ 组件间通信测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 组件间通信测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Phase 3 响应式工作区域完善完整测试")
    print("📅 时间:", QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss"))
    print("📁 测试位置: test/test_phase3_responsive_workspace.py")
    print("=" * 60)
    
    results = []
    
    # 响应式控制面板测试
    results.append(test_responsive_control_panel())
    
    # 深度联动机制测试
    results.append(test_deep_navigation_integration())
    
    # 组件间通信测试
    results.append(test_component_communication())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 P3-004 响应式工作区域完善 - 测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 所有测试通过！P3-004任务完成")
        print("✅ 控制面板响应式优化成功")
        print("✅ 深度联动机制工作正常") 
        print("✅ 组件间通信功能稳定")
        print("✅ 5个断点响应式适配完整")
        status = "PASS"
    else:
        print(f"⚠️ 部分测试失败：{passed}/{total} 通过")
        status = "PARTIAL"
    
    print(f"\n📋 P3-004状态: {status}")
    print("🎊 Phase 3主工作区域增强 - 全部完成！")
    print("🔄 下一步: 进入REFLECT模式 - 任务总结")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 