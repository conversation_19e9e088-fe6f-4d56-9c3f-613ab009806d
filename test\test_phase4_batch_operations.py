#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Phase 4 多选批量操作功能测试

测试P4-002任务的实现效果，验证：
1. Ctrl+Click非连续多选功能
2. Shift+Click连续范围选择功能
3. 批量编辑、删除、展开/折叠功能
4. 批量复制和导出功能
5. 多选状态的视觉反馈

位置: test/test_phase4_batch_operations.py
运行方式: python test/test_phase4_batch_operations.py
"""

import sys
import os
import traceback
import time
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_multi_selection():
    """测试多选功能基础测试"""
    print("🧪 Phase 4 多选批量操作功能测试")
    print("=" * 50)
    
    try:
        # 测试1：导入多选组件
        print("1️⃣ 测试导入多选批量操作组件...")
        from src.gui.prototype.widgets.virtualized_expandable_table import (
            VirtualizedExpandableTable, create_sample_data
        )
        print("   ✅ 多选批量操作组件导入成功")
        
        # 测试2：创建表格
        print("2️⃣ 测试创建表格和多选设置...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        table = VirtualizedExpandableTable()
        sample_data, headers = create_sample_data(10)
        table.set_data(sample_data, headers)
        print("   ✅ 表格创建和数据设置成功")
        
        # 测试3：多选模式设置
        print("3️⃣ 测试多选模式控制...")
        table.set_multi_select_enabled(True)
        if table.multi_select_enabled:
            print("   ✅ 多选模式启用成功")
        
        table.set_multi_select_enabled(False)
        if not table.multi_select_enabled:
            print("   ✅ 多选模式禁用成功")
        
        # 重新启用多选模式
        table.set_multi_select_enabled(True)
        
        # 测试4：选择状态管理
        print("4️⃣ 测试选择状态管理...")
        
        # 模拟普通选择
        table._handle_normal_click(0, 1)
        selected_count = table.get_selected_rows_count()
        if selected_count == 1:
            print("   ✅ 普通单选功能正常")
        
        # 模拟Ctrl+Click
        table._handle_ctrl_click(2)
        selected_count = table.get_selected_rows_count()
        if selected_count == 2:
            print("   ✅ Ctrl+Click多选功能正常")
        
        # 模拟Shift+Click
        table._handle_shift_click(4)
        selected_count = table.get_selected_rows_count()
        if selected_count >= 3:  # 应该选择范围2-4
            print("   ✅ Shift+Click范围选择功能正常")
        
        # 测试5：选择操作
        print("5️⃣ 测试选择操作...")
        
        # 全选测试
        table.select_all_rows()
        if table.get_selected_rows_count() == 10:
            print("   ✅ 全选功能正常")
        
        # 清除选择测试
        table.clear_selection()
        if table.get_selected_rows_count() == 0:
            print("   ✅ 清除选择功能正常")
        
        print("✅ 多选功能基础测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 多选基础测试失败: {e}")
        traceback.print_exc()
        return False

def test_batch_operations():
    """测试批量操作功能"""
    print("\n🔧 测试批量操作功能...")
    
    try:
        # 导入组件
        from src.gui.prototype.widgets.virtualized_expandable_table import (
            VirtualizedExpandableTable, create_sample_data
        )
        
        # 创建表格
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        table = VirtualizedExpandableTable()
        sample_data, headers = create_sample_data(8)
        table.set_data(sample_data, headers)
        print("   ✅ 批量操作测试表格创建成功")
        
        # 设置多选状态
        table.select_all_rows()
        initial_selected = table.get_selected_rows_count()
        print(f"   ✅ 选中{initial_selected}行进行批量操作测试")
        
        # 测试批量操作统计
        print("   📊 测试批量操作统计...")
        stats = table.get_batch_operation_stats()
        if stats and 'selected_rows' in stats:
            print(f"   ✅ 统计信息获取成功: {stats['selected_rows']}行选中")
        
        # 测试批量编辑
        print("   📝 测试批量编辑...")
        batch_edit_signals = []
        
        def on_batch_operation_completed(operation, count, affected_rows):
            batch_edit_signals.append((operation, count, affected_rows))
            print(f"   📡 批量操作完成: {operation}, 影响{count}行")
        
        table.batch_operation_completed.connect(on_batch_operation_completed)
        
        # 执行批量编辑 (修改第1列)
        edit_success = table.batch_edit_cells(1, "批量编辑测试值")
        if edit_success and len(batch_edit_signals) > 0:
            print("   ✅ 批量编辑功能正常")
        
        # 测试批量复制
        print("   📋 测试批量复制...")
        copied_data = table.batch_copy_data()
        if copied_data and len(copied_data) > 0:
            print(f"   ✅ 批量复制成功: 复制{len(copied_data)}行数据")
        
        # 测试批量导出
        print("   📤 测试批量导出...")
        exported_data = table.batch_export_data()
        if exported_data and 'rows' in exported_data:
            print(f"   ✅ 批量导出成功: 导出{len(exported_data['rows'])}行数据")
        
        # 测试批量展开/折叠
        print("   📂 测试批量展开/折叠...")
        expand_success = table.batch_expand_rows()
        collapse_success = table.batch_collapse_rows()
        
        if expand_success or collapse_success:
            print("   ✅ 批量展开/折叠功能可用")
        
        print("✅ 批量操作功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 批量操作测试失败: {e}")
        traceback.print_exc()
        return False

def test_interactive_batch_operations():
    """测试交互式批量操作功能"""
    print("\n👆 交互式批量操作功能测试...")
    
    try:
        # 导入组件
        from src.gui.prototype.widgets.virtualized_expandable_table import (
            VirtualizedExpandableTable, create_sample_data
        )
        
        # 创建应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("P4-002 多选批量操作功能交互测试")
        window.setGeometry(100, 100, 1200, 700)
        
        # 创建中央组件
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
📝 P4-002 多选批量操作功能测试指南:
• 普通点击：单选行
• Ctrl+点击：非连续多选 (添加/移除选择)
• Shift+点击：连续范围选择
• 使用下方按钮测试各种批量操作
• 观察状态栏显示的选择信息
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                border: 1px solid #87ceeb;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        layout.addWidget(info_label)
        
        # 创建表格
        table = VirtualizedExpandableTable()
        sample_data, headers = create_sample_data(15)
        table.set_data(sample_data, headers)
        
        # 启用多选模式
        table.set_multi_select_enabled(True)
        
        # 状态标签
        status_label = QLabel("状态: 0行选中")
        status_label.setStyleSheet("QLabel { color: #2e7d32; font-weight: bold; padding: 5px; }")
        
        def update_selection_status():
            count = table.get_selected_rows_count()
            selected_rows = table.get_selected_rows_list()
            if count > 0:
                if count <= 5:
                    rows_text = f"行{selected_rows}"
                else:
                    rows_text = f"行{selected_rows[:3]}...等{count}行"
                status_label.setText(f"状态: {count}行选中 ({rows_text})")
            else:
                status_label.setText("状态: 0行选中")
        
        # 连接选择变化信号
        table.selection_changed.connect(lambda: update_selection_status())
        
        # 批量操作按钮
        button_layout = QHBoxLayout()
        
        # 选择操作按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(lambda: table.select_all_rows())
        
        clear_selection_btn = QPushButton("清除选择")
        clear_selection_btn.clicked.connect(lambda: table.clear_selection())
        
        # 批量操作按钮
        batch_edit_btn = QPushButton("批量编辑第2列")
        batch_edit_btn.clicked.connect(
            lambda: table.batch_edit_cells(1, f"批量值_{int(time.time())}")
        )
        
        batch_copy_btn = QPushButton("批量复制")
        def do_batch_copy():
            data = table.batch_copy_data()
            if data:
                QMessageBox.information(window, "复制成功", f"已复制{len(data)}行数据")
        batch_copy_btn.clicked.connect(do_batch_copy)
        
        batch_export_btn = QPushButton("批量导出")
        def do_batch_export():
            data = table.batch_export_data()
            if data:
                QMessageBox.information(window, "导出成功", 
                                      f"已导出{len(data['rows'])}行数据\\n包含{len(data['headers'])}列")
        batch_export_btn.clicked.connect(do_batch_export)
        
        # 批量模式切换
        batch_mode_btn = QPushButton("切换批量模式")
        def toggle_batch_mode():
            current = table.get_batch_operation_mode()
            table.set_batch_operation_mode(not current)
            mode_text = "批量模式" if not current else "普通模式"
            QMessageBox.information(window, "模式切换", f"已切换到{mode_text}")
        batch_mode_btn.clicked.connect(toggle_batch_mode)
        
        # 添加按钮到布局
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(clear_selection_btn)
        button_layout.addWidget(batch_edit_btn)
        button_layout.addWidget(batch_copy_btn)
        button_layout.addWidget(batch_export_btn)
        button_layout.addWidget(batch_mode_btn)
        button_layout.addStretch()
        
        # 添加组件到主布局
        layout.addLayout(button_layout)
        layout.addWidget(table)
        layout.addWidget(status_label)
        
        window.setCentralWidget(central_widget)
        
        # 显示窗口
        window.show()
        
        print("   ✅ 交互式测试窗口已打开")
        print("   💡 请测试各种多选和批量操作功能")
        print("   ⚠️ 测试完成后关闭窗口以继续")
        
        # 运行事件循环
        for i in range(50):  # 运行5秒
            app.processEvents()
            time.sleep(0.1)
        
        print("   ✅ 交互式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 交互式测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Phase 4 多选批量操作功能完整测试")
    print("📅 时间:", QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss"))
    print("📁 测试位置: test/test_phase4_batch_operations.py")
    print("=" * 60)
    
    results = []
    
    # 多选功能测试
    results.append(test_multi_selection())
    
    # 批量操作测试
    results.append(test_batch_operations())
    
    # 交互式测试
    results.append(test_interactive_batch_operations())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 P4-002 多选批量操作功能 - 测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 所有测试通过！P4-002任务完成")
        print("✅ Ctrl+Click非连续多选功能正常")
        print("✅ Shift+Click连续选择功能正常")
        print("✅ 批量编辑/复制/导出功能正常")
        print("✅ 选择状态管理和视觉反馈正常")
        status = "PASS"
    else:
        print(f"⚠️ 部分测试失败：{passed}/{total} 通过")
        status = "PARTIAL"
    
    print(f"\n📋 P4-002状态: {status}")
    print("🔄 下一步: P4-003 右键菜单系统")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 