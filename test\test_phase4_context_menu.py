#!/usr/bin/env python3
"""
Phase 4 - P4-003 右键菜单系统测试程序

测试虚拟化可展开表格的右键菜单功能：
- 上下文感知菜单显示
- 动态菜单项调整
- 菜单动作处理
- 性能和统计验证

作者: PyQt5重构项目组
创建时间: 2025-06-19 19:30
"""

import sys
import os
import time
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QTextEdit, QGroupBox, QCheckBox, QComboBox,
    QSpinBox, QProgressBar, QFrame, QSplitter, QTabWidget, QFormLayout,
    QListWidget, QListWidgetItem, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot, QPoint
from PyQt5.QtGui import QFont, QColor, QPalette

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
# 直接添加widgets目录
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src', 'gui', 'prototype', 'widgets'))

# 直接导入表格组件，避免包导入问题
from virtualized_expandable_table import (
    VirtualizedExpandableTable, ContextMenuType, MenuAction, 
    ContextMenuContext, create_sample_data
)

# 修复日志导入
try:
    from utils.log_config import setup_logger
except ImportError:
    # 如果无法导入日志配置，使用标准日志
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class Phase4ContextMenuTestWindow(QMainWindow):
    """P4-003 右键菜单系统测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        
        # 测试状态
        self.test_results = {}
        self.test_count = 0
        self.successful_tests = 0
        
        # 测试表格
        self.table = None
        self.context_menu_stats = {}
        
        self.setup_ui()
        self.setup_test_data()
        self.connect_signals()
        
        # 自动开始测试
        QTimer.singleShot(1000, self.run_automatic_tests)
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Phase 4 - P4-003 右键菜单系统测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2196F3;
            }
        """)
        
        # 主容器
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：表格和菜单测试区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 表格区域
        self.setup_table_area(left_layout)
        
        # 菜单测试控制
        self.setup_menu_controls(left_layout)
        
        splitter.addWidget(left_widget)
        
        # 右侧：测试结果和统计
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 测试结果显示
        self.setup_test_results_display(right_layout)
        
        # 菜单统计显示
        self.setup_menu_stats_display(right_layout)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([800, 600])
    
    def setup_table_area(self, layout):
        """设置表格测试区域"""
        group = QGroupBox("虚拟化可展开表格 - 右键菜单测试")
        group_layout = QVBoxLayout(group)
        
        # 表格说明
        info_label = QLabel("""
<b>右键菜单测试说明：</b><br>
• 在不同位置右键点击观察菜单变化<br>
• 尝试单选、多选状态下的菜单<br>
• 测试已展开/折叠行的菜单差异<br>
• 验证编辑状态下的菜单项<br>
• 观察空白区域和表头的菜单
        """)
        info_label.setStyleSheet("background-color: #e3f2fd; padding: 10px; border-radius: 5px;")
        group_layout.addWidget(info_label)
        
        # 创建表格
        self.table = VirtualizedExpandableTable()
        group_layout.addWidget(self.table)
        
        layout.addWidget(group)
    
    def setup_menu_controls(self, layout):
        """设置菜单测试控制"""
        group = QGroupBox("右键菜单测试控制")
        group_layout = QFormLayout(group)
        
        # 菜单启用控制
        self.menu_enabled_checkbox = QCheckBox("启用右键菜单")
        self.menu_enabled_checkbox.setChecked(True)
        self.menu_enabled_checkbox.stateChanged.connect(self.on_menu_enabled_changed)
        group_layout.addRow("菜单状态:", self.menu_enabled_checkbox)
        
        # 模拟右键点击按钮
        self.simulate_buttons_layout = QHBoxLayout()
        
        simulate_single_btn = QPushButton("模拟单行右键")
        simulate_single_btn.clicked.connect(lambda: self.simulate_context_menu("single_row"))
        self.simulate_buttons_layout.addWidget(simulate_single_btn)
        
        simulate_multi_btn = QPushButton("模拟多行右键")
        simulate_multi_btn.clicked.connect(lambda: self.simulate_context_menu("multi_row"))
        self.simulate_buttons_layout.addWidget(simulate_multi_btn)
        
        simulate_header_btn = QPushButton("模拟表头右键")
        simulate_header_btn.clicked.connect(lambda: self.simulate_context_menu("header"))
        self.simulate_buttons_layout.addWidget(simulate_header_btn)
        
        simulate_empty_btn = QPushButton("模拟空白右键")
        simulate_empty_btn.clicked.connect(lambda: self.simulate_context_menu("empty_area"))
        self.simulate_buttons_layout.addWidget(simulate_empty_btn)
        
        group_layout.addRow("模拟测试:", self.simulate_buttons_layout)
        
        # 测试按钮
        test_buttons_layout = QHBoxLayout()
        
        run_tests_btn = QPushButton("运行所有测试")
        run_tests_btn.clicked.connect(self.run_automatic_tests)
        run_tests_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        test_buttons_layout.addWidget(run_tests_btn)
        
        clear_results_btn = QPushButton("清除测试结果")
        clear_results_btn.clicked.connect(self.clear_test_results)
        test_buttons_layout.addWidget(clear_results_btn)
        
        show_stats_btn = QPushButton("显示菜单统计")
        show_stats_btn.clicked.connect(self.update_menu_stats)
        test_buttons_layout.addWidget(show_stats_btn)
        
        group_layout.addRow("测试操作:", test_buttons_layout)
        
        layout.addWidget(group)
    
    def setup_test_results_display(self, layout):
        """设置测试结果显示"""
        group = QGroupBox("测试结果")
        group_layout = QVBoxLayout(group)
        
        # 测试进度
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        group_layout.addWidget(self.progress_bar)
        
        # 测试状态标签
        self.status_label = QLabel("等待测试...")
        self.status_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        group_layout.addWidget(self.status_label)
        
        # 测试结果文本区域
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setFont(QFont("Consolas", 10))
        self.results_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555;
                border-radius: 5px;
            }
        """)
        group_layout.addWidget(self.results_text)
        
        layout.addWidget(group)
    
    def setup_menu_stats_display(self, layout):
        """设置菜单统计显示"""
        group = QGroupBox("右键菜单统计")
        group_layout = QVBoxLayout(group)
        
        # 统计信息显示
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setFont(QFont("Consolas", 9))
        self.stats_text.setMaximumHeight(200)
        group_layout.addWidget(self.stats_text)
        
        # 菜单类型列表
        self.menu_types_list = QListWidget()
        self.menu_types_list.setMaximumHeight(150)
        group_layout.addWidget(QLabel("支持的菜单类型:"))
        group_layout.addWidget(self.menu_types_list)
        
        layout.addWidget(group)
    
    def setup_test_data(self):
        """设置测试数据"""
        try:
            # 创建测试数据
            data, headers = create_sample_data(20)
            self.table.set_data(data, headers)
            
            # 填充菜单类型列表
            menu_types = [
                "SINGLE_ROW - 单行菜单",
                "MULTI_ROW - 多行菜单", 
                "CELL_EDIT - 单元格编辑菜单",
                "HEADER - 表头菜单",
                "EMPTY_AREA - 空白区域菜单",
                "EXPANDED_ROW - 已展开行菜单",
                "COLLAPSED_ROW - 已折叠行菜单"
            ]
            
            for menu_type in menu_types:
                item = QListWidgetItem(menu_type)
                if "单行" in menu_type or "多行" in menu_type:
                    item.setBackground(QColor("#e8f5e8"))
                elif "编辑" in menu_type:
                    item.setBackground(QColor("#fff3e0"))
                elif "表头" in menu_type or "空白" in menu_type:
                    item.setBackground(QColor("#e3f2fd"))
                else:
                    item.setBackground(QColor("#f3e5f5"))
                
                self.menu_types_list.addItem(item)
            
            self.log_test("测试数据设置完成", True)
            
        except Exception as e:
            self.log_test(f"设置测试数据失败: {e}", False)
    
    def connect_signals(self):
        """连接信号"""
        try:
            # 连接表格信号
            if self.table:
                self.table.context_menu_requested.connect(self.on_context_menu_requested)
                self.table.context_action_triggered.connect(self.on_context_action_triggered)
                self.table.selection_changed.connect(self.on_selection_changed)
                self.table.cell_edited.connect(self.on_cell_edited)
            
            self.log_test("信号连接完成", True)
            
        except Exception as e:
            self.log_test(f"连接信号失败: {e}", False)
    
    def run_automatic_tests(self):
        """运行自动化测试"""
        try:
            self.clear_test_results()
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 5)
            self.status_label.setText("正在运行右键菜单系统测试...")
            
            # 测试序列
            tests = [
                ("菜单系统初始化", self.test_menu_system_initialization),
                ("菜单上下文检测", self.test_context_detection),
                ("菜单项动态调整", self.test_menu_item_adjustment),
                ("菜单动作处理", self.test_menu_action_handling),
                ("菜单性能统计", self.test_menu_performance)
            ]
            
            for i, (test_name, test_func) in enumerate(tests):
                self.progress_bar.setValue(i)
                self.status_label.setText(f"执行测试: {test_name}")
                
                try:
                    success = test_func()
                    self.record_test_result(test_name, success)
                    time.sleep(0.3)  # 短暂延迟以便观察
                except Exception as e:
                    self.record_test_result(test_name, False, str(e))
            
            self.progress_bar.setValue(5)
            self.complete_tests()
            
        except Exception as e:
            self.log_test(f"自动化测试执行失败: {e}", False)
    
    def test_menu_system_initialization(self):
        """测试菜单系统初始化"""
        try:
            # 检查菜单组件是否存在
            if not hasattr(self.table, 'context_menu'):
                self.log_test("❌ 右键菜单组件不存在", False)
                return False
            
            # 检查菜单是否启用
            if not self.table.context_menu_enabled:
                self.log_test("❌ 右键菜单未启用", False)
                return False
            
            # 检查菜单配置
            menu_configs = self.table.context_menu.menu_configs
            if not menu_configs:
                self.log_test("❌ 菜单配置为空", False)
                return False
            
            config_count = sum(len(configs) for configs in menu_configs.values())
            self.log_test(f"✅ 菜单系统初始化成功: {len(menu_configs)}种菜单类型, {config_count}个菜单项", True)
            return True
            
        except Exception as e:
            self.log_test(f"❌ 菜单系统初始化测试失败: {e}", False)
            return False
    
    def test_context_detection(self):
        """测试上下文检测"""
        try:
            success_count = 0
            total_tests = 0
            
            # 测试不同位置的上下文检测
            test_positions = [
                (QPoint(50, 50), "表格内容区域"),
                (QPoint(50, 20), "表头区域"),
                (QPoint(500, 300), "可能的空白区域"),
                (QPoint(30, 80), "第一列(展开控制列)")
            ]
            
            for position, description in test_positions:
                total_tests += 1
                context = self.table._determine_context_menu_context(position)
                
                if context is not None:
                    success_count += 1
                    menu_type = context.menu_type.value if context.menu_type else "unknown"
                    self.log_test(f"  ✓ {description}: {menu_type}", True)
                else:
                    self.log_test(f"  ✗ {description}: 无法确定上下文", False)
            
            if success_count == total_tests:
                self.log_test(f"✅ 上下文检测测试完成: {success_count}/{total_tests} 通过", True)
                return True
            else:
                self.log_test(f"⚠️ 上下文检测部分通过: {success_count}/{total_tests}", False)
                return False
                
        except Exception as e:
            self.log_test(f"❌ 上下文检测测试失败: {e}", False)
            return False
    
    def test_menu_item_adjustment(self):
        """测试菜单项动态调整"""
        try:
            success_count = 0
            total_tests = 0
            
            # 测试单选状态
            total_tests += 1
            self.table.clearSelection()
            self.table.selectRow(0)
            context = ContextMenuContext(
                menu_type=ContextMenuType.SINGLE_ROW,
                clicked_row=0,
                clicked_column=1,
                selected_rows=[0],
                is_editing=False,
                row_expanded=False
            )
            menu_items = self.table.context_menu._build_menu_items(context)
            if menu_items:
                success_count += 1
                self.log_test(f"  ✓ 单选状态菜单: {len(menu_items)} 个菜单项", True)
            else:
                self.log_test("  ✗ 单选状态菜单项为空", False)
            
            # 测试多选状态
            total_tests += 1
            self.table.clearSelection()
            for i in range(3):
                self.table.selectRow(i)
            context = ContextMenuContext(
                menu_type=ContextMenuType.MULTI_ROW,
                clicked_row=1,
                clicked_column=1,
                selected_rows=[0, 1, 2],
                is_editing=False,
                row_expanded=False
            )
            multi_menu_items = self.table.context_menu._build_menu_items(context)
            if multi_menu_items:
                success_count += 1
                self.log_test(f"  ✓ 多选状态菜单: {len(multi_menu_items)} 个菜单项", True)
            else:
                self.log_test("  ✗ 多选状态菜单项为空", False)
            
            # 测试编辑状态
            total_tests += 1
            context = ContextMenuContext(
                menu_type=ContextMenuType.CELL_EDIT,
                clicked_row=0,
                clicked_column=1,
                selected_rows=[0],
                is_editing=True,
                row_expanded=False
            )
            edit_menu_items = self.table.context_menu._build_menu_items(context)
            if edit_menu_items:
                success_count += 1
                self.log_test(f"  ✓ 编辑状态菜单: {len(edit_menu_items)} 个菜单项", True)
            else:
                self.log_test("  ✗ 编辑状态菜单项为空", False)
            
            if success_count == total_tests:
                self.log_test(f"✅ 菜单项动态调整测试完成: {success_count}/{total_tests} 通过", True)
                return True
            else:
                self.log_test(f"⚠️ 菜单项动态调整部分通过: {success_count}/{total_tests}", False)
                return False
                
        except Exception as e:
            self.log_test(f"❌ 菜单项动态调整测试失败: {e}", False)
            return False
    
    def test_menu_action_handling(self):
        """测试菜单动作处理"""
        try:
            success_count = 0
            total_tests = 0
            
            # 测试基础动作
            test_actions = [
                ("copy", "复制动作"),
                ("select_all", "全选动作"),
                ("expand_all", "展开所有动作"),
                ("refresh_data", "刷新数据动作")
            ]
            
            for action_name, description in test_actions:
                total_tests += 1
                try:
                    context = ContextMenuContext(
                        menu_type=ContextMenuType.SINGLE_ROW,
                        clicked_row=0,
                        clicked_column=1,
                        selected_rows=[0]
                    )
                    
                    # 模拟动作触发
                    self.table._handle_context_action(action_name, context)
                    success_count += 1
                    self.log_test(f"  ✓ {description}: 处理成功", True)
                    
                except Exception as e:
                    self.log_test(f"  ✗ {description}: 处理失败 - {e}", False)
            
            if success_count == total_tests:
                self.log_test(f"✅ 菜单动作处理测试完成: {success_count}/{total_tests} 通过", True)
                return True
            else:
                self.log_test(f"⚠️ 菜单动作处理部分通过: {success_count}/{total_tests}", False)
                return False
                
        except Exception as e:
            self.log_test(f"❌ 菜单动作处理测试失败: {e}", False)
            return False
    
    def test_menu_performance(self):
        """测试菜单性能统计"""
        try:
            # 获取菜单统计信息
            stats = self.table.get_context_menu_stats()
            
            if not stats:
                self.log_test("❌ 无法获取菜单统计信息", False)
                return False
            
            # 检查统计指标
            required_keys = ['total_shows', 'action_counts', 'average_show_time']
            missing_keys = [key for key in required_keys if key not in stats]
            
            if missing_keys:
                self.log_test(f"❌ 统计信息缺少字段: {missing_keys}", False)
                return False
            
            # 记录统计信息
            self.context_menu_stats = stats.copy()
            
            self.log_test(f"✅ 菜单性能统计获取成功", True)
            self.log_test(f"  - 总显示次数: {stats.get('total_shows', 0)}", True)
            self.log_test(f"  - 平均显示时间: {stats.get('average_show_time', 0):.2f}ms", True)
            self.log_test(f"  - 动作统计: {len(stats.get('action_counts', {}))}", True)
            
            return True
            
        except Exception as e:
            self.log_test(f"❌ 菜单性能统计测试失败: {e}", False)
            return False
    
    def complete_tests(self):
        """完成测试"""
        try:
            success_rate = (self.successful_tests / self.test_count * 100) if self.test_count > 0 else 0
            
            self.status_label.setText(f"测试完成 - 成功率: {success_rate:.1f}%")
            self.progress_bar.setVisible(False)
            
            # 更新菜单统计显示
            self.update_menu_stats()
            
            # 输出总结
            self.log_test("\n" + "="*50, True)
            self.log_test("🎯 P4-003 右键菜单系统测试总结", True)
            self.log_test("="*50, True)
            self.log_test(f"📊 总测试项: {self.test_count}", True)
            self.log_test(f"✅ 成功项: {self.successful_tests}", True)
            self.log_test(f"❌ 失败项: {self.test_count - self.successful_tests}", True)
            self.log_test(f"📈 成功率: {success_rate:.1f}%", True)
            
            if success_rate >= 80:
                self.log_test("\n🎉 测试结果: 优秀 - 右键菜单系统运行正常!", True)
            elif success_rate >= 60:
                self.log_test("\n⚠️ 测试结果: 良好 - 部分功能需要优化", True)
            else:
                self.log_test("\n❌ 测试结果: 需要改进 - 存在重要问题", False)
            
        except Exception as e:
            self.log_test(f"完成测试时出错: {e}", False)
    
    def record_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        self.test_count += 1
        if success:
            self.successful_tests += 1
        
        self.test_results[test_name] = {
            'success': success,
            'details': details,
            'timestamp': time.time()
        }
        
        status = "✅ 通过" if success else "❌ 失败"
        message = f"{status} - {test_name}"
        if details:
            message += f": {details}"
        
        self.log_test(message, success)
    
    def log_test(self, message: str, success: bool = True):
        """记录测试日志"""
        color = "#00ff00" if success else "#ff6b6b"
        timestamp = time.strftime("%H:%M:%S")
        
        formatted_message = f'<span style="color: #888;">[{timestamp}]</span> <span style="color: {color};">{message}</span>'
        
        self.results_text.append(formatted_message)
        self.results_text.verticalScrollBar().setValue(
            self.results_text.verticalScrollBar().maximum()
        )
        
        QApplication.processEvents()
    
    def update_menu_stats(self):
        """更新菜单统计显示"""
        try:
            stats = self.table.get_context_menu_stats()
            
            if stats:
                stats_text = f"""右键菜单统计信息:
━━━━━━━━━━━━━━━━━━━━━━━━━━
总显示次数: {stats.get('total_shows', 0)}
平均显示时间: {stats.get('average_show_time', 0):.2f}ms
最常用动作: {stats.get('most_used_action', 'none')}

动作使用统计:
"""
                action_counts = stats.get('action_counts', {})
                for action, count in sorted(action_counts.items(), key=lambda x: x[1], reverse=True):
                    stats_text += f"  {action}: {count}次\n"
                
                self.stats_text.setPlainText(stats_text)
            else:
                self.stats_text.setPlainText("暂无菜单统计数据")
                
        except Exception as e:
            self.stats_text.setPlainText(f"获取统计信息失败: {e}")
    
    def simulate_context_menu(self, menu_type: str):
        """模拟右键菜单显示"""
        try:
            if menu_type == "single_row":
                # 选择一行并模拟右键
                self.table.clearSelection()
                self.table.selectRow(0)
                position = QPoint(100, 80)
            elif menu_type == "multi_row":
                # 选择多行并模拟右键
                self.table.clearSelection()
                for i in range(3):
                    self.table.selectRow(i)
                position = QPoint(100, 100)
            elif menu_type == "header":
                # 模拟表头右键
                position = QPoint(100, 25)
            elif menu_type == "empty_area":
                # 模拟空白区域右键
                position = QPoint(500, 400)
            else:
                return
            
            # 确定上下文并显示菜单
            context = self.table._determine_context_menu_context(position)
            if context:
                global_pos = self.table.mapToGlobal(position)
                self.table.context_menu.show_context_menu(context, global_pos)
                self.log_test(f"模拟{menu_type}右键菜单显示成功", True)
            else:
                self.log_test(f"模拟{menu_type}右键菜单失败：无法确定上下文", False)
                
        except Exception as e:
            self.log_test(f"模拟右键菜单失败: {e}", False)
    
    def clear_test_results(self):
        """清除测试结果"""
        self.test_results.clear()
        self.test_count = 0
        self.successful_tests = 0
        self.results_text.clear()
        self.status_label.setText("测试结果已清除")
    
    @pyqtSlot(object, object)
    def on_context_menu_requested(self, context, position):
        """处理右键菜单请求"""
        menu_type = context.menu_type.value if context.menu_type else "unknown"
        self.log_test(f"🖱️ 右键菜单请求: {menu_type} (行:{context.clicked_row}, 列:{context.clicked_column})", True)
    
    @pyqtSlot(str, object)
    def on_context_action_triggered(self, action_name: str, context):
        """处理右键菜单动作触发"""
        self.log_test(f"🎯 菜单动作触发: {action_name}", True)
    
    @pyqtSlot(list)
    def on_selection_changed(self, selected_rows):
        """处理选择变化"""
        if selected_rows:
            self.log_test(f"📝 选择变化: {len(selected_rows)}行被选中", True)
    
    @pyqtSlot(int, int, object, object)
    def on_cell_edited(self, row: int, column: int, old_value, new_value):
        """处理单元格编辑"""
        self.log_test(f"✏️ 单元格编辑: 行{row}, 列{column}, {old_value} → {new_value}", True)
    
    def on_menu_enabled_changed(self, state):
        """处理菜单启用状态变化"""
        enabled = state == Qt.Checked
        self.table.set_context_menu_enabled(enabled)
        self.log_test(f"菜单状态变更: {'启用' if enabled else '禁用'}", True)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = Phase4ContextMenuTestWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 