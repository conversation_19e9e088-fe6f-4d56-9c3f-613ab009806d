"""
Phase 4核心交互功能测试 - P4-004 快捷键支持系统

测试功能:
1. 快捷键注册和管理
2. 快捷键触发和处理
3. 快捷键帮助系统
4. 快捷键冲突检测
5. 快捷键使用统计

作者: PyQt5重构项目组
创建时间: 2025-06-19 18:30
"""

import sys
import os
import time
import random
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QLabel, QTextEdit, QGroupBox, QCheckBox, QSpinBox,
    QSplitter, QFrame, QMessageBox, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot, QPoint
from PyQt5.QtGui import QFont, QKeySequence

from src.gui.prototype.widgets.virtualized_expandable_table import (
    VirtualizedExpandableTable, create_sample_data, KeyboardShortcutManager
)
from src.utils.log_config import setup_logger


class Phase4KeyboardShortcutsTestWindow(QMainWindow):
    """Phase 4快捷键支持系统测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        
        # 设置窗口
        self.setWindowTitle("Phase 4核心交互功能测试 - P4-004 快捷键支持系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 测试状态
        self.test_results = {}
        self.test_count = 0
        self.success_count = 0
        
        # 初始化UI
        self.setup_ui()
        self.setup_test_data()
        
        # 连接信号
        self.connect_signals()
        
        # 开始测试
        QTimer.singleShot(1000, self.run_automatic_tests)
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧：表格测试区域
        left_frame = QFrame()
        left_frame.setFixedWidth(700)
        left_layout = QVBoxLayout(left_frame)
        
        # 表格组件
        self.table = VirtualizedExpandableTable()
        left_layout.addWidget(QLabel("快捷键支持表格 (试试按F1显示帮助)"))
        left_layout.addWidget(self.table)
        
        # 快捷键控制面板
        self.setup_shortcut_controls(left_layout)
        
        main_layout.addWidget(left_frame)
        
        # 右侧：测试结果区域
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)
        
        # 测试结果显示
        self.setup_test_results_display(right_layout)
        
        # 快捷键信息显示
        self.setup_shortcut_info_display(right_layout)
        
        main_layout.addWidget(right_frame)
    
    def setup_shortcut_controls(self, layout):
        """设置快捷键控制面板"""
        control_group = QGroupBox("快捷键控制")
        control_layout = QVBoxLayout(control_group)
        
        # 启用/禁用控制
        enable_layout = QHBoxLayout()
        self.shortcuts_enabled_cb = QCheckBox("启用快捷键")
        self.shortcuts_enabled_cb.setChecked(True)
        self.shortcuts_enabled_cb.stateChanged.connect(self.on_shortcuts_enabled_changed)
        enable_layout.addWidget(self.shortcuts_enabled_cb)
        
        # 显示帮助按钮
        self.show_help_btn = QPushButton("显示快捷键帮助 (F1)")
        self.show_help_btn.clicked.connect(self.show_shortcuts_help)
        enable_layout.addWidget(self.show_help_btn)
        
        control_layout.addLayout(enable_layout)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        self.test_shortcuts_btn = QPushButton("测试所有快捷键")
        self.test_shortcuts_btn.clicked.connect(self.test_all_shortcuts)
        test_layout.addWidget(self.test_shortcuts_btn)
        
        self.detect_conflicts_btn = QPushButton("检测快捷键冲突")
        self.detect_conflicts_btn.clicked.connect(self.detect_shortcut_conflicts)
        test_layout.addWidget(self.detect_conflicts_btn)
        
        control_layout.addLayout(test_layout)
        
        layout.addWidget(control_group)
    
    def setup_test_results_display(self, layout):
        """设置测试结果显示区域"""
        results_group = QGroupBox("测试结果")
        results_layout = QVBoxLayout(results_group)
        
        # 测试状态
        self.test_status_label = QLabel("准备开始测试...")
        self.test_status_label.setFont(QFont("Arial", 10, QFont.Bold))
        results_layout.addWidget(self.test_status_label)
        
        # 测试结果列表
        self.test_results_list = QListWidget()
        self.test_results_list.setMaximumHeight(200)
        results_layout.addWidget(self.test_results_list)
        
        # 测试日志
        self.test_log = QTextEdit()
        self.test_log.setMaximumHeight(200)
        self.test_log.setFont(QFont("Consolas", 9))
        results_layout.addWidget(QLabel("测试日志:"))
        results_layout.addWidget(self.test_log)
        
        layout.addWidget(results_group)
    
    def setup_shortcut_info_display(self, layout):
        """设置快捷键信息显示区域"""
        info_group = QGroupBox("快捷键信息")
        info_layout = QVBoxLayout(info_group)
        
        # 快捷键统计
        self.shortcut_stats_label = QLabel("加载中...")
        info_layout.addWidget(self.shortcut_stats_label)
        
        # 快捷键列表
        self.shortcut_list = QTextEdit()
        self.shortcut_list.setReadOnly(True)
        self.shortcut_list.setFont(QFont("Consolas", 9))
        info_layout.addWidget(self.shortcut_list)
        
        layout.addWidget(info_group)
    
    def setup_test_data(self):
        """设置测试数据"""
        try:
            # 创建测试数据
            data, headers = create_sample_data(15)
            self.table.set_data(data, headers)
            
            # 更新快捷键信息显示
            self.update_shortcut_info()
            
            self.log_test("测试数据设置完成: 15行数据")
            
        except Exception as e:
            self.log_test(f"设置测试数据失败: {e}", False)
    
    def connect_signals(self):
        """连接信号"""
        try:
            # 连接表格的快捷键信号
            self.table.shortcut_triggered.connect(self.on_shortcut_triggered)
            self.table.shortcut_help_requested.connect(self.on_shortcut_help_requested)
            
            # 连接其他表格信号
            self.table.cell_edited.connect(self.on_cell_edited)
            self.table.batch_operation_completed.connect(self.on_batch_operation_completed)
            
            self.log_test("信号连接完成")
            
        except Exception as e:
            self.log_test(f"信号连接失败: {e}", False)
    
    def run_automatic_tests(self):
        """运行自动测试"""
        try:
            self.log_test("开始自动测试...")
            self.test_status_label.setText("正在运行自动测试...")
            
            # 测试1: 快捷键管理器初始化
            self.test_shortcut_manager_initialization()
            
            # 测试2: 快捷键配置验证
            self.test_shortcut_configurations()
            
            # 测试3: 快捷键冲突检测
            self.test_shortcut_conflict_detection()
            
            # 测试4: 快捷键统计功能
            self.test_shortcut_statistics()
            
            # 测试5: 快捷键帮助系统
            self.test_shortcut_help_system()
            
            # 完成测试
            self.complete_tests()
            
        except Exception as e:
            self.log_test(f"自动测试失败: {e}", False)
    
    def test_shortcut_manager_initialization(self):
        """测试快捷键管理器初始化"""
        try:
            self.log_test("=" * 50)
            self.log_test("测试1: 快捷键管理器初始化")
            
            # 检查快捷键管理器是否存在
            assert hasattr(self.table, 'shortcut_manager'), "表格应该有shortcut_manager属性"
            assert self.table.shortcut_manager is not None, "快捷键管理器不应为空"
            
            # 检查快捷键是否注册
            manager = self.table.shortcut_manager
            assert len(manager.shortcuts) > 0, "应该注册了快捷键"
            assert len(manager.shortcut_configs) > 0, "应该有快捷键配置"
            
            # 检查基本快捷键
            expected_shortcuts = ['edit_cell', 'select_all', 'copy_data', 'show_help']
            for shortcut in expected_shortcuts:
                assert shortcut in manager.shortcut_configs, f"应该有快捷键: {shortcut}"
            
            self.record_test_result("快捷键管理器初始化", True, f"注册了{len(manager.shortcuts)}个快捷键")
            
        except Exception as e:
            self.record_test_result("快捷键管理器初始化", False, str(e))
    
    def test_shortcut_configurations(self):
        """测试快捷键配置验证"""
        try:
            self.log_test("测试2: 快捷键配置验证")
            
            manager = self.table.shortcut_manager
            configs = manager.shortcut_configs
            
            # 验证配置完整性
            required_fields = ['key', 'description', 'category', 'action', 'enabled']
            valid_configs = 0
            
            for name, config in configs.items():
                try:
                    for field in required_fields:
                        assert field in config, f"快捷键{name}缺少字段: {field}"
                    
                    # 验证按键序列有效
                    assert config['key'], f"快捷键{name}的按键序列不能为空"
                    
                    # 验证描述不为空
                    assert config['description'], f"快捷键{name}的描述不能为空"
                    
                    valid_configs += 1
                    
                except Exception as e:
                    self.log_test(f"快捷键配置验证失败: {name} - {e}")
            
            # 检查分类覆盖
            categories = set(config['category'] for config in configs.values())
            expected_categories = {'编辑', '选择', '剪贴板', '展开', '批量操作', '导航', '系统'}
            assert len(categories & expected_categories) >= 5, "应该覆盖主要的快捷键分类"
            
            self.record_test_result("快捷键配置验证", True, 
                                  f"验证了{valid_configs}/{len(configs)}个配置，覆盖{len(categories)}个分类")
            
        except Exception as e:
            self.record_test_result("快捷键配置验证", False, str(e))
    
    def test_shortcut_conflict_detection(self):
        """测试快捷键冲突检测"""
        try:
            self.log_test("测试3: 快捷键冲突检测")
            
            manager = self.table.shortcut_manager
            conflicts = manager.detect_conflicts()
            
            # 记录冲突情况
            if conflicts:
                self.log_test(f"检测到{len(conflicts)}个快捷键冲突:")
                for conflict in conflicts:
                    self.log_test(f"  冲突按键: {conflict['key']}")
                    self.log_test(f"  冲突快捷键: {conflict['shortcuts']}")
            else:
                self.log_test("未检测到快捷键冲突")
            
            # 验证冲突检测功能
            assert isinstance(conflicts, list), "冲突检测应该返回列表"
            
            # 检查冲突数量是否合理（通常应该很少或没有）
            conflict_rate = len(conflicts) / len(manager.shortcut_configs) if manager.shortcut_configs else 0
            assert conflict_rate < 0.1, f"快捷键冲突率过高: {conflict_rate:.2%}"
            
            self.record_test_result("快捷键冲突检测", True, 
                                  f"检测完成，发现{len(conflicts)}个冲突")
            
        except Exception as e:
            self.record_test_result("快捷键冲突检测", False, str(e))
    
    def test_shortcut_statistics(self):
        """测试快捷键统计功能"""
        try:
            self.log_test("测试4: 快捷键统计功能")
            
            manager = self.table.shortcut_manager
            stats = manager.get_usage_statistics()
            
            # 验证统计数据结构
            required_stats = ['total_shortcuts', 'enabled_shortcuts', 'registered_shortcuts', 
                            'total_usage_count', 'usage_stats']
            for stat in required_stats:
                assert stat in stats, f"统计数据缺少字段: {stat}"
            
            # 验证统计数据合理性
            assert stats['total_shortcuts'] > 0, "总快捷键数应该大于0"
            assert stats['enabled_shortcuts'] > 0, "启用的快捷键数应该大于0"
            assert stats['registered_shortcuts'] >= 0, "注册的快捷键数应该>=0"
            assert stats['total_usage_count'] >= 0, "总使用次数应该>=0"
            
            # 验证使用统计字典
            assert isinstance(stats['usage_stats'], dict), "使用统计应该是字典"
            
            self.record_test_result("快捷键统计功能", True, 
                                  f"总计{stats['total_shortcuts']}个快捷键，{stats['enabled_shortcuts']}个启用")
            
        except Exception as e:
            self.record_test_result("快捷键统计功能", False, str(e))
    
    def test_shortcut_help_system(self):
        """测试快捷键帮助系统"""
        try:
            self.log_test("测试5: 快捷键帮助系统")
            
            # 测试帮助文本生成
            help_text = self.table.get_shortcuts_help_text()
            assert isinstance(help_text, str), "帮助文本应该是字符串"
            assert len(help_text) > 100, "帮助文本应该有一定长度"
            assert "快捷键帮助" in help_text, "帮助文本应该包含标题"
            
            # 测试分类获取
            categories = self.table.get_shortcuts_by_category()
            assert isinstance(categories, dict), "分类应该是字典"
            assert len(categories) > 0, "应该有快捷键分类"
            
            # 验证分类结构
            for category, shortcuts in categories.items():
                assert isinstance(shortcuts, list), f"分类{category}应该是列表"
                if shortcuts:  # 如果分类不为空
                    shortcut = shortcuts[0]
                    required_fields = ['name', 'key', 'description', 'enabled']
                    for field in required_fields:
                        assert field in shortcut, f"快捷键信息缺少字段: {field}"
            
            self.record_test_result("快捷键帮助系统", True, 
                                  f"生成帮助文本{len(help_text)}字符，{len(categories)}个分类")
            
        except Exception as e:
            self.record_test_result("快捷键帮助系统", False, str(e))
    
    def complete_tests(self):
        """完成测试"""
        try:
            # 计算测试结果
            success_rate = (self.success_count / self.test_count) * 100 if self.test_count > 0 else 0
            
            # 更新状态
            status_text = f"测试完成: {self.success_count}/{self.test_count} ({success_rate:.1f}%)"
            self.test_status_label.setText(status_text)
            
            if success_rate >= 80:
                self.test_status_label.setStyleSheet("color: green; font-weight: bold;")
                result_msg = "✅ P4-004快捷键支持系统测试通过！"
            else:
                self.test_status_label.setStyleSheet("color: red; font-weight: bold;")
                result_msg = "❌ 部分测试失败，需要检查问题"
            
            self.log_test("=" * 50)
            self.log_test(result_msg)
            self.log_test(f"测试统计: {self.success_count}/{self.test_count} 通过")
            
            # 显示最终的快捷键信息
            self.update_shortcut_info()
            
        except Exception as e:
            self.log_test(f"完成测试时出错: {e}", False)
    
    def record_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        self.test_count += 1
        if success:
            self.success_count += 1
        
        # 添加到结果列表
        status = "✅" if success else "❌"
        item_text = f"{status} {test_name}"
        if details:
            item_text += f" - {details}"
        
        item = QListWidgetItem(item_text)
        if success:
            item.setBackground(Qt.green)
        else:
            item.setBackground(Qt.red)
        
        self.test_results_list.addItem(item)
        self.test_results[test_name] = {'success': success, 'details': details}
        
        # 记录到日志
        self.log_test(f"{status} {test_name}: {details}")
    
    def log_test(self, message: str, success: bool = True):
        """记录测试日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        self.test_log.append(log_entry)
        
        if not success:
            self.logger.error(message)
        else:
            self.logger.info(message)
    
    def update_shortcut_info(self):
        """更新快捷键信息显示"""
        try:
            # 获取统计信息
            stats = self.table.get_shortcut_usage_statistics()
            
            # 更新统计标签
            stats_text = (f"总快捷键: {stats.get('total_shortcuts', 0)} | "
                         f"已启用: {stats.get('enabled_shortcuts', 0)} | "
                         f"已注册: {stats.get('registered_shortcuts', 0)} | "
                         f"总使用: {stats.get('total_usage_count', 0)}次")
            self.shortcut_stats_label.setText(stats_text)
            
            # 更新快捷键列表
            categories = self.table.get_shortcuts_by_category()
            shortcut_text = "快捷键列表:\n" + "="*40 + "\n"
            
            for category, shortcuts in categories.items():
                shortcut_text += f"\n【{category}】\n"
                for shortcut in shortcuts:
                    status = "✓" if shortcut['enabled'] else "✗"
                    usage = f"({shortcut['usage_count']}次)" if shortcut['usage_count'] > 0 else ""
                    shortcut_text += f"  {status} {shortcut['key']:<15} {shortcut['description']} {usage}\n"
            
            self.shortcut_list.setPlainText(shortcut_text)
            
        except Exception as e:
            self.log_test(f"更新快捷键信息失败: {e}", False)
    
    # ==================== 事件处理方法 ====================
    
    @pyqtSlot(str, object)
    def on_shortcut_triggered(self, shortcut_name: str, context_data: Dict[str, Any]):
        """处理快捷键触发事件"""
        try:
            key_sequence = context_data.get('key_sequence', '')
            description = context_data.get('description', '')
            
            self.log_test(f"快捷键触发: {shortcut_name} ({key_sequence}) - {description}")
            
            # 更新快捷键信息（重新加载使用统计）
            QTimer.singleShot(100, self.update_shortcut_info)
            
        except Exception as e:
            self.log_test(f"处理快捷键触发事件失败: {e}", False)
    
    @pyqtSlot()
    def on_shortcut_help_requested(self):
        """处理快捷键帮助请求事件"""
        self.log_test("快捷键帮助请求事件触发")
    
    @pyqtSlot(int, int, object, object)
    def on_cell_edited(self, row: int, column: int, old_value, new_value):
        """处理单元格编辑事件"""
        self.log_test(f"单元格编辑: 行{row}, 列{column}, {old_value} -> {new_value}")
    
    @pyqtSlot(str, int, list)
    def on_batch_operation_completed(self, operation: str, count: int, affected_rows: list):
        """处理批量操作完成事件"""
        self.log_test(f"批量操作完成: {operation}, 影响{count}行")
    
    def on_shortcuts_enabled_changed(self, state):
        """处理快捷键启用状态变化"""
        enabled = state == Qt.Checked
        self.table.set_shortcuts_enabled(enabled)
        self.log_test(f"快捷键系统{'启用' if enabled else '禁用'}")
        self.update_shortcut_info()
    
    def show_shortcuts_help(self):
        """显示快捷键帮助"""
        self.table.show_shortcut_help()
        self.log_test("显示快捷键帮助对话框")
    
    def test_all_shortcuts(self):
        """测试所有快捷键"""
        try:
            self.log_test("开始手动测试快捷键...")
            
            # 这里可以模拟按键事件或者显示快捷键测试指导
            categories = self.table.get_shortcuts_by_category()
            
            test_guide = "快捷键测试指导:\n" + "="*30 + "\n"
            for category, shortcuts in categories.items():
                test_guide += f"\n【{category}】\n"
                for shortcut in shortcuts[:3]:  # 只显示前3个
                    if shortcut['enabled']:
                        test_guide += f"  按 {shortcut['key']} - {shortcut['description']}\n"
            
            # 显示测试指导
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "快捷键测试", 
                                  test_guide + f"\n总计可测试 {sum(len(s) for s in categories.values())} 个快捷键")
            
            self.log_test("显示快捷键测试指导")
            
        except Exception as e:
            self.log_test(f"测试所有快捷键失败: {e}", False)
    
    def detect_shortcut_conflicts(self):
        """检测快捷键冲突"""
        try:
            conflicts = self.table.detect_shortcut_conflicts()
            
            if conflicts:
                conflict_text = f"检测到 {len(conflicts)} 个快捷键冲突:\n\n"
                for conflict in conflicts:
                    conflict_text += f"按键: {conflict['key']}\n"
                    conflict_text += f"冲突的快捷键: {', '.join(conflict['shortcuts'])}\n"
                    conflict_text += f"功能: {', '.join(conflict['descriptions'])}\n\n"
            else:
                conflict_text = "未检测到快捷键冲突，所有快捷键配置正常！"
            
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "快捷键冲突检测", conflict_text)
            
            self.log_test(f"快捷键冲突检测完成: {len(conflicts)}个冲突")
            
        except Exception as e:
            self.log_test(f"检测快捷键冲突失败: {e}", False)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = Phase4KeyboardShortcutsTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 