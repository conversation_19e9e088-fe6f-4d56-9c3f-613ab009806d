#!/usr/bin/env python3
"""
PyQt5重构高保真原型 - 测试程序

测试原型主窗口的各项功能，包括：
- 三段式布局架构
- 响应式设计切换
- Material Design样式
- 组件通信机制

使用方法:
    python test_prototype.py
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.utils.log_config import setup_logger


class PrototypeTestRunner:
    """
    原型测试运行器
    """
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.logger = setup_logger(__name__)
    
    def setup_app(self):
        """
        设置应用程序
        """
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("PyQt5重构高保真原型")
        self.app.setApplicationVersion("2.0")
        
        # 设置应用程序样式
        self.app.setStyleSheet("""
            QApplication {
                font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
                font-size: 12px;
            }
        """)
        
        self.logger.info("应用程序初始化完成")
    
    def create_main_window(self):
        """
        创建主窗口
        """
        try:
            self.main_window = PrototypeMainWindow()
            self.logger.info("主窗口创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"主窗口创建失败: {e}")
            QMessageBox.critical(None, "错误", f"主窗口创建失败:\n{e}")
            return False
    
    def test_responsive_behavior(self):
        """
        测试响应式行为
        """
        if not self.main_window:
            return
            
        self.logger.info("开始测试响应式行为")
        
        # 测试不同窗口尺寸
        test_sizes = [
            (1024, 768),   # xs断点
            (1200, 800),   # sm断点
            (1400, 900),   # md断点
            (1600, 1000),  # lg断点
            (1920, 1080)   # xl断点
        ]
        
        def resize_test(index=0):
            if index < len(test_sizes):
                width, height = test_sizes[index]
                self.main_window.resize(width, height)
                
                breakpoint = self.main_window.responsive_manager.get_current_breakpoint()
                self.logger.info(f"测试尺寸 {width}x{height}: 断点 {breakpoint}")
                
                # 继续下一个测试
                QTimer.singleShot(1000, lambda: resize_test(index + 1))
            else:
                self.logger.info("响应式测试完成")
        
        # 开始测试
        QTimer.singleShot(2000, resize_test)
    
    def test_component_communication(self):
        """
        测试组件通信
        """
        if not self.main_window:
            return
            
        self.logger.info("开始测试组件通信")
        
        # 获取组件列表
        components = self.main_window.communication_manager.get_component_list()
        self.logger.info(f"已注册组件: {components}")
        
        # 获取连接映射
        connections = self.main_window.communication_manager.get_connection_map()
        self.logger.info(f"组件连接: {connections}")
        
        # 验证组件健康状态
        health = self.main_window.communication_manager.verify_components_health()
        self.logger.info(f"组件健康状态: {health}")
    
    def test_navigation_interaction(self):
        """
        测试导航交互
        """
        if not self.main_window:
            return
            
        self.logger.info("开始测试导航交互")
        
        # 模拟导航选择
        navigation_panel = self.main_window.navigation_panel
        tree_widget = navigation_panel.tree_widget
        
        # 选择第一个根节点
        root_item = tree_widget.topLevelItem(0)
        if root_item:
            tree_widget.setCurrentItem(root_item)
            self.logger.info(f"选择导航项: {root_item.text(0)}")
    
    def show_window(self):
        """
        显示窗口
        """
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
            
            # 启动测试
            QTimer.singleShot(1000, self.test_component_communication)
            QTimer.singleShot(2000, self.test_navigation_interaction)
            QTimer.singleShot(3000, self.test_responsive_behavior)
            
            self.logger.info("原型窗口已显示")
    
    def run(self):
        """
        运行测试
        """
        self.logger.info("开始运行原型测试")
        
        # 设置应用程序
        self.setup_app()
        
        # 创建主窗口
        if not self.create_main_window():
            return 1
        
        # 显示窗口
        self.show_window()
        
        # 运行应用程序
        self.logger.info("启动应用程序主循环")
        return self.app.exec_()


def main():
    """
    主函数
    """
    runner = PrototypeTestRunner()
    return runner.run()


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 