# -*- coding: utf-8 -*-
"""
PyQt5与Python 3.12兼容性测试
测试目的：验证PyQt5在Python 3.12环境下的基本功能
作者：系统测试团队
日期：2025-01-22
"""

import sys
import platform
import subprocess


def test_python_version():
    """测试Python版本"""
    print(f"Python版本: {sys.version}")
    print(f"Python版本信息: {sys.version_info}")
    
    # 检查是否为Python 3.12
    if sys.version_info.major == 3 and sys.version_info.minor == 12:
        print("✓ 运行在Python 3.12环境")
        return True
    else:
        print(f"⚠ 当前Python版本为 {sys.version_info.major}.{sys.version_info.minor}")
        return False


def test_pyqt5_import():
    """测试PyQt5基本导入"""
    try:
        import PyQt5
        print(f"✓ PyQt5导入成功，版本: {PyQt5.QtCore.PYQT_VERSION_STR}")
        return True
    except ImportError as e:
        print(f"✗ PyQt5导入失败: {e}")
        return False


def test_pyqt5_core_modules():
    """测试PyQt5核心模块"""
    test_results = {}
    
    # 测试QtCore
    try:
        from PyQt5 import QtCore
        test_results['QtCore'] = True
        print(f"✓ QtCore导入成功，Qt版本: {QtCore.QT_VERSION_STR}")
    except ImportError as e:
        test_results['QtCore'] = False
        print(f"✗ QtCore导入失败: {e}")
    
    # 测试QtGui
    try:
        from PyQt5 import QtGui
        test_results['QtGui'] = True
        print("✓ QtGui导入成功")
    except ImportError as e:
        test_results['QtGui'] = False
        print(f"✗ QtGui导入失败: {e}")
    
    # 测试QtWidgets
    try:
        from PyQt5 import QtWidgets
        test_results['QtWidgets'] = True
        print("✓ QtWidgets导入成功")
    except ImportError as e:
        test_results['QtWidgets'] = False
        print(f"✗ QtWidgets导入失败: {e}")
    
    return test_results


def test_pyqt5_basic_functionality():
    """测试PyQt5基本功能"""
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel
        from PyQt5.QtCore import Qt
        
        # 创建应用程序实例
        app = QApplication([])
        
        # 创建窗口
        window = QWidget()
        window.setWindowTitle("PyQt5 Python 3.12 兼容性测试")
        window.resize(300, 200)
        
        # 创建标签
        label = QLabel("测试成功！", window)
        label.setAlignment(Qt.AlignCenter)
        label.resize(280, 180)
        
        print("✓ PyQt5基本功能测试通过")
        
        # 不显示窗口，只测试创建
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ PyQt5基本功能测试失败: {e}")
        return False


def test_pyqt5_qt5_dependency():
    """测试PyQt5-Qt5依赖"""
    try:
        # 检查PyQt5-Qt5是否正确安装
        result = subprocess.run(
            [sys.executable, "-c", "import PyQt5.QtCore; print('PyQt5-Qt5 OK')"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✓ PyQt5-Qt5依赖正常")
            return True
        else:
            print(f"✗ PyQt5-Qt5依赖检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ PyQt5-Qt5依赖检查异常: {e}")
        return False


def test_memory_usage():
    """测试内存使用情况"""
    try:
        import psutil
        import os
        
        # 获取当前进程内存使用
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 导入PyQt5模块
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        app = QApplication([])
        
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = memory_after - memory_before
        
        print(f"✓ 内存使用测试 - 导入前: {memory_before:.1f}MB, 导入后: {memory_after:.1f}MB, 增加: {memory_increase:.1f}MB")
        
        app.quit()
        return True
        
    except ImportError:
        print("⚠ psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"✗ 内存使用测试失败: {e}")
        return False


def get_package_versions():
    """获取相关包版本信息"""
    packages = ['PyQt5', 'PyQt5-Qt5', 'sip']
    
    for package in packages:
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", package],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if line.startswith('Version:'):
                        version = line.split(':')[1].strip()
                        print(f"📦 {package}: {version}")
                        break
            else:
                print(f"📦 {package}: 未安装")
                
        except Exception as e:
            print(f"📦 {package}: 检查失败 - {e}")


def main():
    """主测试函数"""
    print("=" * 60)
    print("PyQt5与Python 3.12兼容性测试")
    print("=" * 60)
    
    # 系统信息
    print(f"\n🖥️ 系统信息:")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    
    # Python版本测试
    print(f"\n🐍 Python版本测试:")
    python_ok = test_python_version()
    
    # 包版本信息
    print(f"\n📦 包版本信息:")
    get_package_versions()
    
    # PyQt5导入测试
    print(f"\n📥 PyQt5导入测试:")
    import_ok = test_pyqt5_import()
    
    if not import_ok:
        print("\n❌ PyQt5导入失败，终止测试")
        return False
    
    # 核心模块测试
    print(f"\n🔧 核心模块测试:")
    modules_result = test_pyqt5_core_modules()
    
    # 基本功能测试
    print(f"\n⚙️ 基本功能测试:")
    functionality_ok = test_pyqt5_basic_functionality()
    
    # 依赖检查
    print(f"\n🔗 依赖检查:")
    dependency_ok = test_pyqt5_qt5_dependency()
    
    # 内存使用测试
    print(f"\n💾 内存使用测试:")
    memory_ok = test_memory_usage()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    all_core_modules_ok = all(modules_result.values()) if modules_result else False
    
    tests = [
        ("Python 3.12版本", python_ok),
        ("PyQt5导入", import_ok),
        ("核心模块", all_core_modules_ok),
        ("基本功能", functionality_ok),
        ("依赖检查", dependency_ok),
        ("内存使用", memory_ok)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！PyQt5与Python 3.12兼容性良好")
        return True
    else:
        print("⚠️ 部分测试失败，建议检查环境配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 