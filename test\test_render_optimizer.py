"""
渲染性能优化模块测试

测试GUI渲染性能优化的各个组件功能。
"""

import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QRect, QPointF, Qt
from PyQt5.QtGui import QPixmap, QColor, QPainter
from PyQt5.QtTest import QTest

# 添加项目路径
sys.path.append('.')

from src.gui.render_optimizer import (
    RenderMetrics, CacheItem, RenderCache, OptimizedPainter,
    AnimationManager, StyleOptimizer, PerformanceMonitor,
    OptimizedWidget, RenderOptimizer
)


class TestRenderMetrics(unittest.TestCase):
    """测试渲染性能指标"""
    
    def test_render_metrics_creation(self):
        """测试性能指标创建"""
        metrics = RenderMetrics()
        
        self.assertEqual(metrics.fps, 0.0)
        self.assertEqual(metrics.frame_time, 0.0)
        self.assertEqual(metrics.paint_time, 0.0)
        self.assertEqual(metrics.cache_hit_rate, 0.0)
        self.assertEqual(metrics.memory_usage, 0.0)
        self.assertEqual(metrics.gpu_usage, 0.0)
        self.assertEqual(metrics.draw_calls, 0)
        self.assertEqual(metrics.cached_items, 0)
    
    def test_render_metrics_update(self):
        """测试性能指标更新"""
        metrics = RenderMetrics()
        
        metrics.fps = 60.0
        metrics.frame_time = 16.67
        metrics.cache_hit_rate = 85.5
        
        self.assertEqual(metrics.fps, 60.0)
        self.assertEqual(metrics.frame_time, 16.67)
        self.assertEqual(metrics.cache_hit_rate, 85.5)


class TestCacheItem(unittest.TestCase):
    """测试缓存项"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
    
    def test_cache_item_creation(self):
        """测试缓存项创建"""
        pixmap = QPixmap(100, 100)
        pixmap.fill(QColor(255, 0, 0))
        
        item = CacheItem(
            key="test_key",
            pixmap=pixmap,
            last_used=time.time(),
            use_count=1,
            memory_size=40000
        )
        
        self.assertEqual(item.key, "test_key")
        self.assertEqual(item.pixmap.size(), pixmap.size())
        self.assertEqual(item.use_count, 1)
        self.assertEqual(item.memory_size, 40000)


class TestRenderCache(unittest.TestCase):
    """测试渲染缓存"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.cache = RenderCache(max_memory_mb=10)
    
    def test_cache_put_and_get(self):
        """测试缓存存取"""
        pixmap = QPixmap(50, 50)
        pixmap.fill(QColor(0, 255, 0))
        
        # 存储缓存项
        self.cache.put("test_key", pixmap)
        
        # 获取缓存项
        retrieved_pixmap = self.cache.get("test_key")
        
        self.assertIsNotNone(retrieved_pixmap)
        self.assertEqual(retrieved_pixmap.size(), pixmap.size())
        
        # 验证缓存中存在该项
        self.assertIn("test_key", self.cache.cache)
        
    def test_cache_miss(self):
        """测试缓存未命中"""
        retrieved_pixmap = self.cache.get("non_existent_key")
        self.assertIsNone(retrieved_pixmap)
        
    def test_cache_cleanup(self):
        """测试缓存清理"""
        # 添加多个缓存项
        for i in range(20):
            pixmap = QPixmap(100, 100)
            pixmap.fill(QColor(i * 10, 0, 0))
            self.cache.put(f"key_{i}", pixmap)
        
        initial_count = len(self.cache.cache)
        
        # 触发清理
        self.cache._cleanup()
        
        # 验证清理后缓存项减少
        self.assertLess(len(self.cache.cache), initial_count)


class TestOptimizedPainter(unittest.TestCase):
    """测试优化绘制器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.painter = OptimizedPainter()
    
    def test_batch_operations(self):
        """测试批量操作"""
        self.assertFalse(self.painter.is_batching)
        
        # 开始批量操作
        self.painter.begin_batch()
        self.assertTrue(self.painter.is_batching)
        
        # 模拟添加操作
        pixmap = QPixmap(100, 100)
        mock_painter = Mock()
        
        # 结束批量操作
        self.painter.end_batch(mock_painter)
        self.assertFalse(self.painter.is_batching)
    
    def test_cached_drawing(self):
        """测试缓存绘制"""
        pixmap = QPixmap(200, 200)
        pixmap.fill(QColor(255, 255, 255))
        
        mock_painter = Mock()
        rect = QRect(0, 0, 100, 100)
        color = QColor(255, 0, 0)
        
        # 第一次绘制（应该创建缓存）
        self.painter.draw_cached_rect(mock_painter, rect, color, "test_rect")
        
        # 验证缓存中存在该项
        cached_pixmap = self.painter.cache.get("test_rect")
        self.assertIsNotNone(cached_pixmap)


class TestAnimationManager(unittest.TestCase):
    """测试动画管理器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.animation_manager = AnimationManager()
    
    def test_create_fade_animation(self):
        """测试创建淡入动画"""
        from PyQt5.QtWidgets import QWidget
        
        widget = QWidget()
        animation = self.animation_manager.create_fade_animation(widget, 300)
        
        self.assertIsNotNone(animation)
        self.assertEqual(animation.duration(), 300)
        self.assertEqual(animation.startValue(), 0.0)
        self.assertEqual(animation.endValue(), 1.0)
    
    def test_create_slide_animation(self):
        """测试创建滑动动画"""
        from PyQt5.QtWidgets import QWidget
        
        widget = QWidget()
        start_pos = QPointF(0, 0)
        end_pos = QPointF(100, 100)
        
        animation = self.animation_manager.create_slide_animation(widget, start_pos, end_pos, 300)
        
        self.assertIsNotNone(animation)
        self.assertEqual(animation.duration(), 300)
        self.assertEqual(animation.startValue(), start_pos)
        self.assertEqual(animation.endValue(), end_pos)
    
    def test_create_scale_animation(self):
        """测试创建缩放动画"""
        from PyQt5.QtWidgets import QWidget
        
        widget = QWidget()
        widget.setGeometry(0, 0, 100, 100)
        
        animation = self.animation_manager.create_scale_animation(widget, 1.5, 200)
        
        self.assertIsNotNone(animation)
        self.assertEqual(animation.duration(), 200)


class TestStyleOptimizer(unittest.TestCase):
    """测试样式优化器"""
    
    def test_compile_stylesheet(self):
        """测试编译样式表"""
        optimizer = StyleOptimizer()
        
        original_css = """
        /* 这是注释 */
        QWidget {
            background-color: white;
            padding: 10px;
        }
        
        QPushButton {
            color: blue;
        }
        """
        
        compiled_css = optimizer.compile_stylesheet(original_css)
        
        # 验证注释被移除
        self.assertNotIn("/*", compiled_css)
        self.assertNotIn("*/", compiled_css)
        
        # 验证样式内容仍然存在
        self.assertIn("QWidget", compiled_css)
        self.assertIn("QPushButton", compiled_css)
        self.assertIn("background-color", compiled_css)
    
    def test_style_property_cache(self):
        """测试样式属性缓存"""
        optimizer = StyleOptimizer()
        
        # 缓存样式属性
        optimizer.cache_style_property("QPushButton", "color", "blue")
        
        # 获取缓存的属性
        cached_value = optimizer.get_cached_style_property("QPushButton", "color")
        
        self.assertEqual(cached_value, "blue")
        
        # 测试不存在的属性
        non_existent = optimizer.get_cached_style_property("QLabel", "font-size")
        self.assertIsNone(non_existent)


class TestPerformanceMonitor(unittest.TestCase):
    """测试性能监控器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.monitor = PerformanceMonitor()
    
    def test_monitor_creation(self):
        """测试监控器创建"""
        self.assertIsNotNone(self.monitor.metrics)
        self.assertIsInstance(self.monitor.metrics, RenderMetrics)
        self.assertEqual(len(self.monitor.frame_times), 0)
        self.assertEqual(self.monitor.max_frame_history, 60)
    
    def test_update_metrics(self):
        """测试更新性能指标"""
        test_metrics = RenderMetrics()
        test_metrics.fps = 60.0
        test_metrics.frame_time = 16.67
        test_metrics.cache_hit_rate = 90.0
        test_metrics.memory_usage = 50.0
        test_metrics.draw_calls = 100
        
        self.monitor.update_metrics(test_metrics)
        
        self.assertEqual(self.monitor.metrics.fps, 60.0)
        self.assertEqual(self.monitor.metrics.frame_time, 16.67)
        self.assertEqual(self.monitor.metrics.cache_hit_rate, 90.0)
        self.assertEqual(self.monitor.metrics.memory_usage, 50.0)
        self.assertEqual(self.monitor.metrics.draw_calls, 100)


class TestOptimizedWidget(unittest.TestCase):
    """测试优化控件"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.widget = OptimizedWidget()
    
    def test_widget_creation(self):
        """测试控件创建"""
        self.assertIsNotNone(self.widget.painter)
        self.assertIsNotNone(self.widget.animation_manager)
        self.assertIsNotNone(self.widget.style_optimizer)
        
        # 验证性能属性设置
        self.assertTrue(self.widget.testAttribute(Qt.WA_OpaquePaintEvent))
        self.assertTrue(self.widget.testAttribute(Qt.WA_NoSystemBackground))
        self.assertTrue(self.widget.testAttribute(Qt.WA_StaticContents))
    
    def test_animation_methods(self):
        """测试动画方法"""
        # 测试淡入动画
        self.widget.animate_fade_in(300)
        
        # 测试滑入动画
        self.widget.animate_slide_in("left", 300)
        self.widget.animate_slide_in("right", 300)
        self.widget.animate_slide_in("top", 300)
        self.widget.animate_slide_in("bottom", 300)
        
        # 验证动画管理器中有动画
        self.assertGreater(len(self.widget.animation_manager.animations), 0)


class TestRenderOptimizer(unittest.TestCase):
    """测试渲染优化器"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
        
        self.optimizer = RenderOptimizer()
    
    def test_optimizer_creation(self):
        """测试优化器创建"""
        self.assertIsNotNone(self.optimizer.render_cache)
        self.assertIsNotNone(self.optimizer.animation_manager)
        self.assertIsNotNone(self.optimizer.style_optimizer)
        self.assertIsNotNone(self.optimizer.performance_monitor)
        
        # 验证默认优化设置
        self.assertTrue(self.optimizer.optimization_enabled['hardware_acceleration'])
        self.assertTrue(self.optimizer.optimization_enabled['render_caching'])
        self.assertTrue(self.optimizer.optimization_enabled['batch_updates'])
        self.assertTrue(self.optimizer.optimization_enabled['style_optimization'])
        self.assertTrue(self.optimizer.optimization_enabled['animation_optimization'])
    
    def test_optimization_toggles(self):
        """测试优化开关"""
        # 测试硬件加速开关
        self.optimizer._on_hardware_accel_toggled(False)
        self.assertFalse(self.optimizer.optimization_enabled['hardware_acceleration'])
        
        # 测试渲染缓存开关
        self.optimizer._on_render_cache_toggled(False)
        self.assertFalse(self.optimizer.optimization_enabled['render_caching'])
        
        # 测试批量更新开关
        self.optimizer._on_batch_updates_toggled(False)
        self.assertFalse(self.optimizer.optimization_enabled['batch_updates'])
        
        # 测试样式优化开关
        self.optimizer._on_style_cache_toggled(False)
        self.assertFalse(self.optimizer.optimization_enabled['style_optimization'])
        
        # 测试动画优化开关
        self.optimizer._on_animation_opt_toggled(False)
        self.assertFalse(self.optimizer.optimization_enabled['animation_optimization'])
    
    def test_fps_limit_change(self):
        """测试FPS限制改变"""
        self.optimizer._on_fps_limit_changed(120)
        self.assertEqual(self.optimizer.fps_limit_label.text(), "FPS限制: 120")
        
        self.optimizer._on_fps_limit_changed(30)
        self.assertEqual(self.optimizer.fps_limit_label.text(), "FPS限制: 30")
    
    def test_apply_optimizations(self):
        """测试应用优化"""
        # 启用所有优化
        for key in self.optimizer.optimization_enabled:
            self.optimizer.optimization_enabled[key] = True
        
        # 应用优化
        self.optimizer._apply_optimizations()
        
        # 验证至少有一些优化被应用
        self.assertTrue(any(self.optimizer.optimization_enabled.values()))
    
    def test_clear_cache(self):
        """测试清理缓存"""
        # 添加一些缓存项
        pixmap = QPixmap(50, 50)
        self.optimizer.render_cache.put("test_key", pixmap)
        self.optimizer.style_optimizer.cache_style_property("test", "prop", "value")
        
        # 清理缓存
        self.optimizer._clear_cache()
        
        # 验证缓存被清理
        self.assertEqual(len(self.optimizer.style_optimizer.style_cache), 0)
        self.assertEqual(len(self.optimizer.style_optimizer.compiled_styles), 0)
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        metrics = self.optimizer.get_performance_metrics()
        
        self.assertIsInstance(metrics, RenderMetrics)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试"""
        if not QApplication.instance():
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
    
    def test_render_optimization_workflow(self):
        """测试渲染优化工作流"""
        # 创建渲染优化器
        optimizer = RenderOptimizer()
        
        # 验证初始状态
        self.assertIsInstance(optimizer.metrics, RenderMetrics)
        self.assertTrue(optimizer.optimization_enabled['hardware_acceleration'])
        
        # Mock Toast管理器以避免Qt对象生命周期问题
        with patch.object(optimizer.toast_manager, 'show_success') as mock_success:
            # 测试优化应用
            optimizer._apply_optimizations()
            mock_success.assert_called()
        
        # 测试缓存操作
        pixmap = QPixmap(100, 100)
        optimizer.render_cache.put("test", pixmap)
        retrieved = optimizer.render_cache.get("test")
        self.assertIsNotNone(retrieved)
        
        # 测试缓存清理
        optimizer._clear_cache()
        
        # 测试性能指标获取
        metrics = optimizer.get_performance_metrics()
        self.assertIsInstance(metrics, RenderMetrics)
    
    def test_animation_workflow(self):
        """测试动画工作流"""
        widget = OptimizedWidget()
        
        # 测试各种动画
        widget.animate_fade_in(300)
        widget.animate_slide_in("left", 300)
        
        # 验证动画管理器中有动画
        self.assertGreater(len(widget.animation_manager.animations), 0)
    
    def test_cache_and_style_integration(self):
        """测试缓存和样式集成"""
        optimizer = RenderOptimizer()
        
        # 测试样式编译和缓存
        css = "QWidget { color: red; }"
        compiled = optimizer.style_optimizer.compile_stylesheet(css)
        self.assertIsNotNone(compiled)
        
        # 测试样式属性缓存
        optimizer.style_optimizer.cache_style_property("QWidget", "color", "red")
        cached_value = optimizer.style_optimizer.get_cached_style_property("QWidget", "color")
        self.assertEqual(cached_value, "red")
        
        # 测试渲染缓存
        pixmap = QPixmap(50, 50)
        optimizer.render_cache.put("style_test", pixmap)
        retrieved = optimizer.render_cache.get("style_test")
        self.assertEqual(retrieved.size(), pixmap.size())


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestRenderMetrics,
        TestCacheItem,
        TestRenderCache,
        TestOptimizedPainter,
        TestAnimationManager,
        TestStyleOptimizer,
        TestPerformanceMonitor,
        TestOptimizedWidget,
        TestRenderOptimizer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1) 