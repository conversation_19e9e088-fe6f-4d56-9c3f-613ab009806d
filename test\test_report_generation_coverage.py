"""
Report Generation模块测试覆盖
测试目标：从15%-31%提升至80%以上覆盖率
重点：Excel生成、Word生成、报告管理、预览功能
"""
import pytest
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from openpyxl import Workbook
from docx import Document

# 报告生成模块导入
from src.modules.report_generation.excel_generator import ExcelGenerator
from src.modules.report_generation.word_generator import WordGenerator
from src.modules.report_generation.report_manager import ReportManager
from src.modules.report_generation.preview_manager import PreviewManager
from src.modules.report_generation.template_engine import TemplateEngine


class TestExcelGeneratorCoverage:
    """Excel生成器完整测试覆盖"""

    def setup_method(self):
        """测试环境设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.generator = ExcelGenerator()
        
        # 模拟数据
        self.sample_data = {
            'title': '月度薪资异动报告',
            'date': '2025-01-22',
            'employee_data': [
                {'employee_id': 'E001', 'name': '张三', 'salary_change': 500, 'change_type': '调薪'},
                {'employee_id': 'E002', 'name': '李四', 'salary_change': -200, 'change_type': '扣款'},
                {'employee_id': 'E003', 'name': '王五', 'salary_change': 300, 'change_type': '奖金'}
            ]
        }

    def teardown_method(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_excel_generator_initialization(self):
        """测试Excel生成器初始化"""
        generator = ExcelGenerator()
        assert generator is not None
        assert hasattr(generator, 'generate_report')
        assert hasattr(generator, 'template_engine')

    @patch.object(TemplateEngine, 'get_template_info')
    @patch.object(TemplateEngine, 'get_template_path')
    @patch.object(TemplateEngine, 'validate_template_data')
    def test_generate_report_basic(self, mock_validate, mock_get_path, mock_get_info):
        """测试基础报告生成"""
        # 设置Mock返回值
        mock_get_info.return_value = {'type': 'excel', 'name': 'test_report'}
        mock_get_path.return_value = Path(self.temp_dir) / 'template.xlsx'
        mock_validate.return_value = {'valid': True, 'missing_fields': []}
        
        # 创建模板文件
        template_path = Path(self.temp_dir) / 'template.xlsx'
        wb = Workbook()
        ws = wb.active
        ws['A1'] = '{{title}}'
        ws['A2'] = '{{date}}'
        wb.save(template_path)
        
        output_path = os.path.join(self.temp_dir, 'test_report.xlsx')
        
        result = self.generator.generate_report(
            template_key='test_template',
            data=self.sample_data,
            output_path=output_path
        )
        
        assert result is True
        assert os.path.exists(output_path)

    @patch.object(TemplateEngine, 'get_template_info')
    def test_generate_report_invalid_template_type(self, mock_get_info):
        """测试无效模板类型"""
        mock_get_info.return_value = {'type': 'word', 'name': 'test_report'}
        
        output_path = os.path.join(self.temp_dir, 'test_report.xlsx')
        
        result = self.generator.generate_report(
            template_key='invalid_template',
            data=self.sample_data,
            output_path=output_path
        )
        
        assert result is False

    def test_preprocess_data(self):
        """测试数据预处理"""
        processed_data = self.generator._preprocess_data(self.sample_data, 'test_template')
        
        assert processed_data is not None
        assert isinstance(processed_data, dict)
        assert 'title' in processed_data
        assert 'date' in processed_data

    @patch.object(TemplateEngine, 'get_template_info')
    def test_get_template_structure(self, mock_get_info):
        """测试获取模板结构"""
        mock_get_info.return_value = {'type': 'excel', 'name': 'test_report'}
        
        # 创建测试模板
        template_path = Path(self.temp_dir) / 'template.xlsx'
        wb = Workbook()
        ws = wb.active
        ws['A1'] = '{{title}}'
        ws['A2'] = '{{date}}'
        wb.save(template_path)
        
        with patch.object(self.generator.template_engine, 'get_template_path', return_value=template_path):
            structure = self.generator.get_template_structure('test_template')
            
            assert structure is not None
            assert isinstance(structure, dict)

    def test_create_summary_report(self):
        """测试创建汇总报告"""
        change_data = [
            {'name': '张三', 'change_amount': 500, 'change_type': '调薪'},
            {'name': '李四', 'change_amount': -200, 'change_type': '扣款'}
        ]
        
        output_path = os.path.join(self.temp_dir, 'summary_report.xlsx')
        
        result = self.generator.create_summary_report(
            change_data=change_data,
            output_path=output_path
        )
        
        assert result is True
        assert os.path.exists(output_path)

    @patch.object(TemplateEngine, 'get_template_info')
    @patch.object(TemplateEngine, 'get_template_path')
    def test_preview_report(self, mock_get_path, mock_get_info):
        """测试报告预览"""
        mock_get_info.return_value = {'type': 'excel', 'name': 'test_report'}
        
        # 创建模板文件
        template_path = Path(self.temp_dir) / 'template.xlsx'
        wb = Workbook()
        ws = wb.active
        ws['A1'] = '{{title}}'
        wb.save(template_path)
        mock_get_path.return_value = template_path
        
        preview_path = self.generator.preview_report('test_template', self.sample_data)
        
        assert preview_path is not None
        assert os.path.exists(preview_path)


class TestWordGeneratorCoverage:
    """Word生成器完整测试覆盖"""

    def setup_method(self):
        """测试环境设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模板目录和TemplateEngine实例
        self.template_dir = os.path.join(self.temp_dir, 'template')
        os.makedirs(self.template_dir, exist_ok=True)
        
        # 使用Mock的TemplateEngine来避免依赖问题
        with patch('src.modules.report_generation.template_engine.TemplateEngine') as MockTemplateEngine:
            mock_engine = Mock()
            MockTemplateEngine.return_value = mock_engine
            self.generator = WordGenerator(template_engine=mock_engine)
        
        self.sample_data = {
            'title': '月度薪资异动报告',
            'date': '2025-01-22',
            'summary': '本月共处理3人次薪资异动',
            'employee_data': [
                {'name': '张三', 'change': 500, 'reason': '绩效调薪'},
                {'name': '李四', 'change': -200, 'reason': '迟到扣款'},
                {'name': '王五', 'change': 300, 'reason': '项目奖金'}
            ]
        }

    def teardown_method(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_word_generator_initialization(self):
        """测试Word生成器初始化"""
        with patch('src.modules.report_generation.template_engine.TemplateEngine'):
            generator = WordGenerator()
            assert generator is not None
            assert hasattr(generator, 'generate_document')
            assert hasattr(generator, 'template_engine')

    def test_generate_document_basic(self):
        """测试基础文档生成"""
        # 设置Mock返回值
        self.generator.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        self.generator.template_engine.get_template_path.return_value = Path(self.temp_dir) / 'template.docx'
        self.generator.template_engine.validate_template_data.return_value = {'valid': True, 'missing_fields': []}
        
        # 创建模板文件
        template_path = Path(self.temp_dir) / 'template.docx'
        doc = Document()
        doc.add_heading('{{title}}', 0)
        doc.add_paragraph('报告日期：{{date}}')
        doc.save(template_path)
        
        output_path = os.path.join(self.temp_dir, 'test_document.docx')
        
        result = self.generator.generate_document(
            template_key='test_template',
            data=self.sample_data,
            output_path=output_path
        )
        
        assert result is True
        assert os.path.exists(output_path)

    def test_generate_document_invalid_template_type(self):
        """测试无效模板类型"""
        self.generator.template_engine.get_template_info.return_value = {'type': 'excel', 'name': 'test_document'}
        
        output_path = os.path.join(self.temp_dir, 'test_document.docx')
        
        result = self.generator.generate_document(
            template_key='invalid_template',
            data=self.sample_data,
            output_path=output_path
        )
        
        assert result is False

    def test_preprocess_data(self):
        """测试数据预处理"""
        processed_data = self.generator._preprocess_data(self.sample_data, 'test_template')
        
        assert processed_data is not None
        assert isinstance(processed_data, dict)
        assert 'title' in processed_data

    def test_get_template_placeholders(self):
        """测试获取模板占位符"""
        self.generator.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        
        # 创建测试模板
        template_path = Path(self.temp_dir) / 'template.docx'
        doc = Document()
        doc.add_heading('{{title}}', 0)
        doc.add_paragraph('{{date}} - {{summary}}')
        doc.save(template_path)
        
        self.generator.template_engine.get_template_path.return_value = template_path
        
        placeholders = self.generator.get_template_placeholders('test_template')
        
        assert placeholders is not None
        assert isinstance(placeholders, list)

    def test_preview_document(self):
        """测试文档预览"""
        self.generator.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        
        # 创建模板文件
        template_path = Path(self.temp_dir) / 'template.docx'
        doc = Document()
        doc.add_heading('{{title}}', 0)
        doc.save(template_path)
        
        self.generator.template_engine.get_template_path.return_value = template_path
        self.generator.template_engine.validate_template_data.return_value = {'valid': True, 'missing_fields': []}
        
        # Mock generate_document方法使其返回True
        with patch.object(self.generator, 'generate_document', return_value=True):
            preview_path = self.generator.preview_document('test_template', self.sample_data)
            
            assert preview_path is not None
            assert preview_path.endswith('.docx')


class TestReportManagerCoverage:
    """报告管理器完整测试覆盖"""

    def setup_method(self):
        """测试环境设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 使用Mock来避免依赖问题
        with patch('src.modules.report_generation.report_manager.TemplateEngine') as MockTemplateEngine, \
             patch('src.modules.report_generation.report_manager.WordGenerator') as MockWordGenerator, \
             patch('src.modules.report_generation.report_manager.ExcelGenerator') as MockExcelGenerator:
            
            self.manager = ReportManager(output_base_path=self.temp_dir)
            
        self.sample_data = {
            'title': '月度薪资异动报告',
            'date': '2025-01-22',
            'employee_data': [
                {'employee_id': 'E001', 'name': '张三', 'salary_change': 500},
                {'employee_id': 'E002', 'name': '李四', 'salary_change': -200}
            ]
        }

    def teardown_method(self):
        """清理测试环境"""
        if hasattr(self, 'manager'):
            self.manager.shutdown()
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_report_manager_initialization(self):
        """测试报告管理器初始化"""
        with patch('src.modules.report_generation.report_manager.TemplateEngine'), \
             patch('src.modules.report_generation.report_manager.WordGenerator'), \
             patch('src.modules.report_generation.report_manager.ExcelGenerator'):
            
            manager = ReportManager()
            assert manager is not None
            assert hasattr(manager, 'generate_report')
            assert hasattr(manager, 'batch_generate')
            assert hasattr(manager, 'get_task_status')

    def test_generate_report_sync_word(self):
        """测试同步生成Word报告"""
        self.manager.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        self.manager.word_generator.generate_document.return_value = True
        
        result = self.manager.generate_report(
            template_key='test_template',
            data=self.sample_data,
            async_mode=False
        )
        
        assert result is True

    def test_generate_report_sync_excel(self):
        """测试同步生成Excel报告"""
        self.manager.template_engine.get_template_info.return_value = {'type': 'excel', 'name': 'test_report'}
        self.manager.excel_generator.generate_report.return_value = True
        
        result = self.manager.generate_report(
            template_key='test_template',
            data=self.sample_data,
            async_mode=False
        )
        
        assert result is True

    def test_generate_report_async(self):
        """测试异步生成报告"""
        self.manager.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        
        task_id = self.manager.generate_report(
            template_key='test_template',
            data=self.sample_data,
            async_mode=True
        )
        
        assert task_id is not None
        assert isinstance(task_id, str)

    def test_batch_generate(self):
        """测试批量生成报告"""
        self.manager.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        
        generation_requests = [
            {
                'template_key': 'template1',
                'data': self.sample_data,
                'output_filename': 'report1.docx'
            },
            {
                'template_key': 'template2',
                'data': self.sample_data,
                'output_filename': 'report2.docx'
            }
        ]
        
        results = self.manager.batch_generate(
            generation_requests=generation_requests,
            async_mode=True
        )
        
        assert len(results) == 2
        assert all(isinstance(r, str) for r in results if r is not None)

    def test_task_status_management(self):
        """测试任务状态管理"""
        self.manager.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        
        # 提交异步任务
        task_id = self.manager.generate_report(
            template_key='test_template',
            data=self.sample_data,
            async_mode=True
        )
        
        # 检查任务状态
        status = self.manager.get_task_status(task_id)
        assert status is not None
        assert 'task_id' in status
        assert 'status' in status

    def test_get_all_tasks(self):
        """测试获取所有任务"""
        self.manager.template_engine.get_template_info.return_value = {'type': 'word', 'name': 'test_document'}
        
        # 提交几个任务
        for i in range(3):
            self.manager.generate_report(
                template_key=f'template_{i}',
                data=self.sample_data,
                async_mode=True
            )
        
        all_tasks = self.manager.get_all_tasks()
        assert len(all_tasks) >= 3

    def test_cleanup_completed_tasks(self):
        """测试清理已完成任务"""
        # 先添加一些已完成的任务
        from src.modules.report_generation.report_manager import ReportGenerationTask
        
        for i in range(5):
            task = ReportGenerationTask(
                task_id=f'task_{i}',
                template_key=f'template_{i}',
                data=self.sample_data,
                output_path=f'/tmp/output_{i}.docx',
                task_type='word'
            )
            task.status = 'completed'
            self.manager.tasks[task.task_id] = task
        
        cleaned_count = self.manager.cleanup_completed_tasks(keep_recent=2)
        assert cleaned_count >= 0
        assert len(self.manager.tasks) <= 5

    def test_generation_history(self):
        """测试生成历史记录"""
        # 手动添加历史记录
        self.manager._save_generation_history(
            template_key='test_template',
            output_path='/tmp/test.docx',
            data=self.sample_data,
            success=True
        )
        
        history = self.manager.get_generation_history(limit=10)
        assert len(history) >= 1
        assert history[0]['template_key'] == 'test_template'

    def test_cleanup_output_files(self):
        """测试清理输出文件"""
        # 创建一些测试文件
        test_files = []
        for i in range(3):
            test_file = Path(self.temp_dir) / f'test_file_{i}.docx'
            test_file.touch()
            test_files.append(test_file)
        
        # 清理文件
        cleaned_count = self.manager.cleanup_output_files(older_than_days=0)
        assert cleaned_count >= 0


class TestPreviewManagerCoverage:
    """预览管理器完整测试覆盖"""

    def setup_method(self):
        """测试环境设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.preview_manager = PreviewManager(temp_dir=self.temp_dir)
        
        # 创建测试文件
        self.excel_file = os.path.join(self.temp_dir, 'test.xlsx')
        self.word_file = os.path.join(self.temp_dir, 'test.docx')
        
        # 创建Excel文件
        wb = Workbook()
        ws = wb.active
        ws['A1'] = '测试数据'
        ws['B1'] = '数值'
        ws['A2'] = '张三'
        ws['B2'] = 1000
        wb.save(self.excel_file)
        
        # 创建Word文件
        doc = Document()
        doc.add_heading('测试文档', 0)
        doc.add_paragraph('这是测试内容')
        doc.save(self.word_file)

    def teardown_method(self):
        """清理测试环境"""
        if hasattr(self, 'preview_manager'):
            try:
                self.preview_manager.cleanup_temp_files()
            except:
                pass
        if os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
            except OSError:
                # Windows文件锁定问题，忽略
                pass

    def test_preview_manager_initialization(self):
        """测试预览管理器初始化"""
        manager = PreviewManager()
        assert manager is not None
        assert hasattr(manager, 'preview_document')
        assert hasattr(manager, 'print_document')

    def test_preview_document_nonexistent_file(self):
        """测试预览不存在的文件"""
        nonexistent_file = os.path.join(self.temp_dir, 'nonexistent.xlsx')
        
        result = self.preview_manager.preview_document(
            file_path=nonexistent_file,
            preview_type='extract'
        )
        
        assert result is False

    def test_preview_document_invalid_type(self):
        """测试无效的预览类型"""
        result = self.preview_manager.preview_document(
            file_path=self.excel_file,
            preview_type='invalid_type'
        )
        
        assert result is False

    @patch('os.startfile')
    def test_preview_with_system_windows(self, mock_startfile):
        """测试Windows系统预览"""
        with patch.object(self.preview_manager, 'system', 'windows'):
            result = self.preview_manager.preview_document(
                file_path=self.excel_file,
                preview_type='system'
            )
            
            assert result is True
            mock_startfile.assert_called_once_with(self.excel_file)

    @patch('subprocess.run')
    @patch('platform.system')
    def test_preview_with_system_macos(self, mock_system, mock_run):
        """测试macOS系统预览"""
        mock_system.return_value = 'Darwin'
        
        # 重新创建PreviewManager以使用新的platform.system返回值
        preview_manager = PreviewManager(temp_dir=self.temp_dir)
        
        result = preview_manager.preview_document(
            file_path=self.excel_file,
            preview_type='system'
        )

        assert result is True
        mock_run.assert_called_once_with(['open', self.excel_file])

    def test_extract_word_content(self):
        """测试Word内容提取"""
        content = self.preview_manager._extract_word_content(self.word_file)
        
        assert content is not None
        assert '测试文档' in content
        assert '这是测试内容' in content

    def test_extract_excel_content(self):
        """测试Excel内容提取"""
        content = self.preview_manager._extract_excel_content(self.excel_file)
        
        assert content is not None
        assert '测试数据' in content
        assert '张三' in content

    def test_preview_with_copy(self):
        """测试复制预览"""
        with patch.object(self.preview_manager, '_preview_with_system', return_value=True) as mock_system:
            result = self.preview_manager.preview_document(
                file_path=self.excel_file,
                preview_type='copy'
            )
            
            assert result is True
            mock_system.assert_called_once()

    def test_preview_history(self):
        """测试预览历史记录"""
        # 添加预览历史
        self.preview_manager._add_preview_history(self.excel_file, 'system')
        self.preview_manager._add_preview_history(self.word_file, 'extract')
        
        history = self.preview_manager.get_preview_history(limit=5)
        
        assert len(history) >= 2
        assert any(h['file_path'] == self.excel_file for h in history)
        assert any(h['file_path'] == self.word_file for h in history)

    def test_cleanup_temp_files(self):
        """测试清理临时文件"""
        # 创建一些临时文件
        temp_file1 = os.path.join(self.temp_dir, 'temp1.txt')
        temp_file2 = os.path.join(self.temp_dir, 'temp2.txt')
        
        with open(temp_file1, 'w') as f:
            f.write('test')
        with open(temp_file2, 'w') as f:
            f.write('test')
            
        self.preview_manager.temp_files.add(temp_file1)
        self.preview_manager.temp_files.add(temp_file2)
        
        cleaned_count = self.preview_manager.cleanup_temp_files()
        
        assert cleaned_count >= 0

    @patch('subprocess.run')
    def test_print_document(self, mock_run):
        """测试文档打印"""
        with patch.object(self.preview_manager, 'system', 'windows'):
            result = self.preview_manager.print_document(
                file_path=self.word_file,
                printer_name='Microsoft Print to PDF'
            )
            
            # 由于打印功能依赖系统环境，这里主要测试方法调用
            assert result in [True, False]

    def test_get_available_printers(self):
        """测试获取可用打印机"""
        printers = self.preview_manager.get_available_printers()
        
        assert isinstance(printers, list)
        # 打印机列表可能为空，这取决于系统环境


class TestTemplateEngineCoverage:
    """模板引擎完整测试覆盖"""

    def setup_method(self):
        """测试环境设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模板目录
        self.template_dir = os.path.join(self.temp_dir, 'template')
        os.makedirs(self.template_dir, exist_ok=True)
        
        # 创建一些测试模板文件
        self._create_test_templates()
        
        # 使用指定的模板目录初始化模板引擎
        self.engine = TemplateEngine(template_base_path=self.template_dir)

    def _create_test_templates(self):
        """创建测试模板文件"""
        # 创建Excel模板
        excel_path = os.path.join(self.template_dir, 'test.xlsx')
        wb = Workbook()
        ws = wb.active
        ws['A1'] = '{{title}}'
        ws['A2'] = '{{date}}'
        wb.save(excel_path)
        
        # 创建Word模板
        word_path = os.path.join(self.template_dir, 'test.docx')
        doc = Document()
        doc.add_heading('{{title}}', 0)
        doc.add_paragraph('Date: {{date}}, Summary: {{summary}}')
        doc.save(word_path)

    def teardown_method(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
            except OSError:
                # Windows文件锁定问题，忽略
                pass

    def test_template_engine_initialization(self):
        """测试模板引擎初始化"""
        # 创建临时模板目录
        temp_template_dir = os.path.join(self.temp_dir, 'test_template')
        os.makedirs(temp_template_dir, exist_ok=True)
        
        engine = TemplateEngine(template_base_path=temp_template_dir)
        assert engine is not None
        assert hasattr(engine, 'get_template_info')
        assert hasattr(engine, 'get_template_path')

    def test_extract_placeholders(self):
        """测试占位符提取"""
        text = "Hello {{name}}, today is {{date}}"
        placeholders = self.engine.extract_placeholders(text)
        
        assert 'name' in placeholders
        assert 'date' in placeholders

    def test_replace_placeholders(self):
        """测试占位符替换"""
        text = "Hello {{name}}, today is {{date}}"
        data = {'name': '张三', 'date': '2025-01-22'}
        
        result = self.engine.replace_placeholders(text, data)
        
        assert result == "Hello 张三, today is 2025-01-22"

    def test_replace_placeholders_missing_data(self):
        """测试占位符替换 - 缺少数据"""
        text = "Hello {{name}}, today is {{date}}"
        data = {'name': '张三'}  # 缺少date
        
        result = self.engine.replace_placeholders(text, data)
        
        # 缺少的占位符应该保持原样或替换为空字符串
        assert '张三' in result
        assert ('{{date}}' in result or result.count('{{') == 0)

    def test_validate_template_data(self):
        """测试模板数据验证"""
        # 测试已定义模板的验证
        complete_data = {'month': '5', 'year': '2025', 'total_amount': 50000, 'employee_count': 10}
        result = self.engine.validate_template_data('salary_approval', complete_data)
        
        assert result['valid'] is True
        assert len(result['missing_fields']) == 0
        
        # 测试不完整数据的验证
        incomplete_data = {'month': '5', 'year': '2025'}  # 缺少total_amount和employee_count
        result = self.engine.validate_template_data('salary_approval', incomplete_data)
        
        assert result['valid'] is False
        assert 'total_amount' in result['missing_fields']
        assert 'employee_count' in result['missing_fields']
        
        # 测试未定义模板的验证（应该返回valid=True）
        result = self.engine.validate_template_data('unknown_template', complete_data)
        assert result['valid'] is True 