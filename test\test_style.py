#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""测试样式加载脚本"""

import sys
import traceback

def test_style_import():
    """测试样式导入"""
    try:
        print("1. 测试基础导入...")
        from src.gui.modern_style import MaterialDesignPalette
        print("   MaterialDesignPalette 导入成功")
        
        print("2. 测试样式表导入...")
        from src.gui.modern_style import ModernStyleSheet
        print("   ModernStyleSheet 导入成功")
        
        print("3. 测试样式生成...")
        style = ModernStyleSheet.get_complete_style()
        print(f"   样式长度: {len(style)} 字符")
        
        if len(style) == 0:
            print("   警告: 样式为空!")
        else:
            print("   样式预览:")
            print("   " + style[:300] + "..." if len(style) > 300 else style)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()
        return False

def test_main_window_import():
    """测试主窗口导入"""
    try:
        print("\n4. 测试主窗口导入...")
        from src.gui.modern_main_window import ModernMainWindow
        print("   ModernMainWindow 导入成功")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖模块"""
    try:
        print("\n5. 测试依赖模块...")
        
        # 测试 toast_system
        try:
            from src.gui.toast_system import toast_manager
            print("   toast_system 导入成功")
        except Exception as e:
            print(f"   toast_system 导入失败: {e}")
        
        # 测试 dialogs
        try:
            from src.gui.dialogs import DataImportDialog
            print("   dialogs 导入成功")
        except Exception as e:
            print(f"   dialogs 导入失败: {e}")
            
        # 测试 windows
        try:
            from src.gui.windows import ChangeDetectionWindow
            print("   windows 导入成功")
        except Exception as e:
            print(f"   windows 导入失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试样式和导入问题...")
    
    # 添加项目路径
    from pathlib import Path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    # 运行测试
    success = True
    success &= test_style_import()
    success &= test_main_window_import()
    success &= test_dependencies()
    
    if success:
        print("\n✓ 所有测试通过")
    else:
        print("\n✗ 存在问题需要修复")
    
    sys.exit(0 if success else 1) 