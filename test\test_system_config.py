"""
月度工资异动处理系统 - 系统配置模块测试

测试系统配置模块的各个组件：
- ConfigManager: 配置管理器
- TemplateManager: 模板管理器
- UserPreferences: 用户偏好设置
- SystemMaintenance: 系统维护工具

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
作者: 开发团队
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 导入测试模块
from src.modules.system_config import (
    ConfigManager, 
    TemplateManager, 
    UserPreferences, 
    SystemMaintenance
)
from src.utils.log_config import setup_logger

# 初始化日志
logger = setup_logger("test_system_config")


def test_config_manager():
    """测试配置管理器"""
    print("\n" + "="*60)
    print("🔧 测试配置管理器 (ConfigManager)")
    print("="*60)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 加载配置
        config = config_manager.load_config()
        print(f"✅ 配置加载成功，版本: {config.get('version', 'unknown')}")
        
        # 获取配置值
        db_name = config_manager.get_config_value("database.db_name")
        print(f"✅ 数据库名称: {db_name}")
        
        # 设置配置值
        success = config_manager.set_config_value("test.value", "test_data", save_immediately=False)
        if success:
            print("✅ 配置值设置成功")
        
        # 验证配置
        validator = config_manager.validator
        is_valid, errors = validator.validate_config(config)
        print(f"✅ 配置验证: {'通过' if is_valid else '失败'}")
        if errors:
            for error in errors[:3]:  # 只显示前3个错误
                print(f"   - {error}")
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {str(e)}")
        assert False, f"配置管理器测试失败: {str(e)}"


def test_template_manager():
    """测试模板管理器"""
    print("\n" + "="*60)
    print("📄 测试模板管理器 (TemplateManager)")
    print("="*60)
    
    try:
        # 创建模板管理器
        template_manager = TemplateManager()
        
        # 列出所有模板
        templates = template_manager.list_templates()
        print(f"✅ 发现 {len(templates)} 个模板文件")
        
        if templates:
            for template in templates[:3]:  # 只显示前3个模板
                print(f"   - {template['name']} ({template['type']})")
        
        # 检查模板变化
        changes = template_manager.check_template_changes()
        if changes:
            print(f"✅ 检测到 {len(changes)} 个模板发生变化")
        else:
            print("✅ 所有模板文件状态正常")
        
    except Exception as e:
        print(f"❌ 模板管理器测试失败: {str(e)}")
        assert False, f"模板管理器测试失败: {str(e)}"


def test_user_preferences():
    """测试用户偏好设置"""
    print("\n" + "="*60)
    print("⚙️ 测试用户偏好设置 (UserPreferences)")
    print("="*60)
    
    try:
        # 创建用户偏好管理器
        preferences = UserPreferences()
        
        # 加载偏好设置
        prefs = preferences.load_preferences()
        print(f"✅ 偏好设置加载成功，版本: {prefs.get('version', 'unknown')}")
        
        # 获取偏好值
        theme = preferences.get_preference("ui.theme", "default")
        print(f"✅ 界面主题: {theme}")
        
        # 设置偏好值
        success = preferences.set_preference("test.setting", "test_value", save_immediately=False)
        if success:
            print("✅ 偏好设置成功")
        
        # 更新最近使用
        success = preferences.update_last_used("import_directory", str(project_root / "data"))
        if success:
            print("✅ 最近使用目录更新成功")
        
    except Exception as e:
        print(f"❌ 用户偏好设置测试失败: {str(e)}")
        assert False, f"用户偏好设置测试失败: {str(e)}"


def test_system_maintenance():
    """测试系统维护工具"""
    print("\n" + "="*60)
    print("🔧 测试系统维护工具 (SystemMaintenance)")
    print("="*60)
    
    try:
        # 创建系统维护工具
        maintenance = SystemMaintenance()
        
        # 收集系统信息
        system_info = maintenance.collect_system_info()
        if "error" not in system_info:
            platform_info = system_info.get("platform", {})
            print(f"✅ 系统信息收集成功")
            print(f"   - 操作系统: {platform_info.get('system', 'unknown')}")
            print(f"   - 处理器: {platform_info.get('processor', 'unknown')}")
        else:
            print(f"⚠️ 系统信息收集出现错误: {system_info['error']}")
        
        # 性能检查
        performance = maintenance.performance_check()
        if "error" not in performance:
            print(f"✅ 性能检查完成")
            print(f"   - CPU使用率: {performance.get('cpu_usage', 0):.1f}%")
            print(f"   - 内存使用率: {performance.get('memory_usage', 0):.1f}%")
            
            # 显示建议
            recommendations = performance.get('recommendations', [])
            if recommendations:
                print("   - 优化建议:")
                for rec in recommendations[:2]:  # 只显示前2个建议
                    print(f"     • {rec}")
        else:
            print(f"⚠️ 性能检查出现错误: {performance['error']}")
        
        # 系统诊断（简化版，避免过多输出）
        print("✅ 执行系统诊断...")
        diagnosis = maintenance.system_diagnosis()
        overall_status = diagnosis.get("overall_status", "unknown")
        print(f"   - 总体状态: {overall_status}")
        
    except Exception as e:
        print(f"❌ 系统维护工具测试失败: {str(e)}")
        assert False, f"系统维护工具测试失败: {str(e)}"


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始测试系统配置模块...")
    print(f"📍 项目根目录: {project_root}")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("配置管理器", test_config_manager()))
    test_results.append(("模板管理器", test_template_manager()))
    test_results.append(("用户偏好设置", test_user_preferences()))
    test_results.append(("系统维护工具", test_system_maintenance()))
    
    # 显示测试结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("🎉 所有测试通过！系统配置模块工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关组件。")
    
    return failed == 0


if __name__ == "__main__":
    # 执行测试
    success = run_all_tests()
    
    # 退出状态码
    sys.exit(0 if success else 1) 