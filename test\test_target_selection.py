#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目标选择组件测试

测试动态目标选择组件的功能，包括智能推断、动态新增等。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt

from src.gui.widgets.target_selection_widget import TargetSelectionWidget
from src.gui.dialogs import DataImportDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("目标选择组件测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
目标选择组件测试

功能测试：
1. 智能推断当前年月
2. 动态新增年份
3. 动态新增人员类别
4. 路径预览实时更新
5. 配置文件自动保存加载

操作说明：
- 修改任意选择项，观察路径预览变化
- 点击"+ 新增年份"添加下一年
- 点击"+ 新增类别"添加自定义人员类别
- 点击"测试导入对话框"查看集成效果
        """)
        info_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 添加目标选择组件
        self.target_widget = TargetSelectionWidget()
        self.target_widget.target_changed.connect(self._on_target_changed)
        self.target_widget.new_category_added.connect(self._on_new_category_added)
        layout.addWidget(self.target_widget)
        
        # 添加当前选择显示
        self.current_selection_label = QLabel("当前选择: 未初始化")
        self.current_selection_label.setStyleSheet("font-weight: bold; color: #0078d4;")
        layout.addWidget(self.current_selection_label)
        
        # 添加测试按钮
        test_dialog_btn = QPushButton("测试导入对话框")
        test_dialog_btn.clicked.connect(self._test_import_dialog)
        layout.addWidget(test_dialog_btn)
        
        # 添加路径设置测试按钮
        test_path_btn = QPushButton("测试路径设置")
        test_path_btn.clicked.connect(self._test_path_setting)
        layout.addWidget(test_path_btn)
        
        # 初始化显示
        self._update_current_selection()
    
    def _on_target_changed(self, target_info):
        """处理目标变化"""
        print(f"目标变化: {target_info}")
        self._update_current_selection()
    
    def _on_new_category_added(self, category_info):
        """处理新增类别"""
        print(f"新增类别: {category_info}")
    
    def _update_current_selection(self):
        """更新当前选择显示"""
        target = self.target_widget.get_current_target()
        path_str = self.target_widget.get_target_path_string()
        
        display_text = f"""
当前选择:
  模块: {target['module']}
  年份: {target['year']}
  月份: {target['month']}
  类别: {target['category']}
  
完整路径: {path_str}
        """.strip()
        
        self.current_selection_label.setText(display_text)
    
    def _test_import_dialog(self):
        """测试导入对话框"""
        current_path = self.target_widget.get_target_path_string()
        dialog = DataImportDialog(self, target_path=current_path)
        dialog.exec_()
    
    def _test_path_setting(self):
        """测试路径设置功能"""
        test_paths = [
            "工资表 > 2024年 > 12月 > 退休人员",
            "异动人员表 > 2025年 > 6月 > 起薪",
            "工资表 > 2025年 > 7月 > 全部在职人员"
        ]
        
        import random
        test_path = random.choice(test_paths)
        print(f"设置测试路径: {test_path}")
        
        self.target_widget.set_target_from_path(test_path)
        self._update_current_selection()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("目标选择组件测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    return app.exec_()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 