#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户接受测试 (User Acceptance Test) 框架

本模块实现端到端的用户接受测试，验证系统功能完整性和用户体验。

测试覆盖:
1. 端到端业务流程测试
2. 边界条件和异常场景测试  
3. 用户体验流畅性评估
4. 数据完整性验证
5. 性能可接受度测试
"""

import unittest
import tempfile
import shutil
import os
import sys
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Any, Optional
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.application_service import ApplicationService, ServiceResult
from src.utils.log_config import setup_logger

class UserAcceptanceTestBase(unittest.TestCase):
    """用户接受测试基类"""
    
    def setUp(self):
        """测试前置准备"""
        self.logger = setup_logger(f"{__name__}.{self.__class__.__name__}")
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="uat_test_")
        self.test_data_dir = Path(self.temp_dir) / "test_data"
        self.output_dir = Path(self.temp_dir) / "output" 
        self.backup_dir = Path(self.temp_dir) / "backup"
        
        # 创建必要的目录
        for dir_path in [self.test_data_dir, self.output_dir, self.backup_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建应用服务实例
        self.app_service = ApplicationService()
        
        # 性能基准
        self.performance_thresholds = {
            "initialization_time": 5.0,  # 初始化时间 ≤ 5秒
            "data_import_time": 10.0,    # 数据导入时间 ≤ 10秒  
            "change_detection_time": 15.0, # 异动检测时间 ≤ 15秒
            "report_generation_time": 20.0, # 报告生成时间 ≤ 20秒
            "memory_usage_mb": 500.0,    # 内存使用 ≤ 500MB
        }
        
        # 测试统计
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "performance_issues": [],
            "error_messages": []
        }
        
        self.logger.info(f"UAT测试环境初始化完成: {self.temp_dir}")
    
    def tearDown(self):
        """测试后置清理"""
        try:
            # 清理应用服务
            if hasattr(self.app_service, 'cleanup'):
                self.app_service.cleanup()
            
            # 清理临时目录
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            
            self.logger.info("UAT测试环境清理完成")
            
        except Exception as e:
            self.logger.error(f"UAT测试清理失败: {e}")
    
    def _create_mock_excel_data(self, filename: str) -> str:
        """创建模拟Excel测试数据"""
        file_path = self.test_data_dir / filename
        
        # 模拟Excel文件内容 (这里用JSON模拟)
        mock_data = {
            "员工信息": [
                {"工号": "001", "姓名": "张三", "身份证": "110101199001011234"},
                {"工号": "002", "姓名": "李四", "身份证": "110101199002021234"},
                {"工号": "003", "姓名": "王五", "身份证": "110101199003031234"}
            ],
            "工资数据": [
                {"工号": "001", "基本工资": 5000, "绩效奖金": 1000},
                {"工号": "002", "基本工资": 6000, "绩效奖金": 1200},
                {"工号": "003", "基本工资": 5500, "绩效奖金": 1100}
            ]
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(mock_data, f, ensure_ascii=False, indent=2)
        
        return str(file_path)
    
    def _measure_performance(self, operation_name: str, operation_func, *args, **kwargs):
        """测量操作性能"""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            result = operation_func(*args, **kwargs)
            success = True
        except Exception as e:
            result = e
            success = False
        
        end_time = time.time()
        end_memory = self._get_memory_usage()
        
        execution_time = end_time - start_time
        memory_used = max(0, end_memory - start_memory)
        
        # 记录性能数据
        performance_data = {
            "operation": operation_name,
            "execution_time": execution_time,
            "memory_used": memory_used,
            "success": success,
            "result": result
        }
        
        # 检查性能阈值
        threshold = self.performance_thresholds.get(f"{operation_name.lower()}_time")
        if threshold and execution_time > threshold:
            self.performance_issues.append(f"{operation_name}执行时间过长: {execution_time:.2f}s > {threshold}s")
        
        memory_threshold = self.performance_thresholds.get("memory_usage_mb")
        if memory_threshold and memory_used > memory_threshold: 
            self.performance_issues.append(f"{operation_name}内存使用过多: {memory_used:.2f}MB > {memory_threshold}MB")
        
        self.logger.info(f"性能测量 - {operation_name}: {execution_time:.2f}s, {memory_used:.2f}MB")
        
        return performance_data
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量 (MB)"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _assert_service_result(self, result: ServiceResult, operation_name: str):
        """验证服务结果"""
        self.test_results["total_tests"] += 1
        
        if result.success:
            self.test_results["passed_tests"] += 1
            self.logger.info(f"✅ {operation_name}: {result.message}")
        else:
            self.test_results["failed_tests"] += 1
            error_msg = f"❌ {operation_name}: {result.message}"
            self.test_results["error_messages"].append(error_msg)
            self.logger.error(error_msg)
        
        self.assertTrue(result.success, f"{operation_name} 失败: {result.message}")


class TestEndToEndWorkflow(UserAcceptanceTestBase):
    """端到端业务流程测试"""
    
    def test_complete_business_workflow(self):
        """测试完整业务流程 - 核心UAT场景"""
        print("\n" + "="*80)
        print("🚀 开始端到端业务流程测试")
        print("="*80)
        
        # === 阶段1: 系统初始化 ===
        print("\n📋 阶段1: 系统初始化")
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(), 
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            perf_data = self._measure_performance(
                "initialization", 
                self.app_service.initialize
            )
            
            self._assert_service_result(perf_data["result"], "系统初始化")
            print(f"   ✅ 初始化时间: {perf_data['execution_time']:.2f}秒")
        
        # === 阶段2: 数据导入 ===
        print("\n📂 阶段2: 数据导入")
        
        # 创建测试数据
        salary_file = self._create_mock_excel_data("工资表.xlsx")
        baseline_file = self._create_mock_excel_data("基准数据.xlsx")
        
        with patch('os.path.exists', return_value=True):
            # 导入工资数据
            perf_data = self._measure_performance(
                "data_import",
                self.app_service.import_excel_data,
                salary_file
            )
            self._assert_service_result(perf_data["result"], "工资数据导入")
            
            # 导入基准数据
            perf_data = self._measure_performance(
                "baseline_import", 
                self.app_service.import_baseline_data,
                baseline_file
            )
            self._assert_service_result(perf_data["result"], "基准数据导入")
        
        # === 阶段3: 数据验证 ===
        print("\n🔍 阶段3: 数据验证")
        
        # Mock数据验证结果
        mock_validation = Mock()
        mock_validation.is_valid = True
        mock_validation.error_summary = ""
        self.app_service._data_validator.validate_all.return_value = mock_validation
        
        perf_data = self._measure_performance(
            "data_validation",
            self.app_service.validate_data
        )
        self._assert_service_result(perf_data["result"], "数据验证")
        
        # === 阶段4: 异动检测 ===
        print("\n🎯 阶段4: 异动检测")
        
        # Mock异动检测结果
        mock_detection = Mock()
        mock_detection.changes = [
            {"employee_id": "001", "change_type": "工资调整", "amount": 500},
            {"employee_id": "002", "change_type": "奖金发放", "amount": 200}
        ]
        self.app_service._change_detector.detect_all_changes.return_value = mock_detection
        
        perf_data = self._measure_performance(
            "change_detection",
            self.app_service.detect_changes
        )
        self._assert_service_result(perf_data["result"], "异动检测")
        
        change_count = len(perf_data["result"].data.changes) if perf_data["result"].data else 0
        print(f"   ✅ 检测到异动: {change_count}个")
        
        # === 阶段5: 报告生成 ===
        print("\n📊 阶段5: 报告生成")
        
        # Mock报告生成
        self.app_service._change_detector.get_latest_results.return_value = mock_detection
        self.app_service._report_manager.generate_word_report.return_value = str(self.output_dir / "report.docx")
        self.app_service._report_manager.generate_excel_report.return_value = str(self.output_dir / "report.xlsx")
        
        # 生成Word报告
        perf_data = self._measure_performance(
            "report_generation",
            self.app_service.generate_word_report
        )
        self._assert_service_result(perf_data["result"], "Word报告生成")
        
        # 生成Excel报告
        perf_data = self._measure_performance(
            "excel_report_generation", 
            self.app_service.generate_excel_report
        )
        self._assert_service_result(perf_data["result"], "Excel报告生成")
        
        # === 阶段6: 数据导出 ===
        print("\n💾 阶段6: 数据导出")
        
        export_file = str(self.output_dir / "异动数据.xlsx")
        self.app_service._excel_importer.export_changes = Mock()
        
        perf_data = self._measure_performance(
            "data_export",
            self.app_service.export_change_data,
            export_file
        )
        self._assert_service_result(perf_data["result"], "异动数据导出")
        
        # === 流程完成 ===
        print("\n🎉 端到端业务流程测试完成")
        print(f"   📊 总测试: {self.test_results['total_tests']}")
        print(f"   ✅ 通过: {self.test_results['passed_tests']}")
        print(f"   ❌ 失败: {self.test_results['failed_tests']}")
        print(f"   📈 成功率: {(self.test_results['passed_tests']/self.test_results['total_tests']*100):.1f}%")
        
        # 验证整体成功率
        success_rate = self.test_results['passed_tests'] / self.test_results['total_tests']
        self.assertGreaterEqual(success_rate, 0.95, "端到端业务流程成功率应≥95%")
    
    def test_parallel_operations_workflow(self):
        """测试并发操作工作流程"""
        print("\n" + "="*60)
        print("🔄 并发操作工作流程测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(), 
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            init_result = self.app_service.initialize()
            self.assertTrue(init_result.success)
            
            # 模拟并发数据导入
            files = [
                self._create_mock_excel_data(f"数据{i}.xlsx") 
                for i in range(1, 4)
            ]
            
            with patch('os.path.exists', return_value=True):
                import_results = []
                start_time = time.time()
                
                for file_path in files:
                    result = self.app_service.import_excel_data(file_path)
                    import_results.append(result)
                
                total_time = time.time() - start_time
                
                # 验证所有导入都成功
                for i, result in enumerate(import_results):
                    self.assertTrue(result.success, f"文件{i+1}导入失败")
                
                print(f"   ✅ 并发导入3个文件耗时: {total_time:.2f}秒")
                print(f"   ✅ 平均单文件处理: {total_time/len(files):.2f}秒")
                
                # 性能要求: 并发处理不应显著降低性能
                self.assertLess(total_time, 30.0, "并发操作总时间应<30秒")


class TestBoundaryConditions(UserAcceptanceTestBase):
    """边界条件和异常场景测试"""
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        print("\n" + "="*60)
        print("🔍 空数据处理测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # 测试没有异动数据的报告生成
            self.app_service._change_detector.get_latest_results.return_value = None
            
            result = self.app_service.generate_word_report()
            self.assertFalse(result.success)
            self.assertIn("没有可生成报告的异动数据", result.message)
            
            print("   ✅ 空数据报告生成正确处理")
            
            # 测试空异动检测
            mock_empty_detection = Mock()
            mock_empty_detection.changes = []
            self.app_service._change_detector.detect_all_changes.return_value = mock_empty_detection
            
            result = self.app_service.detect_changes()
            self.assertTrue(result.success)
            self.assertEqual(len(result.data.changes), 0)
            
            print("   ✅ 空异动检测正确处理")
    
    def test_large_data_handling(self):
        """测试大数据量处理"""
        print("\n" + "="*60)
        print("📈 大数据量处理测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # 模拟大量异动数据
            large_changes = [
                {"employee_id": f"{i:06d}", "change_type": "调薪", "amount": 100 + i}
                for i in range(10000)  # 10,000条异动记录
            ]
            
            mock_large_detection = Mock()
            mock_large_detection.changes = large_changes
            self.app_service._change_detector.detect_all_changes.return_value = mock_large_detection
            
            # 测试大数据异动检测
            start_time = time.time()
            result = self.app_service.detect_changes()
            detection_time = time.time() - start_time
            
            self.assertTrue(result.success)
            print(f"   ✅ 大数据异动检测: {len(large_changes)}条记录, {detection_time:.2f}秒")
            
            # 性能要求: 大数据处理应在合理时间内完成
            self.assertLess(detection_time, 60.0, "大数据异动检测应<60秒")
    
    def test_invalid_file_handling(self):
        """测试无效文件处理"""
        print("\n" + "="*60)
        print("⚠️ 无效文件处理测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # 测试不存在的文件
            result = self.app_service.import_excel_data("不存在的文件.xlsx")
            self.assertFalse(result.success)
            self.assertIn("文件不存在", result.message)
            print("   ✅ 不存在文件正确处理")
            
            # 测试空文件路径
            result = self.app_service.import_excel_data("")
            self.assertFalse(result.success)
            print("   ✅ 空文件路径正确处理")
            
            # 测试None文件路径
            result = self.app_service.import_excel_data(None)
            self.assertFalse(result.success)
            print("   ✅ None文件路径正确处理")


class TestUserExperience(UserAcceptanceTestBase):
    """用户体验流畅性测试"""
    
    def test_system_responsiveness(self):
        """测试系统响应性"""
        print("\n" + "="*60)
        print("⚡ 系统响应性测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 测试系统初始化响应时间
            start_time = time.time()
            result = self.app_service.initialize()
            init_time = time.time() - start_time
            
            self.assertTrue(result.success)
            self.assertLess(init_time, 5.0, "系统初始化应<5秒")
            print(f"   ✅ 系统初始化响应时间: {init_time:.2f}秒")
            
            # 测试状态查询响应时间
            start_time = time.time()
            result = self.app_service.get_system_status()
            status_time = time.time() - start_time
            
            self.assertTrue(result.success)
            self.assertLess(status_time, 1.0, "状态查询应<1秒")
            print(f"   ✅ 状态查询响应时间: {status_time:.2f}秒")
    
    def test_error_message_clarity(self):
        """测试错误消息清晰度"""
        print("\n" + "="*60)
        print("💬 错误消息清晰度测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # 测试文件不存在错误
            result = self.app_service.import_excel_data("不存在.xlsx")
            self.assertFalse(result.success)
            self.assertIn("文件不存在", result.message)
            self.assertNotEqual(result.message, "")  # 确保有具体错误信息
            print(f"   ✅ 文件不存在错误: {result.message}")
            
            # 测试空数据错误
            self.app_service._change_detector.get_latest_results.return_value = None
            result = self.app_service.generate_word_report()
            self.assertFalse(result.success)
            self.assertIn("没有可生成报告的异动数据", result.message)
            print(f"   ✅ 空数据错误: {result.message}")


class TestDataIntegrity(UserAcceptanceTestBase):
    """数据完整性验证测试"""
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("\n" + "="*60)
        print("🎯 数据一致性测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # Mock一致的测试数据
            test_changes = [
                {"employee_id": "001", "change_type": "调薪", "amount": 500},
                {"employee_id": "002", "change_type": "奖金", "amount": 300}
            ]
            
            mock_detection = Mock()
            mock_detection.changes = test_changes
            
            self.app_service._change_detector.detect_all_changes.return_value = mock_detection
            self.app_service._change_detector.get_latest_results.return_value = mock_detection
            
            # 执行异动检测
            detect_result = self.app_service.detect_changes()
            self.assertTrue(detect_result.success)
            
            # 获取检测结果
            get_result = self.app_service.get_detection_results()
            self.assertTrue(get_result.success)
            
            # 验证数据一致性
            detected_changes = detect_result.data.changes
            retrieved_changes = get_result.data.changes
            
            self.assertEqual(len(detected_changes), len(retrieved_changes))
            self.assertEqual(detected_changes, retrieved_changes)
            
            print(f"   ✅ 数据一致性验证: {len(test_changes)}条记录保持一致")
    
    def test_data_validation_accuracy(self):
        """测试数据验证准确性"""
        print("\n" + "="*60)
        print("✅ 数据验证准确性测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # 测试有效数据验证
            mock_valid_result = Mock()
            mock_valid_result.is_valid = True
            mock_valid_result.error_summary = ""
            self.app_service._data_validator.validate_all.return_value = mock_valid_result
            
            result = self.app_service.validate_data()
            self.assertTrue(result.success)
            print("   ✅ 有效数据验证通过")
            
            # 测试无效数据验证
            mock_invalid_result = Mock()
            mock_invalid_result.is_valid = False
            mock_invalid_result.error_summary = "字段缺失: 工号, 数据类型错误: 工资"
            self.app_service._data_validator.validate_all.return_value = mock_invalid_result
            
            result = self.app_service.validate_data()
            self.assertFalse(result.success)
            self.assertIn("数据验证发现问题", result.message)
            print("   ✅ 无效数据验证正确拒绝")


class TestPerformanceAcceptance(UserAcceptanceTestBase):
    """性能可接受度测试"""
    
    def test_performance_benchmarks(self):
        """测试性能基准可接受度"""
        print("\n" + "="*60)
        print("🚀 性能基准可接受度测试")
        print("="*60)
        
        with patch.multiple(
            'src.core.application_service',
            ConfigManager=Mock(),
            UserPreferences=Mock(),
            ExcelImporter=Mock(),
            DataValidator=Mock(),
            BaselineManager=Mock(),
            ChangeDetector=Mock(),
            ReportManager=Mock()
        ):
            # 初始化服务
            self.app_service.initialize()
            
            # 性能测试场景
            test_scenarios = [
                ("小数据集", 100),
                ("中数据集", 1000), 
                ("大数据集", 5000)
            ]
            
            for scenario_name, data_size in test_scenarios:
                print(f"\n   📊 {scenario_name}性能测试 ({data_size}条记录)")
                
                # 创建测试数据
                test_changes = [
                    {"employee_id": f"{i:06d}", "change_type": "调薪", "amount": 100 + i}
                    for i in range(data_size)
                ]
                
                mock_detection = Mock()
                mock_detection.changes = test_changes
                self.app_service._change_detector.detect_all_changes.return_value = mock_detection
                
                # 测试异动检测性能
                start_time = time.time()
                result = self.app_service.detect_changes()
                detection_time = time.time() - start_time
                
                self.assertTrue(result.success)
                print(f"      ✅ 异动检测: {detection_time:.2f}秒")
                
                # 性能基准验证
                if data_size <= 100:
                    self.assertLess(detection_time, 5.0, f"{scenario_name}异动检测应<5秒")
                elif data_size <= 1000:
                    self.assertLess(detection_time, 10.0, f"{scenario_name}异动检测应<10秒")
                else:
                    self.assertLess(detection_time, 30.0, f"{scenario_name}异动检测应<30秒")
    
    def test_memory_usage_acceptance(self):
        """测试内存使用可接受度"""
        print("\n" + "="*60)
        print("💾 内存使用可接受度测试")
        print("="*60)
        
        try:
            import psutil
            
            # 记录初始内存
            initial_memory = self._get_memory_usage()
            print(f"   📈 初始内存使用: {initial_memory:.2f}MB")
            
            with patch.multiple(
                'src.core.application_service',
                ConfigManager=Mock(),
                UserPreferences=Mock(),
                ExcelImporter=Mock(),
                DataValidator=Mock(),
                BaselineManager=Mock(),
                ChangeDetector=Mock(),
                ReportManager=Mock()
            ):
                # 执行完整工作流程
                self.app_service.initialize()
                
                # 模拟大数据处理
                large_data = [{"id": i} for i in range(10000)]
                
                peak_memory = self._get_memory_usage()
                memory_increase = peak_memory - initial_memory
                
                print(f"   📈 峰值内存使用: {peak_memory:.2f}MB")
                print(f"   📈 内存增长: {memory_increase:.2f}MB")
                
                # 内存使用应在可接受范围内
                self.assertLess(memory_increase, 200.0, "内存增长应<200MB")
                self.assertLess(peak_memory, 500.0, "峰值内存使用应<500MB")
                
        except ImportError:
            print("   ⚠️ psutil不可用，跳过内存测试")


# 测试套件执行器
def run_user_acceptance_tests():
    """运行所有用户接受测试"""
    print("\n" + "="*80)
    print("🎯 用户接受测试 (UAT) 开始执行")
    print("="*80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestEndToEndWorkflow,
        TestBoundaryConditions,
        TestUserExperience,
        TestDataIntegrity,
        TestPerformanceAcceptance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试总结
    print("\n" + "="*80)
    print("📊 用户接受测试总结")
    print("="*80)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors))/result.testsRun*100):.1f}%")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  ❌ {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  🚨 {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    # 运行用户接受测试
    success = run_user_acceptance_tests()
    
    if success:
        print("\n🎉 所有用户接受测试通过！系统准备就绪。")
        exit(0)
    else:
        print("\n⚠️ 部分用户接受测试失败，需要修复后再次测试。")
        exit(1) 