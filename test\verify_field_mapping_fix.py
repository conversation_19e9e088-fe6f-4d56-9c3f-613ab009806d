#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证分页字段映射修复效果

功能：
1. 检查配置文件格式的一致性
2. 验证分页时字段映射的正确应用
3. 测试用户编辑功能的可靠性

创建时间: 2025-01-27
"""

import json
import sys
import pandas as pd
from pathlib import Path
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.config_sync_manager import ConfigSyncManager


def check_config_format_consistency():
    """检查配置文件格式的一致性"""
    print("🔍 检查配置文件格式一致性...")
    
    config_path = "state/data/field_mappings.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 无法加载配置文件: {e}")
        return False
    
    table_mappings = config.get('table_mappings', {})
    
    format_stats = {
        'db_field': 0,      # 数据库字段名格式
        'excel_column': 0,  # Excel列名格式
        'mixed': 0,         # 混合格式
        'unknown': 0        # 未知格式
    }
    
    inconsistent_tables = []
    
    for table_name, table_data in table_mappings.items():
        field_mappings = table_data.get('field_mappings', {})
        
        if not field_mappings:
            continue
        
        # 检测格式
        chinese_keys = 0
        english_keys = 0
        
        for key in field_mappings.keys():
            if re.match(r'^[a-z_][a-z0-9_]*$', key):
                english_keys += 1
            elif any('\u4e00' <= char <= '\u9fff' for char in key):
                chinese_keys += 1
        
        total_keys = len(field_mappings)
        
        if english_keys > total_keys * 0.8:
            format_type = 'db_field'
        elif chinese_keys > total_keys * 0.8:
            format_type = 'excel_column'
            inconsistent_tables.append(table_name)
        elif english_keys > 0 and chinese_keys > 0:
            format_type = 'mixed'
            inconsistent_tables.append(table_name)
        else:
            format_type = 'unknown'
            inconsistent_tables.append(table_name)
        
        format_stats[format_type] += 1
    
    print(f"📊 格式统计:")
    print(f"   数据库字段名格式: {format_stats['db_field']} 个表")
    print(f"   Excel列名格式: {format_stats['excel_column']} 个表")
    print(f"   混合格式: {format_stats['mixed']} 个表")
    print(f"   未知格式: {format_stats['unknown']} 个表")
    
    if inconsistent_tables:
        print(f"\n⚠️  发现 {len(inconsistent_tables)} 个格式不一致的表:")
        for table in inconsistent_tables[:5]:  # 只显示前5个
            print(f"   - {table}")
        if len(inconsistent_tables) > 5:
            print(f"   ... 还有 {len(inconsistent_tables) - 5} 个表")
        return False
    else:
        print("✅ 所有表的字段映射格式一致！")
        return True


def test_pagination_consistency():
    """测试分页时字段映射的一致性"""
    print("\n🔄 测试分页字段映射一致性...")
    
    config_sync = ConfigSyncManager()
    
    # 选择一个测试表
    test_table = "salary_data_2026_08_active_employees"
    
    # 获取字段映射
    field_mapping = config_sync.load_mapping(test_table)
    
    if not field_mapping:
        print(f"❌ 无法加载表 {test_table} 的字段映射")
        return False
    
    print(f"📋 测试表: {test_table}")
    print(f"📝 字段映射数量: {len(field_mapping)}")
    
    # 模拟数据库返回的数据（使用数据库字段名）
    db_columns = list(field_mapping.keys())[:5]  # 取前5个字段
    
    # 创建模拟数据
    mock_data = {}
    for col in db_columns:
        mock_data[col] = [f"测试数据{i}" for i in range(3)]
    
    df = pd.DataFrame(mock_data)
    
    print(f"🗄️  模拟数据库返回的列名: {list(df.columns)}")
    
    # 应用字段映射
    def apply_field_mapping(df, field_mapping):
        """应用字段映射"""
        column_rename_map = {}
        for db_field, display_name in field_mapping.items():
            if db_field in df.columns and display_name:
                column_rename_map[db_field] = display_name
        
        if column_rename_map:
            return df.rename(columns=column_rename_map)
        return df
    
    # 模拟第一页
    page1_df = apply_field_mapping(df.copy(), field_mapping)
    
    # 模拟第二页（相同的字段映射逻辑）
    page2_df = apply_field_mapping(df.copy(), field_mapping)
    
    page1_columns = list(page1_df.columns)
    page2_columns = list(page2_df.columns)
    
    print(f"📄 第一页映射后列名: {page1_columns}")
    print(f"📄 第二页映射后列名: {page2_columns}")
    
    if page1_columns == page2_columns:
        print("✅ 分页字段映射一致性测试通过！")
        return True
    else:
        print("❌ 分页字段映射不一致！")
        return False


def test_user_edit_functionality():
    """测试用户编辑功能"""
    print("\n✏️  测试用户编辑功能...")
    
    config_sync = ConfigSyncManager()
    
    # 选择一个测试表
    test_table = "salary_data_2026_08_active_employees"
    
    # 获取原始映射
    original_mapping = config_sync.load_mapping(test_table)
    
    if not original_mapping:
        print(f"❌ 无法加载表 {test_table} 的字段映射")
        return False
    
    # 选择一个字段进行测试
    test_field = list(original_mapping.keys())[0]
    original_display_name = original_mapping[test_field]
    new_display_name = f"{original_display_name}_测试修改"
    
    print(f"🎯 测试字段: {test_field}")
    print(f"📝 原始显示名: {original_display_name}")
    print(f"📝 新显示名: {new_display_name}")
    
    # 更新字段映射
    success = config_sync.update_single_field_mapping(test_table, test_field, new_display_name)
    
    if not success:
        print("❌ 字段映射更新失败")
        return False
    
    # 验证更新结果
    updated_mapping = config_sync.load_mapping(test_table)
    
    if updated_mapping[test_field] == new_display_name:
        print("✅ 用户编辑功能测试通过！")
        
        # 恢复原始值
        config_sync.update_single_field_mapping(test_table, test_field, original_display_name)
        print("🔄 已恢复原始值")
        return True
    else:
        print("❌ 字段映射更新验证失败")
        return False


def main():
    """主验证函数"""
    print("🚀 开始验证分页字段映射修复效果")
    print("=" * 60)
    
    results = []
    
    # 1. 检查配置格式一致性
    results.append(check_config_format_consistency())
    
    # 2. 测试分页一致性
    results.append(test_pagination_consistency())
    
    # 3. 测试用户编辑功能
    results.append(test_user_edit_functionality())
    
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    
    test_names = [
        "配置格式一致性",
        "分页字段映射一致性", 
        "用户编辑功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    all_passed = all(results)
    
    if all_passed:
        print("\n🎉 所有验证测试通过！分页字段映射问题已彻底解决！")
        print("用户现在可以享受一致、可靠的中文表头显示体验。")
    else:
        print("\n⚠️  部分验证测试失败，需要进一步检查和修复。")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
